# MissionX API Documentation

## Table of Contents
- [Authentication](#authentication)
- [User Management](#user-management)
- [Orders](#orders)
- [Scheduled Orders](#scheduled-orders)
- [Disputes](#disputes)
- [Social Media](#social-media)
- [Personality & Language](#personality--language)
- [Emergency Contacts](#emergency-contacts)
- [User Profile](#user-profile)
- [Referrals & Points](#referrals--points)
- [Levels](#levels)
- [Availability Management](#availability-management)
- [Feedback](#feedback)
- [Gifts](#gifts)
- [Service Configuration](#service-configuration)
- [User Services](#user-services)
- [Talents](#talents)
- [Missions](#missions)
- [User Reviews & Followers](#user-reviews--followers)
- [Bank Accounts](#bank-accounts)
- [Withdrawals](#withdrawals)
- [Chat](#chat)
- [E-KYC](#e-kyc)
- [Payments & Credits](#payments--credits)
- [Races](#races)
- [Static Content](#static-content)
- [Homepage](#homepage)

## Authentication

### OTP & Login
- `POST /auth/request-otp` - Request OTP (throttled: 1 request per minute)
  - Controller: `LoginController@requestOtp`
  - Flow: Validates phone number, generates OTP, sends via SMS
- `POST /auth/login` - User login
  - Controller: `LoginController@login`
  - Flow: Validates credentials, generates JWT token, returns user data
- `POST /auth/register` - User registration
  - Controller: `RegisterController@register`
  - Flow: Validates user data, creates user account, sends verification email
- `POST /auth/logout` - User logout (requires auth)
  - Controller: `LoginController@logout`
  - Flow: Invalidates current session token

### Password Management
- `POST /auth/verify-otp-for-reset` - Verify OTP for password reset
  - Controller: `ForgotPasswordController@verifyOtp`
  - Flow: Validates OTP, generates reset token
- `POST /auth/reset-password` - Reset password
  - Controller: `ForgotPasswordController@resetPassword`
  - Flow: Validates reset token, updates password

### Device Token Management
- `POST /auth/user/device-token` - Store device token (requires auth)
  - Controller: `DeviceTokenController@store`
  - Flow: Stores device token for push notifications
- `DELETE /auth/user/device-token` - Remove device token (requires auth)
  - Controller: `DeviceTokenController@destroy`
  - Flow: Removes device token from user's devices

### OAuth
- `GET /auth/oauth/{provider}/redirect` - OAuth redirect (providers: google, apple)
  - Controller: `OAuthController@redirect`
  - Flow: Redirects to provider's OAuth page
- `GET /auth/oauth/{provider}/callback` - OAuth callback (providers: google, apple)
  - Controller: `OAuthController@callback`
  - Flow: Handles OAuth callback, creates/updates user account

### Email Verification
- `GET /email/verify` - Verify email
  - Controller: `EmailVerificationController@verify`
  - Flow: Verifies email token, updates user status
- `POST /email/send-verification` - Send verification email (requires auth)
  - Controller: `EmailVerificationController@sendVerification`
  - Flow: Generates verification token, sends verification email

## User Management

### User Profile
- `GET /user` - Get current user info (requires auth)
  - Controller: `UserProfileController@getProfile`
  - Flow: Returns authenticated user's profile data
- `GET /user/profile` - Get user profile (requires auth)
  - Controller: `UserProfileController@getProfile`
  - Flow: Returns detailed user profile with settings
- `GET /user/profile/{id}` - Get user profile by ID (requires auth)
  - Controller: `UserProfileController@getProfileById`
  - Flow: Returns public profile data for specified user
- `PUT /user/profile` - Update user profile (requires auth)
  - Controller: `UserProfileController@updateProfile`
  - Flow: Validates and updates user profile data
- `POST /user/profile-picture` - Update profile picture (requires auth)
  - Controller: `UserProfileController@updateProfilePicture`
  - Flow: Handles image upload, updates profile picture
- `GET /user/biography` - Get user biography (requires auth)
  - Controller: `UserProfileController@getBiography`
  - Flow: Returns user's biography
- `PUT /user/biography` - Update user biography (requires auth)
  - Controller: `UserProfileController@updateBiography`
  - Flow: Updates user's biography
- `GET /user/allow-3rd-party-access` - Get 3rd party access setting (requires auth)
  - Controller: `UserProfileController@getAllow3rdPartyAccess`
  - Flow: Returns user's 3rd party access settings
- `PUT /user/allow-3rd-party-access` - Update 3rd party access setting (requires auth)
  - Controller: `UserProfileController@updateAllow3rdPartyAccess`
  - Flow: Updates user's 3rd party access settings
- `POST /user/voice-note` - Upload voice note (requires auth)
  - Controller: `UserProfileController@uploadVoiceNote`
  - Flow: Handles voice note upload, updates user profile
- `DELETE /user/voice-note` - Delete voice note (requires auth)
  - Controller: `UserProfileController@deleteVoiceNote`
  - Flow: Removes voice note from user profile
- `GET /user/all-profile` - Get complete user profile (requires auth)
  - Controller: `UserProfileController@getAllProfile`
  - Flow: Returns complete user profile with all related data
- `GET /user/all-profile/{id}` - Get complete user profile by ID (requires auth)
  - Controller: `UserProfileController@getAllProfileById`
  - Flow: Returns complete profile data for specified user

### Media Management
- `POST /user/media` - Upload media (requires auth)
  - Controller: `UserProfileController@uploadMedia`
  - Flow: Handles media upload, associates with user profile
- `DELETE /user/media` - Delete media (requires auth)
  - Controller: `UserProfileController@deleteMedia`
  - Flow: Removes media from user profile
- `PUT /user/media/reorder` - Reorder media (requires auth)
  - Controller: `UserProfileController@reorderMedia`
  - Flow: Updates media display order

## Orders

### Order Management
- `POST /orders` - Create new order (requires auth)
  - Controller: `OrderController@orderNow`
  - Flow: Validates order data, creates order, notifies talent
- `POST /orders/{orderId}/respond` - Respond to order (requires auth)
  - Controller: `OrderController@respondToOrder`
  - Flow: Handles talent's response to order request
- `POST /orders/{orderId}/complete` - Complete order (requires auth)
  - Controller: `OrderController@completeOrder`
  - Flow: Marks order as completed, processes payment
- `POST /orders/{orderId}/cancel` - Cancel order (requires auth)
  - Controller: `OrderController@cancelOrder`
  - Flow: Cancels order, processes refund if applicable
- `GET /orders/{orderId}` - Get order details (requires auth)
  - Controller: `OrderController@getOrder`
  - Flow: Returns detailed order information
- `GET /orders` - Get user orders (requires auth)
  - Controller: `OrderController@getUserOrders`
  - Flow: Returns list of user's orders with pagination

### Order Reviews
- `POST /orders/{orderId}/reviews` - Create review (requires auth)
  - Controller: `OrderController@createReview`
  - Flow: Creates review for completed order
- `GET /orders/{orderId}/reviews` - Get order reviews (requires auth)
  - Controller: `OrderController@getReviews`
  - Flow: Returns reviews for specific order

### Order Disputes
- `POST /orders/{orderId}/disputes` - Create dispute (requires auth)
  - Controller: `OrderController@disputeOrder`
  - Flow: Creates dispute for order, notifies relevant parties

### Order Status
- `GET /orders/{orderId}/check-overtime` - Check order overtime (requires auth)
  - Controller: `OrderController@checkOrderOvertime`
  - Flow: Checks if order has exceeded time limit
- `GET /orders/talents/{talentId}/wait-time` - Get estimated wait time (requires auth)
  - Controller: `OrderController@getEstimatedWaitTime`
  - Flow: Calculates estimated wait time for talent

## Scheduled Orders

### Scheduled Order Management
- `POST /scheduled-orders` - Create scheduled order (requires auth)
  - Controller: `ScheduledOrderController@orderForLater`
  - Flow: Creates scheduled order, checks availability
- `POST /scheduled-orders/{orderId}/respond` - Respond to scheduled order (requires auth)
  - Controller: `ScheduledOrderController@respondToScheduledOrder`
  - Flow: Handles talent's response to scheduled order
- `POST /scheduled-orders/{orderId}/complete` - Complete scheduled order (requires auth)
  - Controller: `ScheduledOrderController@completeScheduledOrder`
  - Flow: Marks scheduled order as completed
- `POST /scheduled-orders/{orderId}/cancel` - Cancel scheduled order (requires auth)
  - Controller: `ScheduledOrderController@cancelScheduledOrder`
  - Flow: Cancels scheduled order, processes refund if applicable
- `GET /scheduled-orders/{orderId}` - Get scheduled order details (requires auth)
  - Controller: `ScheduledOrderController@getScheduledOrder`
  - Flow: Returns detailed scheduled order information
- `GET /scheduled-orders` - Get user scheduled orders (requires auth)
  - Controller: `ScheduledOrderController@getUserScheduledOrders`
  - Flow: Returns list of user's scheduled orders

### Availability Management
- `GET /scheduled-orders/talents/{talentId}/availability` - Get talent availability (requires auth)
  - Controller: `ScheduledOrderController@getTalentAvailability`
  - Flow: Returns talent's availability schedule
- `GET /scheduled-orders/talents/{talentId}/special-hours` - Get talent special hours (requires auth)
  - Controller: `ScheduledOrderController@getTalentSpecialHours`
  - Flow: Returns talent's special hours
- `GET /scheduled-orders/talents/{talentId}/available-time-slots` - Get available time slots (requires auth)
  - Controller: `ScheduledOrderController@getTalentAvailableTimeSlots`
  - Flow: Returns available time slots for booking
- `POST /scheduled-orders/check-availability` - Check talent availability (requires auth)
  - Controller: `ScheduledOrderController@checkTalentAvailability`
  - Flow: Checks if talent is available for specific time slot

## Disputes

### Dispute Management
- `GET /disputes/types` - Get dispute types (requires auth)
  - Controller: `DisputeController@getDisputeTypes`
  - Flow: Returns available dispute types
- `POST /disputes` - Create dispute (requires auth)
  - Controller: `DisputeController@createDispute`
  - Flow: Creates new dispute, notifies relevant parties
- `GET /disputes` - Get user disputes (requires auth)
  - Controller: `DisputeController@getUserDisputes`
  - Flow: Returns user's disputes with pagination
- `GET /disputes/{id}` - Get dispute details (requires auth)
  - Controller: `DisputeController@getDispute`
  - Flow: Returns detailed dispute information

## Social Media

### Posts
- `GET /social-posts/random` - Get random post (requires auth)
  - Controller: `SocialPostController@random`
  - Flow: Returns random post from feed
- `GET /social-posts/feed` - Get paginated feed (requires auth)
  - Controller: `SocialPostController@paginatedFeed`
  - Flow: Returns paginated social media feed
- `GET /social-posts` - List posts (requires auth)
  - Controller: `SocialPostController@index`
  - Flow: Returns list of posts with filters
- `POST /social-posts` - Create post (requires auth)
  - Controller: `SocialPostController@store`
  - Flow: Creates new social media post
- `GET /social-posts/{id}` - Get post (requires auth)
  - Controller: `SocialPostController@show`
  - Flow: Returns detailed post information
- `PUT /social-posts/{id}` - Update post (requires auth)
  - Controller: `SocialPostController@update`
  - Flow: Updates existing post
- `DELETE /social-posts/{id}` - Delete post (requires auth)
  - Controller: `SocialPostController@destroy`
  - Flow: Deletes post and associated media
- `PUT /social-posts/{id}/hide` - Hide post (requires auth)
  - Controller: `SocialPostController@hidePost`
  - Flow: Hides post from user's feed
- `POST /social-posts/{id}/like` - Toggle like (requires auth)
  - Controller: `SocialPostController@toggleLike`
  - Flow: Toggles like status for post

### Comments
- `GET /social-posts/{postId}/comments` - List comments (requires auth)
  - Controller: `SocialPostCommentController@index`
  - Flow: Returns comments for post
- `POST /social-posts/{postId}/comments` - Create comment (requires auth)
  - Controller: `SocialPostCommentController@store`
  - Flow: Creates new comment on post
- `GET /social-posts/{postId}/comments/{id}` - Get comment (requires auth)
  - Controller: `SocialPostCommentController@show`
  - Flow: Returns detailed comment information
- `PUT /social-posts/{postId}/comments/{id}` - Update comment (requires auth)
  - Controller: `SocialPostCommentController@update`
  - Flow: Updates existing comment
- `DELETE /social-posts/{postId}/comments/{id}` - Delete comment (requires auth)
  - Controller: `SocialPostCommentController@destroy`
  - Flow: Deletes comment

### Places
- `GET /places/search` - Search places (requires auth)
  - Controller: `PlacesController@search`
  - Flow: Searches for places using Google Places API
- `GET /places/details` - Get place details (requires auth)
  - Controller: `PlacesController@details`
  - Flow: Returns detailed place information

## Personality & Language

### Personality
- `GET /personalities` - List personalities (requires auth)
  - Controller: `PersonalityController@index`
  - Flow: Returns available personality types
- `GET /users/personalities` - Get user personalities (requires auth)
  - Controller: `PersonalityController@getUserPersonalities`
  - Flow: Returns user's selected personalities
- `POST /users/personalities` - Update user personalities (requires auth)
  - Controller: `PersonalityController@updateUserPersonalities`
  - Flow: Updates user's personality selections

### Language
- `GET /languages` - List languages (requires auth)
  - Controller: `LanguageController@index`
  - Flow: Returns available languages
- `GET /languages/{id}` - Get language details (requires auth)
  - Controller: `LanguageController@show`
  - Flow: Returns detailed language information
- `GET /users/languages` - Get user languages (requires auth)
  - Controller: `LanguageController@getUserLanguages`
  - Flow: Returns user's selected languages
- `POST /users/languages` - Update user languages (requires auth)
  - Controller: `LanguageController@updateUserLanguages`
  - Flow: Updates user's language selections

## Emergency Contacts

### Contact Management
- `GET /emergency-contacts` - List contacts (requires auth)
  - Controller: `EmergencyContactController@index`
  - Flow: Returns user's emergency contacts
- `POST /emergency-contacts` - Create contact (requires auth)
  - Controller: `EmergencyContactController@store`
  - Flow: Creates new emergency contact
- `GET /emergency-contacts/{id}` - Get contact (requires auth)
  - Controller: `EmergencyContactController@show`
  - Flow: Returns detailed contact information
- `PUT /emergency-contacts/{id}` - Update contact (requires auth)
  - Controller: `EmergencyContactController@update`
  - Flow: Updates existing emergency contact
- `DELETE /emergency-contacts/{id}` - Delete contact (requires auth)
  - Controller: `EmergencyContactController@destroy`
  - Flow: Deletes emergency contact

## Referrals & Points

### Referrals
- `GET /referrals` - Get referred users (requires auth)
  - Controller: `ReferralController@getReferredUsers`
  - Flow: Returns list of users referred by current user
- `POST /referrals/search` - Search referred users (requires auth)
  - Controller: `ReferralController@getReferredUsersByCode`
  - Flow: Searches for users by referral code

### Points
- `GET /points/transactions` - Get point transaction history (requires auth)
  - Controller: `PointController@getTransactionHistory`
  - Flow: Returns user's point transaction history

## Levels

### Level Management
- `GET /levels` - Get all levels (requires auth)
  - Controller: `LevelController@getLevels`
  - Flow: Returns all available levels
- `GET /user/level` - Get user level (requires auth)
  - Controller: `LevelController@getUserLevel`
  - Flow: Returns user's current level
- `GET /user/experience/history` - Get experience history (requires auth)
  - Controller: `LevelController@getExperienceHistory`
  - Flow: Returns user's experience gain history

## Availability Management

### Availability
- `GET /user/availability` - Get availability (requires auth)
  - Controller: `UserAvailabilityController@index`
  - Flow: Returns user's availability schedule
- `POST /user/availability` - Create availability (requires auth)
  - Controller: `UserAvailabilityController@store`
  - Flow: Creates new availability slot
- `GET /user/availability/{id}` - Get availability details (requires auth)
  - Controller: `UserAvailabilityController@show`
  - Flow: Returns detailed availability information
- `PUT /user/availability/{id}` - Update availability (requires auth)
  - Controller: `UserAvailabilityController@update`
  - Flow: Updates existing availability slot
- `DELETE /user/availability/{id}` - Delete availability (requires auth)
  - Controller: `UserAvailabilityController@destroy`
  - Flow: Deletes availability slot
- `POST /user/availability/batch` - Batch update availability (requires auth)
  - Controller: `UserAvailabilityController@batchUpdate`
  - Flow: Updates multiple availability slots
- `GET /user/availability/status` - Get availability status (requires auth)
  - Controller: `UserAvailabilityController@getStatus`
  - Flow: Returns current availability status

### Override
- `POST /user/availability/override` - Set override (requires auth)
  - Controller: `UserAvailabilityController@setOverride`
  - Flow: Sets temporary availability override
- `GET /user/availability/override` - Get override (requires auth)
  - Controller: `UserAvailabilityController@getOverride`
  - Flow: Returns current availability override
- `DELETE /user/availability/override` - Remove override (requires auth)
  - Controller: `UserAvailabilityController@removeOverride`
  - Flow: Removes availability override

## Feedback

### Feedback Management
- `POST /feedback` - Submit feedback (throttled: 10 requests per minute)
  - Controller: `FeedbackController@submitFeedback`
  - Flow: Creates new feedback entry
- `GET /feedback/topics` - Get feedback topics (requires auth)
  - Controller: `FeedbackController@getTopics`
  - Flow: Returns available feedback topics

## Gifts

### Gift Management
- `GET /gifts` - Get available gifts (requires auth)
  - Controller: `GiftController@getAvailableGiftItems`
  - Flow: Returns available gift items
- `POST /gifts/purchase` - Purchase gift (requires auth)
  - Controller: `GiftController@purchaseGiftItem`
  - Flow: Processes gift purchase
- `POST /gifts/redeem` - Redeem gift with points (requires auth)
  - Controller: `GiftController@redeemGiftWithPoints`
  - Flow: Redeems gift using points
- `GET /user/gifts` - Get user gifts (requires auth)
  - Controller: `GiftController@getUserGifts`
  - Flow: Returns user's gift inventory
- `POST /user/gifts/{id}/sell` - Sell gift (requires auth)
  - Controller: `GiftController@sellGift`
  - Flow: Processes gift sale
- `POST /user/gifts/gift` - Gift to user (requires auth)
  - Controller: `GiftController@giftToUser`
  - Flow: Transfers gift to another user
- `GET /user/gifts/statistics` - Get gift statistics (requires auth)
  - Controller: `GiftController@getTotalGiftsReceived`
  - Flow: Returns gift statistics
- `GET /user/gifts/contributors` - Get gift contributors (requires auth)
  - Controller: `GiftController@getGiftContributors`
  - Flow: Returns list of gift contributors
- `GET /user/gifts/inventory-statistics` - Get inventory statistics (requires auth)
  - Controller: `GiftController@getGiftInventoryStatistics`
  - Flow: Returns gift inventory statistics
- `GET /user/gifts/transactions` - Get gift transaction history (requires auth)
  - Controller: `GiftController@getGiftTransactionHistory`
  - Flow: Returns gift transaction history

## Service Configuration

### Service Management
- `GET /service-configuration/categories` - Get service categories (requires auth)
  - Controller: `ServiceConfigurationController@getServiceCategories`
  - Flow: Returns available service categories
- `GET /service-configuration/types` - Get service types (requires auth)
  - Controller: `ServiceConfigurationController@getServiceTypes`
  - Flow: Returns available service types
- `GET /service-configuration/styles` - Get service styles (requires auth)
  - Controller: `ServiceConfigurationController@getServiceStyles`
  - Flow: Returns available service styles
- `GET /service-configuration/pricing-option-types` - Get pricing option types (requires auth)
  - Controller: `ServiceConfigurationController@getPricingOptionTypes`
  - Flow: Returns available pricing option types
- `GET /service-configuration/service-links` - Get service links (requires auth)
  - Controller: `ServiceConfigurationController@getServiceLinks`
  - Flow: Returns service-related links

## User Services

### Service Management
- `GET /user/services` - List user services (requires auth)
  - Controller: `UserServiceController@index`
  - Flow: Returns user's services
- `POST /user/services` - Create service (requires auth)
  - Controller: `UserServiceController@store`
  - Flow: Creates new service
- `GET /user/services/{id}` - Get service details (requires auth)
  - Controller: `UserServiceController@show`
  - Flow: Returns detailed service information
- `GET /user/services/user/{userId}/approved` - Get approved services (requires auth)
  - Controller: `UserServiceController@getApprovedServices`
  - Flow: Returns user's approved services
- `PUT /user/services` - Update service (requires auth)
  - Controller: `UserServiceController@update`
  - Flow: Updates existing service
- `PUT /user/services/{id}/toggle-active` - Toggle service active status (requires auth)
  - Controller: `UserServiceController@toggleActive`
  - Flow: Toggles service active status

## Talents

### Talent Management
- `GET /talents` - Filter talents (requires auth)
  - Controller: `TalentFilterController@filterTalents`
  - Flow: Returns filtered list of talents
- `GET /talents/service-types` - Get filtered service types (requires auth)
  - Controller: `TalentFilterController@getFilteredServiceTypes`
  - Flow: Returns service types with talent counts
- `GET /talents/service-styles` - Get filtered service styles (requires auth)
  - Controller: `TalentFilterController@getFilteredServiceStyles`
  - Flow: Returns service styles with talent counts

## Missions

### Mission Management
- `GET /missions` - List missions (requires auth)
  - Controller: `MissionController@index`
  - Flow: Returns list of available missions
- `POST /missions` - Create mission (requires auth)
  - Controller: `MissionController@store`
  - Flow: Creates new mission
- `GET /missions/{id}` - Get mission details (requires auth)
  - Controller: `MissionController@show`
  - Flow: Returns detailed mission information
- `PUT /missions/{id}` - Update mission (requires auth)
  - Controller: `MissionController@update`
  - Flow: Updates existing mission
- `DELETE /missions/{id}` - Delete mission (requires auth)
  - Controller: `MissionController@destroy`
  - Flow: Deletes mission
- `POST /missions/{id}/complete` - Complete mission (requires auth)
  - Controller: `MissionController@completeMission`
  - Flow: Marks mission as completed
- `POST /missions/{id}/cancel` - Cancel mission (requires auth)
  - Controller: `MissionController@cancelMission`
  - Flow: Cancels mission
- `DELETE /missions/{mission_id}/images/{image_id}` - Delete mission image (requires auth)
  - Controller: `MissionController@deleteImage`
  - Flow: Deletes mission image

### Mission Applicants
- `POST /missions/{mission_id}/apply` - Apply for mission (requires auth)
  - Controller: `MissionApplicantController@apply`
  - Flow: Creates mission application
- `GET /missions/{mission_id}/applicants` - Get applicants (requires auth)
  - Controller: `MissionApplicantController@getApplicants`
  - Flow: Returns list of mission applicants
- `POST /missions/{mission_id}/applicants/{child_id}/approve` - Approve applicant (requires auth)
  - Controller: `MissionApplicantController@approveApplicant`
  - Flow: Approves mission applicant
- `POST /missions/{mission_id}/applicants/{child_id}/reject` - Reject applicant (requires auth)
  - Controller: `MissionApplicantController@rejectApplicant`
  - Flow: Rejects mission applicant

### Mission Bookmarks
- `POST /missions/{mission_id}/bookmark` - Toggle bookmark (requires auth)
  - Controller: `MissionBookmarkController@toggleBookmark`
  - Flow: Toggles mission bookmark status
- `GET /missions/user/bookmarked-missions` - Get bookmarked missions (requires auth)
  - Controller: `MissionBookmarkController@index`
  - Flow: Returns user's bookmarked missions

### Mission Disputes
- `POST /missions/{mission}/disputes` - Create mission dispute (requires auth)
  - Controller: `MissionDisputeController@store`
  - Flow: Creates mission dispute

## User Reviews & Followers

### Reviews
- `GET /users/{userId}/reviews` - Get user reviews (requires auth)
  - Controller: `UserReviewController@getUserReviews`
  - Flow: Returns user's reviews

### Followers
- `POST /users/{userId}/follow` - Toggle follow (requires auth)
  - Controller: `UserFollowerController@toggleFollow`
  - Flow: Toggles follow status
- `GET /users/{userId}/followers` - Get followers (requires auth)
  - Controller: `UserFollowerController@getFollowers`
  - Flow: Returns user's followers
- `GET /users/{userId}/following` - Get following (requires auth)
  - Controller: `UserFollowerController@getFollowing`
  - Flow: Returns users being followed

## Bank Accounts

### Account Management
- `GET /user/bank-accounts` - List bank accounts (requires auth)
  - Controller: `UserBankAccountController@index`
  - Flow: Returns user's bank accounts
- `POST /user/bank-accounts` - Create bank account (requires auth)
  - Controller: `UserBankAccountController@store`
  - Flow: Creates new bank account
- `PUT /user/bank-accounts/{id}` - Update bank account (requires auth)
  - Controller: `UserBankAccountController@update`
  - Flow: Updates existing bank account
- `DELETE /user/bank-accounts/{id}` - Delete bank account (requires auth)
  - Controller: `UserBankAccountController@destroy`
  - Flow: Deletes bank account
- `POST /user/bank-accounts/{id}/set-primary` - Set primary account (requires auth)
  - Controller: `UserBankAccountController@setPrimary`
  - Flow: Sets bank account as primary

## Withdrawals

### Withdrawal Management
- `POST /withdrawals/credits` - Withdraw credits (requires auth, eKYC, email verification)
  - Controller: `WithdrawalController@withdrawCredits`
  - Flow: Processes credit withdrawal
- `GET /withdrawals/currencies` - Get available currencies (requires auth, eKYC, email verification)
  - Controller: `WithdrawalController@getAvailableCurrencies`
  - Flow: Returns available withdrawal currencies
- `GET /withdrawals/history` - Get transaction history (requires auth, eKYC, email verification)
  - Controller: `WithdrawalController@getTransactionHistory`
  - Flow: Returns withdrawal transaction history
- `GET /withdrawals/{id}` - Get transaction details (requires auth, eKYC, email verification)
  - Controller: `WithdrawalController@getTransaction`
  - Flow: Returns detailed transaction information

### Gift Exchange
- `POST /gifts/exchange` - Exchange gift for credits (requires auth)
  - Controller: `WithdrawalController@exchangeGiftForCredits`
  - Flow: Exchanges gift for credits

## Chat

### Conversation Management
- `GET /chat/conversations` - Get conversations (requires auth)
  - Controller: `ChatController@getConversations`
  - Flow: Returns user's conversations
- `GET /chat/conversations/{conversationId}/messages` - Get messages (requires auth)
  - Controller: `ChatController@getMessages`
  - Flow: Returns conversation messages
- `POST /chat/conversations` - Start conversation (requires auth)
  - Controller: `ChatController@startConversation`
  - Flow: Creates new conversation

### Message Management
- `POST /chat/messages/text` - Send text message (requires auth)
  - Controller: `ChatController@sendTextMessage`
  - Flow: Sends text message
- `POST /chat/messages/attachment` - Send message with attachment (requires auth)
  - Controller: `ChatController@sendMessageWithAttachment`
  - Flow: Sends message with attachment
- `POST /chat/messages/status` - Update message status (requires auth)
  - Controller: `ChatController@updateMessageStatus`
  - Flow: Updates message status

## E-KYC

### Verification
- `POST /ekyc/malaysian` - Verify Malaysian (requires auth)
  - Controller: `EKycController@verifyMalaysian`
  - Flow: Processes Malaysian ID verification
- `POST /ekyc/foreigner` - Verify foreigner (requires auth)
  - Controller: `EKycController@verifyForeigner`
  - Flow: Processes foreigner ID verification
- `GET /ekyc/status` - Get verification status (requires auth)
  - Controller: `EKycController@getVerificationStatus`
  - Flow: Returns verification status
- `GET /ekyc/history` - Get verification history (requires auth)
  - Controller: `EKycController@getVerificationHistory`
  - Flow: Returns verification history
- `GET /ekyc/methods` - Get available methods (requires auth)
  - Controller: `EKycController@getAvailableMethods`
  - Flow: Returns available verification methods

### Manual Verification (Backoffice)
- `GET /ekyc/manual/pending` - Get pending verifications (requires backoffice auth)
  - Controller: `EKycController@getPendingManualVerifications`
  - Flow: Returns pending manual verifications
- `POST /ekyc/manual/{id}/approve` - Approve verification (requires backoffice auth)
  - Controller: `EKycController@approveManualVerification`
  - Flow: Approves manual verification
- `POST /ekyc/manual/{id}/reject` - Reject verification (requires backoffice auth)
  - Controller: `EKycController@rejectManualVerification`
  - Flow: Rejects manual verification

## Payments & Credits

### Payment Management
- `POST /payments` - Create payment (requires auth)
  - Controller: `PaymentController@createPayment`
  - Flow: Creates new payment
- `POST /payments/retry` - Retry payment (requires auth)
  - Controller: `PaymentController@retryPayment`
  - Flow: Retries failed payment
- `POST /payments/callback` - Payment callback
  - Controller: `PaymentController@handleCallback`
  - Flow: Handles payment gateway callback
- `GET /payments/return` - Payment return
  - Controller: `PaymentController@handleReturn`
  - Flow: Handles payment return URL

### Game Profiles
- `GET /user-game-profiles` - List game profiles (requires auth)
  - Controller: `UserGameProfileController@index`
  - Flow: Returns user's game profiles
- `POST /user-game-profiles` - Create game profile (requires auth)
  - Controller: `UserGameProfileController@store`
  - Flow: Creates new game profile
- `GET /user-game-profiles/{id}` - Get game profile (requires auth)
  - Controller: `UserGameProfileController@show`
  - Flow: Returns detailed game profile
- `PUT /user-game-profiles/{id}` - Update game profile (requires auth)
  - Controller: `UserGameProfileController@update`
  - Flow: Updates existing game profile
- `DELETE /user-game-profiles/{id}` - Delete game profile (requires auth)
  - Controller: `UserGameProfileController@destroy`
  - Flow: Deletes game profile
- `PUT /user-game-profiles/{id}/visible` - Set profile visibility (requires auth)
  - Controller: `UserGameProfileController@setVisible`
  - Flow: Updates profile visibility

### Credits
- `GET /credits/balance` - Get credit balance (requires auth)
  - Controller: `CreditController@getBalance`
  - Flow: Returns user's credit balance
- `GET /credits/transactions` - Get transaction history (requires auth)
  - Controller: `CreditController@getTransactionHistory`
  - Flow: Returns credit transaction history
- `GET /credits/{channel}/packages` - Get available packages (requires auth)
  - Controller: `CreditController@getAvailablePackages`
  - Flow: Returns available credit packages

## Races

### Race Management
- `GET /races` - List races
  - Controller: `RaceController@index`
  - Flow: Returns available races

## Static Content

### Content Management
- `GET /static-content/terms-conditions` - Get terms and conditions
  - Controller: `StaticContentController@getTermsConditions`
  - Flow: Returns terms and conditions
- `GET /static-content/privacy-policy` - Get privacy policy
  - Controller: `StaticContentController@getPrivacyPolicy`
  - Flow: Returns privacy policy
- `GET /static-content/about-us` - Get about us
  - Controller: `StaticContentController@getAboutUs`
  - Flow: Returns about us content
- `GET /static-content/contact-us` - Get contact us
  - Controller: `StaticContentController@getContactUs`
  - Flow: Returns contact information
- `GET /static-content/allow-3rd-party-access` - Get 3rd party access content
  - Controller: `UserSettingController@getAllow3rdPartyAccess`
  - Flow: Returns 3rd party access content

### Carousel
- `GET /carousel-slides` - List carousel slides
  - Controller: `CarouselSlideController@index`
  - Flow: Returns carousel slides
- `POST /carousel-slides` - Create carousel slide
  - Controller: `CarouselSlideController@store`
  - Flow: Creates new carousel slide
- `GET /carousel-slides/{id}` - Get carousel slide
  - Controller: `CarouselSlideController@show`
  - Flow: Returns detailed slide information
- `PUT /carousel-slides/{id}` - Update carousel slide
  - Controller: `CarouselSlideController@update`
  - Flow: Updates existing slide
- `DELETE /carousel-slides/{id}` - Delete carousel slide
  - Controller: `CarouselSlideController@destroy`
  - Flow: Deletes carousel slide

## Homepage

### Homepage Data
- `GET /homepage` - Get homepage data
  - Controller: `HomeController@getHomepageData`
  - Flow: Returns homepage data
- `GET /homepage/new-talents` - Get new registered talents
  - Controller: `HomeController@getNewRegisteredTalents`
  - Flow: Returns newly registered talents
- `GET /homepage/recommended-talents` - Get recommended talents
  - Controller: `HomeController@getRecommendedTalents`
  - Flow: Returns recommended talents
- `GET /homepage/online-talents` - Get online talents
  - Controller: `HomeController@getOnlineTalents`
  - Flow: Returns online talents
- `GET /homepage/available-missions-count` - Get available missions count
  - Controller: `HomeController@getAvailableMissionsCount`
  - Flow: Returns count of available missions
- `GET /homepage/popular-service-types` - Get popular service types
  - Controller: `HomeController@getPopularServiceTypes`
  - Flow: Returns popular service types 