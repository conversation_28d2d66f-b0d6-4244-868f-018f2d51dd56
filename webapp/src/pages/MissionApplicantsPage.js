import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import MissionLayout from '../components/mission/MissionLayout';
import RejectionModal from '../components/mission/RejectionModal';
import ApplicantCard from '../components/mission/ApplicantCard';
import { missionApi } from '../services/missionApi';
import { transformApiMissionToFrontend } from '../utils/missionTransformers';
import { getApiErrorMessage } from '../utils/errorHandlers';
import { useToast } from '../components/common/ToastProvider';

const MissionApplicantsPage = () => {
  const { missionId } = useParams();
  const navigate = useNavigate();
  const toast = useToast();
  const [mission, setMission] = useState(null);
  const [applicants, setApplicants] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedApplicants, setSelectedApplicants] = useState([]);
  const [filter, setFilter] = useState('all');
  const [showRejectionModal, setShowRejectionModal] = useState(false);
  const [currentApplicant, setCurrentApplicant] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);

  // Fetch mission and applicants data
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Fetch mission details
        const missionResponse = await missionApi.getMissionById(missionId);
        const transformedMission = transformApiMissionToFrontend(missionResponse.data);

        if (!transformedMission) {
          setError('Mission not found');
          setIsLoading(false);
          return;
        }

        setMission(transformedMission);

        // Fetch applicants
        const applicantsResponse = await missionApi.getMissionApplicants(missionId);
        setApplicants(applicantsResponse.data);
      } catch (error) {
        console.error('Error fetching mission data:', error);
        setError(getApiErrorMessage(error));
        toast.error('Failed to load mission data. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [missionId, toast]);

  // Format date for display
  const formatDate = (dateString) => {
    const options = {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    };
    return new Date(dateString).toLocaleDateString('en-US', options);
  };



  // Handle applicant selection
  const toggleApplicantSelection = (applicantId) => {
    if (selectedApplicants.includes(applicantId)) {
      setSelectedApplicants(selectedApplicants.filter(id => id !== applicantId));
    } else {
      setSelectedApplicants([...selectedApplicants, applicantId]);
    }
  };

  // Handle accept applicant
  const handleAccept = async (applicantId) => {
    setIsProcessing(true);

    try {
      // Show loading toast
      toast.loading('Accepting applicant...');

      // Call API to accept applicant
      const response = await missionApi.approveApplicant(missionId, applicantId);

      // Update local state
      setApplicants(applicants.map(applicant =>
        applicant.id === applicantId ? { ...applicant, status: 'approved' } : applicant
      ));

      // Show success toast
      toast.success('Applicant accepted successfully');

      // Update mission slots if needed
      if (response.data.mission_status) {
        // Refresh mission data
        const missionResponse = await missionApi.getMissionById(missionId);
        const transformedMission = transformApiMissionToFrontend(missionResponse.data);
        setMission(transformedMission);
      }
    } catch (error) {
      console.error('Error accepting applicant:', error);
      toast.error(getApiErrorMessage(error) || 'Failed to accept applicant');
    } finally {
      setIsProcessing(false);
    }
  };

  // Open rejection modal
  const openRejectionModal = (applicant) => {
    setCurrentApplicant(applicant);
    setShowRejectionModal(true);
  };

  // Handle reject applicant
  const handleReject = async (rejectionData) => {
    if (!currentApplicant) return;

    setIsProcessing(true);
    setShowRejectionModal(false);

    try {
      // Show loading toast
      toast.loading('Rejecting applicant...');

      // Call API to reject applicant
      await missionApi.rejectApplicant(missionId, currentApplicant.id, {
        rejection_reason: rejectionData.reason
      });

      // Update local state
      setApplicants(applicants.map(applicant =>
        applicant.id === currentApplicant.id ? { ...applicant, status: 'rejected' } : applicant
      ));

      // Show success toast
      toast.success('Applicant rejected successfully');
    } catch (error) {
      console.error('Error rejecting applicant:', error);
      toast.error(getApiErrorMessage(error) || 'Failed to reject applicant');
    } finally {
      setIsProcessing(false);
      setCurrentApplicant(null);
    }
  };

  // Handle bulk actions
  const handleBulkAction = async (action, applicantIds) => {
    if (applicantIds.length === 0) return;

    setIsProcessing(true);

    try {
      // Show loading toast
      toast.loading(`Processing ${applicantIds.length} applicants...`);

      // Process each selected applicant individually
      const promises = applicantIds.map(applicantId => {
        const applicant = applicants.find(a => a.id === applicantId);
        if (!applicant) return Promise.resolve();

        if (action === 'approve') {
          return missionApi.approveApplicant(missionId, applicant.child_id);
        } else if (action === 'reject') {
          return missionApi.rejectApplicant(missionId, applicant.child_id, {
            rejection_reason: 'Bulk rejection'
          });
        }
        return Promise.resolve();
      });

      await Promise.all(promises);

      // Update local state
      setApplicants(applicants.map(applicant =>
        applicantIds.includes(applicant.id) ? { ...applicant, status: action === 'approve' ? 'approved' : 'rejected' } : applicant
      ));

      // Show success toast
      toast.success(`${applicantIds.length} applicants processed successfully`);

      // Clear selection
      setSelectedApplicants([]);

      // Refresh mission data
      const missionResponse = await missionApi.getMissionById(missionId);
      const transformedMission = transformApiMissionToFrontend(missionResponse.data);
      setMission(transformedMission);
    } catch (error) {
      console.error('Error processing bulk action:', error);
      toast.error(getApiErrorMessage(error) || 'Failed to process applicants');
    } finally {
      setIsProcessing(false);
    }
  };

  // Filter applicants
  const filteredApplicants = applicants.filter(applicant => {
    if (filter === 'all') return true;
    return applicant.status === filter;
  });

  // Create custom actions for the navigation bar
  const navActions = (
    <button
      onClick={() => navigate(`/missions/${missionId}`)}
      className="px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 transition-colors flex items-center"
    >
      <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
      </svg>
      View Mission
    </button>
  );

  return (
    <MissionLayout
      title="Mission Applicants"
      backPath="/missions/my-missions"
      actions={navActions}
    >
        {isLoading ? (
          <div className="flex justify-center py-20">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
          </div>
        ) : error ? (
          <div className="bg-white rounded-xl shadow-md p-8 text-center max-w-md mx-auto">
            <svg className="w-16 h-16 text-red-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h3 className="text-lg font-medium text-gray-900 mb-1">Error Loading Mission</h3>
            <p className="text-gray-500 mb-4">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-indigo-600 text-white rounded-lg font-medium hover:bg-indigo-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        ) : (
          <>
            {/* Mission Info */}
            <motion.div
              className="bg-white rounded-2xl shadow-md overflow-hidden mb-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <div className="p-6">
                <h2 className="text-xl font-bold text-gray-800 mb-2">{mission.title}</h2>
                <div className="flex flex-wrap gap-4 mb-4">
                  <div className="flex items-center text-gray-600">
                    <svg className="w-5 h-5 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    {formatDate(mission.date)}
                  </div>
                  <div className="flex items-center text-gray-600">
                    <svg className="w-5 h-5 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    {mission.slots_filled}/{mission.slots_total} slots filled
                  </div>
                  <div className="flex items-center text-indigo-600 font-medium">
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    {mission.bounty} credits per slot
                  </div>
                </div>

                {/* Slots Progress */}
                <div className="mb-4">
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-sm font-medium text-gray-700">Slots Filled</span>
                    <span className="text-sm text-gray-500">{mission.slots_filled}/{mission.slots_total}</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2.5">
                    <div
                      className="bg-indigo-600 h-2.5 rounded-full"
                      style={{ width: `${(mission.slots_filled / mission.slots_total) * 100}%` }}
                    ></div>
                  </div>
                </div>

                {/* Remaining Slots */}
                <div className="bg-indigo-50 p-4 rounded-lg">
                  <p className="text-indigo-800">
                    <span className="font-medium">{mission.slots_total - mission.slots_filled}</span> {mission.slots_total - mission.slots_filled === 1 ? 'slot' : 'slots'} remaining to be filled.
                    {applicants.filter(a => a.status === 'pending').length > 0 && (
                      <span> You have {applicants.filter(a => a.status === 'pending').length} pending {applicants.filter(a => a.status === 'pending').length === 1 ? 'application' : 'applications'}.</span>
                    )}
                  </p>
                </div>
              </div>
            </motion.div>

            {/* Applicants List */}
            <motion.div
              className="bg-white rounded-2xl shadow-md overflow-hidden"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              {/* Filters and Actions */}
              <div className="p-6 border-b border-gray-100">
                <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                  {/* Filters */}
                  <div className="flex items-center space-x-2">
                    <button
                      className={`px-3 py-1 rounded-lg text-sm font-medium ${filter === 'all' ? 'bg-indigo-100 text-indigo-800' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'} transition-colors`}
                      onClick={() => setFilter('all')}
                    >
                      All ({applicants.length})
                    </button>
                    <button
                      className={`px-3 py-1 rounded-lg text-sm font-medium ${filter === 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'} transition-colors`}
                      onClick={() => setFilter('pending')}
                    >
                      Pending ({applicants.filter(a => a.status === 'pending').length})
                    </button>
                    <button
                      className={`px-3 py-1 rounded-lg text-sm font-medium ${filter === 'accepted' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'} transition-colors`}
                      onClick={() => setFilter('accepted')}
                    >
                      Accepted ({applicants.filter(a => a.status === 'accepted').length})
                    </button>
                    <button
                      className={`px-3 py-1 rounded-lg text-sm font-medium ${filter === 'rejected' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'} transition-colors`}
                      onClick={() => setFilter('rejected')}
                    >
                      Rejected ({applicants.filter(a => a.status === 'rejected').length})
                    </button>
                  </div>


                </div>
              </div>

              {/* Bulk Actions */}
              {selectedApplicants.length > 0 && (
                <div className="mx-6 mb-6 flex items-center space-x-2 p-4 bg-indigo-50 border border-indigo-200 rounded-lg">
                  <span className="text-sm text-gray-700">
                    {selectedApplicants.length} selected
                  </span>
                  <button
                    className="px-3 py-1 bg-green-100 text-green-800 rounded-lg text-sm font-medium hover:bg-green-200 transition-colors"
                    onClick={() => handleBulkAction('approve', selectedApplicants)}
                    disabled={isProcessing}
                  >
                    {isProcessing ? 'Processing...' : 'Accept All'}
                  </button>
                  <button
                    className="px-3 py-1 bg-red-100 text-red-800 rounded-lg text-sm font-medium hover:bg-red-200 transition-colors"
                    onClick={() => handleBulkAction('reject', selectedApplicants)}
                    disabled={isProcessing}
                  >
                    {isProcessing ? 'Processing...' : 'Reject All'}
                  </button>
                  <button
                    className="px-3 py-1 bg-gray-100 text-gray-700 rounded-lg text-sm font-medium hover:bg-gray-200 transition-colors"
                    onClick={() => setSelectedApplicants([])}
                    disabled={isProcessing}
                  >
                    Clear
                  </button>
                </div>
              )}

              {/* Applicants */}
              <div className="divide-y divide-gray-100">
                {filteredApplicants.length > 0 ? (
                  filteredApplicants.map(applicant => (
                    <ApplicantCard
                      key={applicant.id}
                      applicant={applicant}
                      isSelected={selectedApplicants.includes(applicant.id)}
                      onToggleSelect={() => toggleApplicantSelection(applicant.id)}
                      onAccept={() => handleAccept(applicant.id)}
                      onReject={() => openRejectionModal(applicant)}
                      isProcessing={isProcessing}
                    />
                  ))
                ) : (
                  <div className="p-8 text-center">
                    <svg className="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                    <h3 className="text-lg font-medium text-gray-900 mb-1">No applicants found</h3>
                    <p className="text-gray-500">
                      {filter !== 'all'
                        ? `No ${filter} applicants for this mission.`
                        : 'No one has applied to this mission yet.'}
                    </p>
                  </div>
                )}
              </div>
            </motion.div>
          </>
        )}
      {/* Rejection Modal */}
      <RejectionModal
        isOpen={showRejectionModal}
        onClose={() => setShowRejectionModal(false)}
        onSubmit={handleReject}
        applicant={currentApplicant}
      />
    </MissionLayout>
  );
};

export default MissionApplicantsPage;
