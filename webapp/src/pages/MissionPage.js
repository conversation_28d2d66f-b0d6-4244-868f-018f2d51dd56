import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import MissionListing from '../components/mission/MissionListing';
import MissionLayout from '../components/mission/MissionLayout';

import { missionApi } from '../services/missionApi';
import { transformApiMissionsToFrontend } from '../utils/missionTransformers';
import { getApiErrorMessage } from '../utils/errorHandlers';

// This will be our main Mission Page component
const MissionPage = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('browse');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [missions, setMissions] = useState([]);

  // Mission statistics
  const [missionStats, setMissionStats] = useState({
    completed: 0,
    active: 0,
    hosted: 0
  });

  // Fetch missions from API
  useEffect(() => {
    const fetchMissions = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Map frontend tabs to API status
        let statusFilter = '';
        if (activeTab === 'browse') {
          statusFilter = 'open';
        } else if (activeTab === 'featured') {
          // For featured missions, we'll show high-bounty or popular missions
          statusFilter = 'open';
        }

        // Fetch missions with status filter
        const response = await missionApi.getMissions({
          status: statusFilter
        });

        // Transform API missions to frontend format
        const transformedMissions = transformApiMissionsToFrontend(response.data.data);

        // Update missions state
        setMissions(transformedMissions);

        // Update mission stats
        const openMissions = response.data.data.filter(m => m.status === 'open').length;
        const completedMissions = response.data.data.filter(m => m.status === 'completed').length;

        // For hosted missions, we need to make a separate API call
        // This is a placeholder until we implement the hosted missions API
        const hostedMissions = 0;

        setMissionStats({
          active: openMissions,
          completed: completedMissions,
          hosted: hostedMissions
        });
      } catch (error) {
        console.error('Error fetching missions:', error);
        setError(getApiErrorMessage(error));
      } finally {
        setIsLoading(false);
      }
    };

    fetchMissions();
  }, [activeTab]);

  return (
    <MissionLayout
      title="Missions"
      showBackButton={false}
      useMainNav={true}
      useMissionMobileNav={true}
    >
      {/* Enhanced Mission Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="bg-gradient-to-r from-indigo-600 to-blue-600 rounded-2xl shadow-lg p-8 mb-8 text-white"
      >
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
          <div className="mb-6 lg:mb-0">
            <h1 className="text-4xl font-bold mb-2">Discover Missions</h1>
            <p className="text-indigo-100 text-lg">
              Join gaming missions, earn rewards, and connect with fellow gamers
            </p>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-3 gap-4 lg:gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold">{missionStats.active}</div>
              <div className="text-indigo-200 text-sm">Active</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{missionStats.completed}</div>
              <div className="text-indigo-200 text-sm">Completed</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{missionStats.hosted}</div>
              <div className="text-indigo-200 text-sm">Hosted</div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="flex flex-col sm:flex-row gap-3 mt-6">
          <motion.button
            onClick={() => navigate('/missions/create')}
            className="flex items-center justify-center px-6 py-3 bg-white text-indigo-600 rounded-lg font-medium hover:bg-indigo-50 transition-colors shadow-md"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Create Mission
          </motion.button>

          <motion.button
            onClick={() => navigate('/missions/my-missions')}
            className="flex items-center justify-center px-6 py-3 bg-white/10 text-white rounded-lg font-medium hover:bg-white/20 transition-colors border border-white/20"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
            My Missions
          </motion.button>
        </div>
      </motion.div>

      {/* Missions For You Section */}
      {error ? (
        <div className="bg-white rounded-2xl shadow-md p-8">
          <div className="text-center">
            <div className="flex flex-col items-center">
              <svg className="w-16 h-16 text-red-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <h3 className="text-lg font-medium text-gray-900 mb-1">Error Loading Missions</h3>
              <p className="text-gray-500 mb-4">{error}</p>
              <motion.button
                onClick={() => window.location.reload()}
                className="px-6 py-2.5 bg-indigo-600 text-white rounded-lg font-medium hover:bg-indigo-700 transition-colors shadow-md"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <svg className="w-4 h-4 mr-2 inline-block" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                Retry
              </motion.button>
            </div>
          </div>
        </div>
      ) : (
        <MissionListing
          missions={activeTab === 'featured' ? missions.filter(m => m.bounty >= 500) : missions}
          isLoading={isLoading}
          showViewToggle={true}
          viewToggleOptions={[
            { id: 'browse', label: 'Browse All', icon: 'M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z', count: missionStats.active },
            { id: 'featured', label: 'Featured', icon: 'M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z' }
          ]}
          activeView={activeTab}
          onViewChange={setActiveTab}
        />
      )}
    </MissionLayout>
  );
};

export default MissionPage;
