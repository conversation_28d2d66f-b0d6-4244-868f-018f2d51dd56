import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import MissionPreview from '../components/mission/MissionPreview';
import MissionLayout from '../components/mission/MissionLayout';
import WalletBalanceCard from '../components/mission/WalletBalanceCard';
import MissionPaymentSummary from '../components/mission/MissionPaymentSummary';
import MissionImageUploader from '../components/mission/MissionImageUploader';
import { missionApi } from '../services/missionApi';
import { referenceDataApi } from '../services/referenceDataApi';
import { transformFrontendMissionToApi } from '../utils/missionTransformers';
import { getApiErrorMessage } from '../utils/errorHandlers';
import { useToast } from '../components/common/ToastProvider';
import missionPaymentService from '../services/missionPaymentService';

// Step 1: Basic Information Component
const BasicInfoStep = ({ formData, setFormData, goToNextStep }) => {
  const [errors, setErrors] = useState({});
  const [isValidating, setIsValidating] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });

    // Clear error for this field when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateStep = () => {
    const newErrors = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Mission title is required';
    } else if (formData.title.length < 5) {
      newErrors.title = 'Mission title must be at least 5 characters';
    } else if (formData.title.length > 100) {
      newErrors.title = 'Mission title must be less than 100 characters';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Mission description is required';
    } else if (formData.description.length < 20) {
      newErrors.description = 'Description must be at least 20 characters';
    } else if (formData.description.length > 500) {
      newErrors.description = 'Description must be less than 500 characters';
    }

    if (!formData.bounty || formData.bounty <= 0) {
      newErrors.bounty = 'Bounty must be greater than 0';
    } else if (formData.bounty < 50) {
      newErrors.bounty = 'Minimum bounty is 50 credits';
    } else if (formData.bounty > 10000) {
      newErrors.bounty = 'Maximum bounty is 10,000 credits';
    }

    if (!formData.slotsTotal || formData.slotsTotal <= 0) {
      newErrors.slotsTotal = 'Number of participants must be greater than 0';
    } else if (formData.slotsTotal > 20) {
      newErrors.slotsTotal = 'Maximum 20 participants allowed';
    }

    if (!formData.date) {
      newErrors.date = 'Start date is required';
    } else {
      const startDate = new Date(formData.date);
      const now = new Date();
      const minDate = new Date(now.getTime() + 30 * 60 * 1000); // 30 minutes from now

      if (startDate < minDate) {
        newErrors.date = 'Start date must be at least 30 minutes from now';
      }
    }

    if (!formData.endDate) {
      newErrors.endDate = 'End date is required';
    } else if (formData.date) {
      const startDate = new Date(formData.date);
      const endDate = new Date(formData.endDate);
      const minDuration = 30 * 60 * 1000; // 30 minutes

      if (endDate <= startDate) {
        newErrors.endDate = 'End date must be after start date';
      } else if (endDate - startDate < minDuration) {
        newErrors.endDate = 'Mission must be at least 30 minutes long';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const isStepValid = () => {
    return (
      formData.title.trim() !== '' &&
      formData.description.trim() !== '' &&
      formData.bounty > 0 &&
      formData.slotsTotal > 0 &&
      formData.date !== '' &&
      formData.endDate !== ''
    );
  };

  const handleNext = () => {
    setIsValidating(true);
    if (validateStep()) {
      goToNextStep();
    }
    setIsValidating(false);
  };

  return (
    <div className="p-6 w-full max-w-2xl sm:max-w-2xl ">
      <div className="mb-6">
        <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
          Mission Title*
        </label>
        <div className="relative">
          <input
            type="text"
            id="title"
            name="title"
            value={formData.title}
            onChange={handleChange}
            className={`w-full border ${errors.title ? 'border-red-500' : 'border-gray-300'} rounded-lg px-4 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500`}
            placeholder="E.g., MLBB - Tank, Mage Support Needed!"
            required
          />
          <div className="absolute right-3 top-2 text-xs text-gray-400">
            {formData.title.length}/100
          </div>
        </div>
        {errors.title && (
          <p className="mt-1 text-sm text-red-500">{errors.title}</p>
        )}
        <p className="mt-1 text-xs text-gray-500">
          Choose a clear, descriptive title that explains what you need help with
        </p>
      </div>

      <div className="mb-6">
        <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
          Description*
        </label>
        <div className="relative">
          <textarea
            id="description"
            name="description"
            value={formData.description}
            onChange={handleChange}
            rows="5"
            className={`w-full border ${errors.description ? 'border-red-500' : 'border-gray-300'} rounded-lg px-4 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 resize-none`}
            placeholder="Describe your mission in detail. What game are you playing? What roles do you need? What's the objective?"
            required
          ></textarea>
          <div className="absolute right-3 bottom-2 text-xs text-gray-400">
            {formData.description.length}/500
          </div>
        </div>
        {errors.description && (
          <p className="mt-1 text-sm text-red-500">{errors.description}</p>
        )}
        <p className="mt-1 text-xs text-gray-500">
          Provide clear details about your mission, including game type, objectives, and what you expect from participants
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div>
          <label htmlFor="bounty" className="block text-sm font-medium text-gray-700 mb-1">
            Bounty (Credits)*
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <span className="text-gray-500">₵</span>
            </div>
            <input
              type="number"
              id="bounty"
              name="bounty"
              value={formData.bounty}
              onChange={handleChange}
              min="50"
              max="10000"
              className={`w-full border ${errors.bounty ? 'border-red-500' : 'border-gray-300'} rounded-lg pl-8 pr-4 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500`}
              required
            />
          </div>
          {errors.bounty && (
            <p className="mt-1 text-sm text-red-500">{errors.bounty}</p>
          )}
          <p className="mt-1 text-xs text-gray-500">
            Amount of credits to pay each participant (50-10,000)
          </p>
          {formData.bounty > 0 && (
            <div className="mt-2 p-2 bg-blue-50 rounded-lg">
              <p className="text-xs text-blue-700">
                <strong>Total cost:</strong> ₵{formData.bounty * formData.slotsTotal} + ₵{Math.ceil(formData.bounty * formData.slotsTotal * 0.05)} platform fee = ₵{formData.bounty * formData.slotsTotal + Math.ceil(formData.bounty * formData.slotsTotal * 0.05)}
              </p>
            </div>
          )}
        </div>
        <div>
          <label htmlFor="slotsTotal" className="block text-sm font-medium text-gray-700 mb-1">
            Number of Participants*
          </label>
          <input
            type="number"
            id="slotsTotal"
            name="slotsTotal"
            value={formData.slotsTotal}
            onChange={handleChange}
            min="1"
            max="20"
            className={`w-full border ${errors.slotsTotal ? 'border-red-500' : 'border-gray-300'} rounded-lg px-4 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500`}
            required
          />
          {errors.slotsTotal && (
            <p className="mt-1 text-sm text-red-500">{errors.slotsTotal}</p>
          )}
          <p className="mt-1 text-xs text-gray-500">
            How many participants do you need? (1-20)
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div>
          <label htmlFor="date" className="block text-sm font-medium text-gray-700 mb-1">
            Start Date & Time*
          </label>
          <input
            type="datetime-local"
            id="date"
            name="date"
            value={formData.date}
            onChange={handleChange}
            min={new Date(Date.now() + 30 * 60 * 1000).toISOString().slice(0, 16)}
            className={`w-full border ${errors.date ? 'border-red-500' : 'border-gray-300'} rounded-lg px-4 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500`}
            required
          />
          {errors.date && (
            <p className="mt-1 text-sm text-red-500">{errors.date}</p>
          )}
          <p className="mt-1 text-xs text-gray-500">
            Mission must start at least 30 minutes from now
          </p>
        </div>
        <div>
          <label htmlFor="endDate" className="block text-sm font-medium text-gray-700 mb-1">
            End Date & Time*
          </label>
          <input
            type="datetime-local"
            id="endDate"
            name="endDate"
            value={formData.endDate}
            onChange={handleChange}
            min={formData.date || new Date(Date.now() + 60 * 60 * 1000).toISOString().slice(0, 16)}
            className={`w-full border ${errors.endDate ? 'border-red-500' : 'border-gray-300'} rounded-lg px-4 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500`}
            required
          />
          {errors.endDate && (
            <p className="mt-1 text-sm text-red-500">{errors.endDate}</p>
          )}
          <p className="mt-1 text-xs text-gray-500">
            Mission must be at least 30 minutes long
          </p>
          {formData.date && formData.endDate && (
            <div className="mt-2 p-2 bg-green-50 rounded-lg">
              <p className="text-xs text-green-700">
                <strong>Duration:</strong> {Math.round((new Date(formData.endDate) - new Date(formData.date)) / (1000 * 60))} minutes
              </p>
            </div>
          )}
        </div>
      </div>

      <div className="flex justify-end">
        <button
          type="button"
          onClick={handleNext}
          disabled={!isStepValid() || isValidating}
          className={`px-6 py-2 rounded-lg font-medium flex items-center ${
            isStepValid() && !isValidating
              ? 'bg-indigo-600 text-white hover:bg-indigo-700'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
          } transition-colors`}
        >
          {isValidating ? (
            <>
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Validating...
            </>
          ) : (
            <>
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
              </svg>
              Continue
            </>
          )}
        </button>
      </div>

      {/* Validation summary */}
      {Object.keys(errors).length > 0 && (
        <div className="mt-4 p-3 bg-red-50 border border-red-100 rounded-lg">
          <p className="text-sm text-red-600 font-medium mb-1">Please fix the following issues:</p>
          <ul className="text-xs text-red-500 list-disc list-inside">
            {Object.values(errors).map((error, index) => (
              <li key={index}>{error}</li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

// Step 2: Mission Details Component
const MissionDetailsStep = ({
  formData,
  setFormData,
  goToNextStep,
  goToPreviousStep,
  serviceTypes = [],
  serviceStyles = [],
  levels = []
}) => {
  const handleChange = (e) => {
    const { name, value } = e.target;

    // Special handling for select fields that need to update API IDs
    if (name === 'theme') {
      // Find the corresponding service type
      const serviceType = serviceTypes.find(type => type.name === value);
      if (serviceType) {
        setFormData({
          ...formData,
          [name]: value,
          service_type_id: serviceType.id
        });
        return;
      }
    } else if (name === 'style') {
      // Find the corresponding service style
      const serviceStyle = serviceStyles.find(style => style.name === value);
      if (serviceStyle) {
        setFormData({
          ...formData,
          [name]: value,
          service_style_id: serviceStyle.id
        });
        return;
      }
    }

    // Default handling
    setFormData({
      ...formData,
      [name]: value
    });
  };

  // Handle nested object changes (like levelRequirement)
  const handleNestedChange = (parent, key, value) => {
    // Special handling for level requirement
    if (parent === 'levelRequirement' && key === 'min') {
      // Find the corresponding level
      const level = levels.find(l => l.level === parseInt(value));
      if (level) {
        setFormData({
          ...formData,
          [parent]: {
            ...formData[parent],
            [key]: value
          },
          min_level_id: level.id
        });
        return;
      }
    }

    // Default handling
    setFormData({
      ...formData,
      [parent]: {
        ...formData[parent],
        [key]: value
      }
    });
  };

  // Handle array field changes
  const handleArrayChange = (field, index, value) => {
    const newArray = [...formData[field]];
    newArray[index] = value;
    setFormData({
      ...formData,
      [field]: newArray
    });
  };

  // Add new item to array field
  const addArrayItem = (field) => {
    setFormData({
      ...formData,
      [field]: [...formData[field], '']
    });
  };

  // Remove item from array field
  const removeArrayItem = (field, index) => {
    const newArray = [...formData[field]];
    newArray.splice(index, 1);
    setFormData({
      ...formData,
      [field]: newArray
    });
  };

  const isStepValid = () => {
    return (
      formData.theme.trim() !== '' &&
      formData.style.trim() !== '' &&
      formData.language.trim() !== '' &&
      formData.service_type_id !== null &&
      formData.service_style_id !== null &&
      formData.min_level_id !== null
    );
  };

  return (
    <div className="p-6">
      <div className="grid grid-cols-1 gap-6 mb-6">
        <div>
          <label htmlFor="theme" className="block text-sm font-medium text-gray-700 mb-1">
            Game/Theme*
          </label>
          <select
            id="theme"
            name="theme"
            value={formData.theme}
            onChange={handleChange}
            className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            required
          >
            {serviceTypes.length > 0 ? (
              serviceTypes.map(type => (
                <option key={type.id} value={type.name}>
                  {type.name}
                </option>
              ))
            ) : (
              <option value="">Loading...</option>
            )}
          </select>
          <p className="mt-1 text-sm text-gray-500">
            The game or activity for this mission
          </p>
        </div>
        <div>
          <label htmlFor="style" className="block text-sm font-medium text-gray-700 mb-1">
            Mission Style*
          </label>
          <select
            id="style"
            name="style"
            value={formData.style}
            onChange={handleChange}
            className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            required
          >
            {serviceStyles.length > 0 ? (
              serviceStyles.map(style => (
                <option key={style.id} value={style.name}>
                  {style.name}
                </option>
              ))
            ) : (
              <>
                <option value="Casual">Casual</option>
                <option value="Competitive">Competitive</option>
                <option value="Ranked">Ranked</option>
                <option value="Tournament">Tournament</option>
                <option value="Training">Training</option>
              </>
            )}
          </select>
          <p className="mt-1 text-sm text-gray-500">
            The type of gameplay experience
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div>
          <label htmlFor="platform" className="block text-sm font-medium text-gray-700 mb-1">
            Platform
          </label>
          <input
            type="text"
            id="platform"
            name="platform"
            value={formData.platform}
            onChange={handleChange}
            className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            placeholder="E.g., Mobile, PC, Console"
          />
        </div>
        <div>
          <label htmlFor="language" className="block text-sm font-medium text-gray-700 mb-1">
            Language*
          </label>
          <input
            type="text"
            id="language"
            name="language"
            value={formData.language}
            onChange={handleChange}
            className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            placeholder="E.g., English"
            required
          />
        </div>
      </div>

      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Level Requirement
        </label>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label htmlFor="levelMin" className="block text-xs text-gray-500 mb-1">
              Minimum Level
            </label>
            <select
              id="levelMin"
              value={formData.levelRequirement.min}
              onChange={(e) => handleNestedChange('levelRequirement', 'min', parseInt(e.target.value))}
              className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            >
              {levels.length > 0 ? (
                levels.map(level => (
                  <option key={level.id} value={level.level}>
                    {level.name} (Level {level.level})
                  </option>
                ))
              ) : (
                <>
                  <option value="1">Beginner (Level 1)</option>
                  <option value="10">Intermediate (Level 10)</option>
                  <option value="20">Advanced (Level 20)</option>
                  <option value="30">Expert (Level 30)</option>
                </>
              )}
            </select>
          </div>
          <div>
            <label htmlFor="levelMax" className="block text-xs text-gray-500 mb-1">
              Maximum Level
            </label>
            <input
              type="number"
              id="levelMax"
              value={formData.levelRequirement.max}
              onChange={(e) => handleNestedChange('levelRequirement', 'max', parseInt(e.target.value))}
              min={formData.levelRequirement.min || 1}
              max="99"
              className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            />
          </div>
        </div>
        <p className="mt-1 text-sm text-gray-500">
          Set the level range for participants
        </p>
      </div>

      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Requirements
        </label>
        {formData.requirements.map((req, index) => (
          <div key={index} className="flex mb-2">
            <input
              type="text"
              value={req}
              onChange={(e) => handleArrayChange('requirements', index, e.target.value)}
              className="flex-1 border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="E.g., Microphone required"
            />
            <button
              type="button"
              onClick={() => removeArrayItem('requirements', index)}
              className="ml-2 text-red-500 hover:text-red-700"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </button>
          </div>
        ))}
        <button
          type="button"
          onClick={() => addArrayItem('requirements')}
          className="mt-2 text-indigo-600 hover:text-indigo-800 text-sm font-medium flex items-center"
        >
          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          Add Requirement
        </button>
      </div>

      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Tags
        </label>
        {formData.tags.map((tag, index) => (
          <div key={index} className="flex mb-2">
            <input
              type="text"
              value={tag}
              onChange={(e) => handleArrayChange('tags', index, e.target.value)}
              className="flex-1 border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="E.g., Tournament"
            />
            <button
              type="button"
              onClick={() => removeArrayItem('tags', index)}
              className="ml-2 text-red-500 hover:text-red-700"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </button>
          </div>
        ))}
        <button
          type="button"
          onClick={() => addArrayItem('tags')}
          className="mt-2 text-indigo-600 hover:text-indigo-800 text-sm font-medium flex items-center"
        >
          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          Add Tag
        </button>
      </div>

      <div className="flex justify-between">
        <button
          type="button"
          onClick={goToPreviousStep}
          className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 transition-colors"
        >
          Back
        </button>
        <button
          type="button"
          onClick={goToNextStep}
          disabled={!isStepValid()}
          className={`px-6 py-2 rounded-lg font-medium ${
            isStepValid()
              ? 'bg-indigo-600 text-white hover:bg-indigo-700'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
          } transition-colors`}
        >
          Continue
        </button>
      </div>
    </div>
  );
};

// Step 3: Mission Images Component
const MissionImagesStep = ({ formData, setFormData, goToNextStep, goToPreviousStep, missionId = null }) => {
  const handleImagesChange = (images) => {
    setFormData({
      ...formData,
      images
    });
  };

  return (
    <div className="p-6 w-full max-w-2xl sm:max-w-2xl">
      <div className="mb-6">
        <h3 className="text-lg font-medium text-gray-800 mb-2">
          Mission Images
        </h3>
        <p className="text-sm text-gray-500 mb-4">
          Upload images that showcase your mission. The first image will be used as the primary image.
        </p>

        <MissionImageUploader
          images={formData.images}
          onImagesChange={handleImagesChange}
          maxImages={5}
          maxSizeMB={5}
          missionId={missionId}
        />

        <div className="mt-4 text-sm text-gray-500">
          <p className="flex items-center">
            <svg className="w-4 h-4 mr-1 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Tips for great mission images:
          </p>
          <ul className="list-disc pl-6 mt-1 space-y-1">
            <li>Use high-quality, clear images</li>
            <li>Show gameplay or relevant content</li>
            <li>Avoid text-heavy images</li>
            <li>Ensure images are appropriate and follow community guidelines</li>
          </ul>
        </div>
      </div>

      <div className="flex justify-between">
        <button
          type="button"
          onClick={goToPreviousStep}
          className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 transition-colors"
        >
          Back
        </button>
        <button
          type="button"
          onClick={goToNextStep}
          className="px-6 py-2 bg-indigo-600 text-white rounded-lg font-medium hover:bg-indigo-700 transition-colors"
        >
          Continue
        </button>
      </div>
    </div>
  );
};

// Step 4: Review and Submit Component
const ReviewSubmitStep = ({ formData, goToPreviousStep, handleSubmit, isSubmitting }) => {
  const [hasSufficientBalance, setHasSufficientBalance] = useState(true);

  // Calculate platform fee (5% of bounty)
  const platformFee = Math.ceil(formData.bounty * 0.05);
  const totalAmount = formData.bounty + platformFee;

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'Not provided';
    const options = {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    };
    return new Date(dateString).toLocaleString('en-US', options);
  };

  // Check wallet balance on component mount
  useEffect(() => {
    const checkBalance = async () => {
      try {
        const hasSufficient = await missionPaymentService.checkSufficientBalance(totalAmount);
        setHasSufficientBalance(hasSufficient);
      } catch (error) {
        console.error('Error checking wallet balance:', error);
      }
    };

    checkBalance();
  }, [totalAmount]);

  return (
    <div className="p-6 ">
      <h3 className="text-lg font-semibold text-gray-800 mb-4">Review Your Mission</h3>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        {/* Mission Preview */}
        <div>
          <h4 className="font-medium text-gray-700 mb-4">Mission Preview</h4>
          <MissionPreview mission={formData} />
        </div>

        {/* Mission Details Summary */}
        <div>
          <h4 className="font-medium text-gray-700 mb-4">Mission Details</h4>
          <div className="bg-gray-50 p-4 rounded-lg h-full">
            <div className="mb-4">
              <h5 className="font-medium text-gray-700 mb-2">Basic Information</h5>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-500">Title</p>
                  <p className="font-medium text-gray-800">{formData.title || 'Not provided'}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Bounty</p>
                  <p className="font-medium text-gray-800">{formData.bounty} credits</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Slots</p>
                  <p className="font-medium text-gray-800">{formData.slotsTotal}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Start Date & Time</p>
                  <p className="font-medium text-gray-800">
                    {formatDate(formData.date)}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">End Date & Time</p>
                  <p className="font-medium text-gray-800">
                    {formatDate(formData.endDate)}
                  </p>
                </div>
              </div>
            </div>

            <div className="mb-4">
              <h5 className="font-medium text-gray-700 mb-2">Mission Details</h5>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-500">Game/Theme</p>
                  <p className="font-medium text-gray-800">{formData.theme || 'Not provided'}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Style</p>
                  <p className="font-medium text-gray-800">{formData.style}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Platform</p>
                  <p className="font-medium text-gray-800">{formData.platform || 'Not provided'}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Language</p>
                  <p className="font-medium text-gray-800">{formData.language}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Level Requirement</p>
                  <p className="font-medium text-gray-800">
                    LV{formData.levelRequirement.min}-LV{formData.levelRequirement.max}
                  </p>
                </div>
              </div>
            </div>

            <div className="mb-4">
              <h5 className="font-medium text-gray-700 mb-2">Requirements</h5>
              {formData.requirements.filter(r => r).length > 0 ? (
                <ul className="list-disc pl-5 text-gray-800">
                  {formData.requirements.filter(r => r).map((req, index) => (
                    <li key={index}>{req}</li>
                  ))}
                </ul>
              ) : (
                <p className="text-gray-500">No requirements specified</p>
              )}
            </div>

            <div className="mb-4">
              <h5 className="font-medium text-gray-700 mb-2">Tags</h5>
              <div className="flex flex-wrap gap-2">
                {formData.tags.filter(t => t).map((tag, index) => (
                  <span key={index} className="bg-gray-100 text-gray-800 text-sm px-3 py-1 rounded-full">
                    {tag}
                  </span>
                ))}
                {formData.tags.filter(t => t).length === 0 && (
                  <p className="text-gray-500">No tags specified</p>
                )}
              </div>
            </div>

            {/* Images */}
            {formData.images && formData.images.length > 0 && (
              <div>
                <h5 className="font-medium text-gray-700 mb-2">Images</h5>
                <div className="grid grid-cols-3 gap-2">
                  {formData.images.map((image, index) => (
                    <div key={index} className="relative aspect-w-16 aspect-h-9 bg-gray-100 rounded-lg overflow-hidden">
                      <img
                        src={typeof image === 'string' ? image : URL.createObjectURL(image)}
                        alt={`Mission ${index + 1}`}
                        className="object-cover w-full h-full"
                      />
                      {index === 0 && (
                        <div className="absolute top-1 left-1 bg-indigo-500 text-white text-xs px-1.5 py-0.5 rounded">
                          Primary
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Payment Summary Section */}
      <div className="mb-6">
        <h4 className="font-medium text-gray-700 mb-4">Payment Details</h4>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <MissionPaymentSummary
            bounty={formData.bounty}
            platformFee={platformFee}
            totalAmount={totalAmount}
            paymentType="creation"
          />
          <WalletBalanceCard
            requiredAmount={totalAmount}
            showAddCredits={true}
            actionType="create"
          />
        </div>
      </div>

      <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <p className="text-sm text-yellow-700">
              By creating this mission, you agree to our Terms of Service and Community Guidelines.
            </p>
          </div>
        </div>
      </div>

      <div className="flex justify-between">
        <button
          type="button"
          onClick={goToPreviousStep}
          className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 transition-colors"
        >
          Back
        </button>
        <button
          type="submit"
          onClick={handleSubmit}
          className="px-6 py-2 bg-indigo-600 text-white rounded-lg font-medium hover:bg-indigo-700 transition-colors flex items-center"
          disabled={isSubmitting || !hasSufficientBalance}
        >
          {isSubmitting ? (
            <>
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Creating Mission...
            </>
          ) : !hasSufficientBalance ? (
            'Insufficient Balance'
          ) : (
            'Create Mission'
          )}
        </button>
      </div>
    </div>
  );
};

const MissionCreatePage = () => {
  const navigate = useNavigate();
  const toast = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentStep, setCurrentStep] = useState(1);
  const [showSuccess, setShowSuccess] = useState(false);

  // Reference data
  const [serviceTypes, setServiceTypes] = useState([]);
  const [serviceStyles, setServiceStyles] = useState([]);
  const [levels, setLevels] = useState([]);

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    bounty: 100,
    date: '',
    endDate: '',
    slotsTotal: 2,
    levelRequirement: { min: 1, max: 99 },
    style: 'Casual',
    theme: '',
    category: 'Game',
    platform: '',
    language: 'English',
    requirements: [''],
    tags: [''],
    // Image data
    images: [],
    // API-specific fields
    service_type_id: null,
    service_style_id: null,
    min_level_id: null
  });

  // Store the created mission ID
  const [createdMissionId, setCreatedMissionId] = useState(null);

  // Fetch reference data
  useEffect(() => {
    const fetchReferenceData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Fetch service types, styles, and levels
        const [typesResponse, stylesResponse, levelsResponse] = await Promise.all([
          referenceDataApi.getServiceTypes(),
          referenceDataApi.getServiceStyles(),
          referenceDataApi.getLevels()
        ]);

        // Set reference data
        setServiceTypes(typesResponse.data || []);
        setServiceStyles(stylesResponse.data || []);
        setLevels(levelsResponse.data || []);

        // Set default values if available
        if (typesResponse.data && typesResponse.data.length > 0) {
          setFormData(prev => ({
            ...prev,
            service_type_id: typesResponse.data[0].id,
            theme: typesResponse.data[0].name
          }));
        }

        if (stylesResponse.data && stylesResponse.data.length > 0) {
          setFormData(prev => ({
            ...prev,
            service_style_id: stylesResponse.data[0].id,
            style: stylesResponse.data[0].name
          }));
        }

        if (levelsResponse.data && levelsResponse.data.length > 0) {
          setFormData(prev => ({
            ...prev,
            min_level_id: levelsResponse.data[0].id,
            levelRequirement: {
              min: levelsResponse.data[0].level,
              max: 99
            }
          }));
        }
      } catch (error) {
        console.error('Error fetching reference data:', error);
        setError(getApiErrorMessage(error));
        toast.error('Failed to load reference data. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchReferenceData();
  }, [toast]);

  // Go to next step
  const goToNextStep = () => {
    setCurrentStep(currentStep + 1);
    window.scrollTo(0, 0);
  };

  // Go to previous step
  const goToPreviousStep = () => {
    setCurrentStep(currentStep - 1);
    window.scrollTo(0, 0);
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Calculate platform fee (5% of bounty)
      const platformFee = Math.ceil(formData.bounty * 0.05);
      const totalAmount = formData.bounty + platformFee;

      // Check if user has sufficient balance
      const hasSufficientBalance = await missionPaymentService.checkSufficientBalance(totalAmount);

      if (!hasSufficientBalance) {
        toast.error('Insufficient wallet balance to create this mission');
        setIsSubmitting(false);
        return;
      }

      // Transform frontend mission data to API format
      const apiMissionData = transformFrontendMissionToApi({
        ...formData,
        // Map frontend field names to API field names
        date: formData.date,
        end_date: formData.endDate,
        slots_total: formData.slotsTotal,
        // Ensure required fields are present
        service_type_id: formData.service_type_id,
        service_style_id: formData.service_style_id,
        min_level_id: formData.min_level_id,
        // Add payment details
        platform_fee: platformFee,
        total_amount: totalAmount
      });

      // Create mission
      const response = await missionApi.createMission(apiMissionData);
      const missionId = response.data.id;

      // Store the created mission ID
      setCreatedMissionId(missionId);

      // Process payment for mission creation
      await missionPaymentService.processMissionCreationPayment(
        missionId,
        totalAmount
      );

      // Upload images if any
      if (formData.images && formData.images.length > 0) {
        try {
          // Upload each image
          const imageUploadPromises = formData.images.map(async (image, index) => {
            // Skip if image is a string (already uploaded)
            if (typeof image === 'string') return;

            // Upload the image
            await missionApi.uploadMissionImage(missionId, image);

            // Log success
            console.log(`Uploaded image ${index + 1} for mission ${missionId}`);
          });

          // Wait for all image uploads to complete
          await Promise.all(imageUploadPromises);

          console.log('All images uploaded successfully');
        } catch (imageError) {
          console.error('Error uploading mission images:', imageError);
          // Don't fail the whole submission if image upload fails
          toast.warning('Mission created, but some images could not be uploaded');
        }
      }

      // Show success message
      setIsSubmitting(false);
      setShowSuccess(true);

      // Show success toast
      toast.success('Mission created successfully!');

      // Redirect to missions page after showing success message
      setTimeout(() => {
        navigate('/missions/my-missions');
      }, 2000);
    } catch (error) {
      console.error('Error creating mission:', error);
      setIsSubmitting(false);

      // Show error toast
      toast.error(getApiErrorMessage(error) || 'Failed to create mission');
    }
  };

  // Show loading state
  if (isLoading) {
    return (
      <MissionLayout title="Create Mission" backPath="/missions">
        <div className="flex justify-center items-center py-20">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
        </div>
      </MissionLayout>
    );
  }

  // Show error state
  if (error) {
    return (
      <MissionLayout title="Create Mission" backPath="/missions">
        <div className="bg-white rounded-xl shadow-md p-8 text-center max-w-md mx-auto">
          <svg className="w-16 h-16 text-red-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 mb-1">Error Loading Data</h3>
          <p className="text-gray-500 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-indigo-600 text-white rounded-lg font-medium hover:bg-indigo-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </MissionLayout>
    );
  }

  return (
    <MissionLayout title="Create Mission" backPath="/missions">
      {/* Success Message */}
      <AnimatePresence>
        {showSuccess && (
          <motion.div
            className="fixed inset-0 flex items-center justify-center z-50 bg-black/50 backdrop-blur-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <motion.div
              className="bg-white rounded-2xl shadow-xl p-8 max-w-md text-center"
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              transition={{ type: 'spring', damping: 25, stiffness: 500 }}
            >
              <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-10 h-10 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-2">Mission Created!</h3>
              <p className="text-gray-600 mb-6">
                Your mission has been successfully created and is now available for talents to apply.
              </p>
              <div className="animate-pulse text-sm text-gray-500">
                Redirecting to your missions...
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
        {/* Progress Steps */}
        <motion.div
          className="mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="flex items-center justify-between w-full max-w-2xl sm:max-w-2xl mb-4">
            <h2 className="text-xl font-bold text-gray-800">
              Step {currentStep} of 4: {
                currentStep === 1 ? 'Basic Information' :
                currentStep === 2 ? 'Mission Details' :
                currentStep === 3 ? 'Mission Images' :
                'Review & Submit'
              }
            </h2>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2.5">
            <div
              className="bg-indigo-600 h-2.5 rounded-full transition-all duration-500"
              style={{ width: `${(currentStep / 4) * 100}%` }}
            ></div>
          </div>
        </motion.div>

        {/* Form Container */}
        <motion.div
          className="bg-white rounded-2xl shadow-md overflow-hidden"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <form onSubmit={handleSubmit} className='max-w-6xl justify-center items-center'>
            {/* Step 1: Basic Information */}
            {currentStep === 1 && (
              <BasicInfoStep
                formData={formData}
                setFormData={setFormData}
                goToNextStep={goToNextStep}
              />
            )}

            {/* Step 2: Mission Details */}
            {currentStep === 2 && (
              <MissionDetailsStep
                formData={formData}
                setFormData={setFormData}
                goToNextStep={goToNextStep}
                goToPreviousStep={goToPreviousStep}
                serviceTypes={serviceTypes}
                serviceStyles={serviceStyles}
                levels={levels}
              />
            )}

            {/* Step 3: Mission Images */}
            {currentStep === 3 && (
              <MissionImagesStep
                formData={formData}
                setFormData={setFormData}
                goToNextStep={goToNextStep}
                goToPreviousStep={goToPreviousStep}
                missionId={createdMissionId}
              />
            )}

            {/* Step 4: Review & Submit */}
            {currentStep === 4 && (
              <ReviewSubmitStep
                formData={formData}
                goToPreviousStep={goToPreviousStep}
                handleSubmit={handleSubmit}
                isSubmitting={isSubmitting}
              />
            )}
          </form>
        </motion.div>
    </MissionLayout>
  );
};

export default MissionCreatePage;
