import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useToast } from '../components/common/ToastProvider';
import MissionCompletionModal from '../components/mission/MissionCompletionModal';
import { missionApi } from '../services/missionApi';
import { missionEventService } from '../services/missionEventService';
import { getApiErrorMessage } from '../utils/errorHandlers';
import missionPaymentService from '../services/missionPaymentService';

// Import mission execution components
import MissionExecutionLayout from '../components/mission/execution/MissionExecutionLayout';
import MissionHeader from '../components/mission/execution/MissionHeader';
import TaskChecklist from '../components/mission/execution/TaskChecklist';
import ParticipantsList from '../components/mission/execution/ParticipantsList';
import MissionChatButton from '../components/mission/execution/MissionChatButton';
import MissionRewards from '../components/mission/execution/MissionRewards';

const MissionExecutionPage = () => {
  const { missionId } = useParams();
  const navigate = useNavigate();
  const toast = useToast();

  // State
  const [mission, setMission] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [showCompletionModal, setShowCompletionModal] = useState(false);
  const [currentUser, setCurrentUser] = useState({
    id: localStorage.getItem('user_id') || 'user-1',
    name: localStorage.getItem('user_name') || 'Alex Chen',
    avatar: localStorage.getItem('user_avatar') || '/images/profile-2.jpeg',
    level: parseInt(localStorage.getItem('user_level') || '45')
  });

  // Check if current user is host
  const isHost = mission?.host?.id === currentUser.id;

  // Fetch mission data
  useEffect(() => {
    const fetchMission = async () => {
      setIsLoading(true);

      try {
        // Fetch mission execution details from API
        const response = await missionApi.getMissionExecutionDetails(missionId);

        // Set mission data
        setMission(response.data);

        // Set loading state
        setIsLoading(false);
      } catch (error) {
        console.error('Error fetching mission:', error);
        toast.error(getApiErrorMessage(error) || 'Failed to load mission details');
        setIsLoading(false);
      }
    };

    fetchMission();
  }, [missionId, toast]);

  // Handle task completion
  const handleTaskComplete = async (taskId) => {
    try {
      // Get task data
      const task = mission.tasks.find(t => t.id === taskId);
      if (!task) {
        throw new Error('Task not found');
      }

      // Use mission event service to handle task completion
      await missionEventService.handleTaskCompleted(
        missionId,
        taskId,
        task,
        currentUser
      );

      // Update local state
      setMission(prev => ({
        ...prev,
        tasks: prev.tasks.map(t =>
          t.id === taskId ? { ...t, completed: true } : t
        )
      }));

      // Show success toast
      toast.success('Task marked as completed');
    } catch (error) {
      console.error('Error completing task:', error);
      toast.error(getApiErrorMessage(error) || 'Failed to complete task');
    }
  };

  // Handle task uncomplete
  const handleTaskUncomplete = async (taskId) => {
    try {
      // Get task data
      const task = mission.tasks.find(t => t.id === taskId);
      if (!task) {
        throw new Error('Task not found');
      }

      // Use mission event service to handle task uncomplete
      await missionEventService.handleTaskUncompleted(
        missionId,
        taskId,
        task,
        currentUser
      );

      // Update local state
      setMission(prev => ({
        ...prev,
        tasks: prev.tasks.map(t =>
          t.id === taskId ? { ...t, completed: false } : t
        )
      }));

      // Show success toast
      toast.success('Task marked as incomplete');
    } catch (error) {
      console.error('Error uncompleting task:', error);
      toast.error(getApiErrorMessage(error) || 'Failed to update task');
    }
  };

  // Handle send message
  const handleSendMessage = async (message) => {
    try {
      // Send message to API
      const response = await missionApi.sendMissionMessage(missionId, {
        senderId: currentUser.id,
        ...message
      });

      // Update local state
      setMission(prev => ({
        ...prev,
        messages: [
          ...prev.messages,
          {
            id: response.data.chat_message.id || `msg-${Date.now()}`,
            senderId: currentUser.id,
            ...message
          }
        ]
      }));
    } catch (error) {
      console.error('Error sending message:', error);
      toast.error(getApiErrorMessage(error) || 'Failed to send message');
    }
  };

  // Handle remove participant
  const handleRemoveParticipant = async (participantId) => {
    try {
      // Find participant data
      const participant = mission.participants.find(p => p.id === participantId);
      if (!participant) {
        throw new Error('Participant not found');
      }

      // Use mission event service to handle participant removal
      await missionEventService.handleParticipantLeft(
        missionId,
        {
          id: participant.id,
          name: participant.name
        }
      );

      // Update local state
      setMission(prev => ({
        ...prev,
        participants: prev.participants.filter(p => p.id !== participantId)
      }));

      // Show success toast
      toast.success(`${participant.name} has been removed from the mission`);
    } catch (error) {
      console.error('Error removing participant:', error);
      toast.error(getApiErrorMessage(error) || 'Failed to remove participant');
    }
  };

  // Handle promote to host
  const handlePromoteToHost = (participantId) => {
    const participant = mission.participants.find(p => p.id === participantId);
    if (!participant) return;

    setMission(prev => {
      const updatedParticipants = [
        ...prev.participants.filter(p => p.id !== participantId),
        { ...prev.host, role: 'Participant' }
      ];

      return {
        ...prev,
        host: { ...participant, role: 'Host' },
        participants: updatedParticipants,
        messages: [
          ...prev.messages,
          {
            id: `msg-${Date.now()}`,
            senderId: 'system',
            text: `${participant.name} is now the host of this mission`,
            timestamp: new Date().toISOString(),
            type: 'system'
          }
        ]
      };
    });
  };

  // Handle claim reward
  const handleClaimReward = () => {
    setMission(prev => ({
      ...prev,
      rewardsClaimed: [...(prev.rewardsClaimed || []), currentUser.id],
      messages: [
        ...prev.messages,
        {
          id: `msg-${Date.now()}`,
          senderId: 'system',
          text: `${currentUser.name} has claimed their reward`,
          timestamp: new Date().toISOString(),
          type: 'system'
        }
      ]
    }));
  };

  // Handle distribute rewards
  const handleDistributeRewards = (distribution) => {
    setMission(prev => {
      const updatedParticipants = prev.participants.map(participant => {
        const reward = distribution.find(d => d.id === participant.id);
        return {
          ...participant,
          rewardAmount: reward ? reward.amount : 0
        };
      });

      return {
        ...prev,
        participants: updatedParticipants,
        rewardsClaimed: [...(prev.participants.map(p => p.id) || [])],
        messages: [
          ...prev.messages,
          {
            id: `msg-${Date.now()}`,
            senderId: 'system',
            text: `Rewards have been distributed by the host`,
            timestamp: new Date().toISOString(),
            type: 'system'
          }
        ]
      };
    });
  };

  // Complete mission
  const handleCompleteMission = () => {
    setShowCompletionModal(true);
  };

  // Handle completion submission
  const handleCompletionSubmit = async (completionData) => {
    try {
      // Show loading toast
      toast.loading('Processing mission completion...');

      // Use mission event service to handle mission completion
      await missionEventService.handleMissionCompleted(
        missionId,
        {
          ...completionData,
          rewards: {
            total: mission.bounty,
            distribution: completionData.ratings
          }
        },
        currentUser
      );

      // Process payment for mission completion
      await missionPaymentService.processMissionCompletionPayment(
        missionId,
        completionData
      );

      // Update mission status locally
      setMission(prev => ({
        ...prev,
        status: 'completed'
      }));

      // Close modal
      setShowCompletionModal(false);

      // Show success message
      toast.success('Mission completed and payments processed successfully!');

      // Navigate back to missions page after a delay
      setTimeout(() => {
        navigate('/missions/my-missions');
      }, 2000);
    } catch (error) {
      console.error('Error completing mission:', error);
      toast.error(getApiErrorMessage(error) || 'Failed to complete mission');
    }
  };

  // Render loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-indigo-50 to-white flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-indigo-600 font-medium">Loading mission...</p>
        </div>
      </div>
    );
  }

  // Render error state if mission not found
  if (!mission) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-indigo-50 to-white flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6 bg-white rounded-xl shadow-md">
          <svg className="w-16 h-16 text-indigo-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h2 className="text-xl font-bold text-gray-800 mb-2">Mission Not Found</h2>
          <p className="text-gray-600 mb-4">The mission you're looking for doesn't exist or you don't have permission to view it.</p>
          <button
            className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
            onClick={() => navigate('/missions')}
          >
            Back to Missions
          </button>
        </div>
      </div>
    );
  }

  return (
    <MissionExecutionLayout
      mission={mission}
      activeTab={activeTab}
      onTabChange={setActiveTab}
      onCompleteMission={handleCompleteMission}
      isHost={isHost}
    >
      {/* Tab Content */}
      <div className="space-y-6">
        {/* Overview Tab */}
        {activeTab === 'overview' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <MissionHeader mission={mission} />

            <div className="mt-6 grid grid-cols-1 lg:grid-cols-2 gap-6">
              <TaskChecklist
                tasks={mission.tasks}
                onTaskComplete={handleTaskComplete}
                onTaskUncomplete={handleTaskUncomplete}
                isHost={isHost}
                missionStatus={mission.status}
              />

              <ParticipantsList
                participants={mission.participants}
                host={mission.host}
                isHost={isHost}
                missionStatus={mission.status}
                onRemoveParticipant={handleRemoveParticipant}
                onPromoteToHost={handlePromoteToHost}
              />
            </div>
          </motion.div>
        )}

        {/* Tasks Tab */}
        {activeTab === 'tasks' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <TaskChecklist
              tasks={mission.tasks}
              onTaskComplete={handleTaskComplete}
              onTaskUncomplete={handleTaskUncomplete}
              isHost={isHost}
              missionStatus={mission.status}
            />
          </motion.div>
        )}

        {/* Chat Tab */}
        {activeTab === 'chat' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <MissionChatButton
              mission={mission}
              currentUser={currentUser}
              className="h-[600px]"
            />
          </motion.div>
        )}

        {/* Participants Tab */}
        {activeTab === 'participants' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <ParticipantsList
              participants={mission.participants}
              host={mission.host}
              isHost={isHost}
              missionStatus={mission.status}
              onRemoveParticipant={handleRemoveParticipant}
              onPromoteToHost={handlePromoteToHost}
            />
          </motion.div>
        )}

        {/* Rewards Tab */}
        {activeTab === 'rewards' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <MissionRewards
              mission={mission}
              currentUser={currentUser}
              isHost={isHost}
              onClaimReward={handleClaimReward}
              onDistributeRewards={handleDistributeRewards}
            />
          </motion.div>
        )}
      </div>

      {/* Mission Completion Modal */}
      <MissionCompletionModal
        isOpen={showCompletionModal}
        onClose={() => setShowCompletionModal(false)}
        onSubmit={handleCompletionSubmit}
        mission={mission}
        participants={mission?.participants.filter(p => p.id !== currentUser.id) || []}
      />
    </MissionExecutionLayout>
  );
};

export default MissionExecutionPage;
