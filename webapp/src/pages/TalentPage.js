import React, { useState, useEffect, useCallback } from 'react';
import TalentCard from '../components/TalentCard';
import FilterModal from '../components/FilterModal';
import { motion, AnimatePresence } from 'framer-motion';
import talentService from '../services/talentService';
import MainNavigation from '../components/navigation/MainNavigation';
import MobileNavigation from '../components/navigation/MobileNavigation';
import { SectionLoader } from '../components/ui/LoadingIndicator';
// We'll use authentication in future implementations
// import { useAuth } from '../contexts/AuthContext';

// Mock data for demo purposes - will be removed when API integration is complete
const mockTalents = [
  {
    id: 1,
    name: '<PERSON>',
    gender: 'male',
    level: 88,
    profileImage: 'profile-1.jpg',
    rating: 4.5,
    followers: 134,
    bio: "Hi, I'm <PERSON>, a creative soul with a passion for gaming and teaching.",
    isHot: true
  },
  {
    id: 2,
    name: '<PERSON>',
    gender: 'female',
    level: 73,
    profileImage: 'profile-2.jpeg',
    rating: 4.5,
    followers: 224,
    bio: "Hi 👋 creative soul with a passion for gaming, drawing, and creating content.",
    isHot: true
  },
  {
    id: 3,
    name: '<PERSON>',
    gender: 'male',
    level: 52,
    profileImage: 'profile-3.jpg',
    rating: 4.5,
    followers: 24,
    bio: "Hi, I'm Dave, a creative soul with a passion for gaming and strategy.",
    isHot: true
  },
  {
    id: 4,
    name: 'Amy Leong',
    gender: 'female',
    level: 62,
    profileImage: 'profile-5.jpg',
    rating: 4.5,
    followers: 24,
    bio: "Hi 👋 creative soul with a passion for gaming, drawing, and creating content.",
    isHot: true
  }
];

const TalentPage = () => {
  // We'll use authentication status in future implementations
  // const { isAuthenticated } = useAuth();

  // We keep a separate talents state for future implementations
  // that might need the original unfiltered list
  const [, setTalents] = useState([]);
  const [filteredTalents, setFilteredTalents] = useState([]);
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [showSearchModal, setShowSearchModal] = useState(false);
  const [activeFilters, setActiveFilters] = useState({});
  const [searchTerm, setSearchTerm] = useState('');
  const [recentSearches, setRecentSearches] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    perPage: 20
  });

  const popularSearches = [
    'Valorant Coach',
    'League of Legends Pro',
    'Weekend Availability',
    'Fortnite Partner',
  ];

  // Function to fetch talents with current filters
  const fetchTalents = useCallback(async (filters = {}, page = 1) => {
    setLoading(true);
    setError(null);

    try {
      const response = await talentService.filterTalents(filters, page, pagination.perPage);

      if (response.error) {
        setError(response.error.message || 'Failed to fetch talents');
        // Use mock data as fallback if API fails
        setTalents(mockTalents);
        setFilteredTalents(mockTalents);

        // Set default pagination values when using mock data
        setPagination({
          currentPage: 1,
          totalPages: 1,
          totalItems: mockTalents.length,
          perPage: mockTalents.length
        });
      } else {
        setTalents(response.data);
        setFilteredTalents(response.data);

        // Update pagination info if meta data exists
        if (response.meta) {
          setPagination({
            currentPage: response.meta.current_page,
            totalPages: response.meta.last_page,
            totalItems: response.meta.total,
            perPage: response.meta.per_page
          });
        }
      }
    } catch (err) {
      console.error('Error fetching talents:', err);
      setError('Failed to fetch talents');
      // Use mock data as fallback if API fails
      setTalents(mockTalents);
      setFilteredTalents(mockTalents);

      // Set default pagination values when using mock data
      setPagination({
        currentPage: 1,
        totalPages: 1,
        totalItems: mockTalents.length,
        perPage: mockTalents.length
      });
    } finally {
      setLoading(false);
    }
  }, [pagination.perPage]);

  // Fetch talents on component mount
  useEffect(() => {
    // Load recent searches from localStorage
    const savedSearches = localStorage.getItem('recentTalentSearches');
    if (savedSearches) {
      setRecentSearches(JSON.parse(savedSearches));
    }

    // Fetch talents
    fetchTalents(activeFilters, pagination.currentPage);
  }, [fetchTalents, activeFilters, pagination.currentPage]);

  // Handle applying filters
  const handleApplyFilters = (filters) => {
    setActiveFilters(filters);

    // Reset to page 1 when applying new filters
    const newPagination = {
      ...pagination,
      currentPage: 1
    };
    setPagination(newPagination);

    // Fetch talents with the new filters
    fetchTalents(filters, 1);
  };

  // Handle search
  const handleSearch = (term) => {
    setSearchTerm(term);

    // Reset pagination to page 1 when searching
    const newPagination = {
      ...pagination,
      currentPage: 1
    };
    setPagination(newPagination);

    // Create a new filter object with the search term
    const searchFilters = {
      ...activeFilters,
      searchTerm: term.trim() ? term : undefined
    };

    // Update active filters
    setActiveFilters(searchFilters);

    // Fetch talents with the search term
    fetchTalents(searchFilters, 1);

    // Save to recent searches
    if (term.trim()) {
      setRecentSearches(prev => {
        const newSearches = [term, ...prev.filter(s => s !== term)].slice(0, 5);
        localStorage.setItem('recentTalentSearches', JSON.stringify(newSearches));
        return newSearches;
      });
    }
  };

  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      {/* Shared Navigation */}
      <MainNavigation activeItem="/talent" />

      {/* Recommended Tab */}
      <div className="px-4 pt-3 pb-2 flex items-center gap-2 bg-white md:border-t md:border-gray-100">
        <button className="px-4 py-2 text-sm rounded-full bg-gradient-to-r from-indigo-600 to-blue-500 text-white flex items-center whitespace-nowrap transition-all shadow-sm hover:shadow-md hover:scale-105 duration-300">
          Recommended
        </button>

        <div className="ml-auto flex items-center gap-2">
          {/* Search Button */}
          <button
            onClick={() => setShowSearchModal(true)}
            className="px-4 py-2 text-sm rounded-full bg-blue-50 text-blue-600 flex items-center hover:bg-blue-100 transition-all duration-300"
          >
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            Search
          </button>

          {/* Filter Button */}
          <button
            onClick={() => setShowFilterModal(true)}
            className="px-4 py-2 text-sm rounded-full bg-gray-100 text-gray-600 flex items-center hover:bg-gray-200 transition-all duration-300"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4" />
            </svg>
            Filter
          </button>
        </div>
      </div>

      {/* Active Filters */}
      {Object.keys(activeFilters).length > 0 && (
        <div className="bg-white px-4 py-3 border-t border-gray-100 animate-fadeIn">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-gray-700">Active Filters</h3>
            <button
              onClick={() => {
                setActiveFilters({});
                fetchTalents({}, 1);
                setSearchTerm('');
              }}
              className="text-xs text-indigo-600 hover:text-indigo-800 font-medium flex items-center transition-colors"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
              Clear All
            </button>
          </div>

          <div className="flex flex-wrap gap-2">
            {/* Search Term Filter */}
            {activeFilters.searchTerm && (
              <div className="px-3 py-1.5 bg-blue-50 rounded-full text-xs flex items-center text-blue-700 shadow-sm hover:shadow-md transition-shadow duration-300">
                <svg className="w-3 h-3 mr-1 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
                <span className="font-medium">{activeFilters.searchTerm}</span>
                <button
                  onClick={() => {
                    const newFilters = {...activeFilters, searchTerm: undefined};
                    handleApplyFilters(newFilters);
                    setSearchTerm('');
                  }}
                  className="ml-2 p-0.5 rounded-full bg-blue-100 text-blue-500 hover:bg-blue-200 hover:text-blue-700 transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            )}

            {/* Gender Filter */}
            {activeFilters.gender && (
              <div className="px-3 py-1.5 bg-purple-50 rounded-full text-xs flex items-center text-purple-700 shadow-sm hover:shadow-md transition-shadow duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                <span className="font-medium">{activeFilters.gender}</span>
                <button
                  onClick={() => {
                    const newFilters = {...activeFilters, gender: ''};
                    handleApplyFilters(newFilters);
                  }}
                  className="ml-2 p-0.5 rounded-full bg-purple-100 text-purple-500 hover:bg-purple-200 hover:text-purple-700 transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            )}

            {/* Experience Level Filter */}
            {activeFilters.experienceLevel && (
              activeFilters.experienceLevel[0] > 1 || activeFilters.experienceLevel[1] < 99
            ) && (
              <div className="px-3 py-1.5 bg-green-50 rounded-full text-xs flex items-center text-green-700 shadow-sm hover:shadow-md transition-shadow duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                </svg>
                <span className="font-medium">LV {activeFilters.experienceLevel[0]}-{activeFilters.experienceLevel[1]}</span>
                <button
                  onClick={() => {
                    const newFilters = {...activeFilters, experienceLevel: [1, 99]};
                    handleApplyFilters(newFilters);
                  }}
                  className="ml-2 p-0.5 rounded-full bg-green-100 text-green-500 hover:bg-green-200 hover:text-green-700 transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            )}

            {/* Price Range Filter */}
            {activeFilters.priceRange && (
              activeFilters.priceRange[0] > 0 || activeFilters.priceRange[1] < 300
            ) && (
              <div className="px-3 py-1.5 bg-yellow-50 rounded-full text-xs flex items-center text-yellow-700 shadow-sm hover:shadow-md transition-shadow duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span className="font-medium">RM{activeFilters.priceRange[0]}-RM{activeFilters.priceRange[1]}</span>
                <button
                  onClick={() => {
                    const newFilters = {...activeFilters, priceRange: [0, 300]};
                    handleApplyFilters(newFilters);
                  }}
                  className="ml-2 p-0.5 rounded-full bg-yellow-100 text-yellow-500 hover:bg-yellow-200 hover:text-yellow-700 transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            )}

            {/* Race Filter */}
            {activeFilters.raceId && (
              <div className="px-3 py-1.5 bg-red-50 rounded-full text-xs flex items-center text-red-700 shadow-sm hover:shadow-md transition-shadow duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                <span className="font-medium">Race: {activeFilters.raceId}</span>
                <button
                  onClick={() => {
                    const newFilters = {...activeFilters, raceId: undefined};
                    handleApplyFilters(newFilters);
                  }}
                  className="ml-2 p-0.5 rounded-full bg-red-100 text-red-500 hover:bg-red-200 hover:text-red-700 transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            )}

            {/* Languages Filter */}
            {activeFilters.languages && activeFilters.languages.length > 0 &&
              activeFilters.languages.map(language => (
                <div key={language} className="px-3 py-1.5 bg-indigo-50 rounded-full text-xs flex items-center text-indigo-700 shadow-sm hover:shadow-md transition-shadow duration-300">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1 text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
                  </svg>
                  <span className="font-medium">{language}</span>
                  <button
                    onClick={() => {
                      const newLanguages = activeFilters.languages.filter(lang => lang !== language);
                      const newFilters = {...activeFilters, languages: newLanguages};
                      handleApplyFilters(newFilters);
                    }}
                    className="ml-2 p-0.5 rounded-full bg-indigo-100 text-indigo-500 hover:bg-indigo-200 hover:text-indigo-700 transition-colors"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              ))
            }
          </div>
        </div>
      )}

      {/* Talent Grid */}
      <div className="flex-1 overflow-auto p-3 md:p-6 bg-gray-50">
        {loading ? (
          <SectionLoader
            type="morph"
            size="large"
            message="Loading talents..."
            color="indigo"
          />
        ) : error ? (
          <div className="text-center p-8 bg-white rounded-xl shadow-sm max-w-lg mx-auto">
            <div className="inline-flex p-4 bg-red-50 rounded-full mb-4 text-red-500">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 className="text-xl font-bold text-gray-900 mb-2">Error loading talents</h3>
            <p className="text-gray-600 max-w-md mx-auto mb-6">{error}</p>
            <button
              onClick={() => fetchTalents(activeFilters, pagination.currentPage)}
              className="px-6 py-3 bg-gradient-to-r from-indigo-600 to-blue-500 text-white rounded-lg hover:from-indigo-700 hover:to-blue-600 transition-all shadow-md hover:shadow-lg transform hover:scale-105 active:scale-100"
            >
              Try Again
            </button>
          </div>
        ) : (
          <>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 md:gap-6">
              {filteredTalents.length > 0 ? (
                filteredTalents.map((talent, index) => (
                  <motion.div
                    key={talent.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                  >
                    <TalentCard talent={talent} />
                  </motion.div>
                ))
              ) : (
                <div className="col-span-full p-10 text-center bg-white rounded-xl shadow-sm max-w-lg mx-auto">
                  <div className="inline-flex p-4 bg-gray-100 rounded-full mb-4 text-gray-500">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">No talents found</h3>
                  <p className="text-gray-600 max-w-md mx-auto mb-6">We couldn't find any talents matching your filters. Try adjusting your filter criteria.</p>
                  <button
                    onClick={() => {
                      setActiveFilters({});
                      fetchTalents({}, 1);
                    }}
                    className="px-6 py-3 bg-gradient-to-r from-indigo-600 to-blue-500 text-white rounded-lg hover:from-indigo-700 hover:to-blue-600 transition-all shadow-md hover:shadow-lg transform hover:scale-105 active:scale-100"
                  >
                    Clear Filters
                  </button>
                </div>
              )}
            </div>

            {/* Pagination Controls */}
            {filteredTalents.length > 0 && pagination.totalPages > 1 && (
              <div className="flex flex-col items-center mt-10 mb-6">
                <div className="text-sm text-gray-600 mb-3">
                  Showing <span className="font-medium">{(pagination.currentPage - 1) * pagination.perPage + 1}</span> to <span className="font-medium">{Math.min(pagination.currentPage * pagination.perPage, pagination.totalItems)}</span> of <span className="font-medium">{pagination.totalItems}</span> talents
                </div>

                <div className="flex items-center space-x-1">
                  {/* First Page Button */}
                  <button
                    onClick={() => {
                      if (pagination.currentPage > 1) {
                        setPagination({...pagination, currentPage: 1});
                        fetchTalents(activeFilters, 1);
                      }
                    }}
                    disabled={pagination.currentPage === 1}
                    className={`p-2 rounded-md ${
                      pagination.currentPage === 1
                        ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                        : 'bg-white text-indigo-600 hover:bg-indigo-50 border border-gray-200 shadow-sm'
                    }`}
                    aria-label="First Page"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 19l-7-7 7-7m8 14l-7-7 7-7" />
                    </svg>
                  </button>

                  {/* Previous Button */}
                  <button
                    onClick={() => {
                      if (pagination.currentPage > 1) {
                        const newPage = pagination.currentPage - 1;
                        setPagination({...pagination, currentPage: newPage});
                        fetchTalents(activeFilters, newPage);
                      }
                    }}
                    disabled={pagination.currentPage === 1}
                    className={`p-2 rounded-md ${
                      pagination.currentPage === 1
                        ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                        : 'bg-white text-indigo-600 hover:bg-indigo-50 border border-gray-200 shadow-sm'
                    }`}
                    aria-label="Previous Page"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                    </svg>
                  </button>

                  {/* Page Numbers */}
                  {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                    // Calculate page numbers to show
                    let pageNum;
                    if (pagination.totalPages <= 5) {
                      // If 5 or fewer pages, show all
                      pageNum = i + 1;
                    } else if (pagination.currentPage <= 3) {
                      // If near start, show first 5 pages
                      pageNum = i + 1;
                    } else if (pagination.currentPage >= pagination.totalPages - 2) {
                      // If near end, show last 5 pages
                      pageNum = pagination.totalPages - 4 + i;
                    } else {
                      // Otherwise show current page and 2 on each side
                      pageNum = pagination.currentPage - 2 + i;
                    }

                    return (
                      <button
                        key={pageNum}
                        onClick={() => {
                          if (pageNum !== pagination.currentPage) {
                            setPagination({...pagination, currentPage: pageNum});
                            fetchTalents(activeFilters, pageNum);
                          }
                        }}
                        className={`w-10 h-10 flex items-center justify-center rounded-md transition-all ${
                          pageNum === pagination.currentPage
                            ? 'bg-indigo-600 text-white font-medium shadow-md'
                            : 'bg-white text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 border border-gray-200 shadow-sm'
                        }`}
                      >
                        {pageNum}
                      </button>
                    );
                  })}

                  {/* Next Button */}
                  <button
                    onClick={() => {
                      if (pagination.currentPage < pagination.totalPages) {
                        const newPage = pagination.currentPage + 1;
                        setPagination({...pagination, currentPage: newPage});
                        fetchTalents(activeFilters, newPage);
                      }
                    }}
                    disabled={pagination.currentPage === pagination.totalPages}
                    className={`p-2 rounded-md ${
                      pagination.currentPage === pagination.totalPages
                        ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                        : 'bg-white text-indigo-600 hover:bg-indigo-50 border border-gray-200 shadow-sm'
                    }`}
                    aria-label="Next Page"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                    </svg>
                  </button>

                  {/* Last Page Button */}
                  <button
                    onClick={() => {
                      if (pagination.currentPage < pagination.totalPages) {
                        setPagination({...pagination, currentPage: pagination.totalPages});
                        fetchTalents(activeFilters, pagination.totalPages);
                      }
                    }}
                    disabled={pagination.currentPage === pagination.totalPages}
                    className={`p-2 rounded-md ${
                      pagination.currentPage === pagination.totalPages
                        ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                        : 'bg-white text-indigo-600 hover:bg-indigo-50 border border-gray-200 shadow-sm'
                    }`}
                    aria-label="Last Page"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 5l7 7-7 7M5 5l7 7-7 7" />
                    </svg>
                  </button>
                </div>
              </div>
            )}
          </>
        )}
      </div>

      {/* Mobile Navigation */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white shadow-[0_-2px_10px_rgba(0,0,0,0.1)] z-50 backdrop-filter backdrop-blur-md bg-opacity-90">
        <div className="flex justify-around items-center py-2">
          <a href="/home" className="flex flex-col items-center px-3 py-2 text-gray-600 group">
            <svg className="w-6 h-6 transition-transform group-hover:scale-110 duration-300" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
            </svg>
            <span className="text-xs mt-1">Home</span>
          </a>
          <a href="/talent" className="flex flex-col items-center px-3 py-2 text-indigo-600 relative group">
            <div className="absolute h-1 w-5 bg-indigo-600 rounded-full -top-0.5"></div>
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-500" fill="currentColor" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
            </svg>
            <span className="text-xs text-blue-500 font-medium">Talent</span>
          </a>
          <a href="/explore" className="flex flex-col items-center px-3 py-2 text-gray-600 group">
            <svg className="w-6 h-6 transition-transform group-hover:scale-110 duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
            </svg>
            <span className="text-xs mt-1">Explore</span>
          </a>
          <a href="/chat" className="flex flex-col items-center px-3 py-2 text-gray-600 group">
            <svg className="w-6 h-6 transition-transform group-hover:scale-110 duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
            <span className="text-xs mt-1">Chat</span>
          </a>
          <a href="/profile" className="flex flex-col items-center px-3 py-2 text-gray-600 group">
            <svg className="w-6 h-6 transition-transform group-hover:scale-110 duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            <span className="text-xs mt-1">Profile</span>
          </a>
        </div>
      </div>

      {/* Filter Modal */}
      <FilterModal
        isOpen={showFilterModal}
        onClose={() => setShowFilterModal(false)}
        initialFilters={activeFilters}
        onApplyFilters={handleApplyFilters}
      />

      {/* Search Modal */}
      <AnimatePresence>
        {showSearchModal && (
          <div className="fixed inset-0 z-50 overflow-hidden">
            <motion.div
              className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={() => setShowSearchModal(false)}
            />

            <motion.div
              className="fixed inset-x-0 top-0 bg-white rounded-b-3xl overflow-hidden"
              initial={{ y: "-100%" }}
              animate={{ y: 0 }}
              exit={{ y: "-100%" }}
              transition={{ type: "spring", damping: 25, stiffness: 300 }}
            >
              <div className="container mx-auto px-4 py-4">
                {/* Search Input */}
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </div>
                  <input
                    type="search"
                    className="w-full pl-10 pr-10 py-3 border border-gray-200 rounded-xl bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all"
                    placeholder="Search talents..."
                    value={searchTerm}
                    onChange={(e) => handleSearch(e.target.value)}
                    autoFocus
                  />
                  {searchTerm && (
                    <button
                      onClick={() => handleSearch('')}
                      className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    >
                      <svg className="h-5 w-5 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  )}
                </div>

                {/* Recent Searches */}
                {recentSearches.length > 0 && (
                  <div className="mt-6">
                    <div className="flex justify-between items-center mb-3">
                      <h3 className="text-sm font-semibold text-gray-600">Recent Searches</h3>
                      <button
                        onClick={() => {
                          setRecentSearches([]);
                          localStorage.removeItem('recentTalentSearches');
                        }}
                        className="text-sm text-indigo-600 font-medium"
                      >
                        Clear All
                      </button>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {recentSearches.map((term, index) => (
                        <button
                          key={index}
                          onClick={() => {
                            handleSearch(term);
                            setShowSearchModal(false);
                          }}
                          className="px-3 py-1.5 bg-gray-100 hover:bg-gray-200 rounded-full text-sm text-gray-700 font-medium transition-colors flex items-center space-x-1"
                        >
                          <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          <span>{term}</span>
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Popular Searches */}
                <div className="mt-6">
                  <h3 className="text-sm font-semibold text-gray-600 mb-3">Popular Searches</h3>
                  <div className="flex flex-wrap gap-2">
                    {popularSearches.map((term, index) => (
                      <button
                        key={index}
                        onClick={() => {
                          handleSearch(term);
                          setShowSearchModal(false);
                        }}
                        className="px-3 py-1.5 bg-indigo-50 hover:bg-indigo-100 rounded-full text-sm text-indigo-700 font-medium transition-colors flex items-center space-x-1"
                      >
                        <svg className="h-4 w-4 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 18.657A8 8 0 016.343 7.343S7 9 9 10c0-2 .5-5 2.986-7C14 5 16.09 5.777 17.656 7.343A7.975 7.975 0 0120 13a7.975 7.975 0 01-2.343 5.657z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.879 16.121A3 3 0 1012.015 11L11 14H9c0 .768.293 1.536.879 2.121z" />
                        </svg>
                        <span>{term}</span>
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        )}
      </AnimatePresence>

      {/* Mobile Navigation */}
      <MobileNavigation activeItem="/talent" />
    </div>
  );
};

export default TalentPage;