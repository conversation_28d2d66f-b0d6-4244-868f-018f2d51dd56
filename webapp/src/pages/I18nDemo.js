import React from 'react';
import useTranslation from '../hooks/useTranslation';
import EnhancedLanguageSwitcher from '../components/common/EnhancedLanguageSwitcher';
import FormattingDemo from '../components/i18n/FormattingDemo';
import { getLanguageFlag } from '../utils/i18nUtils';

/**
 * Internationalization Demo Page
 * 
 * This page showcases the internationalization features of the application:
 * - Language switching
 * - RTL support
 * - Date and time formatting
 * - Number and currency formatting
 * - Translation capabilities
 */
const I18nDemo = () => {
  const { t, currentLanguage, dir } = useTranslation();
  
  return (
    <div className="min-h-screen bg-gray-100 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-12">
          <h1 className="text-3xl font-extrabold text-gray-900 sm:text-4xl">
            {t('i18n.demo.title', 'Internationalization Demo')}
          </h1>
          <p className="mt-3 text-xl text-gray-500">
            {t('i18n.demo.subtitle', 'Explore the multilingual capabilities of Mission X')}
          </p>
          
          <div className="mt-6 inline-block">
            <EnhancedLanguageSwitcher 
              variant="full"
              showFlags={true}
              className="shadow-md rounded-lg"
            />
          </div>
        </div>
        
        {/* Current Language Info */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">
            {t('i18n.demo.currentLanguage', 'Current Language')}
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="flex items-center space-x-4">
              <div className="text-4xl">
                {getLanguageFlag(currentLanguage)}
              </div>
              <div>
                <p className="text-lg font-medium">
                  {t('i18n.demo.languageCode', 'Language Code')}: <span className="text-indigo-600">{currentLanguage}</span>
                </p>
                <p className="text-lg font-medium">
                  {t('i18n.demo.textDirection', 'Text Direction')}: <span className="text-indigo-600">{dir}</span>
                </p>
              </div>
            </div>
            
            <div className="flex flex-col justify-center">
              <p className="text-gray-600">
                {t('i18n.demo.languageInfo', 'This demo automatically adapts to the selected language, including text direction, date formats, and number formats.')}
              </p>
            </div>
          </div>
        </div>
        
        {/* Formatting Demo */}
        <FormattingDemo />
        
        {/* Translation Examples */}
        <div className="bg-white rounded-lg shadow-md p-6 mt-8">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">
            {t('i18n.demo.translationExamples', 'Translation Examples')}
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-gray-700 mb-3">
                {t('i18n.demo.greetings', 'Greetings')}
              </h3>
              
              <div className="space-y-2">
                <p className="font-medium">
                  {t('i18n.demo.hello', 'Hello')}
                </p>
                <p className="font-medium">
                  {t('i18n.demo.welcome', 'Welcome to Mission X')}
                </p>
                <p className="font-medium">
                  {t('i18n.demo.thankYou', 'Thank you for your interest')}
                </p>
              </div>
            </div>
            
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-gray-700 mb-3">
                {t('i18n.demo.navigation', 'Navigation')}
              </h3>
              
              <div className="space-y-2">
                <p className="font-medium">
                  {t('i18n.demo.home', 'Home')}
                </p>
                <p className="font-medium">
                  {t('i18n.demo.profile', 'Profile')}
                </p>
                <p className="font-medium">
                  {t('i18n.demo.settings', 'Settings')}
                </p>
                <p className="font-medium">
                  {t('i18n.demo.logout', 'Logout')}
                </p>
              </div>
            </div>
            
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-gray-700 mb-3">
                {t('i18n.demo.messages', 'Messages')}
              </h3>
              
              <div className="space-y-2">
                <p className="font-medium">
                  {t('i18n.demo.success', 'Operation completed successfully')}
                </p>
                <p className="font-medium">
                  {t('i18n.demo.error', 'An error occurred. Please try again.')}
                </p>
                <p className="font-medium">
                  {t('i18n.demo.loading', 'Loading...')}
                </p>
              </div>
            </div>
            
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-gray-700 mb-3">
                {t('i18n.demo.buttons', 'Buttons')}
              </h3>
              
              <div className="space-y-2">
                <p className="font-medium">
                  {t('i18n.demo.save', 'Save')}
                </p>
                <p className="font-medium">
                  {t('i18n.demo.cancel', 'Cancel')}
                </p>
                <p className="font-medium">
                  {t('i18n.demo.submit', 'Submit')}
                </p>
                <p className="font-medium">
                  {t('i18n.demo.delete', 'Delete')}
                </p>
              </div>
            </div>
          </div>
        </div>
        
        {/* RTL Support */}
        <div className="bg-white rounded-lg shadow-md p-6 mt-8">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">
            {t('i18n.demo.rtlSupport', 'RTL Support')}
          </h2>
          
          <p className="text-gray-600 mb-4">
            {t('i18n.demo.rtlDescription', 'Mission X supports Right-to-Left (RTL) languages like Arabic and Hebrew. When you switch to an RTL language, the entire interface adapts automatically.')}
          </p>
          
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-gray-700 mb-3">
              {t('i18n.demo.rtlFeatures', 'RTL Features')}
            </h3>
            
            <ul className="list-disc list-inside space-y-2 text-gray-700">
              <li>{t('i18n.demo.rtlFeature1', 'Text alignment automatically switches to right-aligned')}</li>
              <li>{t('i18n.demo.rtlFeature2', 'Directional icons are flipped')}</li>
              <li>{t('i18n.demo.rtlFeature3', 'Form elements adapt to RTL layout')}</li>
              <li>{t('i18n.demo.rtlFeature4', 'Navigation and menus adjust for RTL reading')}</li>
              <li>{t('i18n.demo.rtlFeature5', 'Margins and paddings are mirrored')}</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default I18nDemo;
