import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import MissionPreview from '../components/mission/MissionPreview';
import MissionLayout from '../components/mission/MissionLayout';
import MissionImageUploader from '../components/mission/MissionImageUploader';
import { missionApi } from '../services/missionApi';
import { referenceDataApi } from '../services/referenceDataApi';
import { transformApiMissionToFrontend, transformFrontendMissionToApi } from '../utils/missionTransformers';
import { getApiErrorMessage } from '../utils/errorHandlers';
import { useToast } from '../components/common/ToastProvider';

const MissionEditPage = () => {
  const { missionId } = useParams();
  const navigate = useNavigate();
  const toast = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentStep, setCurrentStep] = useState(1);
  const [showSuccess, setShowSuccess] = useState(false);

  // Reference data
  const [serviceTypes, setServiceTypes] = useState([]);
  const [serviceStyles, setServiceStyles] = useState([]);
  const [levels, setLevels] = useState([]);

  // Original mission data for comparison
  const [originalMission, setOriginalMission] = useState(null);

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    bounty: 100,
    date: '',
    endDate: '',
    slotsTotal: 2,
    levelRequirement: { min: 1, max: 99 },
    style: 'Casual',
    theme: '',
    category: 'Game',
    platform: '',
    language: 'English',
    requirements: [''],
    tags: [''],
    images: [],
    // API-specific fields
    service_type_id: null,
    service_style_id: null,
    min_level_id: null
  });

  // Fetch mission data and reference data
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Fetch mission details
        const missionResponse = await missionApi.getMissionById(missionId);
        const apiMission = missionResponse.data;

        // Transform API mission to frontend format
        const frontendMission = transformApiMissionToFrontend(apiMission);

        if (!frontendMission) {
          setError('Mission not found');
          setIsLoading(false);
          return;
        }

        // Store original mission for comparison
        setOriginalMission(apiMission);

        // Fetch reference data
        const [typesResponse, stylesResponse, levelsResponse] = await Promise.all([
          referenceDataApi.getServiceTypes(),
          referenceDataApi.getServiceStyles(),
          referenceDataApi.getLevels()
        ]);

        // Set reference data
        setServiceTypes(typesResponse.data || []);
        setServiceStyles(stylesResponse.data || []);
        setLevels(levelsResponse.data || []);

        // Set form data from mission
        setFormData({
          title: frontendMission.title || '',
          description: frontendMission.description || '',
          bounty: frontendMission.bounty || 100,
          date: frontendMission.date || '',
          endDate: frontendMission.end_date || '',
          slotsTotal: frontendMission.slots_total || 2,
          levelRequirement: frontendMission.level_requirement || { min: 1, max: 99 },
          style: frontendMission.style || 'Casual',
          theme: frontendMission.theme || '',
          category: frontendMission.category || 'Game',
          platform: frontendMission.platform || '',
          language: frontendMission.language || 'English',
          requirements: frontendMission.requirements?.length > 0 ? frontendMission.requirements : [''],
          tags: frontendMission.tags?.length > 0 ? frontendMission.tags : [''],
          images: apiMission.images || [],
          // API-specific fields
          service_type_id: apiMission.service_type_id,
          service_style_id: apiMission.service_style_id,
          min_level_id: apiMission.min_level_id
        });
      } catch (error) {
        console.error('Error fetching mission data:', error);
        setError(getApiErrorMessage(error));
        toast.error('Failed to load mission data. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [missionId, toast]);

  // Go to next step
  const goToNextStep = () => {
    setCurrentStep(currentStep + 1);
    window.scrollTo(0, 0);
  };

  // Go to previous step
  const goToPreviousStep = () => {
    setCurrentStep(currentStep - 1);
    window.scrollTo(0, 0);
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Transform frontend mission data to API format
      const apiMissionData = transformFrontendMissionToApi({
        ...formData,
        // Map frontend field names to API field names
        date: formData.date,
        end_date: formData.endDate,
        slots_total: formData.slotsTotal,
        // Ensure required fields are present
        service_type_id: formData.service_type_id,
        service_style_id: formData.service_style_id,
        min_level_id: formData.min_level_id
      });

      // Update mission
      const response = await missionApi.updateMission(missionId, apiMissionData);

      // Show success message
      setIsSubmitting(false);
      setShowSuccess(true);

      // Show success toast
      toast.success('Mission updated successfully!');

      // Redirect to mission details page after showing success message
      setTimeout(() => {
        navigate(`/missions/${missionId}`);
      }, 2000);
    } catch (error) {
      console.error('Error updating mission:', error);
      setIsSubmitting(false);

      // Show error toast
      toast.error(getApiErrorMessage(error) || 'Failed to update mission');
    }
  };

  // Show loading state
  if (isLoading) {
    return (
      <MissionLayout title="Edit Mission" backPath={`/missions/${missionId}`}>
        <div className="flex justify-center items-center py-20">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
        </div>
      </MissionLayout>
    );
  }

  // Show error state
  if (error) {
    return (
      <MissionLayout title="Edit Mission" backPath={`/missions/${missionId}`}>
        <div className="bg-white rounded-xl shadow-md p-8 text-center max-w-md mx-auto">
          <svg className="w-16 h-16 text-red-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 mb-1">Error Loading Mission</h3>
          <p className="text-gray-500 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-indigo-600 text-white rounded-lg font-medium hover:bg-indigo-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </MissionLayout>
    );
  }

  return (
    <MissionLayout title="Edit Mission" backPath={`/missions/${missionId}`}>
      {/* Success Message */}
      <AnimatePresence>
        {showSuccess && (
          <motion.div
            className="fixed inset-0 flex items-center justify-center z-50 bg-black/50 backdrop-blur-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <motion.div
              className="bg-white rounded-2xl shadow-xl p-8 max-w-md text-center"
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              transition={{ type: 'spring', damping: 25, stiffness: 500 }}
            >
              <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-10 h-10 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-2">Mission Updated!</h3>
              <p className="text-gray-600 mb-6">
                Your mission has been successfully updated.
              </p>
              <div className="animate-pulse text-sm text-gray-500">
                Redirecting to mission details...
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Form Container */}
      <motion.div
        className="bg-white rounded-2xl shadow-md overflow-hidden"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <form onSubmit={handleSubmit}>
          {/* Form content will be imported from MissionCreatePage */}
          <div className="p-6">
            <h2 className="text-xl font-bold text-gray-800 mb-4">Update Mission Details</h2>
            <p className="text-gray-600 mb-6">
              Make changes to your mission and save to update.
            </p>

            {/* Basic mission details form fields will be added here */}
            <div className="space-y-6">
              {/* Title */}
              <div>
                <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
                  Mission Title*
                </label>
                <input
                  type="text"
                  id="title"
                  name="title"
                  value={formData.title}
                  onChange={(e) => setFormData({...formData, title: e.target.value})}
                  className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="E.g., MLBB - Tank, Mage Support Needed!"
                  required
                />
              </div>

              {/* Mission Images */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Mission Images
                </label>
                <p className="text-sm text-gray-500 mb-4">
                  Upload images that showcase your mission. The first image will be used as the primary image.
                </p>

                <MissionImageUploader
                  images={formData.images}
                  onImagesChange={(images) => setFormData({...formData, images})}
                  maxImages={5}
                  maxSizeMB={5}
                  missionId={missionId}
                />
              </div>

              {/* Submit button */}
              <div className="flex justify-end pt-4">
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className={`px-6 py-2 bg-indigo-600 text-white rounded-lg font-medium hover:bg-indigo-700 transition-colors ${
                    isSubmitting ? 'opacity-70 cursor-not-allowed' : ''
                  }`}
                >
                  {isSubmitting ? (
                    <span className="flex items-center">
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Updating Mission...
                    </span>
                  ) : (
                    'Update Mission'
                  )}
                </button>
              </div>
            </div>
          </div>
        </form>
      </motion.div>
    </MissionLayout>
  );
};

export default MissionEditPage;
