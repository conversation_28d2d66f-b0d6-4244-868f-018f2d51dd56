import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { getCdnUrl } from '../utils/cdnUtils';
import { motion, AnimatePresence } from 'framer-motion';
import MissionLayout from '../components/mission/MissionLayout';
import HostedMissionCard from '../components/mission/HostedMissionCard';
import AppliedMissionCard from '../components/mission/AppliedMissionCard';
import CompletedMissionCard from '../components/mission/CompletedMissionCard';
import EnhancedMissionManagement from '../components/mission/EnhancedMissionManagement';
import MissionAnalytics from '../components/mission/MissionAnalytics';
import { missionApi } from '../services/missionApi';
import { transformApiMissionsToFrontend } from '../utils/missionTransformers';
import { getApiErrorMessage } from '../utils/errorHandlers';
import { useToast } from '../components/common/ToastProvider';

const MyMissionsPage = () => {
  const navigate = useNavigate();
  const toast = useToast();
  const [activeTab, setActiveTab] = useState('hosted');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [hostedMissions, setHostedMissions] = useState([]);
  const [appliedMissions, setAppliedMissions] = useState([]);
  const [completedMissions, setCompletedMissions] = useState([]);
  const [viewMode, setViewMode] = useState('cards'); // 'cards' or 'management'

  // Fetch missions data
  useEffect(() => {
    const fetchMissions = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Get current user ID
        const userId = localStorage.getItem('user_id');

        if (!userId) {
          setError('User not authenticated');
          setIsLoading(false);
          return;
        }

        // Fetch missions based on active tab
        if (activeTab === 'hosted') {
          // Fetch missions created by the current user
          const response = await missionApi.getMissions({
            user_id: userId
          });

          // Transform API missions to frontend format
          const transformedMissions = transformApiMissionsToFrontend(response.data.data);
          setHostedMissions(transformedMissions);
        } else if (activeTab === 'applied') {
          // Fetch missions the user has applied to
          const response = await missionApi.getUserApplications();

          // Transform API applications to frontend format
          const transformedMissions = response.data.map(app => ({
            id: app.mission_id,
            title: app.mission?.title || 'Untitled Mission',
            image: app.mission?.images?.[0]?.url || '/images/mission-default.jpg',
            bounty: app.mission?.bounty || 0,
            date: app.mission?.service_start_date,
            end_date: app.mission?.service_end_date,
            host: {
              name: app.mission?.user?.name || 'Anonymous Host',
              avatar: getCdnUrl(app.mission?.user?.profile_picture || '/images/default-avatar.jpg')
            },
            status: app.status,
            applied_at: app.created_at
          }));

          setAppliedMissions(transformedMissions);
        } else if (activeTab === 'completed') {
          // Fetch completed missions
          const response = await missionApi.getMissions({
            status: 'completed',
            participant_id: userId
          });

          // Transform API missions to frontend format
          const transformedMissions = response.data.data.map(mission => ({
            id: mission.id,
            title: mission.title || 'Untitled Mission',
            image: mission.images?.[0]?.url || '/images/mission-default.jpg',
            bounty: mission.bounty || 0,
            date: mission.service_start_date,
            end_date: mission.service_end_date,
            host: {
              name: mission.user?.name || 'Anonymous Host',
              avatar: getCdnUrl(mission.user?.profile_picture || '/images/default-avatar.jpg')
            },
            status: 'completed',
            completed_at: mission.updated_at,
            rating: mission.user_rating || 0
          }));

          setCompletedMissions(transformedMissions);
        }
      } catch (error) {
        console.error(`Error fetching ${activeTab} missions:`, error);
        setError(getApiErrorMessage(error));
        toast.error(`Failed to load ${activeTab} missions. Please try again.`);
      } finally {
        setIsLoading(false);
      }
    };

    fetchMissions();
  }, [activeTab, toast]);

  // Handle mission update
  const handleMissionUpdate = () => {
    // Refresh missions data
    const fetchMissions = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const userId = localStorage.getItem('user_id');
        if (!userId) {
          setError('User not authenticated');
          setIsLoading(false);
          return;
        }

        if (activeTab === 'hosted') {
          const response = await missionApi.getMissions({ user_id: userId });
          const transformedMissions = transformApiMissionsToFrontend(response.data.data);
          setHostedMissions(transformedMissions);
        }
      } catch (error) {
        console.error('Error fetching missions:', error);
        setError(getApiErrorMessage(error) || 'Failed to load missions');
      } finally {
        setIsLoading(false);
      }
    };

    fetchMissions();
  };

  // Handle mission deletion
  const handleMissionDelete = (missionId) => {
    setHostedMissions(prev => prev.filter(mission => mission.id !== missionId));
  };

  // Render content based on active tab
  const renderContent = () => {
    return (
      <motion.div
        className="bg-white rounded-2xl shadow-md overflow-hidden mb-8"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="border-b border-gray-200">
          <nav className="flex -mb-px">
            <button
              onClick={() => setActiveTab('hosted')}
              className={`py-4 px-6 text-center border-b-2 font-medium text-sm flex-1 ${
                activeTab === 'hosted'
                  ? 'border-indigo-500 bg-transparent hover:bg-transparent text-indigo-600'
                  : 'border-transparent bg-transparent hover:bg-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } transition-colors duration-200`}
            >
              Hosted
              <span className="ml-2 bg-indigo-100 text-indigo-600 px-2 py-0.5 rounded-full text-xs">
                {hostedMissions.length}
              </span>
            </button>
            <button
              onClick={() => setActiveTab('applied')}
              className={`py-4 px-6 text-center border-b-2 font-medium text-sm flex-1 ${
                activeTab === 'applied'
                  ? 'border-indigo-500 bg-transparent hover:bg-transparent text-indigo-600'
                  : 'border-transparent bg-transparent hover:bg-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } transition-colors duration-200`}
            >
              Applied
              <span className="ml-2 bg-indigo-100 text-indigo-600 px-2 py-0.5 rounded-full text-xs">
                {appliedMissions.length}
              </span>
            </button>
            <button
              onClick={() => setActiveTab('completed')}
              className={`py-4 px-6 text-center border-b-2 font-medium text-sm flex-1 ${
                activeTab === 'completed'
                  ? 'border-indigo-500 bg-transparent hover:bg-transparent text-indigo-600'
                  : 'border-transparent bg-transparent hover:bg-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } transition-colors duration-200`}
            >
              Completed
              <span className="ml-2 bg-indigo-100 text-indigo-600 px-2 py-0.5 rounded-full text-xs">
                {completedMissions.length}
              </span>
            </button>
            <button
              onClick={() => setActiveTab('analytics')}
              className={`py-4 px-6 text-center border-b-2 font-medium text-sm flex-1 ${
                activeTab === 'analytics'
                  ? 'border-indigo-500 bg-transparent hover:bg-transparent text-indigo-600'
                  : 'border-transparent bg-transparent hover:bg-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } transition-colors duration-200`}
            >
              Analytics
            </button>
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {isLoading ? (
            <div className="flex justify-center py-10">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
            </div>
          ) : error ? (
            <div className="bg-white rounded-xl shadow-md p-8 text-center max-w-md mx-auto">
              <svg className="w-16 h-16 text-red-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <h3 className="text-lg font-medium text-gray-900 mb-1">Error Loading Missions</h3>
              <p className="text-gray-500 mb-4">{error}</p>
              <button
                onClick={() => window.location.reload()}
                className="px-4 py-2 bg-indigo-600 text-white rounded-lg font-medium hover:bg-indigo-700 transition-colors"
              >
                Try Again
              </button>
            </div>
          ) : (
            <>
              {/* Hosted Missions Tab */}
              {activeTab === 'hosted' && (
                <div>
                  {hostedMissions.length > 0 ? (
                    <div className="space-y-4">
                      <div className="flex justify-between items-center mb-4">
                        <p className="text-gray-600">
                          Manage the missions you've created and review applicants.
                        </p>

                        {/* View Mode Toggle */}
                        <div className="flex bg-gray-100 rounded-lg p-1">
                          <button
                            className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                              viewMode === 'cards'
                                ? 'bg-white text-indigo-600 shadow-sm'
                                : 'text-gray-600 hover:text-gray-800'
                            }`}
                            onClick={() => setViewMode('cards')}
                          >
                            <svg className="w-4 h-4 mr-1 inline-block" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                            </svg>
                            Cards
                          </button>
                          <button
                            className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                              viewMode === 'management'
                                ? 'bg-white text-indigo-600 shadow-sm'
                                : 'text-gray-600 hover:text-gray-800'
                            }`}
                            onClick={() => setViewMode('management')}
                          >
                            <svg className="w-4 h-4 mr-1 inline-block" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                            </svg>
                            Manage
                          </button>
                        </div>
                      </div>

                      <AnimatePresence mode="wait">
                        {viewMode === 'cards' ? (
                          <motion.div
                            key="cards"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: -20 }}
                            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
                          >
                            {hostedMissions.map(mission => (
                              <HostedMissionCard key={mission.id} mission={mission} />
                            ))}
                          </motion.div>
                        ) : (
                          <motion.div
                            key="management"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: -20 }}
                            className="space-y-6"
                          >
                            {hostedMissions.map(mission => (
                              <EnhancedMissionManagement
                                key={mission.id}
                                mission={mission}
                                onUpdate={handleMissionUpdate}
                                onDelete={handleMissionDelete}
                              />
                            ))}
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </div>
                  ) : (
                    <div className="text-center py-10">
                      <svg className="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122" />
                      </svg>
                      <h3 className="text-lg font-medium text-gray-900 mb-1">No hosted missions</h3>
                      <p className="text-gray-500 mb-4">You haven't created any missions yet.</p>
                      <motion.button
                        onClick={() => navigate('/missions/create')}
                        className="px-6 py-2.5 bg-gradient-to-r from-indigo-600 to-blue-600 text-white rounded-lg font-medium shadow-md hover:shadow-lg transition-all"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <svg className="w-5 h-5 mr-2 inline-block" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                        Create Your First Mission
                      </motion.button>
                    </div>
                  )}
                </div>
              )}

              {/* Applied Missions Tab */}
              {activeTab === 'applied' && (
                <div>
                  {appliedMissions.length > 0 ? (
                    <div className="space-y-4">
                      <p className="text-gray-600 mb-4">
                        Track the status of missions you've applied to.
                      </p>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {appliedMissions.map(mission => (
                          <AppliedMissionCard key={mission.id} mission={mission} />
                        ))}
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-10">
                      <svg className="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                      </svg>
                      <h3 className="text-lg font-medium text-gray-900 mb-1">No applied missions</h3>
                      <p className="text-gray-500 mb-4">You haven't applied to any missions yet.</p>
                      <button
                        onClick={() => navigate('/missions')}
                        className="px-4 py-2 bg-indigo-600 text-white rounded-lg font-medium hover:bg-indigo-700 transition-colors"
                      >
                        Browse Available Missions
                      </button>
                    </div>
                  )}
                </div>
              )}

              {/* Completed Missions Tab */}
              {activeTab === 'completed' && (
                <div>
                  {completedMissions.length > 0 ? (
                    <div className="space-y-4">
                      <p className="text-gray-600 mb-4">
                        View your mission history and earnings.
                      </p>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {completedMissions.map(mission => (
                          <CompletedMissionCard key={mission.id} mission={mission} />
                        ))}
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-10">
                      <svg className="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <h3 className="text-lg font-medium text-gray-900 mb-1">No completed missions</h3>
                      <p className="text-gray-500 mb-4">You haven't completed any missions yet.</p>
                      <button
                        onClick={() => navigate('/missions')}
                        className="px-4 py-2 bg-indigo-600 text-white rounded-lg font-medium hover:bg-indigo-700 transition-colors"
                      >
                        Browse Available Missions
                      </button>
                    </div>
                  )}
                </div>
              )}

              {/* Analytics Tab */}
              {activeTab === 'analytics' && (
                <div>
                  <MissionAnalytics missions={[...hostedMissions, ...appliedMissions, ...completedMissions]} />
                </div>
              )}
            </>
          )}
        </div>
      </motion.div>
    );
  };

  return (
    <MissionLayout
      title="My Missions"
      backPath="/missions"
      useMainNav={true}
      useMissionMobileNav={true}
    >
      {renderContent()}
    </MissionLayout>
  );
};

export default MyMissionsPage;
