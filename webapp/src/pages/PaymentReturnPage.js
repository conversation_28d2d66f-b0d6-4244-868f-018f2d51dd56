import React, { useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { motion } from 'framer-motion';
import PaymentReturnHandler from '../components/PaymentReturnHandler';
import walletAPI from '../services/walletService';

/**
 * PaymentReturnPage component
 * 
 * This page handles the return from payment gateways and processes the payment status.
 * It's a dedicated page that users are redirected to after completing a payment.
 */
const PaymentReturnPage = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  
  // Handle payment success
  const handlePaymentSuccess = (data) => {
    // Show success message (handled by PaymentReturnHandler)
    
    // Update wallet balance if needed
    // This is optional as the wallet page will refresh when navigated to
    
    // Get the return path from localStorage or default to wallet
    const returnPath = localStorage.getItem('paymentReturnPath') || '/wallet';
    
    // Auto-redirect is handled by PaymentReturnHandler
  };
  
  // Handle payment error
  const handlePaymentError = (error) => {
    // Show error message (handled by PaymentReturnHandler)
    
    // Log the error for debugging
    console.error('Payment error:', error);
    
    // No need to redirect as PaymentReturnHandler provides buttons
  };
  
  // Check if we need to retry a payment
  useEffect(() => {
    const checkForRetry = async () => {
      const retryId = searchParams.get('retry');
      
      if (retryId) {
        try {
          // Store the return path
          localStorage.setItem('paymentReturnPath', '/wallet');
          
          // Retry the payment
          const response = await walletAPI.retryPayment(retryId);
          
          if (response.data && response.data.payment) {
            // Store the transaction ID
            localStorage.setItem('pendingPaymentId', response.data.payment.transaction_id);
            
            // Redirect to payment gateway
            if (response.data.payment.redirect_url) {
              window.location.href = response.data.payment.redirect_url;
            }
          }
        } catch (error) {
          console.error('Error retrying payment:', error);
        }
      }
    };
    
    checkForRetry();
  }, [searchParams]);
  
  return (
    <div className="min-h-screen bg-gradient-to-b from-indigo-50 to-white flex flex-col">
      {/* Navigation Bar */}
      <nav className="bg-white shadow-md sticky top-0 z-50 backdrop-filter backdrop-blur-md bg-opacity-90">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-800 transform transition-all hover:scale-105 duration-300">
                Mission<span className="text-indigo-600 relative">X
                  <span className="absolute -top-1 -right-1 w-2 h-2 bg-indigo-500 rounded-full animate-pulse-light"></span>
                </span>
              </h1>
            </div>
          </div>
        </div>
      </nav>
      
      {/* Main Content */}
      <div className="flex-1 flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="bg-white rounded-2xl shadow-sm max-w-md w-full mx-4 overflow-hidden"
        >
          <div className="p-1">
            <div className="bg-gradient-to-r from-indigo-500 to-purple-600 rounded-t-xl p-6 text-white">
              <h1 className="text-2xl font-bold text-center">Payment Processing</h1>
            </div>
            
            <div className="p-6">
              <PaymentReturnHandler
                onSuccess={handlePaymentSuccess}
                onError={handlePaymentError}
                showFeedback={true}
                autoRedirect={true}
              />
            </div>
          </div>
        </motion.div>
      </div>
      
      {/* Footer */}
      <footer className="bg-white border-t border-gray-200 py-4">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center">
            <p className="text-sm text-gray-600">
              &copy; {new Date().getFullYear()} MissionX. All rights reserved.
            </p>
            <div className="flex space-x-4">
              <button
                onClick={() => navigate('/wallet')}
                className="text-sm text-indigo-600 hover:text-indigo-800 transition-colors"
              >
                Return to Wallet
              </button>
              <button
                onClick={() => navigate('/help')}
                className="text-sm text-gray-600 hover:text-gray-800 transition-colors"
              >
                Need Help?
              </button>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default PaymentReturnPage;
