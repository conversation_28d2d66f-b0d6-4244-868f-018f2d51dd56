import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import walletAPI from '../services/walletService';

const PaymentReturn = () => {
  const [status, setStatus] = useState('loading'); // loading, success, failed
  const [errorMessage, setErrorMessage] = useState('');
  const [transaction, setTransaction] = useState(null);
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    const checkPaymentStatus = async () => {
      try {
        // Get transaction ID from localStorage or URL params
        const params = new URLSearchParams(location.search);
        const transactionId = params.get('transaction_id') || localStorage.getItem('pendingPaymentId');
        
        if (!transactionId) {
          throw new Error('No transaction ID found');
        }

        // Check payment status with the API
        const response = await walletAPI.checkPaymentStatus(transactionId);
        
        if (response.data.status === 'success') {
          setStatus('success');
          setTransaction(response.data.transaction);
          // Clear the pending payment ID
          localStorage.removeItem('pendingPaymentId');
        } else if (response.data.status === 'pending') {
          // Payment still processing
          setStatus('loading');
          // Check again after 2 seconds
          setTimeout(checkPaymentStatus, 2000);
        } else {
          // Payment failed
          throw new Error(response.data.message || 'Payment failed');
        }
      } catch (err) {
        console.error('Error checking payment status:', err);
        setStatus('failed');
        setErrorMessage(err.message || 'Failed to verify payment status');
        localStorage.removeItem('pendingPaymentId');
      }
    };

    checkPaymentStatus();
  }, [location.search]);

  const handleReturn = () => {
    navigate('/wallet');
  };

  // Loading state
  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-gradient-to-b from-indigo-50 to-white flex flex-col items-center justify-center p-4">
        <div className="bg-white rounded-2xl shadow-xl p-8 max-w-md w-full text-center">
          <div className="w-16 h-16 mx-auto mb-6">
            <svg className="animate-spin w-full h-full text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Verifying Payment</h1>
          <p className="text-gray-600 mb-8">
            Please wait while we confirm your payment status...
          </p>
        </div>
      </div>
    );
  }

  // Success state
  if (status === 'success') {
    return (
      <div className="min-h-screen bg-gradient-to-b from-indigo-50 to-white flex flex-col items-center justify-center p-4">
        <motion.div 
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
          className="bg-white rounded-2xl shadow-xl p-8 max-w-md w-full text-center"
        >
          <div className="w-16 h-16 mx-auto mb-6 text-green-500">
            <svg className="w-full h-full" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Payment Successful!</h1>
          <p className="text-gray-600 mb-4">
            Your payment has been processed successfully and credits have been added to your wallet.
          </p>
          
          {transaction && (
            <div className="bg-gray-50 p-4 rounded-lg mb-6 text-left">
              <div className="flex justify-between mb-2">
                <span className="text-gray-600">Transaction ID:</span>
                <span className="font-medium">{transaction.id}</span>
              </div>
              <div className="flex justify-between mb-2">
                <span className="text-gray-600">Credits Added:</span>
                <span className="font-medium text-green-600">+{transaction.credits}</span>
              </div>
              <div className="flex justify-between mb-2">
                <span className="text-gray-600">New Balance:</span>
                <span className="font-medium">{transaction.balance_after}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Date:</span>
                <span className="font-medium">
                  {new Date(transaction.created_at).toLocaleString()}
                </span>
              </div>
            </div>
          )}
          
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={handleReturn}
            className="px-6 py-2 bg-indigo-600 text-white rounded-lg shadow-sm hover:bg-indigo-700 transition-colors"
          >
            Return to Wallet
          </motion.button>
        </motion.div>
      </div>
    );
  }

  // Failed state
  return (
    <div className="min-h-screen bg-gradient-to-b from-indigo-50 to-white flex flex-col items-center justify-center p-4">
      <motion.div 
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
        className="bg-white rounded-2xl shadow-xl p-8 max-w-md w-full text-center"
      >
        <div className="w-16 h-16 mx-auto mb-6 text-red-500">
          <svg className="w-full h-full" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </div>
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Payment Failed</h1>
        <p className="text-red-600 mb-6">
          {errorMessage || 'There was an issue processing your payment.'}
        </p>
        <div className="flex flex-col space-y-3">
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => navigate('/wallet/top-up')}
            className="px-6 py-2 bg-indigo-600 text-white rounded-lg shadow-sm hover:bg-indigo-700 transition-colors"
          >
            Try Again
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={handleReturn}
            className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Return to Wallet
          </motion.button>
        </div>
      </motion.div>
    </div>
  );
};

export default PaymentReturn; 