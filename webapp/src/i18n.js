import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import { translations, namespaces, supportedLanguages } from './translations';

// Try to import Backend, but don't fail if it's not available
let Backend;
try {
  Backend = require('i18next-http-backend').default;
} catch (e) {
  console.warn('i18next-http-backend not available, dynamic loading of translations will be disabled');
  Backend = null;
}

/**
 * Initialize i18next with enhanced configuration
 * - Improved language detection
 * - Better fallback handling
 * - Performance optimizations
 * - RTL support
 */
// Initialize i18next with available modules
const i18nInstance = i18n;

// Add Backend if available
if (Backend) {
  i18nInstance.use(Backend);
}

// Add language detector
i18nInstance
  .use(LanguageDetector)

  // React integration
  .use(initReactI18next)

  // Initialize with configuration
  .init({
    // Translation resources
    resources: translations,

    // Default language fallback
    fallbackLng: {
      'default': ['en'],
      // Chinese fallbacks
      'zh': ['cn', 'en'],
      'zh-CN': ['cn', 'en'],
      'zh-TW': ['cn', 'en'],
      'zh-HK': ['cn', 'en'],
      // Malay fallbacks
      'ms-MY': ['ms', 'en'],
      'ms-SG': ['ms', 'en'],
      'ms-BN': ['ms', 'en'],
    },

    // Show debug info in development
    debug: process.env.NODE_ENV === 'development',

    // Common namespace used around the app
    defaultNS: 'common',

    // Namespaces to load
    ns: namespaces,

    // Enhanced language detection
    detection: {
      // Detection order (try localStorage first, then browser settings)
      order: ['localStorage', 'navigator', 'htmlTag', 'path', 'subdomain'],

      // LocalStorage key
      lookupLocalStorage: 'userLanguage',

      // Cache detected language
      caches: ['localStorage'],

      // Check for language in URL path
      lookupFromPathIndex: 0,

      // Check for language in URL parameters
      lookupFromURLParams: 'lang',

      // Only use languages we support
      checkWhitelist: true
    },

    // Only load supported languages
    supportedLngs: supportedLanguages.map(lang => lang.code),

    // Don't load fallback translations on init (load on demand)
    partialBundledLanguages: true,

    // Interpolation settings
    interpolation: {
      escapeValue: false, // React already safes from XSS
      format: function(value, format, lng) {
        // Handle date formatting
        if (value instanceof Date) {
          return new Intl.DateTimeFormat(lng).format(value);
        }
        // Handle number formatting
        if (typeof value === 'number' && format) {
          if (format === 'currency') {
            return new Intl.NumberFormat(lng, { style: 'currency', currency: 'MYR' }).format(value);
          }
          if (format === 'percent') {
            return new Intl.NumberFormat(lng, { style: 'percent' }).format(value / 100);
          }
          return new Intl.NumberFormat(lng).format(value);
        }
        return value;
      }
    },

    // React specific settings
    react: {
      useSuspense: true,
      transSupportBasicHtmlNodes: true,
      transKeepBasicHtmlNodesFor: ['br', 'strong', 'i', 'em', 'b', 'p', 'span']
    },
  });

// Set up language change listener to update HTML dir attribute
i18n.on('languageChanged', (lng) => {
  // Update HTML dir attribute based on language
  const direction = i18n.dir(lng);
  document.documentElement.dir = direction;
  document.documentElement.lang = lng;

  // Log language change in development
  if (process.env.NODE_ENV === 'development') {
    console.log(`Language changed to: ${lng} (${direction})`);
  }
});

export default i18n;
