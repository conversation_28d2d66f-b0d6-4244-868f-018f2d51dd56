/**
 * Cache Manager Tests
 */

import { cacheManager } from '../../utils/cacheManager';

describe('cacheManager', () => {
  beforeEach(() => {
    // Clear the cache before each test
    cacheManager.clear();
  });
  
  describe('get and set', () => {
    it('should return null for non-existent keys', () => {
      const result = cacheManager.get('non-existent-key');
      expect(result).toBeNull();
    });
    
    it('should store and retrieve data', () => {
      const data = { foo: 'bar' };
      cacheManager.set('test-key', data);
      const result = cacheManager.get('test-key');
      expect(result).toEqual(data);
    });
    
    it('should return the data when setting', () => {
      const data = { foo: 'bar' };
      const result = cacheManager.set('test-key', data);
      expect(result).toEqual(data);
    });
    
    it('should handle params in keys', () => {
      const data1 = { foo: 'bar' };
      const data2 = { foo: 'baz' };
      const params1 = { page: 1 };
      const params2 = { page: 2 };
      
      cacheManager.set('test-key', data1, params1);
      cacheManager.set('test-key', data2, params2);
      
      const result1 = cacheManager.get('test-key', params1);
      const result2 = cacheManager.get('test-key', params2);
      
      expect(result1).toEqual(data1);
      expect(result2).toEqual(data2);
    });
    
    it('should handle empty params', () => {
      const data = { foo: 'bar' };
      cacheManager.set('test-key', data, {});
      const result = cacheManager.get('test-key');
      expect(result).toEqual(data);
    });
    
    it('should handle params order consistently', () => {
      const data = { foo: 'bar' };
      cacheManager.set('test-key', data, { a: 1, b: 2 });
      const result = cacheManager.get('test-key', { b: 2, a: 1 });
      expect(result).toEqual(data);
    });
  });
  
  describe('expiration', () => {
    it('should expire items after the specified time', () => {
      jest.useFakeTimers();
      
      const data = { foo: 'bar' };
      cacheManager.set('test-key', data, {}, 1000); // 1 second expiration
      
      // Verify it's in the cache
      expect(cacheManager.get('test-key')).toEqual(data);
      
      // Advance time by 1.1 seconds
      jest.advanceTimersByTime(1100);
      
      // Verify it's expired
      expect(cacheManager.get('test-key')).toBeNull();
      
      jest.useRealTimers();
    });
    
    it('should use default expiration if not specified', () => {
      jest.useFakeTimers();
      
      const data = { foo: 'bar' };
      cacheManager.set('test-key', data);
      
      // Verify it's in the cache
      expect(cacheManager.get('test-key')).toEqual(data);
      
      // Advance time by 5 minutes + 100ms
      jest.advanceTimersByTime(5 * 60 * 1000 + 100);
      
      // Verify it's expired
      expect(cacheManager.get('test-key')).toBeNull();
      
      jest.useRealTimers();
    });
  });
  
  describe('invalidate', () => {
    it('should invalidate a specific key', () => {
      const data1 = { foo: 'bar' };
      const data2 = { foo: 'baz' };
      
      cacheManager.set('key1', data1);
      cacheManager.set('key2', data2);
      
      cacheManager.invalidate('key1');
      
      expect(cacheManager.get('key1')).toBeNull();
      expect(cacheManager.get('key2')).toEqual(data2);
    });
    
    it('should invalidate keys with a prefix', () => {
      const data1 = { foo: 'bar' };
      const data2 = { foo: 'baz' };
      const data3 = { foo: 'qux' };
      
      cacheManager.set('prefix:key1', data1);
      cacheManager.set('prefix:key2', data2);
      cacheManager.set('other:key', data3);
      
      cacheManager.invalidate('prefix');
      
      expect(cacheManager.get('prefix:key1')).toBeNull();
      expect(cacheManager.get('prefix:key2')).toBeNull();
      expect(cacheManager.get('other:key')).toEqual(data3);
    });
  });
  
  describe('clear', () => {
    it('should clear all cached items', () => {
      cacheManager.set('key1', { foo: 'bar' });
      cacheManager.set('key2', { foo: 'baz' });
      
      cacheManager.clear();
      
      expect(cacheManager.get('key1')).toBeNull();
      expect(cacheManager.get('key2')).toBeNull();
    });
  });
});
