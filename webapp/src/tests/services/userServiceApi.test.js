/**
 * User Service API Tests
 */

import userServiceApi from '../../services/userServiceApi';
import api from '../../services/api';
import { cacheManager } from '../../utils/cacheManager';

// Mock the api module
jest.mock('../../services/api');

// Mock the cacheManager
jest.mock('../../utils/cacheManager', () => ({
  cacheManager: {
    get: jest.fn(),
    set: jest.fn((key, data) => data),
    invalidate: jest.fn()
  }
}));

// Mock environment variables
const originalEnv = process.env;

describe('userServiceApi', () => {
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Mock environment variables
    process.env = {
      ...originalEnv,
      NODE_ENV: 'development',
      REACT_APP_USE_MOCK_API: 'true'
    };
  });
  
  afterAll(() => {
    // Restore environment variables
    process.env = originalEnv;
  });
  
  describe('getUserServices', () => {
    it('should return cached data if available', async () => {
      // Setup
      const cachedData = {
        success: true,
        data: [{ id: 1, title: 'Cached Service' }]
      };
      cacheManager.get.mockReturnValueOnce(cachedData);
      
      // Execute
      const result = await userServiceApi.getUserServices();
      
      // Verify
      expect(cacheManager.get).toHaveBeenCalledWith('user-services', {});
      expect(result).toEqual(cachedData);
      expect(api.get).not.toHaveBeenCalled();
    });
    
    it('should use mock data in development mode', async () => {
      // Setup
      cacheManager.get.mockReturnValueOnce(null);
      
      // Execute
      const result = await userServiceApi.getUserServices();
      
      // Verify
      expect(result.success).toBe(true);
      expect(Array.isArray(result.data)).toBe(true);
      expect(result.data.length).toBeGreaterThan(0);
      expect(api.get).not.toHaveBeenCalled();
      expect(cacheManager.set).toHaveBeenCalled();
    });
    
    it('should call the API in production mode', async () => {
      // Setup
      process.env.NODE_ENV = 'production';
      cacheManager.get.mockReturnValueOnce(null);
      const apiResponse = { data: [{ id: 1, title: 'API Service' }] };
      api.get.mockResolvedValueOnce(apiResponse);
      
      // Execute
      const result = await userServiceApi.getUserServices();
      
      // Verify
      expect(api.get).toHaveBeenCalledWith('/user/services', { params: {} });
      expect(result.success).toBe(true);
      expect(result.data).toEqual(apiResponse.data);
      expect(cacheManager.set).toHaveBeenCalled();
    });
    
    it('should handle API errors', async () => {
      // Setup
      process.env.NODE_ENV = 'production';
      cacheManager.get.mockReturnValueOnce(null);
      const error = new Error('API Error');
      error.response = { data: { message: 'Service unavailable' } };
      api.get.mockRejectedValueOnce(error);
      
      // Execute
      const result = await userServiceApi.getUserServices();
      
      // Verify
      expect(api.get).toHaveBeenCalledWith('/user/services', { params: {} });
      expect(result.success).toBe(false);
      expect(result.error).toBe('Service unavailable');
    });
  });
  
  describe('getServiceById', () => {
    it('should return cached data if available', async () => {
      // Setup
      const cachedData = {
        success: true,
        data: { id: 1, title: 'Cached Service' }
      };
      cacheManager.get.mockReturnValueOnce(cachedData);
      
      // Execute
      const result = await userServiceApi.getServiceById(1);
      
      // Verify
      expect(cacheManager.get).toHaveBeenCalledWith('user-service-1');
      expect(result).toEqual(cachedData);
      expect(api.get).not.toHaveBeenCalled();
    });
    
    it('should use mock data in development mode', async () => {
      // Setup
      cacheManager.get.mockReturnValueOnce(null);
      
      // Execute
      const result = await userServiceApi.getServiceById(1);
      
      // Verify
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data.id).toBe(1);
      expect(api.get).not.toHaveBeenCalled();
      expect(cacheManager.set).toHaveBeenCalled();
    });
    
    it('should call the API in production mode', async () => {
      // Setup
      process.env.NODE_ENV = 'production';
      cacheManager.get.mockReturnValueOnce(null);
      const apiResponse = { data: { id: 1, title: 'API Service' } };
      api.get.mockResolvedValueOnce(apiResponse);
      
      // Execute
      const result = await userServiceApi.getServiceById(1);
      
      // Verify
      expect(api.get).toHaveBeenCalledWith('/user/services/1');
      expect(result.success).toBe(true);
      expect(result.data).toEqual(apiResponse.data);
      expect(cacheManager.set).toHaveBeenCalled();
    });
  });
  
  describe('createService', () => {
    it('should use mock data in development mode', async () => {
      // Setup
      const serviceData = {
        title: 'New Service',
        description: 'Service description'
      };
      
      // Execute
      const result = await userServiceApi.createService(serviceData);
      
      // Verify
      expect(result.success).toBe(true);
      expect(result.data.message).toContain('created successfully');
      expect(result.data.services[0].title).toBe(serviceData.title);
      expect(api.post).not.toHaveBeenCalled();
      expect(cacheManager.invalidate).toHaveBeenCalledWith('user-services');
    });
    
    it('should call the API in production mode', async () => {
      // Setup
      process.env.NODE_ENV = 'production';
      const serviceData = {
        title: 'New Service',
        description: 'Service description'
      };
      const apiResponse = { 
        data: { 
          message: 'Service created successfully',
          services: [{ ...serviceData, id: 1 }]
        } 
      };
      api.post.mockResolvedValueOnce(apiResponse);
      
      // Execute
      const result = await userServiceApi.createService(serviceData);
      
      // Verify
      expect(api.post).toHaveBeenCalledWith('/user/services', {
        services: [serviceData]
      });
      expect(result.success).toBe(true);
      expect(result.data).toEqual(apiResponse.data);
      expect(cacheManager.invalidate).toHaveBeenCalledWith('user-services');
    });
  });
  
  describe('updateService', () => {
    it('should require an ID for updates', async () => {
      // Setup
      const serviceData = {
        title: 'Updated Service',
        description: 'Updated description'
      };
      
      // Execute
      const result = await userServiceApi.updateService(serviceData);
      
      // Verify
      expect(result.success).toBe(false);
      expect(result.error).toContain('ID is required');
      expect(api.put).not.toHaveBeenCalled();
    });
    
    it('should use mock data in development mode', async () => {
      // Setup
      const serviceData = {
        id: 1,
        title: 'Updated Service',
        description: 'Updated description'
      };
      
      // Execute
      const result = await userServiceApi.updateService(serviceData);
      
      // Verify
      expect(result.success).toBe(true);
      expect(result.data.message).toContain('updated successfully');
      expect(result.data.services[0].title).toBe(serviceData.title);
      expect(api.put).not.toHaveBeenCalled();
      expect(cacheManager.invalidate).toHaveBeenCalledWith('user-services');
      expect(cacheManager.invalidate).toHaveBeenCalledWith('user-service-1');
    });
    
    it('should call the API in production mode', async () => {
      // Setup
      process.env.NODE_ENV = 'production';
      const serviceData = {
        id: 1,
        title: 'Updated Service',
        description: 'Updated description'
      };
      const apiResponse = { 
        data: { 
          message: 'Service updated successfully',
          services: [{ ...serviceData }]
        } 
      };
      api.put.mockResolvedValueOnce(apiResponse);
      
      // Execute
      const result = await userServiceApi.updateService(serviceData);
      
      // Verify
      expect(api.put).toHaveBeenCalledWith('/user/services', {
        services: [serviceData]
      });
      expect(result.success).toBe(true);
      expect(result.data).toEqual(apiResponse.data);
      expect(cacheManager.invalidate).toHaveBeenCalledWith('user-services');
      expect(cacheManager.invalidate).toHaveBeenCalledWith('user-service-1');
    });
  });
  
  describe('toggleServiceActive', () => {
    it('should use mock data in development mode', async () => {
      // Setup
      const serviceId = 1;
      
      // Execute
      const result = await userServiceApi.toggleServiceActive(serviceId);
      
      // Verify
      expect(result.success).toBe(true);
      expect(result.data.message).toBeDefined();
      expect(result.data.is_active).toBeDefined();
      expect(api.put).not.toHaveBeenCalled();
      expect(cacheManager.invalidate).toHaveBeenCalledWith('user-services');
      expect(cacheManager.invalidate).toHaveBeenCalledWith('user-service-1');
    });
    
    it('should call the API in production mode', async () => {
      // Setup
      process.env.NODE_ENV = 'production';
      const serviceId = 1;
      const apiResponse = { 
        data: { 
          message: 'Service enabled successfully',
          is_active: true
        } 
      };
      api.put.mockResolvedValueOnce(apiResponse);
      
      // Execute
      const result = await userServiceApi.toggleServiceActive(serviceId);
      
      // Verify
      expect(api.put).toHaveBeenCalledWith('/user/services/1/toggle-active');
      expect(result.success).toBe(true);
      expect(result.data).toEqual(apiResponse.data);
      expect(cacheManager.invalidate).toHaveBeenCalledWith('user-services');
      expect(cacheManager.invalidate).toHaveBeenCalledWith('user-service-1');
    });
  });
  
  describe('deleteService', () => {
    it('should use mock data in development mode', async () => {
      // Setup
      const serviceId = 1;
      
      // Execute
      const result = await userServiceApi.deleteService(serviceId);
      
      // Verify
      expect(result.success).toBe(true);
      expect(result.data.message).toContain('deleted successfully');
      expect(api.put).not.toHaveBeenCalled();
      expect(cacheManager.invalidate).toHaveBeenCalledWith('user-services');
      expect(cacheManager.invalidate).toHaveBeenCalledWith('user-service-1');
    });
  });
});
