import React from 'react';

// Lazy load page components
export const LazyHome = React.lazy(() => import('./components/Home'));
export const LazyAllGames = React.lazy(() => import('./pages/AllGames'));
export const LazyGameTalents = React.lazy(() => import('./components/GameTalents'));
export const LazyTalentProfile = React.lazy(() => import('./components/TalentProfile'));
export const LazyOrderManagement = React.lazy(() => import('./components/orders/OrderManagement'));
export const LazyOrderDetails = React.lazy(() => import('./components/orders/OrderDetails'));
export const LazyProfile = React.lazy(() => import('./components/Profile'));
export const LazyChat = React.lazy(() => import('./pages/Chat'));
export const LazyEditProfile = React.lazy(() => import('./components/EditProfile'));
export const LazyProfileSetup = React.lazy(() => import('./components/ProfileSetup'));
export const LazyTalent = React.lazy(() => import('./pages/TalentPage'));
export const LazyExplore = React.lazy(() => import('./pages/Explore'));
export const LazyWallet = React.lazy(() => import('./pages/Wallet'));
export const LazyPaymentReturn = React.lazy(() => import('./pages/PaymentReturn'));
export const LazyPaymentReturnPage = React.lazy(() => import('./pages/PaymentReturnPage'));
export const LazyBankAccountsPage = React.lazy(() => import('./pages/BankAccountsPage'));
export const LazyMissionPage = React.lazy(() => import('./pages/MissionPage'));
export const LazyMissionDetailPage = React.lazy(() => import('./pages/MissionDetailPage'));
export const LazyMyMissionsPage = React.lazy(() => import('./pages/MyMissionsPage'));
export const LazyMissionApplicantsPage = React.lazy(() => import('./pages/MissionApplicantsPage'));
export const LazyMissionCreatePage = React.lazy(() => import('./pages/MissionCreatePage'));
export const LazyMissionEditPage = React.lazy(() => import('./pages/MissionEditPage'));
export const LazyMissionExecutionPage = React.lazy(() => import('./pages/MissionExecutionPage'));

// Add missing components
export const LazyProfilePage = React.lazy(() => import('./components/profile/ProfilePage'));
export const LazyProfileSettingsPage = React.lazy(() => import('./components/profile/ProfileSettingsPage'));
export const LazyAvailabilityPage = React.lazy(() => import('./components/profile/AvailabilityPage')); 