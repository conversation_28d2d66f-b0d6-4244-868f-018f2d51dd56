import { getCdnUrl } from './cdnUtils';

/**
 * Homepage Data Transformers
 *
 * Utility functions to transform API data to the format expected by UI components.
 * Provides consistent data transformation with robust fallbacks for missing data.
 */

/**
 * Transform talent data from API format to UI format
 * @param {Object} talent - Talent data from API
 * @returns {Object} - Transformed talent data for UI
 */
export const transformTalentData = (talent) => {
  if (!talent) return null;

  // Default profile image if none provided
  const defaultProfileImage = '/images/profile-placeholder.svg';

  return {
    id: talent.id,
    name: talent.nickname || talent.display_name || 'Unknown Talent',
    level: talent.level?.level || talent.experience_level || 0,
    gender: talent.gender || 'unknown',
    bio: talent.biography || talent.bio || "No biography available",
    image: getCdnUrl(talent.profile_picture || talent.avatar || defaultProfileImage),
    isHot: talent.is_hot || talent.is_featured || false,
    isNew: talent.is_new || (talent.created_at && new Date(talent.created_at) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)),
    isOnline: talent.is_online || false,
    rating: talent.rating || talent.average_rating || 4.5,
    services: talent.services?.map(service => ({
      id: service.id,
      type: service.service_type?.name || '',
      style: service.service_style?.name || '',
      price: service.price || service.base_price || 0
    })) || [],
    skills: talent.skills || talent.specialties || []
  };
};

/**
 * Transform service type data from API format to UI format (for games display)
 * @param {Object} serviceType - Service type data from API
 * @returns {Object} - Transformed service type data for UI
 */
export const transformServiceTypeToGame = (serviceType) => {
  if (!serviceType) return null;

  // Default game image if none provided
  const defaultGameImage = '/images/game-placeholder.svg';

  return {
    id: serviceType.id,
    name: serviceType.name,
    image: getCdnUrl(serviceType.icon_path || defaultGameImage),
    shortName: serviceType.name.length > 20
      ? serviceType.name.split(' ').slice(0, 2).join('\n')
      : serviceType.name,
    description: serviceType.description || 'No description available',
    usageCount: serviceType.usage_count || 0,
    category: serviceType.service_category?.name || 'General'
  };
};

/**
 * Transform carousel slide data from API format to UI format
 * @param {Object} slide - Carousel slide data from API
 * @returns {Object} - Transformed carousel slide data for UI
 */
export const transformCarouselSlide = (slide) => {
  if (!slide) return null;

  return {
    id: slide.id,
    title: slide.title || 'Explore Mission X',
    content: slide.content || 'Join our gaming community today',
    buttonText: slide.button_text || 'Learn More',
    buttonUrl: slide.target_url || '/',
    isClickable: slide.is_clickable || false,
    isNew: slide.is_new || false,
    mediaFiles: slide.media_files || []
  };
};

/**
 * Transform talents pagination data from API format
 * @param {Object} paginationData - Pagination data from API
 * @returns {Object} - Transformed pagination data
 */
export const transformPaginationData = (paginationData) => {
  if (!paginationData) return null;

  return {
    currentPage: paginationData.current_page || 1,
    totalPages: paginationData.last_page || 1,
    totalItems: paginationData.total || 0,
    hasNextPage: !!paginationData.next_page_url,
    hasPrevPage: !!paginationData.prev_page_url,
    nextPageUrl: paginationData.next_page_url || null,
    prevPageUrl: paginationData.prev_page_url || null,
    perPage: paginationData.per_page || 10
  };
};

/**
 * Transform talents list with pagination from API format to UI format
 * @param {Object} apiResponse - API response with talents data and pagination
 * @returns {Object} - Transformed talents data with pagination for UI
 */
export const transformTalentsListWithPagination = (apiResponse) => {
  if (!apiResponse || !apiResponse.data) return { talents: [], pagination: null };

  // Handle both direct data and data.data structures (API inconsistency)
  const paginationData = apiResponse.data.data ? apiResponse.data : apiResponse.data;
  const talentsData = Array.isArray(paginationData.data)
    ? paginationData.data
    : Array.isArray(paginationData) ? paginationData : [];

  const talents = talentsData.map(transformTalentData).filter(Boolean);

  return {
    talents,
    pagination: transformPaginationData(paginationData)
  };
};

/**
 * Transform service types list from API format to UI format (for games display)
 * @param {Array} serviceTypes - Service types data from API
 * @returns {Array} - Transformed service types data for UI
 */
export const transformServiceTypesToGames = (serviceTypes) => {
  if (!Array.isArray(serviceTypes)) return [];

  return serviceTypes.map(transformServiceTypeToGame).filter(Boolean);
};

/**
 * Transform carousel slides from API format to UI format
 * @param {Array} slides - Carousel slides data from API
 * @returns {Array} - Transformed carousel slides data for UI
 */
export const transformCarouselSlides = (slides) => {
  if (!Array.isArray(slides)) return [];

  return slides.map(transformCarouselSlide).filter(Boolean);
};

/**
 * Transform all homepage data from API format to UI format
 * @param {Object} homepageData - Homepage data from API
 * @returns {Object} - Transformed homepage data for UI
 */
export const transformHomepageData = (homepageData) => {
  if (!homepageData || !homepageData.data) {
    return {
      newTalents: { talents: [], pagination: null },
      recommendedTalents: { talents: [], pagination: null },
      onlineTalents: { talents: [], pagination: null },
      availableMissionsCount: 0,
      popularGames: []
    };
  }

  const data = homepageData.data;

  return {
    newTalents: transformTalentsListWithPagination({ data: data.new_talents }),
    recommendedTalents: transformTalentsListWithPagination({ data: data.recommended_talents }),
    onlineTalents: transformTalentsListWithPagination({ data: data.online_talents }),
    availableMissionsCount: data.available_missions_count || 0,
    popularGames: transformServiceTypesToGames(data.popular_service_types || [])
  };
};

export default {
  transformTalentData,
  transformServiceTypeToGame,
  transformCarouselSlide,
  transformPaginationData,
  transformTalentsListWithPagination,
  transformServiceTypesToGames,
  transformCarouselSlides,
  transformHomepageData
};
