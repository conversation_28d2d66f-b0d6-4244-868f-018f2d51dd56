/**
 * Utility functions for handling CDN URLs
 */

const CDN_URL = process.env.REACT_APP_CDN_URL;

/**
 * Converts a relative path to a full CDN URL
 * @param {string} path - The relative path to the asset
 * @returns {string} The full CDN URL
 */
export const getCdnUrl = (path) => {
  if (!path) return '';
  
  // If the path is already a full URL, return it as is
  if (path.startsWith('http://') || path.startsWith('https://')) {
    return path;
  }
  
  // Remove leading slash if present
  const cleanPath = path.startsWith('/') ? path.substring(1) : path;
  
  // Return the full CDN URL
  return `${CDN_URL}/${cleanPath}`;
};

/**
 * Transforms image URLs in an object or array of objects
 * @param {Object|Array} data - The data containing image URLs
 * @param {string|Array} imageFields - The field(s) containing image URLs
 * @returns {Object|Array} The data with transformed image URLs
 */
export const transformImageUrls = (data, imageFields = ['image_url', 'profile_picture', 'cover_image']) => {
  if (!data) return data;
  
  // Convert single field to array
  const fields = Array.isArray(imageFields) ? imageFields : [imageFields];
  
  // Handle arrays
  if (Array.isArray(data)) {
    return data.map(item => transformImageUrls(item, fields));
  }
  
  // Handle objects
  if (typeof data === 'object') {
    const result = { ...data };
    
    fields.forEach(field => {
      if (result[field] && typeof result[field] === 'string') {
        result[field] = getCdnUrl(result[field]);
      }
    });
    
    return result;
  }
  
  return data;
}; 