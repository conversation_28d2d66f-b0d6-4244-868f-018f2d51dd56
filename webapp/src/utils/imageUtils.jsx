import React, { useState, useEffect, useMemo } from 'react';
import { cn } from '../lib/utils';

// In-memory cache for already loaded images
const imageCache = new Map();

/**
 * LazyImage component for optimized image loading
 * 
 * Features:
 * - Lazy loading of images using Intersection Observer
 * - Blur-up loading with low quality placeholder
 * - In-memory caching of loaded images
 * - Error handling with fallback
 * 
 * @param {Object} props
 * @param {string} props.src - Image source URL
 * @param {string} props.alt - Alt text for the image
 * @param {string} props.fallbackSrc - Fallback image to show on error
 * @param {string} props.placeholderColor - Background color while loading
 * @param {Function} props.onLoad - Callback when image loads
 * @param {Function} props.onError - Callback when image fails to load
 */
export const LazyImage = ({
  src,
  alt = '',
  fallbackSrc = '/placeholder-image.png',
  placeholderColor = '#f3f4f6',
  className,
  style,
  onLoad,
  onError,
  ...otherProps
}) => {
  const [isLoaded, setIsLoaded] = useState(imageCache.has(src));
  const [error, setError] = useState(false);
  const [isIntersecting, setIsIntersecting] = useState(false);
  const [lowQualitySrc, setLowQualitySrc] = useState('');
  
  // Generate tiny placeholder for blur-up effect
  useEffect(() => {
    // Skip if already cached or no source
    if (!src || imageCache.has(src) || lowQualitySrc) return;
    
    const generateTinyPlaceholder = async () => {
      try {
        // For production, replace with actual backend endpoint to generate LQIP
        // This is a placeholder implementation using a simple approach
        const tinyUrl = src.includes('?') 
          ? `${src}&w=20&q=10` 
          : `${src}?w=20&q=10`;
        
        setLowQualitySrc(tinyUrl);
      } catch (err) {
        console.error('Failed to generate placeholder:', err);
      }
    };
    
    generateTinyPlaceholder();
  }, [src, lowQualitySrc]);
  
  // Set up intersection observer for lazy loading
  useEffect(() => {
    if (!src) return;
    
    // Skip intersection observer if image is already in cache
    if (imageCache.has(src)) {
      setIsLoaded(true);
      return;
    }

    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        setIsIntersecting(entry.isIntersecting);
      },
      {
        rootMargin: '200px', // Start loading when image is 200px from viewport
        threshold: 0.1
      }
    );
    
    const currentElem = document.querySelector(`[data-img-src="${src}"]`);
    if (currentElem) {
      observer.observe(currentElem);
    }
    
    return () => {
      if (currentElem) {
        observer.unobserve(currentElem);
      }
    };
  }, [src]);
  
  // Load image when it comes into view
  useEffect(() => {
    if (!isIntersecting || !src || isLoaded) return;
    
    // If image is already in cache, use it directly
    if (imageCache.has(src)) {
      setIsLoaded(true);
      return;
    }
    
    const img = new Image();
    
    const handleLoad = () => {
      setIsLoaded(true);
      imageCache.set(src, true);
      onLoad?.();
    };
    
    const handleError = (e) => {
      setError(true);
      onError?.(e);
    };
    
    img.addEventListener('load', handleLoad);
    img.addEventListener('error', handleError);
    
    img.src = src;
    
    return () => {
      img.removeEventListener('load', handleLoad);
      img.removeEventListener('error', handleError);
    };
  }, [isIntersecting, src, isLoaded, onLoad, onError]);
  
  // Determine the src to use
  const displaySrc = useMemo(() => {
    if (error) return fallbackSrc;
    if (isLoaded) return src;
    return lowQualitySrc || null;
  }, [error, isLoaded, src, lowQualitySrc, fallbackSrc]);
  
  // Calculate blur effect
  const blurEffect = useMemo(() => {
    if (isLoaded || error) return 'none';
    if (lowQualitySrc) return 'blur(8px)';
    return 'none';
  }, [isLoaded, error, lowQualitySrc]);

  return (
    <div
      className={cn(
        "overflow-hidden bg-gray-100 relative",
        !isLoaded && "animate-pulse",
        className
      )}
      style={{
        backgroundColor: !isLoaded && !lowQualitySrc ? placeholderColor : undefined,
        ...style
      }}
      data-img-src={src}
    >
      {displaySrc && (
        <img
          src={displaySrc}
          alt={alt}
          style={{
            filter: blurEffect,
            transition: 'filter 0.3s ease-out',
            width: '100%',
            height: '100%',
            objectFit: otherProps.objectFit || 'cover',
            objectPosition: otherProps.objectPosition || 'center'
          }}
          {...otherProps}
        />
      )}
      
      {!isLoaded && !lowQualitySrc && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-6 h-6 border-2 border-indigo-300 border-t-indigo-600 rounded-full animate-spin" />
        </div>
      )}
    </div>
  );
};

/**
 * Preload images for faster display
 * @param {Array<string>} urls - Array of image URLs to preload
 * @returns {Promise<void>}
 */
export const preloadImages = (urls = []) => {
  return Promise.all(
    urls.filter(Boolean).map(
      (url) => 
        new Promise((resolve) => {
          // Skip if already cached
          if (imageCache.has(url)) {
            resolve();
            return;
          }
          
          const img = new Image();
          
          img.onload = () => {
            imageCache.set(url, true);
            resolve();
          };
          
          img.onerror = () => {
            // Still resolve but don't cache
            resolve();
          };
          
          img.src = url;
        })
    )
  );
};

/**
 * Clear the image cache
 * @param {Array<string>} urls - Optional specific URLs to clear (clears all if not provided)
 */
export const clearImageCache = (urls) => {
  if (Array.isArray(urls)) {
    urls.forEach(url => imageCache.delete(url));
  } else {
    imageCache.clear();
  }
}; 