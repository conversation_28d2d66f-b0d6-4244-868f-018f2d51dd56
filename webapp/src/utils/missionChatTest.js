/**
 * Mission Chat Test Utility
 * 
 * This file provides test functions for the mission chat functionality.
 * It can be used to verify that the mission chat utilities are working correctly.
 */

import { 
  findOrCreateMissionChat,
  findChatByMissionId,
  sendMissionSystemMessage,
  addParticipantToMissionChat,
  removeParticipantFromMissionChat,
  sendMissionEventMessage
} from './missionChatUtils';

/**
 * Test the mission chat functionality
 * 
 * @param {string} missionId - The ID of the mission to test
 * @param {string} missionTitle - The title of the mission
 * @returns {Promise<Object>} - Test results
 */
export const testMissionChat = async (missionId, missionTitle) => {
  const results = {
    success: true,
    steps: [],
    errors: []
  };

  try {
    // Step 1: Find or create a mission chat
    try {
      console.log('Step 1: Finding or creating mission chat...');
      const missionChat = await findOrCreateMissionChat(
        missionId,
        missionTitle,
        [1, 2, 3] // Mock participant IDs
      );
      
      results.steps.push({
        step: 'Find or create mission chat',
        success: true,
        data: missionChat
      });
      
      console.log('Mission chat found or created:', missionChat);
    } catch (error) {
      results.steps.push({
        step: 'Find or create mission chat',
        success: false,
        error: error.message
      });
      
      results.success = false;
      results.errors.push(error.message);
      console.error('Error in step 1:', error);
    }
    
    // Step 2: Find chat by mission ID
    try {
      console.log('Step 2: Finding chat by mission ID...');
      const missionChat = await findChatByMissionId(missionId);
      
      results.steps.push({
        step: 'Find chat by mission ID',
        success: true,
        data: missionChat
      });
      
      console.log('Mission chat found:', missionChat);
    } catch (error) {
      results.steps.push({
        step: 'Find chat by mission ID',
        success: false,
        error: error.message
      });
      
      results.success = false;
      results.errors.push(error.message);
      console.error('Error in step 2:', error);
    }
    
    // Step 3: Send a system message
    try {
      console.log('Step 3: Sending system message...');
      const message = await sendMissionSystemMessage(
        missionId,
        'This is a test system message'
      );
      
      results.steps.push({
        step: 'Send system message',
        success: true,
        data: message
      });
      
      console.log('System message sent:', message);
    } catch (error) {
      results.steps.push({
        step: 'Send system message',
        success: false,
        error: error.message
      });
      
      results.success = false;
      results.errors.push(error.message);
      console.error('Error in step 3:', error);
    }
    
    // Step 4: Add a participant
    try {
      console.log('Step 4: Adding participant...');
      const updatedChat = await addParticipantToMissionChat(
        missionId,
        4, // Mock user ID
        'Test User'
      );
      
      results.steps.push({
        step: 'Add participant',
        success: true,
        data: updatedChat
      });
      
      console.log('Participant added:', updatedChat);
    } catch (error) {
      results.steps.push({
        step: 'Add participant',
        success: false,
        error: error.message
      });
      
      results.success = false;
      results.errors.push(error.message);
      console.error('Error in step 4:', error);
    }
    
    // Step 5: Send a mission event message
    try {
      console.log('Step 5: Sending mission event message...');
      const message = await sendMissionEventMessage(
        missionId,
        'mission_started'
      );
      
      results.steps.push({
        step: 'Send mission event message',
        success: true,
        data: message
      });
      
      console.log('Mission event message sent:', message);
    } catch (error) {
      results.steps.push({
        step: 'Send mission event message',
        success: false,
        error: error.message
      });
      
      results.success = false;
      results.errors.push(error.message);
      console.error('Error in step 5:', error);
    }
    
    // Step 6: Remove a participant
    try {
      console.log('Step 6: Removing participant...');
      const updatedChat = await removeParticipantFromMissionChat(
        missionId,
        4, // Mock user ID
        'Test User'
      );
      
      results.steps.push({
        step: 'Remove participant',
        success: true,
        data: updatedChat
      });
      
      console.log('Participant removed:', updatedChat);
    } catch (error) {
      results.steps.push({
        step: 'Remove participant',
        success: false,
        error: error.message
      });
      
      results.success = false;
      results.errors.push(error.message);
      console.error('Error in step 6:', error);
    }
    
  } catch (error) {
    results.success = false;
    results.errors.push(error.message);
    console.error('Error in test:', error);
  }
  
  return results;
};

/**
 * Run the mission chat test
 * 
 * @param {string} missionId - The ID of the mission to test
 * @param {string} missionTitle - The title of the mission
 */
export const runMissionChatTest = async (missionId = 'test-mission-1', missionTitle = 'Test Mission') => {
  console.log(`Running mission chat test for mission ${missionId}...`);
  
  const results = await testMissionChat(missionId, missionTitle);
  
  console.log('Test results:', results);
  
  if (results.success) {
    console.log('✅ All tests passed!');
  } else {
    console.error('❌ Some tests failed:', results.errors);
  }
  
  return results;
};
