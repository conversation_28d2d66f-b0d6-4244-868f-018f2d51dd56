/**
 * Test utility to verify backend API integration for Malaysian banks
 * This file can be used for debugging API connectivity issues
 */

import bankAccountService from '../services/bankAccountService';

/**
 * Test Malaysian banks API endpoint
 */
export const testMalaysianBanksAPI = async () => {
  console.log('🧪 Testing Malaysian Banks API...');
  
  try {
    const response = await bankAccountService.getMalaysianBanks();
    console.log('✅ Malaysian Banks API Success:', response);
    
    const banks = response.data || response || [];
    if (Array.isArray(banks) && banks.length > 0) {
      console.log(`✅ Found ${banks.length} banks:`, banks.map(b => `${b.name} (${b.code})`));
      return { success: true, banks, count: banks.length };
    } else {
      console.warn('⚠️ API returned empty or invalid bank list');
      return { success: false, error: 'Empty bank list', response };
    }
  } catch (error) {
    console.error('❌ Malaysian Banks API Failed:', error);
    return { 
      success: false, 
      error: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      url: error.config?.url
    };
  }
};

/**
 * Test bank account creation flow
 */
export const testBankAccountCreation = async (testData) => {
  console.log('🧪 Testing Bank Account Creation...');
  
  try {
    const response = await bankAccountService.addBankAccount(testData);
    console.log('✅ Bank Account Creation Success:', response);
    
    const account = response.data?.bank_account || response.data;
    if (account && account.id) {
      console.log('✅ Account created with ID:', account.id);
      return { success: true, account };
    } else {
      console.warn('⚠️ Account creation returned unexpected format');
      return { success: false, error: 'Unexpected response format', response };
    }
  } catch (error) {
    console.error('❌ Bank Account Creation Failed:', error);
    return { 
      success: false, 
      error: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      validationErrors: error.response?.data?.errors
    };
  }
};

/**
 * Test complete bank account flow
 */
export const testCompleteBankFlow = async () => {
  console.log('🧪 Testing Complete Bank Account Flow...');
  
  // Step 1: Test Malaysian banks API
  const banksTest = await testMalaysianBanksAPI();
  if (!banksTest.success) {
    return { success: false, step: 'banks', ...banksTest };
  }
  
  // Step 2: Test bank account creation with first available bank
  const firstBank = banksTest.banks[0];
  const testAccountData = {
    malaysian_bank_id: firstBank.id,
    account_number: '*************',
    account_holder_name: 'Test User',
    is_primary: false
  };
  
  const accountTest = await testBankAccountCreation(testAccountData);
  if (!accountTest.success) {
    return { success: false, step: 'account_creation', ...accountTest };
  }
  
  console.log('✅ Complete Bank Account Flow Test Passed!');
  return { 
    success: true, 
    banksCount: banksTest.count,
    createdAccount: accountTest.account
  };
};

// Export for console testing
window.testBankAPI = {
  testMalaysianBanksAPI,
  testBankAccountCreation,
  testCompleteBankFlow
};

export default {
  testMalaysianBanksAPI,
  testBankAccountCreation,
  testCompleteBankFlow
};
