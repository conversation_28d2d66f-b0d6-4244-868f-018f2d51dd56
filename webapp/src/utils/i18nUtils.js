import { supportedLanguages } from '../translations';
import i18n from '../i18n';

/**
 * Utility functions for internationalization
 */

/**
 * Get language metadata by code
 * @param {string} code - Language code
 * @returns {Object|null} Language metadata or null if not found
 */
export const getLanguageByCode = (code) => {
  return supportedLanguages.find(lang => lang.code === code) || null;
};

/**
 * Get enabled languages (for UI display)
 * @returns {Array} Array of enabled languages
 */
export const getEnabledLanguages = () => {
  return supportedLanguages.filter(lang => lang.enabled !== false);
};

/**
 * Format a date according to the current locale
 * @param {Date|string|number} date - Date to format
 * @param {Object} options - Intl.DateTimeFormat options
 * @returns {string} Formatted date
 */
export const formatDate = (date, options = {}) => {
  const dateObj = date instanceof Date ? date : new Date(date);
  const lang = i18n.language || 'en';
  
  try {
    return new Intl.DateTimeFormat(lang, options).format(dateObj);
  } catch (error) {
    console.error('Error formatting date:', error);
    return dateObj.toLocaleDateString();
  }
};

/**
 * Format a date in short format (e.g., MM/DD/YYYY)
 * @param {Date|string|number} date - Date to format
 * @returns {string} Formatted date
 */
export const formatShortDate = (date) => {
  return formatDate(date, { 
    year: 'numeric', 
    month: 'numeric', 
    day: 'numeric' 
  });
};

/**
 * Format a date in long format (e.g., January 1, 2023)
 * @param {Date|string|number} date - Date to format
 * @returns {string} Formatted date
 */
export const formatLongDate = (date) => {
  return formatDate(date, { 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric' 
  });
};

/**
 * Format a time according to the current locale
 * @param {Date|string|number} date - Date to format
 * @param {Object} options - Intl.DateTimeFormat options
 * @returns {string} Formatted time
 */
export const formatTime = (date, options = {}) => {
  const dateObj = date instanceof Date ? date : new Date(date);
  const lang = i18n.language || 'en';
  
  const defaultOptions = { 
    hour: 'numeric', 
    minute: 'numeric',
    hour12: true
  };
  
  try {
    return new Intl.DateTimeFormat(lang, { ...defaultOptions, ...options }).format(dateObj);
  } catch (error) {
    console.error('Error formatting time:', error);
    return dateObj.toLocaleTimeString();
  }
};

/**
 * Format a number according to the current locale
 * @param {number} number - Number to format
 * @param {Object} options - Intl.NumberFormat options
 * @returns {string} Formatted number
 */
export const formatNumber = (number, options = {}) => {
  const lang = i18n.language || 'en';
  
  try {
    return new Intl.NumberFormat(lang, options).format(number);
  } catch (error) {
    console.error('Error formatting number:', error);
    return number.toString();
  }
};

/**
 * Format a currency amount according to the current locale
 * @param {number} amount - Amount to format
 * @param {string} currency - Currency code (default: MYR)
 * @returns {string} Formatted currency amount
 */
export const formatCurrency = (amount, currency = 'MYR') => {
  return formatNumber(amount, { 
    style: 'currency', 
    currency 
  });
};

/**
 * Format a percentage according to the current locale
 * @param {number} value - Value to format (0-100)
 * @returns {string} Formatted percentage
 */
export const formatPercent = (value) => {
  return formatNumber(value / 100, { 
    style: 'percent',
    minimumFractionDigits: 0,
    maximumFractionDigits: 2
  });
};

/**
 * Format a relative time (e.g., "2 hours ago")
 * @param {Date|string|number} date - Date to format
 * @returns {string} Formatted relative time
 */
export const formatRelativeTime = (date) => {
  const dateObj = date instanceof Date ? date : new Date(date);
  const now = new Date();
  const diffInSeconds = Math.floor((now - dateObj) / 1000);
  
  // Get language for formatting
  const lang = i18n.language || 'en';
  
  // Define time units and their values in seconds
  const units = [
    { name: 'year', seconds: 31536000 },
    { name: 'month', seconds: 2592000 },
    { name: 'week', seconds: 604800 },
    { name: 'day', seconds: 86400 },
    { name: 'hour', seconds: 3600 },
    { name: 'minute', seconds: 60 },
    { name: 'second', seconds: 1 }
  ];
  
  // Find the appropriate unit
  for (const unit of units) {
    const value = Math.floor(diffInSeconds / unit.seconds);
    
    if (value >= 1) {
      try {
        // Use RelativeTimeFormat if available
        if (Intl.RelativeTimeFormat) {
          const rtf = new Intl.RelativeTimeFormat(lang, { numeric: 'auto' });
          return rtf.format(-value, unit.name);
        }
        
        // Fallback for browsers without RelativeTimeFormat
        return `${value} ${unit.name}${value > 1 ? 's' : ''} ago`;
      } catch (error) {
        console.error('Error formatting relative time:', error);
        return `${value} ${unit.name}${value > 1 ? 's' : ''} ago`;
      }
    }
  }
  
  // If less than a second
  return 'just now';
};

/**
 * Get the text direction for a language
 * @param {string} langCode - Language code
 * @returns {string} 'rtl' or 'ltr'
 */
export const getTextDirection = (langCode) => {
  const language = getLanguageByCode(langCode);
  return language?.dir || 'ltr';
};

/**
 * Check if a language is RTL
 * @param {string} langCode - Language code
 * @returns {boolean} True if the language is RTL
 */
export const isRTL = (langCode) => {
  return getTextDirection(langCode) === 'rtl';
};

/**
 * Get the flag emoji for a language
 * @param {string} langCode - Language code
 * @returns {string} Flag emoji
 */
export const getLanguageFlag = (langCode) => {
  const language = getLanguageByCode(langCode);
  return language?.flag || '🌐';
};

export default {
  getLanguageByCode,
  getEnabledLanguages,
  formatDate,
  formatShortDate,
  formatLongDate,
  formatTime,
  formatNumber,
  formatCurrency,
  formatPercent,
  formatRelativeTime,
  getTextDirection,
  isRTL,
  getLanguageFlag
};
