import i18n from '../i18n';

// Format date for display
export const formatDate = (dateString, format = 'default') => {
  if (!dateString) return '';

  const date = new Date(dateString);
  const locale = i18n.language || 'en';

  // Map locale codes to Intl locale codes
  const localeMap = {
    'en': 'en-US',
    'ms': 'ms-MY',
    'cn': 'zh-CN'
  };

  const intlLocale = localeMap[locale] || 'en-US';

  switch (format) {
    case 'short':
      return date.toLocaleDateString(intlLocale, {
        month: 'short',
        day: 'numeric'
      });

    case 'medium':
      return date.toLocaleDateString(intlLocale, {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });

    case 'long':
      return date.toLocaleDateString(intlLocale, {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });

    case 'full':
      return date.toLocaleDateString(intlLocale, {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });

    case 'time':
      return date.toLocaleTimeString(intlLocale, {
        hour: '2-digit',
        minute: '2-digit'
      });

    case 'iso':
      return date.toISOString();

    case 'relative':
      const now = new Date();
      const diffTime = Math.abs(now - date);
      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
      const diffHours = Math.floor(diffTime / (1000 * 60 * 60));
      const diffMinutes = Math.floor(diffTime / (1000 * 60));

      // Use RelativeTimeFormat for relative times
      const rtf = new Intl.RelativeTimeFormat(intlLocale, { numeric: 'auto' });

      if (diffDays > 30) {
        return date.toLocaleDateString(intlLocale, {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        });
      } else if (diffDays > 0) {
        return rtf.format(-diffDays, 'day');
      } else if (diffHours > 0) {
        return rtf.format(-diffHours, 'hour');
      } else if (diffMinutes > 0) {
        return rtf.format(-diffMinutes, 'minute');
      } else {
        return i18n.t('common:time.justNow', 'Just now');
      }

    default:
      return date.toLocaleDateString(intlLocale, {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
  }
};

// Format currency for display
export const formatCurrency = (amount, currency = 'USD', options = {}) => {
  if (amount === null || amount === undefined) return '';

  const defaultOptions = {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  };

  const mergedOptions = { ...defaultOptions, ...options };
  const locale = i18n.language || 'en';

  // Map locale codes to Intl locale codes
  const localeMap = {
    'en': 'en-US',
    'ms': 'ms-MY',
    'cn': 'zh-CN'
  };

  const intlLocale = localeMap[locale] || 'en-US';

  // Define currency symbols for special cases
  const currencySymbols = {
    'USD': '$',
    'EUR': '€',
    'GBP': '£',
    'JPY': '¥',
    'MYR': 'RM',
    'SGD': 'S$',
    'AUD': 'A$',
    'CAD': 'C$',
    'CNY': '¥',
    'HKD': 'HK$',
    'IDR': 'Rp',
    'INR': '₹',
    'KRW': '₩',
    'NZD': 'NZ$',
    'PHP': '₱',
    'THB': '฿',
    'TWD': 'NT$',
    'VND': '₫'
  };

  try {
    // Use Intl.NumberFormat with the current locale
    const formattedNumber = new Intl.NumberFormat(intlLocale, {
      style: 'currency',
      currency,
      ...mergedOptions
    }).format(amount);

    // For some currencies and locales, override the formatted output to use a more common format
    if (options.forceSymbol && currencySymbols[currency]) {
      const numberOnly = new Intl.NumberFormat(intlLocale, {
        minimumFractionDigits: mergedOptions.minimumFractionDigits,
        maximumFractionDigits: mergedOptions.maximumFractionDigits
      }).format(amount);

      return `${currencySymbols[currency]}${numberOnly}`;
    }

    return formattedNumber;
  } catch (error) {
    console.error('Error formatting currency:', error);
    return `${amount} ${currency}`;
  }
};

// Format credit amount
export const formatCredits = (amount, options = {}) => {
  if (amount === null || amount === undefined) return '';

  const defaultOptions = {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  };

  const mergedOptions = { ...defaultOptions, ...options };
  const locale = i18n.language || 'en';

  // Map locale codes to Intl locale codes
  const localeMap = {
    'en': 'en-US',
    'ms': 'ms-MY',
    'cn': 'zh-CN'
  };

  const intlLocale = localeMap[locale] || 'en-US';

  try {
    const formattedNumber = new Intl.NumberFormat(intlLocale, mergedOptions).format(amount);

    // Get the translated word for "Credits" based on the current language
    const creditsLabel = i18n.t('common:currency.credits', 'Credits');

    return `${formattedNumber} ${creditsLabel}`;
  } catch (error) {
    console.error('Error formatting credits:', error);
    return `${amount} Credits`;
  }
};

// Format numbers with comma separators
export const formatNumber = (number, options = {}) => {
  if (number === null || number === undefined) return '';

  const defaultOptions = {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  };

  const mergedOptions = { ...defaultOptions, ...options };
  const locale = i18n.language || 'en';

  // Map locale codes to Intl locale codes
  const localeMap = {
    'en': 'en-US',
    'ms': 'ms-MY',
    'cn': 'zh-CN'
  };

  const intlLocale = localeMap[locale] || 'en-US';

  try {
    return new Intl.NumberFormat(intlLocale, mergedOptions).format(number);
  } catch (error) {
    console.error('Error formatting number:', error);
    return `${number}`;
  }
};

// Format file size
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// Format phone number
export const formatPhoneNumber = (phoneNumber) => {
  if (!phoneNumber) return '';

  // Remove all non-numeric characters
  const cleaned = ('' + phoneNumber).replace(/\D/g, '');

  // Format based on length
  if (cleaned.length < 4) {
    return cleaned;
  } else if (cleaned.length < 7) {
    return `${cleaned.slice(0, 3)}-${cleaned.slice(3)}`;
  } else if (cleaned.length < 11) {
    return `${cleaned.slice(0, 3)}-${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
  } else {
    return `+${cleaned.slice(0, 1)} ${cleaned.slice(1, 4)}-${cleaned.slice(4, 7)}-${cleaned.slice(7)}`;
  }
};

// Format transaction type
export const formatTransactionType = (type) => {
  if (!type) return '';

  // Use translations for transaction types
  const translationKey = `wallet:transactions.types.${type}`;
  const defaultValue = type.charAt(0).toUpperCase() + type.slice(1);

  return i18n.t(translationKey, defaultValue);
};

// Format transaction status
export const formatTransactionStatus = (status) => {
  if (!status) return '';

  // Use translations for transaction statuses
  const translationKey = `wallet:transactions.status.${status}`;
  const defaultValue = status.charAt(0).toUpperCase() + status.slice(1);

  return i18n.t(translationKey, defaultValue);
};