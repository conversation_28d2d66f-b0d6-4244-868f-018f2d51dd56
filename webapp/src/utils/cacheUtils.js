/**
 * Cache Utilities
 *
 * This file contains utilities for caching API responses.
 */

/**
 * Cache keys for different types of data
 */
export const CACHE_KEYS = {
  SERVICE_TYPES: 'SERVICE_TYPES',
  SERVICE_STYLES: 'SERVICE_STYLES',
  SERVICE_CATEGORIES: 'SERVICE_CATEGORIES',
  TALENT_FILTERS: 'TALENT_FILTERS',
  TALENT: 'TALENT',
  RACES: 'RACES',
  LANGUAGES: 'LANGUAGES'
};

/**
 * Default cache duration in milliseconds (5 minutes)
 */
export const DEFAULT_CACHE_DURATION = 5 * 60 * 1000;

/**
 * Cache Manager class for handling in-memory caching
 */
export class CacheManager {
  constructor() {
    this.cache = new Map();
  }

  /**
   * Set a value in the cache
   * @param {string} key - The cache key
   * @param {any} data - The data to cache
   * @param {number} duration - The cache duration in milliseconds
   */
  set(key, data, duration = DEFAULT_CACHE_DURATION) {
    const expiryTime = Date.now() + duration;
    this.cache.set(key, {
      data,
      expiryTime
    });

    // Log cache set for debugging
    console.debug(`Cache set: ${key}, expires in ${duration / 1000}s`);
  }

  /**
   * Get a value from the cache
   * @param {string} key - The cache key
   * @returns {any|null} The cached data or null if not found or expired
   */
  get(key) {
    if (!this.cache.has(key)) {
      console.debug(`Cache miss: ${key} (not in cache)`);
      return null;
    }

    const cachedItem = this.cache.get(key);

    // Check if the cached item has expired
    if (Date.now() > cachedItem.expiryTime) {
      console.debug(`Cache miss: ${key} (expired)`);
      this.cache.delete(key);
      return null;
    }

    console.debug(`Cache hit: ${key}`);
    return cachedItem.data;
  }

  /**
   * Check if a key exists in the cache and is not expired
   * @param {string} key - The cache key
   * @returns {boolean} True if the key exists and is not expired
   */
  has(key) {
    if (!this.cache.has(key)) {
      return false;
    }

    const cachedItem = this.cache.get(key);

    // Check if the cached item has expired
    if (Date.now() > cachedItem.expiryTime) {
      this.cache.delete(key);
      return false;
    }

    return true;
  }

  /**
   * Delete a key from the cache
   * @param {string} key - The cache key
   */
  delete(key) {
    this.cache.delete(key);
    console.debug(`Cache deleted: ${key}`);
  }

  /**
   * Clear all items from the cache
   */
  clear() {
    this.cache.clear();
    console.debug('Cache cleared');
  }

  /**
   * Generate a cache key for service types
   * @param {number|null} serviceCategoryId - The service category ID
   * @returns {string} The cache key
   */
  getServiceTypesKey(serviceCategoryId) {
    return `${CACHE_KEYS.SERVICE_TYPES}_${serviceCategoryId || 'all'}`;
  }

  /**
   * Generate a cache key for service styles
   * @param {number|null} serviceTypeId - The service type ID
   * @returns {string} The cache key
   */
  getServiceStylesKey(serviceTypeId) {
    return `${CACHE_KEYS.SERVICE_STYLES}_${serviceTypeId || 'all'}`;
  }

  /**
   * Generate a cache key for talent filters
   * @param {Object} filters - The filter criteria
   * @param {number} page - The page number
   * @param {number} perPage - The number of items per page
   * @returns {string} The cache key
   */
  getTalentFiltersKey(filters, page, perPage) {
    const filterString = JSON.stringify(filters);
    return `${CACHE_KEYS.TALENT_FILTERS}_${filterString}_${page}_${perPage}`;
  }

  /**
   * Generate a cache key for a single talent
   * @param {number} talentId - The talent ID
   * @returns {string} The cache key
   */
  getTalentKey(talentId) {
    return `${CACHE_KEYS.TALENT}_${talentId}`;
  }

  /**
   * Generate a generic cache key
   * @param {string} type - The type of data
   * @param {string|number|null} [id] - Optional identifier
   * @returns {string} The cache key
   */
  getKey(type, id = null) {
    if (id !== null) {
      return `${type}_${id}`;
    }
    return type;
  }
}

/**
 * Singleton instance of the CacheManager
 */
export const cacheManager = new CacheManager();

// Create a default export object
const cacheUtils = {
  CACHE_KEYS,
  DEFAULT_CACHE_DURATION,
  cacheManager
};

export default cacheUtils;
