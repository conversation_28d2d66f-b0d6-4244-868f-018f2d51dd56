/**
 * Mission Chat Utilities
 *
 * This file contains utility functions for managing mission-related chats.
 * It provides functions to find, create, and manage chats associated with missions.
 */

import { chatApi } from '../services/chatApi';

// Select the API implementation
const api = chatApi;

/**
 * Find or create a chat for a specific mission
 *
 * @param {string} missionId - The ID of the mission
 * @param {string} missionTitle - The title of the mission
 * @param {Array} participants - Array of participant user IDs
 * @returns {Promise<Object>} - The chat conversation object
 */
export const findOrCreateMissionChat = async (missionId, missionTitle, participants) => {
  try {
    // First, try to find an existing chat for this mission
    const existingChat = await findChatByMissionId(missionId);

    if (existingChat) {
      console.log(`Found existing chat for mission ${missionId}:`, existingChat.id);
      return existingChat;
    }

    // If no existing chat is found, create a new one
    console.log(`Creating new chat for mission ${missionId}`);
    return createMissionChat(missionId, missionTitle, participants);
  } catch (error) {
    console.error('Error in findOrCreateMissionChat:', error);
    throw new Error(`Failed to find or create mission chat: ${error.message}`);
  }
};

/**
 * Find a chat by mission ID
 *
 * @param {string} missionId - The ID of the mission
 * @returns {Promise<Object|null>} - The chat conversation object or null if not found
 */
export const findChatByMissionId = async (missionId) => {
  try {
    // Try to get conversation directly by metadata if the API supports it
    try {
      const conversation = await api.getConversationByMetadata({
        missionId,
        type: 'mission'
      });

      if (conversation) {
        return conversation;
      }
    } catch (error) {
      // If the direct method fails, fall back to filtering all conversations
      console.log('Direct metadata lookup not supported, falling back to filtering');
    }

    // Get all conversations and filter locally
    const conversations = await api.getConversations(1, 100);

    // Find the conversation with matching mission ID in metadata
    return (conversations.data || conversations).find(chat =>
      chat.metadata &&
      chat.metadata.missionId === missionId &&
      chat.metadata.type === 'mission'
    ) || null;
  } catch (error) {
    console.error('Error in findChatByMissionId:', error);
    throw new Error(`Failed to find chat by mission ID: ${error.message}`);
  }
};

/**
 * Create a new chat for a mission
 *
 * @param {string} missionId - The ID of the mission
 * @param {string} missionTitle - The title of the mission
 * @param {Array} participants - Array of participant user IDs
 * @returns {Promise<Object>} - The newly created chat conversation object
 */
export const createMissionChat = async (missionId, missionTitle, participants) => {
  try {
    // Generate a chat title based on the mission title
    const chatTitle = `Mission: ${missionTitle}`;

    // Create a new conversation with mission metadata
    const newChat = await api.createConversation({
      title: chatTitle,
      participants: participants,
      metadata: {
        missionId: missionId,
        type: 'mission'
      }
    });

    // Get the chat data from the response
    const chatData = newChat.data || newChat;

    // Send a welcome message to the chat
    await api.sendMessage({
      conversationId: chatData.id,
      text: `Welcome to the mission chat for "${missionTitle}"! Use this chat to communicate with all mission participants.`,
      type: 'system',
      metadata: {
        missionId: missionId,
        eventType: 'mission_chat_created'
      }
    });

    console.log(`Created new mission chat: ${chatData.id}`);
    return chatData;
  } catch (error) {
    console.error('Error in createMissionChat:', error);
    throw new Error(`Failed to create mission chat: ${error.message}`);
  }
};

/**
 * Send a system message to a mission chat
 *
 * @param {string} missionId - The ID of the mission
 * @param {string} messageText - The message text
 * @returns {Promise<Object>} - The sent message object
 */
export const sendMissionSystemMessage = async (missionId, messageText) => {
  try {
    // Find the chat for this mission
    const missionChat = await findChatByMissionId(missionId);

    if (!missionChat) {
      console.warn(`No chat found for mission ${missionId}`);
      return null;
    }

    // Send a system message
    return api.sendMessage({
      conversationId: missionChat.id,
      text: messageText,
      type: 'system',
      metadata: {
        missionId
      }
    });
  } catch (error) {
    console.error('Error in sendMissionSystemMessage:', error);
    throw new Error(`Failed to send mission system message: ${error.message}`);
  }
};

/**
 * Add a participant to a mission chat
 *
 * @param {string} missionId - The ID of the mission
 * @param {string} userId - The ID of the user to add
 * @param {string} userName - The name of the user to add
 * @returns {Promise<Object>} - The updated chat conversation object
 */
export const addParticipantToMissionChat = async (missionId, userId, userName) => {
  try {
    // Find the chat for this mission
    const missionChat = await findChatByMissionId(missionId);

    if (!missionChat) {
      console.warn(`No chat found for mission ${missionId}`);
      return null;
    }

    // Add the participant to the chat
    const updatedChat = await api.addParticipantToConversation(missionChat.id, userId);

    // Send a system message about the new participant
    await sendMissionSystemMessage(missionId, `${userName} has joined the mission chat.`);

    return updatedChat.data || updatedChat;
  } catch (error) {
    console.error('Error in addParticipantToMissionChat:', error);
    throw new Error(`Failed to add participant to mission chat: ${error.message}`);
  }
};

/**
 * Remove a participant from a mission chat
 *
 * @param {string} missionId - The ID of the mission
 * @param {string} userId - The ID of the user to remove
 * @param {string} userName - The name of the user to remove
 * @returns {Promise<Object>} - The updated chat conversation object
 */
export const removeParticipantFromMissionChat = async (missionId, userId, userName) => {
  try {
    // Find the chat for this mission
    const missionChat = await findChatByMissionId(missionId);

    if (!missionChat) {
      console.warn(`No chat found for mission ${missionId}`);
      return null;
    }

    // Remove the participant from the chat
    const updatedChat = await api.removeParticipantFromConversation(missionChat.id, userId);

    // Send a system message about the removed participant
    await sendMissionSystemMessage(missionId, `${userName} has left the mission chat.`);

    return updatedChat.data || updatedChat;
  } catch (error) {
    console.error('Error in removeParticipantFromMissionChat:', error);
    throw new Error(`Failed to remove participant from mission chat: ${error.message}`);
  }
};

/**
 * Send a mission event message to the mission chat
 *
 * @param {string} missionId - The ID of the mission
 * @param {string} eventType - The type of event
 * @param {Object} eventData - Additional data for the event
 * @returns {Promise<Object>} - The sent message object
 */
export const sendMissionEventMessage = async (missionId, eventType, eventData = {}) => {
  try {
    // Find the chat for this mission
    const missionChat = await findChatByMissionId(missionId);

    if (!missionChat) {
      console.warn(`No chat found for mission ${missionId}`);
      return null;
    }

    // Create message text based on event type
    let messageText = '';
    switch (eventType) {
      case 'mission_started':
        messageText = `🚀 Mission has started! Good luck everyone.${eventData.startedByName ? ` (Started by ${eventData.startedByName})` : ''}`;
        break;
      case 'mission_completed':
        messageText = `🎉 Mission completed successfully!${eventData.completedByName ? ` (Completed by ${eventData.completedByName})` : ''}`;
        break;
      case 'mission_cancelled':
        messageText = `❌ Mission has been cancelled.${eventData.reason ? ` Reason: ${eventData.reason}` : ''}`;
        break;
      case 'participant_joined':
        messageText = `👋 ${eventData.participantName} has joined the mission.`;
        break;
      case 'participant_left':
        messageText = `👋 ${eventData.participantName} has left the mission.`;
        break;
      case 'task_completed':
        messageText = `✅ Task "${eventData.taskName}" has been completed by ${eventData.completedByName || 'a participant'}.`;
        break;
      case 'task_uncompleted':
        messageText = `⚠️ Task "${eventData.taskName}" has been marked as incomplete by ${eventData.uncompletedByName || 'a participant'}.`;
        break;
      case 'rewards_distributed':
        messageText = `💰 Mission rewards (${eventData.totalRewards || 'XP'}) have been distributed to participants.`;
        break;
      case 'applicant_approved':
        messageText = `✅ ${eventData.applicantName} has been approved to join the mission.`;
        break;
      case 'applicant_rejected':
        messageText = `❌ ${eventData.applicantName}'s application to join the mission has been rejected.`;
        break;
      case 'mission_updated':
        messageText = `📝 Mission details have been updated.${eventData.updatedFields ? ` Updated fields: ${eventData.updatedFields.join(', ')}` : ''}`;
        break;
      case 'payment_processed':
        messageText = `💸 Payment of ${eventData.amount || 'XP'} has been processed for this mission.`;
        break;
      default:
        messageText = `Mission update: ${eventType}`;
    }

    // Send the message
    return api.sendMessage({
      conversationId: missionChat.id,
      text: messageText,
      type: 'system',
      metadata: {
        missionId,
        eventType,
        ...eventData
      }
    });
  } catch (error) {
    console.error('Error in sendMissionEventMessage:', error);
    throw new Error(`Failed to send mission event message: ${error.message}`);
  }
};
