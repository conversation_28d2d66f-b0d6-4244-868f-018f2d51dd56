import React, { Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useLocation, useNavigate } from 'react-router-dom';
import { useEffect, useState } from 'react';
import { LanguageProvider } from './contexts/LanguageContext';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { ProfileProvider } from './contexts/ProfileContext';
import RTLProvider from './components/common/RTLProvider';
import EmailVerification from './components/auth/EmailVerification';
import OAuthCallback from './components/auth/OAuthCallback';
import DeviceRegistration from './components/auth/DeviceRegistration';
import ProfilePage from './components/profile/ProfilePage';
import ProfileSettingsPage from './components/profile/ProfileSettingsPage';
import AvailabilityPage from './components/profile/AvailabilityPage';
import AuthenticationHub from './components/auth/AuthenticationHub';
import './App.css';
import './styles/animations.css';
import { NotificationProvider } from './contexts/NotificationContext';
import ToastProvider from './components/common/ToastProvider';
import { PaymentProvider } from './contexts/PaymentContext';
import { OrderPaymentProvider } from './contexts/OrderPaymentContext';
import { HomepageProvider } from './contexts/HomepageContext';
import { FinancialProviders } from './features/FinancialProviders';
import Profile from './components/Profile';
import Chat from './pages/Chat';
import EditProfile from './components/EditProfile';
import ProfileSetup from './components/ProfileSetup';
import Talent from './pages/TalentPage';
import Explore from './pages/Explore';
import Wallet from './pages/Wallet';
import PaymentReturn from './pages/PaymentReturn';
import PaymentReturnPage from './pages/PaymentReturnPage';
import BankAccountsPage from './pages/BankAccountsPage';
import MissionPage from './pages/MissionPage';
import MissionDetailPage from './pages/MissionDetailPage';
import MyMissionsPage from './pages/MyMissionsPage';
import MissionApplicantsPage from './pages/MissionApplicantsPage';
import MissionCreatePage from './pages/MissionCreatePage';
import MissionEditPage from './pages/MissionEditPage';
import MissionExecutionPage from './pages/MissionExecutionPage';
import LazyWelcomePage from './components/LazyWelcomePage';
import { firebaseMessaging } from './services/firebaseMessaging';
import { initializeNotificationHandlers } from './services/notificationHandlers';
import { LoadingProvider } from './contexts/LoadingContext';
import { PageLoader } from './components/ui/LoadingIndicator';
import * as LazyComponents from './lazyComponents';

// Import test utility for development (will be removed in production)
if (process.env.NODE_ENV === 'development') {
  import('./utils/testBankAPI');
}

// Create a protected route component
const ProtectedRoute = ({ children }) => {
  const { isAuthenticated, initialCheckDone, checkAuth } = useAuth();
  const [isChecking, setIsChecking] = useState(false);
  const navigate = useNavigate();

  // Check for token directly in localStorage as a fallback
  useEffect(() => {
    // If AuthContext says we're not authenticated, double-check localStorage
    if (!isAuthenticated && !isChecking) {
      const token = localStorage.getItem('token');

      // If token exists in localStorage but AuthContext says not authenticated,
      // this could be a sync issue - force a re-check
      if (token) {
        console.log('Token found in localStorage but not authenticated in context. Re-checking...');
        setIsChecking(true);
        checkAuth().finally(() => {
          setIsChecking(false);
        });
      } else {
        // No token in localStorage, redirect to login
        console.error('No authentication token found. Redirecting to login page.');
        navigate('/', { replace: true });
      }
    }
  }, [isAuthenticated, checkAuth, isChecking, navigate]);

  // Re-check authentication when component mounts
  useEffect(() => {
    const verifyToken = async () => {
      if (!isAuthenticated && !isChecking) {
        setIsChecking(true);
        await checkAuth();
        setIsChecking(false);
      }
    };

    verifyToken();
  }, [isAuthenticated, checkAuth, isChecking]);

  // Show loading while checking authentication status
  if (!initialCheckDone || isChecking) {
    return <PageLoader message="Authenticating..." color="indigo" />;
  }

  if (!isAuthenticated) {
    return <Navigate to="/" replace />;
  }

  // Include DeviceRegistration component for push notification registration
  return (
    <>
      <DeviceRegistration />
      {children}
    </>
  );
};

// Component to conditionally apply padding class
const AppLayout = ({ children }) => {
  const location = useLocation();
  const authenticatedPaths = ['/home', '/orders', '/talent', '/explore', '/chat', '/profile', '/wallet', '/missions', '/coaches', '/bank-accounts', '/tdash'];
  const isAuthenticatedRoute = authenticatedPaths.some(path => location.pathname.startsWith(path));

  // Initialize Firebase messaging when in an authenticated route
  useEffect(() => {
    const initializeNotifications = async () => {
      if (isAuthenticatedRoute) {
        try {
          // Register notification handlers for different notification types
          initializeNotificationHandlers();

          // Check for authentication token first
          const authToken = localStorage.getItem('token');
          if (!authToken) {
            console.log('Authentication token not found. Skipping Firebase messaging initialization.');
            return;
          }

          // If supported, initialize Firebase messaging
          if (firebaseMessaging.isSupported()) {
            try {
              const result = await firebaseMessaging.initialize();

              if (result.success) {
                console.log('Firebase messaging initialized successfully');

                // If there's a warning but initialization was still successful
                if (result.warning) {
                  console.warn('Firebase messaging warning:', result.warning);
                  console.warn('Backend error details:', result.backendError);
                }
              } else {
                console.warn('Firebase messaging initialization failed:', result.error || result.message);
                // Non-critical error, app can continue without notifications
              }
            } catch (error) {
              // Catch any unexpected errors to prevent app crash
              console.error('Unexpected error in Firebase messaging initialization:', error);
              // Continue app execution - notifications are not critical
            }
          } else {
            console.log('Firebase messaging not supported in this environment');
          }
        } catch (error) {
          console.error('Error initializing notifications:', error);
        }
      }
    };

    // Add a small delay to ensure authentication is checked first
    const timeoutId = setTimeout(() => {
      initializeNotifications();
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [isAuthenticatedRoute]);

  return (
    <div className={`App ${!isAuthenticatedRoute ? 'with-padding' : ''}`}>
      {children}
    </div>
  );
};

const App = () => {
  return (
    <NotificationProvider>
      <ToastProvider>
        <AuthProvider>
          <ProfileProvider>
            <LanguageProvider>
              <RTLProvider>
                <Router>
                  <LoadingProvider>
                    <FinancialProviders>
                      <PaymentProvider>
                        <OrderPaymentProvider>
                          <HomepageProvider>
                            <AppLayout>
                              <div className="App">
                                <Routes>
                                  {/* Public routes */}
                                  <Route path="/" element={<AuthenticationHub />} />
                                  <Route path="/auth" element={<AuthenticationHub />} />
                                  <Route path="/welcome" element={<LazyWelcomePage />} />

                                  {/* Protected routes */}
                                  <Route
                                    path="/home"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyHome />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/games"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyAllGames />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/game/:gameId/:gameName"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyGameTalents />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/talents/:talentId"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyTalentProfile />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/orders"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyOrderManagement />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/orders/:orderId"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyOrderDetails />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/talent"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyTalent />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/profile"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyProfile />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/profile/:userId"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyProfile />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/profile-v2"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyProfilePage />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/profile-v2/:userId"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyProfilePage />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/edit-profile"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyEditProfile />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/profile/setup"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyProfileSetup />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/profile/settings"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyProfileSettingsPage />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/chat"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyChat />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/explore"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyExplore />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/wallet"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyWallet />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/wallet/payment-return"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyPaymentReturn />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/payment-return"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyPaymentReturnPage />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/bank-accounts"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyBankAccountsPage />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/availability"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyAvailabilityPage />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />

                                  {/* Mission routes */}
                                  <Route
                                    path="/missions"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyMissionPage />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/missions/my-missions"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyMyMissionsPage />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/missions/create"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyMissionCreatePage />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/missions/:missionId/edit"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyMissionEditPage />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/missions/:missionId"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyMissionDetailPage />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />
                                  <Route
                                    path="/missions/:missionId/applicants"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyMissionApplicantsPage />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />

                                  <Route
                                    path="/missions/:missionId/execute"
                                    element={
                                      <ProtectedRoute>
                                        <Suspense fallback={<PageLoader message="Loading..." color="indigo" />}>
                                          <LazyComponents.LazyMissionExecutionPage />
                                        </Suspense>
                                      </ProtectedRoute>
                                    }
                                  />

                                  {/* Authentication Routes */}
                                  <Route path="/email/verify" element={<EmailVerification />} />
                                  <Route path="/auth/oauth/:provider/callback" element={<OAuthCallback />} />

                                  {/* Catch all route - redirect to login */}
                                  <Route path="*" element={<Navigate to="/" replace />} />
                                </Routes>
                              </div>
                            </AppLayout>
                          </HomepageProvider>
                        </OrderPaymentProvider>
                      </PaymentProvider>
                    </FinancialProviders>
                  </LoadingProvider>
                </Router>
              </RTLProvider>
            </LanguageProvider>
          </ProfileProvider>
        </AuthProvider>
      </ToastProvider>
    </NotificationProvider>
  );
};

export default App;
