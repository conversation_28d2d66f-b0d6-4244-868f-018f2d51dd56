/**
 * Authentication Context
 *
 * This context provides authentication state and functions across the application.
 * It handles user authentication, loading states, and error handling.
 */

import React, { createContext, useContext, useState, useEffect, useCallback, useMemo } from 'react';
import authService from '../services/authService';
import otpService from '../services/otpService';
import { deviceTokenService } from '../services/deviceTokenService';

// Create the context
const AuthContext = createContext();

/**
 * AuthProvider component
 *
 * This provider manages authentication state across the application.
 * It handles user authentication, loading states, and error handling.
 *
 * @param {Object} props
 * @param {React.ReactNode} props.children - Child components
 */
export const AuthProvider = ({ children }) => {
  // Authentication state
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [initialCheckDone, setInitialCheckDone] = useState(false);

  /**
   * Check if the user is authenticated
   */
  const checkAuth = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      // Check if token exists in localStorage
      const token = localStorage.getItem('token');

      if (!token) {
        console.log('[AUTH] No token found in localStorage');
        setIsAuthenticated(false);
        setUser(null);
        setLoading(false);
        setInitialCheckDone(true);
        return;
      }

      console.log('[AUTH] Token found, checking authentication status');

      // Get current user data
      const response = await authService.getCurrentUser();

      if (response.success) {
        console.log('[AUTH] Authentication successful');
        setUser(response.data);
        setIsAuthenticated(true);
      } else {
        // If API call was successful but user is not authenticated
        console.warn('[AUTH] API call successful but authentication failed:', response.error);
        setUser(null);
        setIsAuthenticated(false);
        localStorage.removeItem('token');
      }
    } catch (err) {
      console.error('[AUTH] Authentication check failed:', err);

      // Provide more detailed error information
      const errorMessage = err.response?.data?.message || err.message || 'Authentication check failed';
      const statusCode = err.response?.status;

      if (statusCode) {
        console.error(`[AUTH] HTTP Status: ${statusCode}`);
      }

      setError(errorMessage);
      setUser(null);
      setIsAuthenticated(false);
      localStorage.removeItem('token');
    } finally {
      setLoading(false);
      setInitialCheckDone(true);
    }
  }, []);

  /**
   * Login user
   * Backend endpoint: POST /api/auth/login
   * @param {string} mobileNumber - Malaysian mobile number
   * @param {string} password - User's password
   * @param {boolean} [remember=false] - Whether to remember the user
   * @param {Object} [deviceInfo=null] - Optional device information for push notifications
   * @returns {Promise<Object>} Login result
   */
  const login = async (mobileNumber, password, remember = false, deviceInfo = null) => {
    setLoading(true);
    setError(null);

    try {
      const response = await authService.login(mobileNumber, password, deviceInfo);

      if (response.success) {
        setUser(response.user);
        setIsAuthenticated(true);

        // Store mobile number for future logins if remember is true
        if (remember) {
          localStorage.setItem('lastMobileNumber', mobileNumber);
        } else {
          localStorage.removeItem('lastMobileNumber');
        }

        // Initialize device token for push notifications
        try {
          await deviceTokenService.initializeDeviceToken();
        } catch (deviceError) {
          console.warn('[AUTH] Device token initialization failed:', deviceError);
          // Don't fail login if device token fails
        }

        return { success: true, data: response.data, user: response.user, token: response.token };
      } else {
        setError(response.error || 'Login failed');
        return { success: false, error: response.error, validationErrors: response.validationErrors };
      }
    } catch (err) {
      const errorMessage = err.message || 'Login failed';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  /**
   * Logout user
   *
   * @returns {Promise<Object>} Logout result
   */
  const logout = async () => {
    setLoading(true);
    setError(null);

    try {
      // Cleanup device token before logout
      try {
        await deviceTokenService.cleanupDeviceToken();
      } catch (deviceError) {
        console.warn('[AUTH] Device token cleanup failed:', deviceError);
        // Don't fail logout if device token cleanup fails
      }

      const response = await authService.logout();

      // Clear user data regardless of API response
      setUser(null);
      setIsAuthenticated(false);

      return { success: true, data: response.data };
    } catch (err) {
      const errorMessage = err.message || 'Logout failed';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  /**
   * Update user data
   *
   * @param {Object} userData - Updated user data
   */
  const updateUser = useCallback((userData) => {
    setUser(prevUser => ({
      ...prevUser,
      ...userData
    }));
  }, []);

  // Check authentication status on mount
  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  /**
   * Register a new user
   *
   * @param {Object} userData - User registration data
   * @returns {Promise<Object>} Registration result
   */
  const register = async (userData) => {
    setLoading(true);
    setError(null);

    try {
      const response = await authService.register(userData);

      if (response.success) {
        // If registration returns a token and user data, set them
        if (response.data.token && response.data.user) {
          setUser(response.data.user);
          setIsAuthenticated(true);

          // Set first login flag for new registrations
          localStorage.setItem('isFirstLogin', 'true');
          localStorage.setItem('user', JSON.stringify(response.data.user));

          // Initialize device token for push notifications
          try {
            await deviceTokenService.initializeDeviceToken();
          } catch (deviceError) {
            console.warn('[AUTH] Device token initialization failed:', deviceError);
            // Don't fail registration if device token fails
          }
        }

        return { success: true, data: response.data };
      } else {
        setError(response.error || 'Registration failed');
        return { success: false, error: response.error };
      }
    } catch (err) {
      console.error('🔍 Registration error details:', err);

      let errorMessage = 'Registration failed';
      let validationErrors = null;

      if (err.response?.data) {
        console.error('🔍 Server error response:', err.response.data);
        errorMessage = err.response.data.message || err.response.data.error || errorMessage;
        validationErrors = err.response.data.errors || err.response.data.validationErrors;

        if (validationErrors) {
          console.error('📋 Validation errors:', validationErrors);
        }
      } else {
        errorMessage = err.message || errorMessage;
      }

      setError(errorMessage);
      return {
        success: false,
        error: errorMessage,
        validationErrors: validationErrors
      };
    } finally {
      setLoading(false);
    }
  };

  /**
   * Request OTP for registration
   * Backend endpoint: POST /api/auth/request-otp
   * Rate limited: 1 request per 60 seconds
   * @param {string} mobileNumber - Malaysian mobile number
   * @returns {Promise<Object>} OTP request result
   */
  const requestOtp = async (mobileNumber) => {
    setLoading(true);
    setError(null);

    try {
      const response = await authService.requestOtp(mobileNumber);

      if (response.success) {
        return { success: true, data: response.data, message: response.message };
      } else {
        setError(response.error || 'Failed to send OTP');
        return {
          success: false,
          error: response.error,
          validationErrors: response.validationErrors,
          details: response.details
        };
      }
    } catch (err) {
      const errorMessage = err.message || 'Failed to send OTP';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  /**
   * Verify OTP for various purposes
   *
   * @param {string} mobileNumber - User's mobile number
   * @param {string} countryCode - Country code (e.g., +60)
   * @param {string} otp - OTP code
   * @param {string} purpose - Purpose of OTP ('registration', 'login', 'reset')
   * @returns {Promise<Object>} OTP verification result
   */
  const verifyOtp = async (mobileNumber, countryCode, otp, purpose = 'registration') => {
    setLoading(true);
    setError(null);

    try {
      let response;

      // Call the appropriate verification method based on purpose
      switch (purpose) {
        case 'registration':
          response = await otpService.verifyRegistrationOtp(mobileNumber, countryCode, otp);
          break;
        case 'login':
          response = await otpService.verifyLoginOtp(mobileNumber, countryCode, otp);
          // If login OTP verification is successful, set user data
          if (response.success && response.data.token && response.data.user) {
            setUser(response.data.user);
            setIsAuthenticated(true);
          }
          break;
        case 'reset':
          response = await otpService.verifyPasswordResetOtp(mobileNumber, countryCode, otp);
          break;
        default:
          response = await otpService.verifyRegistrationOtp(mobileNumber, countryCode, otp);
      }

      if (response.success) {
        return { success: true, data: response.data };
      } else {
        setError(response.error || `Failed to verify OTP for ${purpose}`);
        return { success: false, error: response.error };
      }
    } catch (err) {
      const errorMessage = err.message || `Failed to verify OTP for ${purpose}`;
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  /**
   * Verify OTP for password reset
   * Backend endpoint: POST /api/auth/verify-otp-for-reset
   * @param {string} mobileNumber - Malaysian mobile number
   * @param {string} otp - 6-digit OTP code
   * @returns {Promise<Object>} OTP verification result
   */
  const verifyOtpForReset = async (mobileNumber, otp) => {
    setLoading(true);
    setError(null);

    try {
      const response = await authService.verifyOtpForReset(mobileNumber, otp);

      if (response.success) {
        return { success: true, data: response.data, message: response.message };
      } else {
        setError(response.error || 'Failed to verify OTP');
        return {
          success: false,
          error: response.error,
          validationErrors: response.validationErrors
        };
      }
    } catch (err) {
      const errorMessage = err.message || 'Failed to verify OTP';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  /**
   * Reset password
   * Backend endpoint: POST /api/auth/reset-password
   * @param {string} mobileNumber - Malaysian mobile number
   * @param {string} password - New password
   * @returns {Promise<Object>} Password reset result
   */
  const resetPassword = async (mobileNumber, password) => {
    setLoading(true);
    setError(null);

    try {
      const response = await authService.resetPassword(mobileNumber, password);

      if (response.success) {
        // Password reset logs out user from all devices
        setUser(null);
        setIsAuthenticated(false);

        return { success: true, data: response.data, message: response.message };
      } else {
        setError(response.error || 'Failed to reset password');
        return {
          success: false,
          error: response.error,
          validationErrors: response.validationErrors
        };
      }
    } catch (err) {
      const errorMessage = err.message || 'Failed to reset password';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  /**
   * Register device token for push notifications
   *
   * @param {string} deviceToken - Firebase device token
   * @param {string} deviceType - Device type (web, android, ios)
   * @param {string} deviceName - Device name/user agent
   * @returns {Promise<Object>} Registration result
   */
  const registerDeviceToken = async (deviceToken, deviceType, deviceName) => {
    if (!isAuthenticated) {
      return { success: false, error: 'User not authenticated' };
    }

    try {
      const response = await authService.registerDeviceToken(deviceToken, deviceType, deviceName);
      return response;
    } catch (err) {
      const errorMessage = err.message || 'Failed to register device token';
      console.error('[AUTH] Device token registration failed:', errorMessage);
      return { success: false, error: errorMessage };
    }
  };

  // Context value - memoized to prevent unnecessary re-renders
  const value = useMemo(() => ({
    user,
    loading,
    error,
    isAuthenticated,
    initialCheckDone,
    login,
    logout,
    register,
    requestOtp,
    verifyOtp,
    verifyOtpForReset,
    resetPassword,
    checkAuth,
    updateUser,
    registerDeviceToken
  }), [
    user,
    loading,
    error,
    isAuthenticated,
    initialCheckDone,
    login,
    logout,
    register,
    requestOtp,
    verifyOtp,
    verifyOtpForReset,
    resetPassword,
    checkAuth,
    updateUser,
    registerDeviceToken
  ]);

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

/**
 * Custom hook to use the auth context
 *
 * @returns {Object} Auth context
 */
export const useAuth = () => {
  const context = useContext(AuthContext);

  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }

  return context;
};

export default AuthContext;
