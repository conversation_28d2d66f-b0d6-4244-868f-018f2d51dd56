import React, { createContext, useContext, useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { AnimatePresence } from 'framer-motion';
import { NavigationLoader } from '../components/ui/LoadingIndicator';

const LoadingContext = createContext();

export const useLoading = () => {
  const context = useContext(LoadingContext);
  if (!context) {
    throw new Error('useLoading must be used within a LoadingProvider');
  }
  return context;
};

export const LoadingProvider = ({ children }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [loadingMessage, setLoadingMessage] = useState('Loading...');
  const [isNavigating, setIsNavigating] = useState(false);
  const location = useLocation();

  // Handle navigation loading
  useEffect(() => {
    setIsNavigating(true);
    
    // Simulate minimum loading time for smooth UX
    const timer = setTimeout(() => {
      setIsNavigating(false);
    }, 300);

    return () => clearTimeout(timer);
  }, [location.pathname]);

  const showLoading = (message = 'Loading...') => {
    setLoadingMessage(message);
    setIsLoading(true);
  };

  const hideLoading = () => {
    setIsLoading(false);
  };

  const value = {
    isLoading,
    loadingMessage,
    isNavigating,
    showLoading,
    hideLoading
  };

  return (
    <LoadingContext.Provider value={value}>
      {children}
      
      {/* Navigation Loading Indicator */}
      <AnimatePresence>
        {isNavigating && <NavigationLoader />}
      </AnimatePresence>
    </LoadingContext.Provider>
  );
};

export default LoadingContext;
