import { getToken, onMessage } from 'firebase/messaging';
import { messaging } from './config';
import axios from 'axios';

// Store notification handlers
let notificationHandlers = [];

// Register notification handler
export const addNotificationHandler = (handler) => {
  notificationHandlers.push(handler);
};

// Remove notification handler
export const removeNotificationHandler = (handler) => {
  notificationHandlers = notificationHandlers.filter(h => h !== handler);
};

// Get Firebase Cloud Messaging token and register it with backend
export const registerDeviceToken = async () => {
  try {
    // Request permission for notifications
    if (Notification.permission !== 'granted') {
      await Notification.requestPermission();
    }
    
    // Get FCM token
    const token = await getToken(messaging);

    // Register the token with the backend
    if (token) {
      await saveTokenToBackend(token);
      return token;
    } else {
      console.log('No registration token available. Request permission to generate one.');
      return null;
    }
  } catch (error) {
    console.error('Error getting or registering FCM token:', error);
    return null;
  }
};

// Save token to backend
const saveTokenToBackend = async (token) => {
  try {
    const userToken = localStorage.getItem('token');
    if (!userToken) return;

    const deviceInfo = {
      device_token: token,
      device_type: 'web',
      device_name: navigator.userAgent
    };

    await axios.post(
      `${process.env.REACT_APP_API_URL}/auth/user/device-token`,
      deviceInfo,
      {
        headers: {
          'Authorization': `Bearer ${userToken}`,
          'Content-Type': 'application/json',
        },
      }
    );
    console.log('FCM Token registered with backend');
  } catch (error) {
    console.error('Error saving token to backend:', error);
  }
};

// Setup FCM message listener
export const setupMessageListener = () => {
  onMessage(messaging, (payload) => {
    console.log('Message received:', payload);
    
    // Create notification data
    const notificationData = {
      id: Date.now(),
      title: payload.notification?.title || 'New Notification',
      body: payload.notification?.body || '',
      data: payload.data || {},
      read: false,
      timestamp: new Date().toISOString(),
    };
    
    // Notify all registered handlers
    notificationHandlers.forEach(handler => handler(notificationData));
    
    // Show browser notification if app is in background
    if (document.visibilityState === 'hidden') {
      // Create notification options
      const notificationOptions = {
        body: notificationData.body,
        icon: '', ///logo192.png
      };
      
      // Show notification
      new Notification(notificationData.title, notificationOptions);
    }
  });
};
