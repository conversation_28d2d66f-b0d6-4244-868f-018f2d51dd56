import walletAPI from './walletService';
import errorHandlingService from './errorHandlingService';
import { WALLET_ERROR_TYPES, API_ERROR_TYPES } from '../utils/errorTypes';

/**
 * Global Balance Service
 *
 * This service provides a centralized balance management system that ensures
 * balance consistency across all components (navigation, wallet, etc.).
 * It implements caching, event-driven updates, and synchronization.
 */
class GlobalBalanceService {
  constructor() {
    this.cache = {
      balance: null,
      lastUpdate: null
    };

    // Cache duration: 30 seconds
    this.CACHE_DURATION = 30 * 1000;

    // Event listeners for balance updates
    this.listeners = new Set();

    // Bind methods to preserve context
    this.getBalance = this.getBalance.bind(this);
    this.updateBalance = this.updateBalance.bind(this);
    this.subscribe = this.subscribe.bind(this);
    this.unsubscribe = this.unsubscribe.bind(this);
    this.notifyListeners = this.notifyListeners.bind(this);
  }

  /**
   * Check if cached balance is still valid
   */
  isCacheValid() {
    if (!this.cache.lastUpdate) return false;
    return Date.now() - this.cache.lastUpdate < this.CACHE_DURATION;
  }

  /**
   * Get balance with caching and event notification
   */
  async getBalance(forceRefresh = false) {
    // Return cached balance if valid and not forcing refresh
    if (!forceRefresh && this.isCacheValid() && this.cache.balance !== null) {
      return this.cache.balance;
    }

    try {
      const response = await walletAPI.getBalance();
      
      // Support both legacy and new API response formats
      const balanceRaw =
        response.data?.balance !== undefined
          ? response.data.balance
          : response.data?.credits_balance;

      const balance = Number(balanceRaw ?? 0);

      // Validate balance value
      if (!Number.isFinite(balance) || balance < 0) {
        throw new Error(`Invalid balance value received: ${balance}`);
      }

      // Update cache
      this.cache.balance = balance;
      this.cache.lastUpdate = Date.now();

      // Notify all listeners of balance update
      this.notifyListeners(balance);

      return balance;
    } catch (error) {
      console.error('Error fetching balance:', error);

      // Process error through error handling service
      const processedError = errorHandlingService.processError(error, 'global_balance', {
        operation: 'getBalance',
        forceRefresh,
        cacheValid: this.isCacheValid(),
        cachedBalance: this.cache.balance
      });

      // For authentication errors, clear cache and throw
      if (processedError.type === API_ERROR_TYPES.AUTHENTICATION_ERROR) {
        this.invalidateCache();
        throw processedError;
      }

      // For other errors, use fallback strategy
      const fallbackBalance = this.determineFallbackBalance(processedError);

      if (fallbackBalance !== null) {
        console.warn(`Using fallback balance (${fallbackBalance}) due to error:`, processedError.message);
        this.notifyListeners(fallbackBalance);
        return fallbackBalance;
      }

      throw processedError;
    }
  }

  /**
   * Update balance manually (for after transactions)
   */
  updateBalance(newBalance) {
    this.cache.balance = newBalance;
    this.cache.lastUpdate = Date.now();
    this.notifyListeners(newBalance);
  }

  /**
   * Invalidate cache to force refresh on next call
   */
  invalidateCache() {
    this.cache.balance = null;
    this.cache.lastUpdate = null;
  }

  /**
   * Subscribe to balance updates
   */
  subscribe(callback) {
    this.listeners.add(callback);

    // Return unsubscribe function
    return () => {
      this.listeners.delete(callback);
    };
  }

  /**
   * Unsubscribe from balance updates
   */
  unsubscribe(callback) {
    this.listeners.delete(callback);
  }

  /**
   * Notify all listeners of balance changes
   */
  notifyListeners(balance) {
    this.listeners.forEach(callback => {
      try {
        callback(balance);
      } catch (error) {
        console.error('Error in balance listener:', error);
      }
    });
  }

  /**
   * Get cached balance without API call
   */
  getCachedBalance() {
    return this.cache.balance;
  }

  /**
   * Check if balance is cached
   */
  hasCache() {
    return this.cache.balance !== null && this.isCacheValid();
  }

  /**
   * Clear all listeners (for cleanup)
   */
  clearListeners() {
    this.listeners.clear();
  }

  /**
   * Refresh balance and notify listeners
   */
  async refreshBalance() {
    try {
      const balance = await this.getBalance(true);
      return balance;
    } catch (error) {
      console.error('Error refreshing balance:', error);
      throw error;
    }
  }

  /**
   * Format balance for display
   */
  formatBalance(balance) {
    if (balance === null || balance === undefined) return '0';
    return balance.toLocaleString();
  }

  /**
   * Determine fallback balance based on error type and available data
   */
  determineFallbackBalance(processedError) {
    // For network errors, use cached balance if available
    if (processedError.type === API_ERROR_TYPES.NETWORK_ERROR) {
      return this.cache.balance !== null ? this.cache.balance : 0;
    }

    // For server errors, use cached balance if recent
    if (processedError.type === API_ERROR_TYPES.SERVER_ERROR) {
      const cacheAge = Date.now() - (this.cache.lastUpdate || 0);
      const maxStaleAge = 5 * 60 * 1000; // 5 minutes

      if (this.cache.balance !== null && cacheAge < maxStaleAge) {
        return this.cache.balance;
      }
      return 0; // Default to 0 for old cache
    }

    // For rate limiting, use cached balance
    if (processedError.type === API_ERROR_TYPES.RATE_LIMIT_ERROR) {
      return this.cache.balance !== null ? this.cache.balance : 0;
    }

    // For verification errors, use cached balance but don't allow transactions
    if ([WALLET_ERROR_TYPES.EMAIL_VERIFICATION_REQUIRED, WALLET_ERROR_TYPES.EKYC_VERIFICATION_REQUIRED].includes(processedError.type)) {
      return this.cache.balance !== null ? this.cache.balance : 0;
    }

    // For other errors, use cached balance if available, otherwise 0
    return this.cache.balance !== null ? this.cache.balance : 0;
  }

  /**
   * Get balance status information
   */
  getBalanceStatus() {
    return {
      balance: this.cache.balance,
      lastUpdate: this.cache.lastUpdate,
      isCached: this.hasCache(),
      isStale: !this.isCacheValid()
    };
  }
}

// Create and export singleton instance
const globalBalanceService = new GlobalBalanceService();

export default globalBalanceService;
