import axios from 'axios';

// Create API instance with proper configuration
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8001/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Accept': 'application/json',
  },
  timeout: 60000, // 60 seconds for file uploads
});

// Add request interceptor to include auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor for better error handling
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    console.error('E-KYC API Error:', error);

    // Handle specific error cases
    if (error.response) {
      const { status, data } = error.response;

      // Handle 403 Forbidden specifically
      if (status === 403) {
        console.error('E-KYC Permission Error:', data.message);
      }

      // Handle 422 Validation errors
      if (status === 422) {
        console.error('E-KYC Validation Error:', data.errors);
      }
    }

    return Promise.reject(error);
  }
);

/**
 * E-KYC Service
 *
 * Provides integration with backend E-KYC endpoints for identity verification.
 * Supports both Malaysian IC and foreign passport verification.
 */
const ekycService = {
  /**
   * Verify Malaysian citizen identity using IC documents
   *
   * @param {FormData} formData - Form data containing:
   *   - ic_front: File (IC front image)
   *   - ic_back: File (IC back image)
   *   - selfie_photo: File (selfie image)
   *   - verification_method: string (optional, defaults to 'manual')
   * @returns {Promise} API response
   */
  verifyMalaysian: async (formData) => {
    try {
      console.log('Submitting Malaysian E-KYC verification...');

      const response = await api.post('/ekyc/malaysian', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      console.log('Malaysian E-KYC verification response:', response.data);
      return response;
    } catch (error) {
      console.error('Malaysian E-KYC verification failed:', error);
      throw error;
    }
  },

  /**
   * Verify foreigner identity using passport documents
   *
   * @param {FormData} formData - Form data containing:
   *   - full_name: string
   *   - passport_number: string
   *   - country: string
   *   - passport_photo: File (passport image)
   *   - selfie_photo: File (selfie image)
   *   - verification_method: string (optional, defaults to 'manual')
   * @returns {Promise} API response
   */
  verifyForeigner: async (formData) => {
    try {
      console.log('Submitting Foreigner E-KYC verification...');

      const response = await api.post('/ekyc/foreigner', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      console.log('Foreigner E-KYC verification response:', response.data);
      return response;
    } catch (error) {
      console.error('Foreigner E-KYC verification failed:', error);
      throw error;
    }
  },

  /**
   * Get user's current E-KYC verification status
   *
   * @param {string} verificationMethod - Optional verification method filter
   * @returns {Promise} API response with verification status
   */
  getVerificationStatus: async (verificationMethod = null) => {
    try {
      console.log('Fetching E-KYC verification status...');

      const params = verificationMethod ? { verification_method: verificationMethod } : {};
      const response = await api.get('/ekyc/status', { params });

      console.log('E-KYC verification status:', response.data);
      return response;
    } catch (error) {
      console.error('Failed to fetch E-KYC verification status:', error);
      throw error;
    }
  },

  /**
   * Get user's E-KYC verification history
   *
   * @returns {Promise} API response with verification history
   */
  getVerificationHistory: async () => {
    try {
      console.log('Fetching E-KYC verification history...');

      const response = await api.get('/ekyc/history');

      console.log('E-KYC verification history:', response.data);
      return response;
    } catch (error) {
      console.error('Failed to fetch E-KYC verification history:', error);
      throw error;
    }
  },

  // Note: getAvailableMethods() endpoint not implemented yet in backend

  /**
   * Helper method to determine document type from user selection
   *
   * @param {string} documentType - Document type from UI
   * @returns {string} Verification type ('malaysian' or 'foreigner')
   */
  getVerificationType: (documentType) => {
    const malaysianTypes = ['malaysian_ic', 'ic', 'mykad'];
    const foreignerTypes = ['passport', 'foreign_passport'];

    if (malaysianTypes.includes(documentType?.toLowerCase())) {
      return 'malaysian';
    } else if (foreignerTypes.includes(documentType?.toLowerCase())) {
      return 'foreigner';
    }

    // Default to Malaysian if unclear
    return 'malaysian';
  },

  /**
   * Helper method to prepare FormData for Malaysian verification
   *
   * @param {Object} documents - Document files
   * @param {File} selfie - Selfie file
   * @param {Object} userInfo - User information (full_name, ic_number)
   * @param {Object} metadata - Additional metadata
   * @returns {FormData} Prepared form data
   */
  prepareMalaysianFormData: (documents, selfie, userInfo = {}, metadata = {}) => {
    const formData = new FormData();

    // Add IC documents with correct field names expected by backend
    const frontDoc = documents.find(doc => doc.side === 'front');
    const backDoc = documents.find(doc => doc.side === 'back');

    if (frontDoc) {
      formData.append('front_ic_photo', frontDoc.file);
    }

    if (backDoc) {
      formData.append('back_ic_photo', backDoc.file);
    }

    // Add selfie
    if (selfie) {
      formData.append('selfie_photo', selfie.file);
    }

    // Add required user information
    if (userInfo.full_name) {
      formData.append('full_name', userInfo.full_name);
    }

    if (userInfo.ic_number) {
      formData.append('ic_number', userInfo.ic_number);
    }

    // Add verification method
    formData.append('verification_method', metadata.verification_method || 'manual');

    // Add session metadata
    if (metadata.session_id) {
      formData.append('session_id', metadata.session_id);
    }

    return formData;
  },

  /**
   * Helper method to prepare FormData for Foreigner verification
   *
   * @param {Object} documents - Document files
   * @param {File} selfie - Selfie file
   * @param {Object} userInfo - User information
   * @param {Object} metadata - Additional metadata
   * @returns {FormData} Prepared form data
   */
  prepareForeignerFormData: (documents, selfie, userInfo, metadata = {}) => {
    const formData = new FormData();

    // Add passport document
    const passportDoc = documents.find(doc => doc.side === 'front') || documents[0];

    if (passportDoc) {
      formData.append('passport_photo', passportDoc.file);
    }

    // Add selfie
    if (selfie) {
      formData.append('selfie_photo', selfie.file);
    }

    // Add user information
    if (userInfo.full_name) {
      formData.append('full_name', userInfo.full_name);
    }

    if (userInfo.passport_number) {
      formData.append('passport_number', userInfo.passport_number);
    }

    if (userInfo.country) {
      formData.append('country', userInfo.country);
    }

    // Add verification method
    formData.append('verification_method', metadata.verification_method || 'manual');

    // Add session metadata
    if (metadata.session_id) {
      formData.append('session_id', metadata.session_id);
    }

    return formData;
  }
};

export default ekycService;
