/**
 * Suggested Replies API Service
 * 
 * TEMPORARY IMPLEMENTATION: This file provides a client-side implementation of suggested replies
 * until the backend API endpoints are ready. The commented-out code shows the planned API integration.
 * 
 * TODO: Replace this temporary implementation with actual backend API calls when endpoints are available:
 * - POST /chat/suggested-replies - Get suggested replies based on conversation context
 * - POST /chat/suggestion-feedback - Send feedback about suggestion quality
 * 
 * @module suggestedRepliesApi
 */

import axios from 'axios';

const BASE_URL = 'https://api.missionx.com/api';

// Create axios instance with default config
const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
});

// Add authorization header to all requests
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

/**
 * Temporary suggestions for various conversation contexts
 * These will be replaced by backend-generated suggestions when the API is ready
 * @const {Object} DEMO_SUGGESTIONS
 */
const DEMO_SUGGESTIONS = {
  general: [
    { text: "Thanks for the message!", id: "general-1" },
    { text: "I'll get back to you soon.", id: "general-2" },
    { text: "That sounds good!", id: "general-3" },
    { text: "Could you tell me more?", id: "general-4" }
  ],
  greeting: [
    { text: "Hi there! How are you?", id: "greeting-1" },
    { text: "Hello! Nice to hear from you.", id: "greeting-2" },
    { text: "Hey! What's up?", id: "greeting-3" }
  ],
  question: [
    { text: "Yes, that works for me.", id: "question-1" },
    { text: "No, sorry I can't.", id: "question-2" },
    { text: "I'll check and let you know.", id: "question-3" },
    { text: "That's a good question.", id: "question-4" }
  ],
  gratitude: [
    { text: "You're welcome!", id: "gratitude-1" },
    { text: "No problem at all!", id: "gratitude-2" },
    { text: "Happy to help!", id: "gratitude-3" }
  ],
  scheduling: [
    { text: "How about tomorrow?", id: "scheduling-1" },
    { text: "I'm free this afternoon.", id: "scheduling-2" },
    { text: "Let's schedule a call.", id: "scheduling-3" },
    { text: "What time works for you?", id: "scheduling-4" }
  ],
  gift: [
    { text: "Thank you for the gift!", id: "gift-1" },
    { text: "That's so thoughtful of you!", id: "gift-2" },
    { text: "I love it!", id: "gift-3" }
  ],
  farewell: [
    { text: "Talk to you later!", id: "farewell-1" },
    { text: "Have a great day!", id: "farewell-2" },
    { text: "Bye for now!", id: "farewell-3" }
  ]
};

/**
 * Analyzes message content to detect the conversation context
 * This client-side implementation uses simple regex patterns to determine context
 * Will be replaced by more sophisticated backend logic in the future
 * 
 * @param {string} message - The message to analyze
 * @param {Array} conversationHistory - Previous messages for additional context
 * @returns {string} The detected context category
 */
const detectMessageContext = (message = "", conversationHistory = []) => {
  const lowerMessage = message.toLowerCase();
  
  // Simple context detection based on keywords and patterns
  if (/^(hi|hello|hey|good morning|good afternoon|good evening)/i.test(lowerMessage)) {
    return 'greeting';
  }
  
  if (/\?$/.test(lowerMessage) || 
      /^(what|where|when|who|how|why|can|could|would|will|do|does|are|is)/i.test(lowerMessage)) {
    return 'question';
  }

  if (/thank(s| you)|appreciate|grateful/i.test(lowerMessage)) {
    return 'gratitude';
  }
  
  if (/schedule|meet|meeting|calendar|appointment|available|time|tomorrow|later|next week/i.test(lowerMessage)) {
    return 'scheduling';
  }
  
  if (/gift|sent you|present|surprise/i.test(lowerMessage)) {
    return 'gift';
  }
  
  if (/bye|goodbye|later|talk soon|see you|ttyl/i.test(lowerMessage)) {
    return 'farewell';
  }
  
  // Default context if no specific patterns detected
  return 'general';
};

export const suggestedRepliesApi = {
  /**
   * Get suggested replies based on conversation context
   * @param {string} lastMessage - The last message received
   * @param {Array} conversationHistory - Recent message history for context
   * @param {number} conversationId - ID of the current conversation
   * @param {number} limit - Maximum number of suggestions to return
   * @returns {Promise<Object>} Response with suggested replies
   */
  getSuggestedReplies: async (lastMessage, conversationHistory = [], conversationId, limit = 4) => {
    try {
      // TODO: Integrate with backend when the API endpoint is available
      // BACKEND INTEGRATION: Uncomment this code when ready
      /*
      const response = await api.post('/chat/suggested-replies', {
        conversation_id: conversationId,
        last_message: lastMessage,
        history: conversationHistory.slice(-5), // Send last 5 messages for context
        limit
      });
      return response.data;
      */
      
      // TEMPORARY IMPLEMENTATION: Using client-side logic with predefined responses
      // This code block will be removed once backend integration is complete
      
      // Detect context based on the last message
      const context = detectMessageContext(lastMessage, conversationHistory);
      
      // Get suggestions based on detected context
      const suggestions = DEMO_SUGGESTIONS[context] || DEMO_SUGGESTIONS.general;
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 300));
      
      // Return formatted response
      return {
        success: true,
        data: {
          suggestions: suggestions.slice(0, limit),
          context
        }
      };
    } catch (error) {
      console.error('Error fetching suggested replies:', error);
      // Return empty suggestions as fallback
      return {
        success: false,
        data: {
          suggestions: [],
          error: error.message
        }
      };
    }
  },
  
  /**
   * Send feedback about suggestion quality to improve future suggestions
   * @param {number} suggestionId - ID of the suggestion
   * @param {boolean} wasHelpful - Whether the suggestion was useful
   * @returns {Promise<Object>} Response confirming feedback receipt
   */
  sendSuggestionFeedback: async (suggestionId, wasHelpful) => {
    try {
      // TODO: Integrate with backend when the API endpoint is available
      // BACKEND INTEGRATION: Uncomment this code when ready
      /*
      const response = await api.post('/chat/suggestion-feedback', {
        suggestion_id: suggestionId,
        was_helpful: wasHelpful
      });
      return response.data;
      */
      
      // TEMPORARY IMPLEMENTATION: Just logging feedback to console
      // This code block will be removed once backend integration is complete
      console.log(`Suggestion feedback: ID ${suggestionId}, Helpful: ${wasHelpful}`);
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 200));
      
      // Return dummy response
      return {
        success: true,
        data: {
          message: 'Feedback received'
        }
      };
    } catch (error) {
      console.error('Error sending suggestion feedback:', error);
      throw error;
    }
  }
}; 