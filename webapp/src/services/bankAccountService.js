import axios from 'axios';
import errorHandlingService from './errorHandlingService';
import { WALLET_ERROR_TYPES, API_ERROR_TYPES } from '../utils/errorTypes';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8001/api';

// Helper function to simulate API delay for development
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Create API instance with auth headers
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to include auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Mock data for development - START WITH EMPTY ARRAY (no phantom accounts)
// Users should add their own bank accounts through the UI
const mockBankAccounts = [];

// Mock Malaysian banks data
const mockMalaysianBanks = [
  {
    id: 1,
    code: 'MBB',
    name: 'Maybank',
    swift_code: 'MBBEMYKL',
    logo_url: '/images/banks/maybank.png',
    account_number_format: '13 digits',
    account_number_regex: '^[0-9]{13}$',
    description: 'Malayan Banking Berhad',
    is_active: true,
    display_order: 1
  },
  {
    id: 2,
    code: 'CIMB',
    name: 'CIMB Bank',
    swift_code: 'CIBBMYKL',
    logo_url: '/images/banks/cimb.png',
    account_number_format: '13 digits',
    account_number_regex: '^[0-9]{13}$',
    description: 'CIMB Bank Berhad',
    is_active: true,
    display_order: 2
  },
  {
    id: 3,
    code: 'PBB',
    name: 'Public Bank',
    swift_code: 'PBBEMYKL',
    logo_url: '/images/banks/public-bank.png',
    account_number_format: '10-12 digits',
    account_number_regex: '^[0-9]{10,12}$',
    description: 'Public Bank Berhad',
    is_active: true,
    display_order: 3
  },
  {
    id: 4,
    code: 'RHB',
    name: 'RHB Bank',
    swift_code: 'RHBBMYKL',
    logo_url: '/images/banks/rhb.png',
    account_number_format: '14 digits',
    account_number_regex: '^[0-9]{14}$',
    description: 'RHB Bank Berhad',
    is_active: true,
    display_order: 4
  },
  {
    id: 5,
    code: 'HLB',
    name: 'Hong Leong Bank',
    swift_code: 'HLBBMYKL',
    logo_url: '/images/banks/hong-leong.png',
    account_number_format: '11-15 digits',
    account_number_regex: '^[0-9]{11,15}$',
    description: 'Hong Leong Bank Berhad',
    is_active: true,
    display_order: 5
  },
  {
    id: 6,
    code: 'AMB',
    name: 'AmBank',
    swift_code: 'ARBKMYKL',
    logo_url: '/images/banks/ambank.png',
    account_number_format: '12 digits',
    account_number_regex: '^[0-9]{12}$',
    description: 'AmBank (M) Berhad',
    is_active: true,
    display_order: 6
  },
  {
    id: 7,
    code: 'BIMB',
    name: 'Bank Islam',
    swift_code: 'BIMBMYKL',
    logo_url: '/images/banks/bank-islam.png',
    account_number_format: '14 digits',
    account_number_regex: '^[0-9]{14}$',
    description: 'Bank Islam Malaysia Berhad',
    is_active: true,
    display_order: 7
  },
  {
    id: 8,
    code: 'BKRM',
    name: 'Bank Rakyat',
    swift_code: 'BKRMMYKL',
    logo_url: '/images/banks/bank-rakyat.png',
    account_number_format: '12 digits',
    account_number_regex: '^[0-9]{12}$',
    description: 'Bank Kerjasama Rakyat Malaysia Berhad',
    is_active: true,
    display_order: 8
  },
  {
    id: 9,
    code: 'OCBC',
    name: 'OCBC Bank',
    swift_code: 'OCBCMYKL',
    logo_url: '/images/banks/ocbc.png',
    account_number_format: '10-12 digits',
    account_number_regex: '^[0-9]{10,12}$',
    description: 'OCBC Bank (Malaysia) Berhad',
    is_active: true,
    display_order: 9
  },
  {
    id: 10,
    code: 'SCB',
    name: 'Standard Chartered',
    swift_code: 'SCBLMYKX',
    logo_url: '/images/banks/standard-chartered.png',
    account_number_format: '10-14 digits',
    account_number_regex: '^[0-9]{10,14}$',
    description: 'Standard Chartered Bank Malaysia Berhad',
    is_active: true,
    display_order: 10
  }
];

// Use real API now that backend has implemented Malaysian banks endpoint
const isDevelopment = false; // Backend API is now available

// Enhanced error handling wrapper
const withErrorHandling = async (operation, context = 'bank_account') => {
  try {
    return await operation();
  } catch (error) {
    const processedError = errorHandlingService.processError(error, context, {
      operation: operation.name || 'unknown',
      timestamp: Date.now()
    });
    throw processedError;
  }
};

// Bank Account API Service
const bankAccountService = isDevelopment
  ? {
      // Get user's bank accounts
      getBankAccounts: async () => {
        return withErrorHandling(async () => {
          await delay(800);
          return {
            data: mockBankAccounts,
            message: 'Bank accounts retrieved successfully'
          };
        }, 'get_bank_accounts');
      },

      // Get Malaysian banks
      getMalaysianBanks: async () => {
        return withErrorHandling(async () => {
          await delay(500);
          return {
            data: mockMalaysianBanks,
            message: 'Malaysian banks retrieved successfully'
          };
        }, 'get_malaysian_banks');
      },

      // Get a specific bank account
      getBankAccount: async (id) => {
        await delay(500);
        const account = mockBankAccounts.find(acc => acc.id === id);

        if (!account) {
          throw new Error('Bank account not found');
        }

        return {
          data: {
            bank_account: account,
            message: 'Bank account retrieved successfully'
          }
        };
      },

      // Add a new bank account
      addBankAccount: async (accountData) => {
        return withErrorHandling(async () => {
          await delay(1000);

          // Find the bank information
          const bank = mockMalaysianBanks.find(b => b.id === accountData.malaysian_bank_id);
          if (!bank) {
            throw new Error('Invalid bank selected');
          }

          const newAccount = {
            id: mockBankAccounts.length + 1,
            user_id: 1,
            malaysian_bank_id: accountData.malaysian_bank_id,
            account_number: accountData.account_number,
            account_holder_name: accountData.account_holder_name,
            is_primary: accountData.is_primary || false,
            is_verified: false,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            // Include bank relationship
            bank: {
              id: bank.id,
              name: bank.name,
              code: bank.code,
              swift_code: bank.swift_code,
              logo_url: bank.logo_url,
              account_number_format: bank.account_number_format,
              description: bank.description
            }
          };

          // Update mock data
          mockBankAccounts.push(newAccount);

          // If this is the primary account, update other accounts
          if (newAccount.is_primary) {
            mockBankAccounts.forEach(acc => {
              if (acc.id !== newAccount.id) {
                acc.is_primary = false;
              }
            });
          }

          return {
            data: {
              bank_account: newAccount,
              message: 'Bank account added successfully'
            }
          };
        }, 'add_bank_account');
      },

      // Update an existing bank account
      updateBankAccount: async (id, accountData) => {
        await delay(1000);
        const index = mockBankAccounts.findIndex(acc => acc.id === id);

        if (index === -1) {
          throw new Error('Bank account not found');
        }

        const updatedAccount = {
          ...mockBankAccounts[index],
          bank_name: accountData.bank_name || mockBankAccounts[index].bank_name,
          account_number: accountData.account_number || mockBankAccounts[index].account_number,
          account_holder_name: accountData.account_holder_name || mockBankAccounts[index].account_holder_name,
          is_primary: accountData.is_primary !== undefined ? accountData.is_primary : mockBankAccounts[index].is_primary,
          swift_code: accountData.swift_code || mockBankAccounts[index].swift_code,
          updated_at: new Date().toISOString(),
        };

        // Update mock data
        mockBankAccounts[index] = updatedAccount;

        // If this is the primary account, update other accounts
        if (updatedAccount.is_primary) {
          mockBankAccounts.forEach(acc => {
            if (acc.id !== id) {
              acc.is_primary = false;
            }
          });
        }

        return {
          data: {
            bank_account: updatedAccount,
            message: 'Bank account updated successfully'
          }
        };
      },

      // Delete a bank account
      deleteBankAccount: async (id) => {
        await delay(800);
        const index = mockBankAccounts.findIndex(acc => acc.id === id);

        if (index === -1) {
          throw new Error('Bank account not found');
        }

        // Check if it's the primary account
        if (mockBankAccounts[index].is_primary) {
          throw new Error('Cannot delete primary bank account');
        }

        // Remove from mock data
        mockBankAccounts.splice(index, 1);

        return {
          data: {
            message: 'Bank account deleted successfully'
          }
        };
      },

      // Set a bank account as primary
      setPrimaryBankAccount: async (id) => {
        await delay(600);
        const index = mockBankAccounts.findIndex(acc => acc.id === id);

        if (index === -1) {
          throw new Error('Bank account not found');
        }

        // Update all accounts
        mockBankAccounts.forEach(acc => {
          acc.is_primary = acc.id === id;
          acc.updated_at = new Date().toISOString();
        });

        return {
          data: {
            bank_account: mockBankAccounts[index],
            message: 'Primary bank account set successfully'
          }
        };
      }
    }
  : {
      // Real API implementations
      getBankAccounts: async () => {
        return withErrorHandling(async () => {
          const response = await api.get('/user/bank-accounts');
          return response;
        }, 'get_bank_accounts');
      },

      getBankAccount: async (id) => {
        return withErrorHandling(async () => {
          const response = await api.get(`/user/bank-accounts/${id}`);
          return response;
        }, 'get_bank_account');
      },

      getMalaysianBanks: async () => {
        return withErrorHandling(async () => {
          const response = await api.get('/malaysian-banks');
          return response;
        }, 'get_malaysian_banks');
      },

      addBankAccount: async (accountData) => {
        return withErrorHandling(async () => {
          const response = await api.post('/user/bank-accounts', accountData);
          return response;
        }, 'add_bank_account');
      },

      updateBankAccount: async (id, accountData) => {
        return withErrorHandling(async () => {
          const response = await api.put(`/user/bank-accounts/${id}`, accountData);
          return response;
        }, 'update_bank_account');
      },

      deleteBankAccount: async (id) => {
        return withErrorHandling(async () => {
          const response = await api.delete(`/user/bank-accounts/${id}`);
          return response;
        }, 'delete_bank_account');
      },

      setPrimaryBankAccount: async (id) => {
        return withErrorHandling(async () => {
          const response = await api.post(`/user/bank-accounts/${id}/set-primary`);
          return response;
        }, 'set_primary_bank_account');
      },

      // Bank account verification (future implementation)
      requestVerification: async (id) => {
        return withErrorHandling(async () => {
          const response = await api.post(`/user/bank-accounts/${id}/request-verification`);
          return response;
        }, 'request_verification');
      },

      checkVerificationStatus: async (id) => {
        return withErrorHandling(async () => {
          const response = await api.get(`/user/bank-accounts/${id}/verification-status`);
          return response;
        }, 'check_verification_status');
      }
    };

export default bankAccountService;