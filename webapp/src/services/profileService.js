/**
 * Profile Service
 *
 * This service handles all API calls related to user profiles.
 */

import api from './api';

// Environment detection
const NODE_ENV = process.env.NODE_ENV || 'development';
const isProd = NODE_ENV === 'production';
const isDevelopment = NODE_ENV === 'development';

// Feature flag to disable mocks even in development (for testing real API in dev)
const USE_MOCK_API = isDevelopment && process.env.REACT_APP_USE_MOCK_API !== 'false';

// Mock profile data for development
const mockProfile = {
  id: 1,
  first_name: '<PERSON>',
  last_name: '<PERSON>',
  nickname: '<PERSON><PERSON><PERSON><PERSON>',
  email: '<EMAIL>',
  mobile_number: '60123456789',
  country_code: '+60',
  gender: 'male',
  date_of_birth: '1995-05-15',
  avatar_url: 'https://randomuser.me/api/portraits/men/32.jpg',
  cover_image_url: 'https://images.unsplash.com/photo-1542751371-adc38448a05e?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80',
  level: 15,
  experience_points: 2500,
  is_verified: true,
  is_talent: true,
  created_at: '2023-01-15T08:30:00Z',
  updated_at: '2023-05-20T14:45:00Z',
  last_active: '2023-05-25T18:20:00Z'
};

// Mock biography data
const mockBiography = {
  id: 1,
  user_id: 1,
  bio: "Hi, I'm Alex! I've been gaming for over 10 years and specialize in FPS and MOBA games. I'm a professional coach and streamer with experience in competitive tournaments. Let's team up and dominate the leaderboards!",
  personalities: ['Friendly', 'Strategic', 'Competitive'],
  languages: ['English', 'Malay'],
  games: [
    { id: 1, name: 'League of Legends', skill_level: 'Expert', years_experience: 8 },
    { id: 2, name: 'Valorant', skill_level: 'Expert', years_experience: 3 },
    { id: 3, name: 'Apex Legends', skill_level: 'Advanced', years_experience: 4 }
  ],
  created_at: '2023-01-15T08:35:00Z',
  updated_at: '2023-05-20T14:50:00Z'
};

// Mock statistics data
const mockStatistics = {
  total_missions_completed: 47,
  total_missions_hosted: 12,
  total_hours_played: 342,
  total_earnings: 4850,
  average_rating: 4.8,
  total_reviews: 38,
  followers_count: 156,
  following_count: 89,
  achievements: [
    { id: 1, name: 'First Blood', description: 'Completed first mission', earned_at: '2023-01-20T10:15:00Z' },
    { id: 2, name: 'Rising Star', description: 'Received 10 five-star reviews', earned_at: '2023-03-05T16:30:00Z' },
    { id: 3, name: 'Mission Master', description: 'Completed 25 missions', earned_at: '2023-04-12T14:20:00Z' }
  ]
};

// Mock services data
const mockServices = [
  {
    id: 1,
    user_id: 1,
    title: 'Professional Gaming Coaching',
    description: 'One-on-one coaching sessions to improve your gaming skills. I specialize in FPS games and can help with aim, strategy, and game sense.',
    game_id: 2,
    game_name: 'Valorant',
    price_per_hour: 50,
    is_available: true,
    created_at: '2023-02-10T09:20:00Z',
    updated_at: '2023-05-15T11:30:00Z'
  },
  {
    id: 2,
    user_id: 1,
    title: 'Duo Queue Partner',
    description: 'Looking to climb the ranked ladder? I can help you reach your desired rank with my experience and skills.',
    game_id: 1,
    game_name: 'League of Legends',
    price_per_hour: 35,
    is_available: true,
    created_at: '2023-02-15T14:40:00Z',
    updated_at: '2023-05-18T10:25:00Z'
  }
];

// Mock availability data
const mockAvailability = {
  weekly_schedule: [
    { day: 'monday', start_time: '18:00', end_time: '22:00', is_available: true },
    { day: 'tuesday', start_time: '18:00', end_time: '22:00', is_available: true },
    { day: 'wednesday', start_time: '18:00', end_time: '22:00', is_available: true },
    { day: 'thursday', start_time: '18:00', end_time: '22:00', is_available: true },
    { day: 'friday', start_time: '16:00', end_time: '23:00', is_available: true },
    { day: 'saturday', start_time: '10:00', end_time: '23:00', is_available: true },
    { day: 'sunday', start_time: '10:00', end_time: '22:00', is_available: true }
  ],
  overrides: [
    { date: '2023-06-15', is_available: false, reason: 'Personal commitment' },
    { date: '2023-06-20', start_time: '14:00', end_time: '20:00', is_available: true }
  ],
  timezone: 'Asia/Kuala_Lumpur'
};

// Mock media gallery data
const mockMediaGallery = [
  {
    id: 1,
    user_id: 1,
    type: 'image',
    url: 'https://images.unsplash.com/photo-1542751371-adc38448a05e?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80',
    thumbnail_url: 'https://images.unsplash.com/photo-1542751371-adc38448a05e?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80',
    title: 'Tournament Victory',
    description: 'Won first place in the regional Valorant tournament',
    created_at: '2023-03-10T15:20:00Z'
  },
  {
    id: 2,
    user_id: 1,
    type: 'image',
    url: 'https://images.unsplash.com/photo-1493711662062-fa541adb3fc8?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80',
    thumbnail_url: 'https://images.unsplash.com/photo-1493711662062-fa541adb3fc8?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80',
    title: 'Streaming Setup',
    description: 'My professional gaming and streaming setup',
    created_at: '2023-04-05T11:45:00Z'
  }
];

/**
 * Mock third-party access data
 */
const mockThirdPartyAccess = {
  enabled: false,
  connected_apps: [
    {
      id: 1,
      name: 'Game Stats Tracker',
      description: 'Tracks your gaming statistics across multiple platforms',
      icon_url: null,
      connected_at: '2023-04-15T10:30:00Z'
    },
    {
      id: 2,
      name: 'Mission Notifier',
      description: 'Sends notifications about new missions and updates',
      icon_url: null,
      connected_at: '2023-05-20T14:45:00Z'
    }
  ]
};

/**
 * Mock voice note data
 */
const mockVoiceNote = {
  id: 1,
  user_id: 1,
  url: 'https://example.com/voice-notes/sample.mp3',
  duration: 12, // seconds
  created_at: '2023-05-10T14:30:00Z',
  updated_at: '2023-05-10T14:30:00Z'
};

/**
 * Mock experience history data
 */
const mockExperienceHistory = [
  {
    id: 1,
    user_id: 1,
    action_type: 'mission_completed',
    description: 'Completed mission: Valorant Coaching Session',
    experience_points: 150,
    level_before: 14,
    level_after: 15,
    created_at: '2023-05-20T14:30:00Z'
  },
  {
    id: 2,
    user_id: 1,
    action_type: 'mission_hosted',
    description: 'Hosted mission: League of Legends Team Practice',
    experience_points: 200,
    level_before: 14,
    level_after: 14,
    created_at: '2023-05-15T10:45:00Z'
  },
  {
    id: 3,
    user_id: 1,
    action_type: 'achievement_earned',
    description: 'Earned achievement: Rising Star',
    experience_points: 300,
    level_before: 13,
    level_after: 14,
    created_at: '2023-05-05T16:20:00Z'
  },
  {
    id: 4,
    user_id: 1,
    action_type: 'service_provided',
    description: 'Provided service: Apex Legends Duo Session',
    experience_points: 100,
    level_before: 13,
    level_after: 13,
    created_at: '2023-04-28T19:15:00Z'
  },
  {
    id: 5,
    user_id: 1,
    action_type: 'mission_completed',
    description: 'Completed mission: Fortnite Squad Leader',
    experience_points: 120,
    level_before: 12,
    level_after: 13,
    created_at: '2023-04-20T21:30:00Z'
  }
];

/**
 * Mock user race information - only used for mock mode
 */
const mockUserRace = {
  race_id: null,
  race_name: ""
};

/**
 * Profile Service
 */
const profileService = {
  /**
   * Get user profile
   *
   * @returns {Promise<Object>} Profile data
   */
  getProfile: async () => {
    if (USE_MOCK_API) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      return {
        success: true,
        data: mockProfile
      };
    }

    try {
      const response = await api.get('/user/profile');
      return response.data;
    } catch (error) {
      console.error('Error fetching profile:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to fetch profile'
      };
    }
  },

  /**
   * Get user biography
   *
   * @returns {Promise<Object>} Biography data
   */
  getBiography: async () => {
    if (USE_MOCK_API) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 300));

      return {
        success: true,
        data: mockBiography
      };
    }

    try {
      const response = await api.get('/user/biography');
      return response.data;
    } catch (error) {
      console.error('Error fetching biography:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to fetch biography'
      };
    }
  },

  /**
   * Get user statistics
   *
   * @returns {Promise<Object>} Statistics data
   */
  getStatistics: async () => {
    if (USE_MOCK_API) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 400));

      return {
        success: true,
        data: mockStatistics
      };
    }

    try {
      const response = await api.get('/user/statistics');
      return response.data;
    } catch (error) {
      console.error('Error fetching statistics:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to fetch statistics'
      };
    }
  },

  /**
   * Get user services
   *
   * @returns {Promise<Object>} Services data
   */
  getServices: async () => {
    if (USE_MOCK_API) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 350));

      return {
        success: true,
        data: mockServices
      };
    }

    try {
      const response = await api.get('/user/services');
      return response.data;
    } catch (error) {
      console.error('Error fetching services:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to fetch services'
      };
    }
  },

  /**
   * Get user availability
   *
   * @returns {Promise<Object>} Availability data
   */
  getAvailability: async () => {
    if (USE_MOCK_API) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 300));

      return {
        success: true,
        data: mockAvailability
      };
    }

    try {
      const response = await api.get('/user/availability');
      return response.data;
    } catch (error) {
      console.error('Error fetching availability:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to fetch availability'
      };
    }
  },

  /**
   * Get user media gallery
   *
   * @returns {Promise<Object>} Media gallery data
   */
  getMediaGallery: async () => {
    if (USE_MOCK_API) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 450));

      return {
        success: true,
        data: mockMediaGallery
      };
    }

    try {
      const response = await api.get('/user/media');
      return response.data;
    } catch (error) {
      console.error('Error fetching media gallery:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to fetch media gallery'
      };
    }
  },

  /**
   * Update user profile
   *
   * @param {Object} profileData - Updated profile data
   * @returns {Promise<Object>} Update result
   */
  updateProfile: async (profileData) => {
    if (USE_MOCK_API) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 600));

      return {
        success: true,
        data: {
          ...mockProfile,
          ...profileData,
          updated_at: new Date().toISOString()
        }
      };
    }

    try {
      // Check if we have files to upload
      const hasFiles = profileData.profile_picture instanceof File || profileData.voice_note instanceof File;

      if (hasFiles) {
        // Use multipart/form-data with POST + _method=PUT for file uploads
        const formData = new FormData();

        // Add _method=PUT for Laravel to properly route the request
        formData.append('_method', 'PUT');

        // Add all profile fields
        Object.keys(profileData).forEach(key => {
          const value = profileData[key];
          if (value !== null && value !== undefined) {
            if (key === 'height' || key === 'weight') {
              // Handle numeric fields - send if valid number or empty string to clear
              const stringValue = value.toString().trim();
              if (stringValue === '' || (!isNaN(stringValue) && parseFloat(stringValue) >= 0)) {
                formData.append(key, stringValue);
              }
            } else if (key === 'biography') {
              // Always send biography, even if empty
              formData.append(key, value.toString());
            } else if (value instanceof File) {
              // Handle file uploads
              formData.append(key, value);
            } else if (typeof value === 'boolean') {
              // Handle boolean values
              formData.append(key, value ? '1' : '0');
            } else if (value !== '') {
              // Handle other non-empty values
              formData.append(key, value.toString());
            }
          }
        });

        // Debug logging
        console.log('=== PROFILE SERVICE DEBUG ===');
        console.log('Using multipart/form-data with POST + _method=PUT');
        console.log('FormData entries:');
        for (let [key, value] of formData.entries()) {
          console.log(`${key}:`, value);
        }
        console.log('==============================');

        const response = await api.post('/user/profile', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        });

        return {
          success: true,
          data: response.data
        };
      } else {
        // Use regular JSON PUT request for non-file updates
        console.log('=== PROFILE SERVICE DEBUG ===');
        console.log('Using JSON PUT request (no files)');
        console.log('Profile data:', profileData);
        console.log('==============================');

        const response = await api.put('/user/profile', profileData);
        return {
          success: true,
          data: response.data
        };
      }
    } catch (error) {
      console.error('Error updating profile:', error);

      // Handle validation errors
      if (error.response?.status === 422 && error.response?.data?.errors) {
        return {
          success: false,
          validationErrors: error.response.data.errors,
          error: error.response.data.message || 'Validation failed'
        };
      }

      return {
        success: false,
        error: error.response?.data?.message || 'Failed to update profile'
      };
    }
  },

  /**
   * Update user biography
   *
   * @param {Object} biographyData - Updated biography data
   * @returns {Promise<Object>} Update result
   */
  updateBiography: async (biographyData) => {
    if (USE_MOCK_API) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      return {
        success: true,
        data: {
          ...mockBiography,
          ...biographyData,
          updated_at: new Date().toISOString()
        }
      };
    }

    try {
      const response = await api.put('/user/biography', biographyData);
      return response.data;
    } catch (error) {
      console.error('Error updating biography:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to update biography'
      };
    }
  },

  /**
   * Upload media to gallery
   *
   * @param {File} file - File to upload
   * @param {string} type - Media type (image, video)
   * @returns {Promise<Object>} Upload result
   */
  uploadMedia: async (file, type = 'image') => {
    if (USE_MOCK_API) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Create a mock URL for the uploaded file
      const mockUrl = 'https://images.unsplash.com/photo-1511512578047-dfb367046420?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80';

      return {
        success: true,
        data: {
          id: mockMediaGallery.length + 1,
          user_id: 1,
          type,
          url: mockUrl,
          thumbnail_url: mockUrl,
          title: file.name,
          description: '',
          created_at: new Date().toISOString()
        }
      };
    }

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', type);

      const response = await api.post('/user/media', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      return response.data;
    } catch (error) {
      console.error('Error uploading media:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to upload media'
      };
    }
  },

  /**
   * Get third-party access settings
   *
   * @returns {Promise<Object>} Third-party access settings
   */
  getThirdPartyAccess: async () => {
    if (USE_MOCK_API) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 300));

      return {
        success: true,
        data: mockThirdPartyAccess
      };
    }

    try {
      const response = await api.get('/user/allow-3rd-party-access');
      return response.data;
    } catch (error) {
      console.error('Error fetching third-party access settings:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to fetch third-party access settings'
      };
    }
  },

  /**
   * Update third-party access settings
   *
   * @param {boolean} enabled - Whether to enable or disable third-party access
   * @returns {Promise<Object>} Update result
   */
  updateThirdPartyAccess: async (enabled) => {
    if (USE_MOCK_API) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Update mock data
      mockThirdPartyAccess.enabled = enabled;

      return {
        success: true,
        data: {
          enabled,
          updated_at: new Date().toISOString()
        }
      };
    }

    try {
      const response = await api.put('/user/allow-3rd-party-access', { allow_3rd_party_access: enabled });
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('Error updating third-party access settings:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to update third-party access settings'
      };
    }
  },

  /**
   * Get user voice note
   *
   * @returns {Promise<Object>} Voice note data
   */
  getVoiceNote: async () => {
    if (USE_MOCK_API) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 300));

      return {
        success: true,
        data: mockVoiceNote
      };
    }

    try {
      const response = await api.get('/user/voice-note');
      return response.data;
    } catch (error) {
      // If 404, it means the user doesn't have a voice note yet
      if (error.response && error.response.status === 404) {
        return {
          success: true,
          data: null
        };
      }

      console.error('Error fetching voice note:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to fetch voice note'
      };
    }
  },

  /**
   * Upload voice note
   *
   * @param {Blob} audioBlob - Audio blob to upload
   * @returns {Promise<Object>} Upload result
   */
  uploadVoiceNote: async (audioBlob) => {
    if (USE_MOCK_API) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 800));

      // Update mock data
      mockVoiceNote.url = URL.createObjectURL(audioBlob);
      mockVoiceNote.updated_at = new Date().toISOString();

      return {
        success: true,
        data: {
          ...mockVoiceNote,
          url: mockVoiceNote.url
        }
      };
    }

    try {
      const formData = new FormData();
      formData.append('audio', audioBlob);

      const response = await api.post('/user/voice-note', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      return response.data;
    } catch (error) {
      console.error('Error uploading voice note:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to upload voice note'
      };
    }
  },

  /**
   * Delete voice note
   *
   * @returns {Promise<Object>} Delete result
   */
  deleteVoiceNote: async () => {
    if (USE_MOCK_API) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Update mock data
      mockVoiceNote.url = null;

      return {
        success: true,
        data: {
          message: 'Voice note deleted successfully'
        }
      };
    }

    try {
      const response = await api.delete('/user/voice-note');
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('Error deleting voice note:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to delete voice note'
      };
    }
  },

  /**
   * Delete media from gallery
   *
   * @param {number} mediaId - ID of the media to delete
   * @returns {Promise<Object>} Delete result
   */
  deleteMedia: async (mediaId) => {
    if (USE_MOCK_API) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Update mock data
      const mediaIndex = mockMediaGallery.findIndex(media => media.id === mediaId);
      if (mediaIndex !== -1) {
        mockMediaGallery.splice(mediaIndex, 1);
      }

      return {
        success: true,
        data: {
          message: 'Media deleted successfully'
        }
      };
    }

    try {
      const response = await api.delete('/user/media', { data: { id: mediaId } });
      return response.data;
    } catch (error) {
      console.error('Error deleting media:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to delete media'
      };
    }
  },

  /**
   * Reorder media in gallery
   *
   * @param {Array<{id: number, order: number}>} mediaOrder - Array of media IDs with their new order
   * @returns {Promise<Object>} Reorder result
   */
  reorderMedia: async (mediaOrder) => {
    if (USE_MOCK_API) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 600));

      // Update mock data order (not actually changing the array order for simplicity)
      return {
        success: true,
        data: {
          message: 'Media reordered successfully',
          media: mockMediaGallery
        }
      };
    }

    try {
      const response = await api.put('/user/media/reorder', { media_order: mediaOrder });
      return response.data;
    } catch (error) {
      console.error('Error reordering media:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to reorder media'
      };
    }
  },

  /**
   * Set media as cover (profile cover photo or video)
   *
   * @param {number} mediaId - ID of the media to set as cover
   * @param {string} coverType - Type of cover ('photo' or 'video')
   * @returns {Promise<Object>} Update result
   */
  setMediaAsCover: async (mediaId, coverType) => {
    if (USE_MOCK_API) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 400));

      // Update mock profile data
      if (coverType === 'photo') {
        const media = mockMediaGallery.find(m => m.id === mediaId);
        if (media) {
          mockProfile.cover_image_url = media.url;
        }
      }

      return {
        success: true,
        data: {
          message: `Media set as ${coverType} cover successfully`,
          profile: mockProfile
        }
      };
    }

    try {
      const response = await api.post('/user/profile-picture', {
        media_id: mediaId,
        cover_type: coverType
      });
      return response.data;
    } catch (error) {
      console.error('Error setting media as cover:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to set media as cover'
      };
    }
  },

  /**
   * Get user experience history
   *
   * @param {Object} options - Query options
   * @param {number} options.page - Page number
   * @param {number} options.limit - Number of items per page
   * @returns {Promise<Object>} Experience history data
   */
  getExperienceHistory: async (options = { page: 1, limit: 10 }) => {
    if (USE_MOCK_API) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 400));

      // Paginate mock data
      const startIndex = (options.page - 1) * options.limit;
      const endIndex = startIndex + options.limit;
      const paginatedData = mockExperienceHistory.slice(startIndex, endIndex);

      return {
        success: true,
        data: {
          items: paginatedData,
          total: mockExperienceHistory.length,
          page: options.page,
          limit: options.limit,
          total_pages: Math.ceil(mockExperienceHistory.length / options.limit)
        }
      };
    }

    try {
      const response = await api.get('/user/experience/history', {
        params: {
          page: options.page,
          limit: options.limit
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching experience history:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to fetch experience history'
      };
    }
  },

  /**
   * Get user level information
   *
   * @returns {Promise<Object>} Level information
   */
  getLevelInfo: async () => {
    if (USE_MOCK_API) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 300));

      return {
        success: true,
        data: {
          current_level: mockProfile.level,
          experience_points: mockProfile.experience_points,
          next_level_points: mockProfile.level * 1000, // Simple formula for mock data
          progress_percentage: 65, // Mock percentage
          level_requirements: {
            current: (mockProfile.level - 1) * 1000,
            next: mockProfile.level * 1000
          }
        }
      };
    }

    try {
      const response = await api.get('/user/level');
      return response.data;
    } catch (error) {
      console.error('Error fetching level information:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to fetch level information'
      };
    }
  },

  /**
   * Get user race information
   *
   * @returns {Promise<Object>} Race information
   */
  getRaceInfo: async () => {
    try {
      const response = await api.get('/races');
      return response.data;
    } catch (error) {
      console.error('Error fetching race information:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to fetch race information'
      };
    }
  },

  /**
   * Get all races from the backoffice
   *
   * @returns {Promise<Object>} Races data
   */
  getRaces: async () => {
    try {
      // This endpoint doesn't require authentication as per the API docs
      const response = await api.get('/races');

      // The response should be an array of race objects with id, name, and description
      if (Array.isArray(response.data)) {
        return {
          success: true,
          data: response.data
        };
      } else {
        // If the response is not in the expected format, return the data as is
        return {
          success: true,
          data: response.data
        };
      }
    } catch (error) {
      console.error('Error fetching races from backoffice:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to fetch races from backoffice'
      };
    }
  },

  /**
   * Update user race
   *
   * @param {number} raceId - ID of the race to select
   * @returns {Promise<Object>} Update result
   */
  updateRace: async (raceId) => {
    try {
      // Update the user's profile with the selected race
      const response = await api.put('/user/profile', {
        race_id: raceId
      });

      // If the update is successful, return the updated profile data
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('Error updating user race:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to update user race'
      };
    }
  }
};

export default profileService;
