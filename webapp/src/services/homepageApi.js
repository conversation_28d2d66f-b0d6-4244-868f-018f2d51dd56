/**
 * Homepage API Service
 *
 * This file provides access to the Homepage API endpoints.
 * Includes retry mechanism for failed requests and consistent error handling.
 */

import axios from 'axios';

/**
 * Retry a function with exponential backoff
 * @param {Function} fn - Function to retry
 * @param {number} maxRetries - Maximum number of retries
 * @param {number} baseDelay - Base delay in ms
 * @returns {Promise<any>} - Result of the function
 */
const retryWithBackoff = async (fn, maxRetries = 3, baseDelay = 300) => {
  let retries = 0;

  const execute = async () => {
    try {
      return await fn();
    } catch (error) {
      // Don't retry if we've reached the max retries
      if (retries >= maxRetries) {
        throw error;
      }

      // Don't retry for certain error types
      if (error.response) {
        // Don't retry for client errors (4xx) except for 408 (timeout) and 429 (too many requests)
        if (error.response.status >= 400 && error.response.status < 500 &&
            error.response.status !== 408 && error.response.status !== 429) {
          throw error;
        }
      }

      // Calculate delay with exponential backoff and jitter
      const delay = Math.min(baseDelay * Math.pow(2, retries) + Math.random() * 100, 5000);
      console.log(`Retrying API call (${retries + 1}/${maxRetries}) after ${delay}ms...`);

      // Wait for the delay
      await new Promise(resolve => setTimeout(resolve, delay));

      // Increment retries and try again
      retries++;
      return execute();
    }
  };

  return execute();
};

// Create base API instance with better fallback and debugging
// Ensure we're using port 8001 for the API as specified in the requirements
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8001/api';

console.log('Homepage API using base URL:', API_BASE_URL);

// Create axios instance with authentication
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  // Add timeout to prevent hanging requests (30 seconds)
  timeout: 30000,
});

// Add request interceptor to include authentication token if available
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    // Log error details for debugging
    console.error('Homepage API Error:', {
      status: error.response?.status,
      data: error.response?.data,
      url: error.config?.url,
      method: error.config?.method,
    });

    // Handle specific error cases
    if (error.code === 'ECONNABORTED') {
      console.error('Request timeout - server might be down or unreachable');
    } else if (!error.response) {
      console.error('Network error - no response received from server');
    } else if (error.response.status === 401) {
      console.error('Authentication error - user might need to log in again');
      // Optionally redirect to login page or refresh token
    } else if (error.response.status === 403) {
      console.error('Authorization error - user might not have permission');
    } else if (error.response.status === 404) {
      console.error('Resource not found - API endpoint might be incorrect');
    } else if (error.response.status >= 500) {
      console.error('Server error - backend might be experiencing issues');
    }

    return Promise.reject(error);
  }
);

// Homepage API service
export const homepageApi = {
  /**
   * Get all homepage data in a single API call
   * @param {Object} options - Options for the API call
   * @param {number} options.perPage - Number of talents per page
   * @param {number} options.limit - Number of service types to return
   * @returns {Promise<Object>} - Homepage data
   */
  getAllHomepageData: async (options = {}) => {
    const { perPage = 10, limit = 10 } = options;
    const queryParams = new URLSearchParams();

    if (perPage) queryParams.append('per_page', perPage);
    if (limit) queryParams.append('limit', limit);

    // Use retry mechanism for better reliability
    return retryWithBackoff(async () => {
      console.log('Attempting to fetch homepage data...');
      const response = await api.get(`/homepage?${queryParams.toString()}`);
      console.log('Homepage data fetched successfully');
      return response;
    });
  },

  /**
   * Get new registered talents
   * @param {Object} options - Options for the API call
   * @param {number} options.page - Page number
   * @param {number} options.perPage - Number of talents per page
   * @returns {Promise<Object>} - New talents data with pagination
   */
  getNewTalents: async (options = {}) => {
    const { page = 1, perPage = 10 } = options;
    const queryParams = new URLSearchParams();

    if (page) queryParams.append('page', page);
    if (perPage) queryParams.append('per_page', perPage);

    return retryWithBackoff(async () => {
      console.log(`Fetching new talents (page ${page})...`);
      const response = await api.get(`/homepage/new-talents?${queryParams.toString()}`);
      console.log(`New talents fetched successfully (page ${page})`);
      return response;
    });
  },

  /**
   * Get recommended talents
   * @param {Object} options - Options for the API call
   * @param {number} options.page - Page number
   * @param {number} options.perPage - Number of talents per page
   * @returns {Promise<Object>} - Recommended talents data with pagination
   */
  getRecommendedTalents: async (options = {}) => {
    const { page = 1, perPage = 10 } = options;
    const queryParams = new URLSearchParams();

    if (page) queryParams.append('page', page);
    if (perPage) queryParams.append('per_page', perPage);

    return retryWithBackoff(async () => {
      console.log(`Fetching recommended talents (page ${page})...`);
      const response = await api.get(`/homepage/recommended-talents?${queryParams.toString()}`);
      console.log(`Recommended talents fetched successfully (page ${page})`);
      return response;
    });
  },

  /**
   * Get online talents
   * @param {Object} options - Options for the API call
   * @param {number} options.page - Page number
   * @param {number} options.perPage - Number of talents per page
   * @returns {Promise<Object>} - Online talents data with pagination
   */
  getOnlineTalents: async (options = {}) => {
    const { page = 1, perPage = 10 } = options;
    const queryParams = new URLSearchParams();

    if (page) queryParams.append('page', page);
    if (perPage) queryParams.append('per_page', perPage);

    return retryWithBackoff(async () => {
      console.log(`Fetching online talents (page ${page})...`);
      const response = await api.get(`/homepage/online-talents?${queryParams.toString()}`);
      console.log(`Online talents fetched successfully (page ${page})`);
      return response;
    });
  },

  /**
   * Get available missions count
   * @returns {Promise<Object>} - Available missions count
   */
  getAvailableMissionsCount: async () => {
    return retryWithBackoff(async () => {
      console.log('Fetching available missions count...');
      const response = await api.get('/homepage/available-missions-count');
      console.log('Available missions count fetched successfully');
      return response;
    });
  },

  /**
   * Get popular service types
   * @param {Object} options - Options for the API call
   * @param {number} options.limit - Number of service types to return
   * @returns {Promise<Object>} - Popular service types
   */
  getPopularServiceTypes: async (options = {}) => {
    const { limit = 10 } = options;
    const queryParams = new URLSearchParams();

    if (limit) queryParams.append('limit', limit);

    return retryWithBackoff(async () => {
      console.log('Fetching popular service types...');
      const response = await api.get(`/homepage/popular-service-types?${queryParams.toString()}`);
      console.log('Popular service types fetched successfully');
      return response;
    });
  },

  /**
   * Get carousel slides
   * @returns {Promise<Object>} - Carousel slides
   */
  getCarouselSlides: async () => {
    return retryWithBackoff(async () => {
      console.log('Fetching carousel slides...');
      const response = await api.get('/carousel-slides');
      console.log('Carousel slides fetched successfully');
      return response;
    });
  }
};

export default homepageApi;
