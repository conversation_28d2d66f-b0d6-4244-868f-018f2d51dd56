import axios from 'axios';

// Create base API instance
const API_BASE_URL = process.env.REACT_APP_API_URL || 'https://api.mission-x.com/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to include auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Simple in-memory cache
const cache = {
  serviceTypes: null,
  serviceStyles: null,
  platforms: null,
  languages: null,
  levels: null,
  // Cache expiration time in milliseconds (30 minutes)
  expirationTime: 30 * 60 * 1000,
  timestamps: {
    serviceTypes: 0,
    serviceStyles: 0,
    platforms: 0,
    languages: 0,
    levels: 0
  }
};

// Check if cache is valid
const isCacheValid = (key) => {
  const timestamp = cache.timestamps[key];
  return timestamp && (Date.now() - timestamp < cache.expirationTime);
};

export const referenceDataApi = {
  /**
   * Get all service types
   * @returns {Promise<Object>} Response with service types
   */
  getServiceTypes: async () => {
    try {
      // Return from cache if valid
      if (isCacheValid('serviceTypes') && cache.serviceTypes) {
        return { data: cache.serviceTypes };
      }

      const response = await api.get('/service-types');

      // Update cache
      cache.serviceTypes = response.data;
      cache.timestamps.serviceTypes = Date.now();

      return response;
    } catch (error) {
      console.error('Error fetching service types:', error);
      throw error;
    }
  },

  /**
   * Get all service styles
   * @returns {Promise<Object>} Response with service styles
   */
  getServiceStyles: async () => {
    try {
      // Return from cache if valid
      if (isCacheValid('serviceStyles') && cache.serviceStyles) {
        return { data: cache.serviceStyles };
      }

      const response = await api.get('/service-styles');

      // Update cache
      cache.serviceStyles = response.data;
      cache.timestamps.serviceStyles = Date.now();

      return response;
    } catch (error) {
      console.error('Error fetching service styles:', error);
      throw error;
    }
  },

  /**
   * Get all levels
   * @returns {Promise<Object>} Response with levels
   */
  getLevels: async () => {
    try {
      // Return from cache if valid
      if (isCacheValid('levels') && cache.levels) {
        return { data: cache.levels };
      }

      const response = await api.get('/levels');

      // Update cache
      cache.levels = response.data;
      cache.timestamps.levels = Date.now();

      return response;
    } catch (error) {
      console.error('Error fetching levels:', error);
      throw error;
    }
  },

  /**
   * Get all platforms
   * @returns {Promise<Object>} Response with platforms
   */
  getPlatforms: async () => {
    try {
      // Return from cache if valid
      if (isCacheValid('platforms') && cache.platforms) {
        return { data: cache.platforms };
      }

      const response = await api.get('/platforms');

      // Update cache
      cache.platforms = response.data;
      cache.timestamps.platforms = Date.now();

      return response;
    } catch (error) {
      console.error('Error fetching platforms:', error);

      // Return fallback data if API fails
      return {
        data: [
          { id: 1, name: 'PC', code: 'pc' },
          { id: 2, name: 'Mobile', code: 'mobile' },
          { id: 3, name: 'Console', code: 'console' }
        ]
      };
    }
  },

  /**
   * Get all languages
   * @returns {Promise<Object>} Response with languages
   */
  getLanguages: async () => {
    try {
      // Return from cache if valid
      if (isCacheValid('languages') && cache.languages) {
        return { data: cache.languages };
      }

      const response = await api.get('/languages');

      // Update cache
      cache.languages = response.data;
      cache.timestamps.languages = Date.now();

      return response;
    } catch (error) {
      console.error('Error fetching languages:', error);

      // Return fallback data if API fails
      return {
        data: [
          { id: 1, name: 'English', code: 'en' },
          { id: 2, name: 'Chinese', code: 'zh' },
          { id: 3, name: 'Japanese', code: 'ja' },
          { id: 4, name: 'Korean', code: 'ko' }
        ]
      };
    }
  },

  /**
   * Clear all cached data
   */
  clearCache: () => {
    cache.serviceTypes = null;
    cache.serviceStyles = null;
    cache.platforms = null;
    cache.languages = null;
    cache.levels = null;
    cache.timestamps.serviceTypes = 0;
    cache.timestamps.serviceStyles = 0;
    cache.timestamps.platforms = 0;
    cache.timestamps.languages = 0;
    cache.timestamps.levels = 0;
  }
};

export default referenceDataApi;
