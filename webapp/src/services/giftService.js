import axios from 'axios';

const API_URL = process.env.REACT_APP_API_URL || '';
const BACKOFFICE_API_URL = process.env.REACT_APP_BACKOFFICE_API_URL || 'https://backoffice.missionx.com/api';
const CDN_URL = process.env.REACT_APP_CDN_URL || 'http://localhost:8080/cdn';

// Debug flag - enable for diagnostic logging
const DEBUG_ENABLED = false;

// Property name mapping between different API sources
const PROPERTY_MAPPINGS = {
  // Gift item properties
  id: ['id', 'gift_id', 'gift_item_id'],
  name: ['name', 'gift_name', 'title'],
  price: ['price', 'credit_price', 'cost', 'gift_price'],
  category: ['category', 'category_name', 'gift_category'],
  image_url: ['image_url', 'icon_path', 'image_path', 'icon_url', 'thumbnail'],

  // User gift properties
  gift_item_id: ['gift_item_id', 'gift_id', 'item_id'],
  quantity: ['quantity', 'count', 'amount'],
  user_gift_id: ['id', 'user_gift_id']
};

// Cache settings
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds
const giftCache = {
  items: null,
  categories: null,
  userGifts: null,
  giftHistory: null,
  giftAnalytics: null,
  popularGifts: null,
  giftPreferences: null,
  timestamps: {
    items: 0,
    categories: 0,
    userGifts: 0,
    giftHistory: 0,
    giftAnalytics: 0,
    popularGifts: 0,
    giftPreferences: 0
  }
};

// Diagnostic logging function
const debugLog = (message, data) => {
  if (!DEBUG_ENABLED) return;

  console.group(`🎁 Gift Service: ${message}`);
  if (data) {
    console.log('Data:', data);

    // Log object structure (property names and types)
    if (typeof data === 'object' && data !== null) {
      console.log('Object structure:');
      const structure = {};

      Object.keys(data).forEach(key => {
        const value = data[key];
        if (Array.isArray(value) && value.length > 0) {
          structure[key] = `Array[${value.length}]: ${typeof value[0]} with properties [${Object.keys(value[0]).join(', ')}]`;
        } else {
          structure[key] = typeof value;
        }
      });

      console.table(structure);
    }
  }
  console.groupEnd();
};

// Debug function to check expected vs actual data format
const validateDataFormat = (actual, expected, context) => {
  if (!DEBUG_ENABLED) return;

  const missingProperties = [];
  const unexpectedProperties = [];
  const actualProps = new Set(Object.keys(actual));

  // Check for missing expected properties
  expected.forEach(prop => {
    if (!actualProps.has(prop)) {
      missingProperties.push(prop);
    }
  });

  // Check for unexpected properties
  actualProps.forEach(prop => {
    if (!expected.includes(prop)) {
      unexpectedProperties.push(prop);
    }
  });

  if (missingProperties.length > 0 || unexpectedProperties.length > 0) {
    console.group(`⚠️ Data format discrepancy in ${context}`);
    if (missingProperties.length > 0) {
      console.warn('Missing expected properties:', missingProperties);
    }
    if (unexpectedProperties.length > 0) {
      console.info('Unexpected properties:', unexpectedProperties);
    }
    console.groupEnd();
  }
};

/**
 * Normalize property names across different API responses
 * @param {Object} data - Original data object with inconsistent property names
 * @param {Object} mappings - Custom property mappings to use (optional)
 * @returns {Object} - Normalized data object with consistent property names
 */
const normalizeProperties = (data, mappings = PROPERTY_MAPPINGS) => {
  if (!data) return null;

  const normalized = {};

  // For each standard property name in our mapping
  Object.entries(mappings).forEach(([standardName, possibleNames]) => {
    // Find the first matching property in the data
    const matchingProp = possibleNames.find(name => data[name] !== undefined);

    // If found, add it to normalized object with the standard name
    if (matchingProp !== undefined) {
      normalized[standardName] = data[matchingProp];
    }
  });

  // Copy any properties not in our mapping
  Object.keys(data).forEach(key => {
    if (!Object.values(mappings).flat().includes(key)) {
      normalized[key] = data[key];
    }
  });

  return normalized;
};

/**
 * Normalize an array of objects using the property normalization function
 * @param {Array} items - Array of objects to normalize
 * @param {Object} mappings - Custom property mappings (optional)
 * @returns {Array} - Array of normalized objects
 */
const normalizeArray = (items, mappings = PROPERTY_MAPPINGS) => {
  if (!items || !Array.isArray(items)) return [];
  return items.map(item => normalizeProperties(item, mappings));
};

/**
 * Transform image URLs to absolute URLs if they are relative
 * @param {Array} items - Array of gift items
 * @param {String} baseUrl - Base URL to prepend to relative paths
 * @param {String} imageProperty - Property name containing the image URL
 * @returns {Array} - Array with transformed image URLs
 */
const transformImageUrls = (items, baseUrl = CDN_URL, imageProperty = 'image_url') => {
  if (!items || !Array.isArray(items)) return [];

  return items.map(item => {
    if (!item) return item;

    const result = { ...item };

    // Check if image property exists and is not already absolute
    if (result[imageProperty] && typeof result[imageProperty] === 'string' && !result[imageProperty].startsWith('http')) {
      // Remove leading slash if present in the path and the base URL ends with slash
      const imagePath = result[imageProperty].startsWith('/') ? result[imageProperty].substring(1) : result[imageProperty];
      result[imageProperty] = `${baseUrl}/${imagePath}`;
    }

    return result;
  });
};

// Create an axios instance with default config
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Create a backoffice API instance
const backofficeApi = axios.create({
  baseURL: BACKOFFICE_API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Same for backoffice API
backofficeApi.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    // Enhanced error logging
    const errorDetails = {
      message: 'API request failed',
      endpoint: error.config?.url || 'unknown',
      method: error.config?.method || 'unknown',
      params: error.config?.params,
      status: error.response?.status,
      responseData: error.response?.data
    };

    debugLog('API Error', errorDetails);

    // Handle authentication errors
    if (error.response && error.response.status === 401) {
      // Redirect to login or refresh token
      localStorage.removeItem('token');
      window.location.href = '/login';
    }

    // Enhance error object with useful information for debugging
    const enhancedError = {
      ...error,
      friendlyMessage: getFriendlyErrorMessage(error),
      details: errorDetails
    };

    return Promise.reject(enhancedError);
  }
);

// Same enhanced error handling for backoffice API
backofficeApi.interceptors.response.use(
  (response) => response,
  (error) => {
    // Enhanced error logging
    const errorDetails = {
      message: 'Backoffice API request failed',
      endpoint: error.config?.url || 'unknown',
      method: error.config?.method || 'unknown',
      params: error.config?.params,
      status: error.response?.status,
      responseData: error.response?.data
    };

    debugLog('Backoffice API Error', errorDetails);

    // Handle authentication errors
    if (error.response && error.response.status === 401) {
      // Redirect to login or refresh token
      localStorage.removeItem('token');
      window.location.href = '/login';
    }

    // Enhance error object with useful information for debugging
    const enhancedError = {
      ...error,
      friendlyMessage: getFriendlyErrorMessage(error),
      details: errorDetails,
      isBackofficeError: true
    };

    return Promise.reject(enhancedError);
  }
);

/**
 * Generate user-friendly error messages based on error type
 * @param {Object} error - Error object
 * @returns {String} - User-friendly error message
 */
const getFriendlyErrorMessage = (error) => {
  if (!error) return 'An unknown error occurred';

  // Response errors (server returned an error)
  if (error.response) {
    const status = error.response.status;

    switch (status) {
      case 400:
        return 'The request was invalid. Please check your input.';
      case 401:
        return 'You need to log in to continue.';
      case 403:
        return 'You do not have permission to perform this action.';
      case 404:
        return 'The requested resource was not found.';
      case 422:
        return error.response.data?.message || 'Validation failed. Please check your data.';
      case 429:
        return 'Too many requests. Please try again later.';
      case 500:
        return 'Server error. Please try again later.';
      default:
        return `Server returned error: ${status}`;
    }
  }

  // Network errors (no response from server)
  if (error.request) {
    return 'Network error. Please check your internet connection.';
  }

  // Other errors
  return error.message || 'An error occurred';
};

// Helper function to check if cache is valid
const isCacheValid = (cacheKey) => {
  const timestamp = giftCache.timestamps[cacheKey];
  if (!timestamp) return false;
  const isValid = Date.now() - timestamp < CACHE_DURATION;

  debugLog(`Cache check: ${cacheKey}`, {
    isValid,
    age: Date.now() - timestamp,
    maxAge: CACHE_DURATION,
    timestamp
  });

  return isValid;
};

// Helper to merge user inventory with gift catalog
const mergeGiftInventory = (giftItems, userGifts) => {
  debugLog('Merging gift inventory', {
    giftItemsCount: giftItems.length,
    userGiftsCount: userGifts.length,
    giftItemsSample: giftItems.slice(0, 2),
    userGiftsSample: userGifts.slice(0, 2)
  });

  // Validate inputs
  if (!giftItems || !Array.isArray(giftItems) || giftItems.length === 0) {
    debugLog('Warning: Empty or invalid gift items', { giftItems });
    return [];
  }

  if (!userGifts || !Array.isArray(userGifts)) {
    debugLog('Warning: Invalid user gifts', { userGifts });
    userGifts = []; // Proceed with empty array if userGifts is invalid
  }

  // Normalize gift items and user gifts to ensure consistent property names
  const normalizedGiftItems = normalizeArray(giftItems);
  const normalizedUserGifts = normalizeArray(userGifts);

  // Transform image URLs to ensure they are absolute
  const giftsWithAbsoluteUrls = transformImageUrls(normalizedGiftItems);

  // Create a map of user's gift inventory
  const userGiftMap = {};
  normalizedUserGifts.forEach(userGift => {
    // Only add to map if it has a valid gift_item_id
    if (userGift.gift_item_id) {
      userGiftMap[userGift.gift_item_id] = userGift;
    } else {
      debugLog('Warning: User gift missing gift_item_id', userGift);
    }
  });

  // Enhance gift items with inventory information
  const mergedGifts = giftsWithAbsoluteUrls.map(giftItem => {
    if (!giftItem.id) {
      debugLog('Warning: Gift item missing id', giftItem);
      return giftItem; // Skip enhancement if no ID
    }

    const userGift = userGiftMap[giftItem.id];
    return {
      ...giftItem,
      owned: !!userGift,
      quantity: userGift ? userGift.quantity : 0,
      user_gift_id: userGift ? userGift.user_gift_id : null
    };
  });

  debugLog('Merged gifts result sample', mergedGifts.slice(0, 2));

  return mergedGifts;
};

// Generate mock gift history if needed for development
const generateMockGiftHistory = (count = 20) => {
  const mockUsers = [
    { id: 101, name: 'Alice Johnson', avatar: 'https://i.pravatar.cc/150?u=a1' },
    { id: 102, name: 'Bob Smith', avatar: 'https://i.pravatar.cc/150?u=a2' },
    { id: 103, name: 'Carol White', avatar: 'https://i.pravatar.cc/150?u=a3' },
    { id: 104, name: 'David Brown', avatar: 'https://i.pravatar.cc/150?u=a4' },
    { id: 105, name: 'Eve Martinez', avatar: 'https://i.pravatar.cc/150?u=a5' }
  ];

  const mockGifts = giftCache.items || [
    { id: 1, name: 'Rose', image_url: 'https://via.placeholder.com/150?text=Rose', price: 100 },
    { id: 2, name: 'Heart', image_url: 'https://via.placeholder.com/150?text=Heart', price: 250 },
    { id: 3, name: 'Star', image_url: 'https://via.placeholder.com/150?text=Star', price: 150 },
    { id: 4, name: 'Diamond', image_url: 'https://via.placeholder.com/150?text=Diamond', price: 500 },
    { id: 5, name: 'Cake', image_url: 'https://via.placeholder.com/150?text=Cake', price: 200 }
  ];

  const currentUserId = parseInt(localStorage.getItem('user_id')) || 100;
  const now = new Date();

  const history = [];
  for (let i = 0; i < count; i++) {
    const isSent = Math.random() > 0.5;
    const randomUser = mockUsers[Math.floor(Math.random() * mockUsers.length)];
    const randomGift = mockGifts[Math.floor(Math.random() * mockGifts.length)];
    const dateDaysAgo = new Date(now);
    dateDaysAgo.setDate(now.getDate() - Math.floor(Math.random() * 30));

    history.push({
      id: `gift_${Date.now()}_${i}`,
      gift_id: randomGift.id,
      gift_name: randomGift.name,
      gift_image_url: randomGift.image_url,
      sender_id: isSent ? currentUserId : randomUser.id,
      sender_name: isSent ? 'You' : randomUser.name,
      sender_avatar: isSent ? null : randomUser.avatar,
      recipient_id: isSent ? randomUser.id : currentUserId,
      recipient_name: isSent ? randomUser.name : 'You',
      recipient_avatar: isSent ? randomUser.avatar : null,
      sent_at: dateDaysAgo.toISOString(),
      message: Math.random() > 0.3 ? `Here's a ${randomGift.name} for you!` : '',
      is_sent: isSent,
      is_received: !isSent
    });
  }

  // Sort by date (newest first)
  history.sort((a, b) => new Date(b.sent_at) - new Date(a.sent_at));

  return history;
};

// Generate mock analytics data
const generateMockAnalytics = () => {
  const mockHistory = giftCache.giftHistory || generateMockGiftHistory(50);

  // Get all sent and received gifts
  const sentGifts = mockHistory.filter(item => item.is_sent);
  const receivedGifts = mockHistory.filter(item => item.is_received);

  // Count by gift type
  const sentByType = {};
  const receivedByType = {};

  sentGifts.forEach(gift => {
    sentByType[gift.gift_name] = (sentByType[gift.gift_name] || 0) + 1;
  });

  receivedGifts.forEach(gift => {
    receivedByType[gift.gift_name] = (receivedByType[gift.gift_name] || 0) + 1;
  });

  // Get top 3 users you've gifted to
  const giftedToUsers = {};
  sentGifts.forEach(gift => {
    giftedToUsers[gift.recipient_id] = {
      count: (giftedToUsers[gift.recipient_id]?.count || 0) + 1,
      name: gift.recipient_name,
      avatar: gift.recipient_avatar
    };
  });

  const topGiftedTo = Object.entries(giftedToUsers)
    .map(([id, data]) => ({ id, ...data }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 3);

  // Get top 3 users who've gifted you
  const giftedByUsers = {};
  receivedGifts.forEach(gift => {
    giftedByUsers[gift.sender_id] = {
      count: (giftedByUsers[gift.sender_id]?.count || 0) + 1,
      name: gift.sender_name,
      avatar: gift.sender_avatar
    };
  });

  const topGiftedBy = Object.entries(giftedByUsers)
    .map(([id, data]) => ({ id, ...data }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 3);

  // Monthly trends (last 6 months)
  const now = new Date();
  const monthlyTrends = [];

  for (let i = 0; i < 6; i++) {
    const monthDate = new Date(now);
    monthDate.setMonth(now.getMonth() - i);
    const monthName = monthDate.toLocaleString('default', { month: 'short' });
    const year = monthDate.getFullYear();
    const month = monthDate.getMonth();

    const sentInMonth = sentGifts.filter(gift => {
      const giftDate = new Date(gift.sent_at);
      return giftDate.getMonth() === month && giftDate.getFullYear() === year;
    }).length;

    const receivedInMonth = receivedGifts.filter(gift => {
      const giftDate = new Date(gift.sent_at);
      return giftDate.getMonth() === month && giftDate.getFullYear() === year;
    }).length;

    monthlyTrends.unshift({
      month: monthName,
      year,
      sent: sentInMonth,
      received: receivedInMonth
    });
  }

  return {
    total_sent: sentGifts.length,
    total_received: receivedGifts.length,
    sent_by_type: sentByType,
    received_by_type: receivedByType,
    top_gifted_to: topGiftedTo,
    top_gifted_by: topGiftedBy,
    monthly_trends: monthlyTrends,
    most_gifted: Object.entries(sentByType)
      .map(([name, count]) => ({ name, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 3),
    most_received: Object.entries(receivedByType)
      .map(([name, count]) => ({ name, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 3)
  };
};

// Calculate gift recommendations based on user history and preferences
const calculateGiftRecommendations = (history, preferences, allGifts) => {
  if (!history || !allGifts || !allGifts.length) return [];

  // Default weights if no preferences
  const weights = {
    popularityWeight: 0.4,
    personalHistoryWeight: 0.3,
    priceWeight: 0.2,
    categoryWeight: 0.1,
    ...preferences
  };

  // Gather data from history
  const receivedGifts = history.filter(item => item.is_received);
  const receivedCounts = {};
  receivedGifts.forEach(gift => {
    receivedCounts[gift.gift_id] = (receivedCounts[gift.gift_id] || 0) + 1;
  });

  // Get popular categories from received gifts
  const categoryCount = {};
  receivedGifts.forEach(gift => {
    const targetGift = allGifts.find(g => g.id === gift.gift_id);
    if (targetGift && targetGift.category) {
      categoryCount[targetGift.category] = (categoryCount[targetGift.category] || 0) + 1;
    }
  });

  const totalReceived = receivedGifts.length || 1;
  const popularCategories = Object.entries(categoryCount)
    .map(([category, count]) => ({ category, score: count / totalReceived }))
    .sort((a, b) => b.score - a.score);

  // Score each gift
  const scoredGifts = allGifts.map(gift => {
    // Popularity score (0-1) - how often this gift appears in all history
    const popularityScore = (receivedCounts[gift.id] || 0) / (totalReceived || 1);

    // Category score (0-1) - how much this gift's category matches preferred categories
    const categoryScore = popularCategories.findIndex(c => c.category === gift.category) > -1
      ? popularCategories.find(c => c.category === gift.category).score
      : 0;

    // Price score (0-1) - normalized price score, with mid-range prices scoring higher
    // Assume 1000 is the max price for normalization
    const normalizedPrice = Math.min(gift.price / 1000, 1);
    const priceScore = 1 - Math.abs(0.5 - normalizedPrice); // Mid-range prices get higher scores

    // Calculate weighted score
    const score =
      weights.popularityWeight * popularityScore +
      weights.categoryWeight * categoryScore +
      weights.priceWeight * priceScore;

    return {
      ...gift,
      recommendation_score: score
    };
  });

  // Return top recommendations, sorted by score
  return scoredGifts
    .sort((a, b) => b.recommendation_score - a.recommendation_score)
    .slice(0, 15);
};

/**
 * Safely execute an API call with fallback and error handling
 * @param {Function} primaryFn - Primary API function to call
 * @param {Function} fallbackFn - Fallback function if primary fails
 * @param {String} errorMessage - Error message for logging
 * @returns {Promise<Object>} API response
 */
const safeApiCall = async (primaryFn, fallbackFn, errorMessage) => {
  try {
    return await primaryFn();
  } catch (error) {
    debugLog(`${errorMessage}. Trying fallback.`, error);

    try {
      // Only try fallback if provided
      if (fallbackFn) {
        return await fallbackFn();
      } else {
        throw error; // Re-throw if no fallback
      }
    } catch (fallbackError) {
      debugLog(`Fallback also failed for: ${errorMessage}`, fallbackError);
      throw fallbackError;
    }
  }
};

const giftAPI = {
  // Get all available gift items (now with caching and backoffice integration)
  getGiftItems: async (forceRefresh = false) => {
    // Check cache first unless forced refresh
    if (!forceRefresh && isCacheValid('items') && giftCache.items) {
      debugLog('Using cached gift items', {
        count: giftCache.items.length,
        sample: giftCache.items.slice(0, 2)
      });
      return { data: { gifts: giftCache.items } };
    }

    try {
      debugLog('Fetching gift items from main API');

      // Use main API as primary source (as per your backend route)
      const response = await api.get('/gifts');

      debugLog('Main API gift items response', {
        status: response.status,
        fullResponse: response.data,
        itemCount: response.data.gift_items?.length || 0,
        sample: response.data.gift_items?.slice(0, 2) || []
      });

      // Handle the correct data structure from backend API
      let giftItems = [];
      if (response.data.gift_items && Array.isArray(response.data.gift_items)) {
        giftItems = response.data.gift_items;
      } else {
        debugLog('Unexpected data structure from main API', response.data);
        giftItems = [];
      }

      // Check for expected data structure
      if (giftItems && giftItems.length > 0) {
        const sampleGift = giftItems[0];
        validateDataFormat(sampleGift, ['id', 'name', 'credit_price', 'icon_path'], 'Main API Gift Item');
      }

      // Normalize and transform gift items
      const normalizedGifts = normalizeArray(giftItems);

      // Ensure images use the configured CDN base URL
      const giftsWithAbsoluteUrls = transformImageUrls(normalizedGifts, CDN_URL, 'image_url');

      // Update cache
      giftCache.items = giftsWithAbsoluteUrls;
      giftCache.timestamps.items = Date.now();

      debugLog('Successfully cached gift items', {
        count: giftsWithAbsoluteUrls.length
      });

      return { data: { gifts: giftsWithAbsoluteUrls } };
    } catch (error) {
      console.error('Error fetching gift items from main API:', error);
      debugLog('Main API error fetching gifts', {
        message: error.message,
        friendlyMessage: error.friendlyMessage,
        status: error.response?.status,
        responseData: error.response?.data
      });

      // Return empty array rather than throwing
      return { data: { gifts: [] }, error: error.friendlyMessage };
    }
  },

  // Get gift categories (disabled - no categories in this system)
  getGiftCategories: async (forceRefresh = false) => {
    // Return empty categories since this system doesn't use categories
    debugLog('Gift categories disabled - returning empty array');
    return { data: { categories: [] } };
  },

  // Get filtered gift items by category (disabled - no categories in this system)
  getGiftItemsByCategory: async (categorySlug) => {
    // Just return all gifts since categories are disabled
    return await giftAPI.getGiftItems();
  },

  // Get user's gift inventory with caching
  getUserGifts: async (forceRefresh = false) => {
    // Check cache first unless forced refresh
    if (!forceRefresh && isCacheValid('userGifts') && giftCache.userGifts) {
      debugLog('Using cached user gifts', {
        count: giftCache.userGifts.length,
        sample: giftCache.userGifts.slice(0, 2)
      });
      return { data: { user_gifts: giftCache.userGifts } };
    }

    try {
      debugLog('Fetching user gifts');
      const response = await api.get('/user/gifts');

      debugLog('User gifts response', {
        status: response.status,
        itemCount: response.data.user_gifts?.length || 0,
        sample: response.data.user_gifts?.slice(0, 2) || []
      });

      // Check for expected data structure
      if (response.data.user_gifts && response.data.user_gifts.length > 0) {
        const sampleUserGift = response.data.user_gifts[0];
        validateDataFormat(sampleUserGift, ['id', 'gift_item_id', 'quantity'], 'User Gift Item');
      }

      // Normalize user gifts data
      const normalizedUserGifts = normalizeArray(response.data.user_gifts || []);

      // Update cache
      giftCache.userGifts = normalizedUserGifts;
      giftCache.timestamps.userGifts = Date.now();

      debugLog('Updated user gifts cache', {
        count: giftCache.userGifts.length,
        timestamp: giftCache.timestamps.userGifts
      });

      return { data: { user_gifts: normalizedUserGifts } };
    } catch (error) {
      console.error('Error fetching user gifts:', error);
      debugLog('User gifts API error', {
        message: error.message,
        friendlyMessage: error.friendlyMessage,
        response: error.response?.data,
        status: error.response?.status
      });

      // Return empty array instead of throwing
      return { data: { user_gifts: [] }, error: error.friendlyMessage };
    }
  },

  // Get both gift catalog and user inventory and merge them
  getEnhancedGiftCatalog: async (categorySlug = null) => {
    try {
      debugLog(`Fetching enhanced gift catalog for category: ${categorySlug || 'all'}`);

      // Get both gift items and user gifts in parallel
      // If one fails, we'll still have partial data
      let giftItems = [];
      let userGifts = [];
      let errors = [];

      try {
        const giftItemsResponse = await (categorySlug
          ? giftAPI.getGiftItemsByCategory(categorySlug)
          : giftAPI.getGiftItems());
        giftItems = giftItemsResponse.data.gifts || [];

        if (giftItemsResponse.error) {
          errors.push({ source: 'gift_items', message: giftItemsResponse.error });
        }
      } catch (giftItemsError) {
        console.error('Error fetching gift items for catalog:', giftItemsError);
        errors.push({
          source: 'gift_items',
          message: giftItemsError.friendlyMessage || 'Could not load gift items'
        });
      }

      try {
        const userGiftsResponse = await giftAPI.getUserGifts();
        userGifts = userGiftsResponse.data.user_gifts || [];

        if (userGiftsResponse.error) {
          errors.push({ source: 'user_gifts', message: userGiftsResponse.error });
        }
      } catch (userGiftsError) {
        console.error('Error fetching user gifts for catalog:', userGiftsError);
        errors.push({
          source: 'user_gifts',
          message: userGiftsError.friendlyMessage || 'Could not load your gift inventory'
        });
      }

      debugLog('Enhanced gift catalog fetched data', {
        giftItemsCount: giftItems.length,
        userGiftsCount: userGifts.length,
        hasErrors: errors.length > 0,
        errors,
        categorySlug
      });

      // Merge the data
      const enhancedGifts = mergeGiftInventory(giftItems, userGifts);

      debugLog('Enhanced gift catalog result', {
        totalCount: enhancedGifts.length,
        ownedCount: enhancedGifts.filter(g => g.owned).length,
        sample: enhancedGifts.slice(0, 2)
      });

      return {
        data: { gifts: enhancedGifts },
        partial: errors.length > 0,
        errors: errors.length > 0 ? errors : undefined
      };
    } catch (error) {
      console.error('Error fetching enhanced gift catalog:', error);
      debugLog('Enhanced gift catalog error', {
        message: error.message,
        friendlyMessage: error.friendlyMessage,
        response: error.response?.data,
        status: error.response?.status
      });

      // Return empty array instead of throwing
      return {
        data: { gifts: [] },
        error: error.friendlyMessage || 'Could not load gift catalog'
      };
    }
  },

  // Purchase a gift item
  purchaseGift: async (giftItemId, quantity = 1) => {
    debugLog('Purchasing gift', { giftItemId, quantity });

    // Invalidate user gifts cache after purchase
    giftCache.timestamps.userGifts = 0;
    debugLog('Invalidated user gifts cache for purchase');

    try {
      const response = await api.post('/gifts/purchase', {
        gift_item_id: giftItemId,
        quantity
      });

      debugLog('Gift purchase successful', {
        giftItemId,
        quantity,
        response: response.data
      });

      return response;
    } catch (error) {
      console.error('Gift purchase failed:', {
        giftItemId,
        quantity,
        error: error.response?.data || error.message,
        status: error.response?.status
      });

      // Re-throw with more context
      const enhancedError = new Error(
        error.response?.data?.message ||
        'Failed to purchase gift. Please check your credits balance and try again.'
      );
      enhancedError.originalError = error;
      enhancedError.giftItemId = giftItemId;
      enhancedError.quantity = quantity;

      throw enhancedError;
    }
  },

  // Sell a gift back to the platform
  sellGift: (userGiftId, quantity = 1) => {
    debugLog('Selling gift', { userGiftId, quantity });

    // Invalidate user gifts cache after selling
    giftCache.timestamps.userGifts = 0;
    debugLog('Invalidated user gifts cache for sale');

    return api.post(`/user/gifts/${userGiftId}/sell`, {
      quantity
    });
  },

  // Gift an item to another user
  giftToUser: (userGiftId, recipientUserId, quantity = 1) => {
    debugLog('Gifting to user', { userGiftId, recipientUserId, quantity });

    // Invalidate user gifts cache after gifting
    giftCache.timestamps.userGifts = 0;
    giftCache.timestamps.giftHistory = 0;

    debugLog('Invalidated caches for gifting', {
      invalidated: ['userGifts', 'giftHistory']
    });

    return api.post('/user/gifts/gift', {
      user_gift_id: userGiftId,
      recipient_user_id: recipientUserId,
      quantity
    });
  },

  // Get gift transaction history
  getTransactionHistory: (limit = 10, offset = 0) => {
    return api.get(`/user/gifts/transactions?limit=${limit}&offset=${offset}`);
  },

  // Get gift statistics
  getGiftStatistics: () => {
    return api.get('/user/gifts/statistics');
  },

  // Get gift contributors
  getGiftContributors: () => {
    return api.get('/user/gifts/contributors');
  },

  // Get gift inventory statistics
  getInventoryStatistics: () => {
    return api.get('/user/gifts/inventory-statistics');
  },

  // Redeem gift with points
  redeemGift: (giftItemId, quantity = 1) => {
    // Invalidate user gifts cache after redemption
    giftCache.timestamps.userGifts = 0;

    return api.post('/gifts/redeem', {
      gift_item_id: giftItemId,
      quantity
    });
  },

  // Clear gift caches (useful for testing or forcing refreshes)
  clearCache: (cacheKey = null) => {
    if (cacheKey) {
      giftCache.timestamps[cacheKey] = 0;
      giftCache[cacheKey] = null;
      debugLog(`Cleared cache for ${cacheKey}`);
    } else {
      // Clear all caches
      Object.keys(giftCache.timestamps).forEach(key => {
        giftCache.timestamps[key] = 0;
        giftCache[key] = null;
      });
      debugLog('Cleared all caches');
    }
  },

  // Get gift history (sent and received)
  getGiftHistory: async (forceRefresh = false, limit = 50, offset = 0) => {
    // Check cache first unless forced refresh
    if (!forceRefresh && isCacheValid('giftHistory') && giftCache.giftHistory) {
      return { data: { history: giftCache.giftHistory } };
    }

    try {
      const response = await api.get(`/gifts/history?limit=${limit}&offset=${offset}`);

      // Update cache
      giftCache.giftHistory = response.data.history || [];
      giftCache.timestamps.giftHistory = Date.now();

      return response;
    } catch (error) {
      console.error('Error fetching gift history:', error);

      // In development, return mock data
      if (process.env.NODE_ENV === 'development') {
        const mockHistory = generateMockGiftHistory(50);
        giftCache.giftHistory = mockHistory;
        giftCache.timestamps.giftHistory = Date.now();

        return { data: { history: mockHistory } };
      }

      throw error;
    }
  },

  // Get gift analytics
  getGiftAnalytics: async (forceRefresh = false) => {
    // Check cache first unless forced refresh
    if (!forceRefresh && isCacheValid('giftAnalytics') && giftCache.giftAnalytics) {
      return { data: { analytics: giftCache.giftAnalytics } };
    }

    try {
      const response = await api.get('/gifts/analytics');

      // Update cache
      giftCache.giftAnalytics = response.data.analytics || {};
      giftCache.timestamps.giftAnalytics = Date.now();

      return response;
    } catch (error) {
      console.error('Error fetching gift analytics:', error);

      // In development, return mock data
      if (process.env.NODE_ENV === 'development') {
        // First ensure we have history data
        if (!giftCache.giftHistory) {
          await giftAPI.getGiftHistory();
        }

        const mockAnalytics = generateMockAnalytics();
        giftCache.giftAnalytics = mockAnalytics;
        giftCache.timestamps.giftAnalytics = Date.now();

        return { data: { analytics: mockAnalytics } };
      }

      throw error;
    }
  },

  // Get popular gifts recommendations
  getPopularGifts: async (forceRefresh = false, limit = 10) => {
    // Check cache first unless forced refresh
    if (!forceRefresh && isCacheValid('popularGifts') && giftCache.popularGifts) {
      return { data: { gifts: giftCache.popularGifts } };
    }

    try {
      const response = await api.get(`/gifts/popular?limit=${limit}`);

      // Update cache
      giftCache.popularGifts = response.data.gifts || [];
      giftCache.timestamps.popularGifts = Date.now();

      return response;
    } catch (error) {
      console.error('Error fetching popular gifts:', error);

      // Fallback to algorithm-based popular gifts
      // First ensure we have all needed data
      if (!giftCache.items) {
        await giftAPI.getGiftItems();
      }

      if (!giftCache.giftHistory) {
        await giftAPI.getGiftHistory();
      }

      const allGifts = giftCache.items || [];
      const history = giftCache.giftHistory || [];
      const preferences = giftCache.giftPreferences || {};

      const recommendations = calculateGiftRecommendations(history, preferences, allGifts)
        .slice(0, limit);

      giftCache.popularGifts = recommendations;
      giftCache.timestamps.popularGifts = Date.now();

      return { data: { gifts: recommendations } };
    }
  },

  // Get personalized gift recommendations
  getRecommendedGifts: async (forceRefresh = false, limit = 10) => {
    try {
      // First ensure we have all needed data
      if (!giftCache.items || forceRefresh) {
        await giftAPI.getGiftItems(forceRefresh);
      }

      if (!giftCache.giftHistory || forceRefresh) {
        await giftAPI.getGiftHistory(forceRefresh);
      }

      if (!giftCache.giftPreferences || forceRefresh) {
        await giftAPI.getGiftPreferences(forceRefresh);
      }

      const allGifts = giftCache.items || [];
      const history = giftCache.giftHistory || [];
      const preferences = giftCache.giftPreferences || {};

      const recommendations = calculateGiftRecommendations(history, preferences, allGifts)
        .slice(0, limit);

      return { data: { gifts: recommendations } };
    } catch (error) {
      console.error('Error generating gift recommendations:', error);
      throw error;
    }
  },

  // Get user gift preferences
  getGiftPreferences: async (forceRefresh = false) => {
    // Check cache first unless forced refresh
    if (!forceRefresh && isCacheValid('giftPreferences') && giftCache.giftPreferences) {
      return { data: { preferences: giftCache.giftPreferences } };
    }

    try {
      const response = await api.get('/user/gift-preferences');

      // Update cache
      giftCache.giftPreferences = response.data.preferences || {};
      giftCache.timestamps.giftPreferences = Date.now();

      return response;
    } catch (error) {
      console.error('Error fetching gift preferences:', error);

      // In development, return default preferences
      if (process.env.NODE_ENV === 'development') {
        const defaultPreferences = {
          popularityWeight: 0.35,
          personalHistoryWeight: 0.35,
          priceWeight: 0.15,
          categoryWeight: 0.15,
          favoritedGifts: []
        };

        giftCache.giftPreferences = defaultPreferences;
        giftCache.timestamps.giftPreferences = Date.now();

        return { data: { preferences: defaultPreferences } };
      }

      throw error;
    }
  },

  // Update user gift preferences
  updateGiftPreferences: async (preferences) => {
    try {
      const response = await api.put('/user/gift-preferences', preferences);

      // Update cache
      giftCache.giftPreferences = response.data.preferences || {};
      giftCache.timestamps.giftPreferences = Date.now();

      // Invalidate recommendations
      giftCache.timestamps.popularGifts = 0;

      return response;
    } catch (error) {
      console.error('Error updating gift preferences:', error);

      // In development mode, mock the response
      if (process.env.NODE_ENV === 'development') {
        // Update the cache directly
        giftCache.giftPreferences = {
          ...giftCache.giftPreferences,
          ...preferences
        };
        giftCache.timestamps.giftPreferences = Date.now();

        return { data: { preferences: giftCache.giftPreferences } };
      }

      throw error;
    }
  },

  // Add gift to favorites
  addToFavorites: async (giftId) => {
    try {
      const response = await api.post('/user/favorites/gifts', { gift_id: giftId });

      // Update preferences cache
      if (giftCache.giftPreferences) {
        giftCache.giftPreferences.favoritedGifts = [
          ...(giftCache.giftPreferences.favoritedGifts || []),
          giftId
        ];
      }

      return response;
    } catch (error) {
      console.error('Error adding gift to favorites:', error);

      // In development mode
      if (process.env.NODE_ENV === 'development') {
        // Update in cache
        if (!giftCache.giftPreferences) {
          await giftAPI.getGiftPreferences();
        }

        if (giftCache.giftPreferences) {
          giftCache.giftPreferences.favoritedGifts = [
            ...(giftCache.giftPreferences.favoritedGifts || []),
            giftId
          ];
        }

        return { data: { success: true } };
      }

      throw error;
    }
  },

  // Remove gift from favorites
  removeFromFavorites: async (giftId) => {
    try {
      const response = await api.delete(`/user/favorites/gifts/${giftId}`);

      // Update preferences cache
      if (giftCache.giftPreferences && giftCache.giftPreferences.favoritedGifts) {
        giftCache.giftPreferences.favoritedGifts = giftCache.giftPreferences.favoritedGifts
          .filter(id => id !== giftId);
      }

      return response;
    } catch (error) {
      console.error('Error removing gift from favorites:', error);

      // In development mode
      if (process.env.NODE_ENV === 'development') {
        // Update in cache
        if (giftCache.giftPreferences && giftCache.giftPreferences.favoritedGifts) {
          giftCache.giftPreferences.favoritedGifts = giftCache.giftPreferences.favoritedGifts
            .filter(id => id !== giftId);
        }

        return { data: { success: true } };
      }

      throw error;
    }
  },

  // Get favorite gifts
  getFavoriteGifts: async () => {
    try {
      // First get preferences to get favorited IDs
      if (!giftCache.giftPreferences || !isCacheValid('giftPreferences')) {
        await giftAPI.getGiftPreferences();
      }

      const favoriteIds = giftCache.giftPreferences?.favoritedGifts || [];

      // Then get all gifts to find the favorites
      if (!giftCache.items || !isCacheValid('items')) {
        await giftAPI.getGiftItems();
      }

      const allGifts = giftCache.items || [];
      const favoriteGifts = allGifts.filter(gift => favoriteIds.includes(gift.id));

      return { data: { gifts: favoriteGifts } };
    } catch (error) {
      console.error('Error fetching favorite gifts:', error);
      throw error;
    }
  }
};

export default giftAPI;