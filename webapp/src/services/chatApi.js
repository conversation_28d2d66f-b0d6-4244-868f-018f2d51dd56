// src/services/chatApi.js
import axios from 'axios';

// Use environment variable for API URL with fallback to localhost
const BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8001/api';

// Create axios instance with default config
const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  withCredentials: true
});

// Add authorization header to all requests
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token'); // Assuming token is stored in localStorage
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Add response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    // Create standardized error response
    const errorResponse = {
      success: false,
      message: 'An error occurred',
      error: 'Unknown error'
    };

    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      errorResponse.message = error.response.data.message || 'Request failed';
      errorResponse.error = error.response.data.error || error.response.statusText;

      if (error.response.data.errors) {
        errorResponse.errors = error.response.data.errors;
      }
    } else if (error.request) {
      // The request was made but no response was received
      errorResponse.message = 'No response from server';
      errorResponse.error = 'Network error';
    } else {
      // Something happened in setting up the request that triggered an Error
      errorResponse.message = 'Request configuration error';
      errorResponse.error = error.message;
    }

    return Promise.reject(errorResponse);
  }
);

export const chatApi = {
  /**
   * Get all conversations for the current user
   * @param {number} page - Page number for pagination
   * @param {number} per_page - Items per page
   * @param {Object} filters - Optional filters for conversations
   * @returns {Promise<Object>} Response data
   */
  getConversations: async (page = 1, per_page = 15, filters = {}) => {
    try {
      const response = await api.get(`/chat/conversations`, {
        params: { page, per_page, ...filters }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching conversations:', error);
      throw error;
    }
  },

  /**
   * Get messages for a specific conversation
   * @param {number} conversationId - ID of the conversation
   * @param {number} page - Page number for pagination
   * @param {number} per_page - Items per page
   * @returns {Promise<Object>} Response data
   */
  getConversationMessages: async (conversationId, page = 1, per_page = 20) => {
    try {
      const response = await api.get(`/chat/conversations/${conversationId}/messages`, {
        params: { page, per_page }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching messages:', error);
      throw error;
    }
  },

  /**
   * Start a new conversation
   * @param {number} talent_id - ID of the talent
   * @param {number|null} order_id - Optional ID of an associated order
   * @param {string} message - Initial message content
   * @returns {Promise<Object>} Response data
   */
  startConversation: async (talent_id, order_id = null, message) => {
    try {
      const payload = { talent_id, message };
      if (order_id) {
        payload.order_id = order_id;
      }

      const response = await api.post(`/chat/conversations`, payload);
      return response.data;
    } catch (error) {
      console.error('Error starting conversation:', error);
      throw error;
    }
  },

  /**
   * Send a text message to a conversation
   * @param {number} conversation_id - ID of the conversation
   * @param {string} message - Message content
   * @returns {Promise<Object>} Response data
   */
  sendTextMessage: async (conversation_id, message) => {
    try {
      const response = await api.post(`/chat/messages/text`, {
        conversation_id,
        message
      });
      return response.data;
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  },

  /**
   * Send a message with attachment
   * @param {number} conversation_id - ID of the conversation
   * @param {string} message - Optional message content
   * @param {File} attachment - Image file to attach
   * @param {Function} onUploadProgress - Optional callback for upload progress
   * @returns {Promise<Object>} Response data
   */
  sendAttachmentMessage: async (conversation_id, message, attachment, onUploadProgress) => {
    try {
      const formData = new FormData();
      formData.append('conversation_id', conversation_id.toString());
      if (message) formData.append('message', message);
      formData.append('attachment', attachment);

      const response = await api.post(`/chat/messages/attachment`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        onUploadProgress: onUploadProgress ?
          progressEvent => {
            const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            onUploadProgress(percentCompleted);
          } : undefined
      });

      return response.data;
    } catch (error) {
      console.error('Error sending attachment:', error);
      throw error;
    }
  },

  /**
   * Update the status of a message
   * @param {number} message_id - ID of the message
   * @param {string} status - New status (delivered or read)
   * @returns {Promise<Object>} Response data
   */
  updateMessageStatus: async (message_id, status) => {
    try {
      if (!['delivered', 'read'].includes(status)) {
        throw new Error('Status must be either "delivered" or "read"');
      }

      const response = await api.post(`/chat/messages/status`, {
        message_id,
        status
      });

      return response.data;
    } catch (error) {
      console.error('Error updating message status:', error);
      throw error;
    }
  },

  /**
   * Report a message as inappropriate
   * @param {number} message_id - ID of the message to report
   * @param {string} reason - Reason for reporting the message
   * @returns {Promise<Object>} Response data
   */
  reportMessage: async (message_id, reason = 'inappropriate_content') => {
    try {
      const response = await api.post(`/chat/messages/report`, {
        message_id,
        reason
      });

      return response.data;
    } catch (error) {
      console.error('Error reporting message:', error);
      throw error;
    }
  },

  /**
   * Archive or unarchive a conversation
   * @param {number} conversation_id - ID of the conversation
   * @param {boolean} archive - Whether to archive (true) or unarchive (false)
   * @returns {Promise<Object>} Response data
   */
  archiveConversation: async (conversation_id, archive = true) => {
    try {
      const response = await api.post(`/chat/conversations/${conversation_id}/archive`, {
        archive
      });

      return response.data;
    } catch (error) {
      console.error('Error archiving conversation:', error);
      throw error;
    }
  },

  /**
   * Delete a conversation
   * @param {number} conversation_id - ID of the conversation to delete
   * @returns {Promise<Object>} Response data
   */
  deleteConversation: async (conversation_id) => {
    try {
      const response = await api.delete(`/chat/conversations/${conversation_id}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting conversation:', error);
      throw error;
    }
  },

  /**
   * Create a new conversation with multiple participants
   * @param {Object} conversationData - Conversation data
   * @param {string} conversationData.title - Conversation title
   * @param {Array} conversationData.participants - Array of participant user IDs
   * @param {Object} conversationData.metadata - Optional metadata for the conversation
   * @returns {Promise<Object>} Response data
   */
  createConversation: async (conversationData) => {
    try {
      const response = await api.post(`/chat/conversations/group`, conversationData);
      return response.data;
    } catch (error) {
      console.error('Error creating conversation:', error);
      throw error;
    }
  },

  /**
   * Get a conversation by its metadata
   * @param {Object} metadata - Metadata to search for
   * @returns {Promise<Object>} Response data
   */
  getConversationByMetadata: async (metadata) => {
    try {
      // Use the filters parameter in getConversations
      const conversations = await chatApi.getConversations(1, 100, { metadata: JSON.stringify(metadata) });

      // Find the first conversation that matches the metadata
      return conversations.data.find(conversation => {
        if (!conversation.metadata) return false;

        // Check if all metadata keys match
        for (const key in metadata) {
          if (conversation.metadata[key] !== metadata[key]) {
            return false;
          }
        }

        return true;
      });
    } catch (error) {
      console.error('Error getting conversation by metadata:', error);
      throw error;
    }
  },

  /**
   * Add a participant to a conversation
   * @param {number} conversation_id - ID of the conversation
   * @param {number} user_id - ID of the user to add
   * @returns {Promise<Object>} Response data
   */
  addParticipantToConversation: async (conversation_id, user_id) => {
    try {
      const response = await api.post(`/chat/conversations/${conversation_id}/participants`, {
        user_id
      });
      return response.data;
    } catch (error) {
      console.error('Error adding participant to conversation:', error);
      throw error;
    }
  },

  /**
   * Remove a participant from a conversation
   * @param {number} conversation_id - ID of the conversation
   * @param {number} user_id - ID of the user to remove
   * @returns {Promise<Object>} Response data
   */
  removeParticipantFromConversation: async (conversation_id, user_id) => {
    try {
      const response = await api.delete(`/chat/conversations/${conversation_id}/participants/${user_id}`);
      return response.data;
    } catch (error) {
      console.error('Error removing participant from conversation:', error);
      throw error;
    }
  },

  /**
   * Send a message with a specific type and metadata
   * @param {Object} messageData - Message data
   * @param {number} messageData.conversationId - ID of the conversation
   * @param {string} messageData.text - Message text
   * @param {string} messageData.type - Message type (text, system, etc.)
   * @param {Object} messageData.metadata - Optional metadata for the message
   * @returns {Promise<Object>} Response data
   */
  sendMessage: async (messageData) => {
    try {
      const { conversationId, text, type = 'text', metadata = {} } = messageData;

      const payload = {
        conversation_id: conversationId,
        message: text,
        type,
        metadata
      };

      const response = await api.post(`/chat/messages`, payload);
      return response.data;
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  }
};
