import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8001/api';

const api = axios.create({
    baseURL: API_BASE_URL,
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    },
    withCredentials: true,
});

// Add request interceptor to include auth token
api.interceptors.request.use(
    (config) => {
        const token = localStorage.getItem('token');
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);



// Profile API endpoints
export const profileAPI = {
    getProfile: async () => {
        try {
            const response = await api.get('/user/profile');
            return response.data;
        } catch (error) {
            console.error('Error fetching profile:', error);
            throw error;
        }
    },
    getBiography: async () => {
        try {
            const response = await api.get('/user/biography');
            return response.data;
        } catch (error) {
            console.error('Error fetching biography:', error);
            throw error;
        }
    },
    getFollowers: async () => {
        try {
            const response = await api.get('/user/followers');
            return response.data;
        } catch (error) {
            console.error('Error fetching followers:', error);
            throw error;
        }
    },
    updateProfile: async (data) => {
        try {
            const response = await api.put('/user/profile', data);
            return response.data;
        } catch (error) {
            console.error('Error updating profile:', error);
            throw error;
        }
    },
    updateBiography: async (data) => {
        try {
            // Ensure data has the correct format
            const formattedData = {
                bio: data.bio || data.biography || ""
            };

            const response = await api.put('/user/biography', formattedData);
            return response.data;
        } catch (error) {
            console.error('Error updating biography:', error);
            throw error;
        }
    },
    uploadMedia: async (file) => {
        try {
            const formData = new FormData();
            formData.append('file', file);

            const response = await api.post('/user/media', formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });
            return response.data;
        } catch (error) {
            console.error('Error uploading media:', error);
            throw error;
        }
    },
    updateSkills: async (skills) => {
        try {
            const response = await api.put('/user/profile/skills', { skills });
            return response.data;
        } catch (error) {
            console.error('Error updating skills:', error);
            throw error;
        }
    },
    deleteSkill: async (skillId) => {
        try {
            const response = await api.delete(`/user/profile/skills/${skillId}`);
            return response.data;
        } catch (error) {
            console.error('Error deleting skill:', error);
            throw error;
        }
    }
};

export default api;
