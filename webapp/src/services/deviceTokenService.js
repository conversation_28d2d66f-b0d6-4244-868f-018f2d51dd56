/**
 * Device Token Service
 * Handles Firebase device token registration for push notifications
 */

import { authService } from './authService';

/**
 * Device Token Service
 * Manages device token registration and removal for push notifications
 */
export const deviceTokenService = {
  /**
   * Get device information for registration
   * @returns {Object} Device information object
   */
  getDeviceInfo: () => {
    return {
      device_type: 'web',
      device_name: navigator.userAgent,
      platform: navigator.platform,
      language: navigator.language,
      screen_resolution: `${window.screen.width}x${window.screen.height}`,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
    };
  },

  /**
   * Generate a mock device token for web
   * In a real implementation, this would use Firebase SDK
   * @returns {string} Mock device token
   */
  generateMockDeviceToken: () => {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2);
    return `web_${timestamp}_${random}`;
  },

  /**
   * Register device token with backend
   * @param {string} deviceToken - Firebase device token
   * @returns {Promise<Object>} Registration result
   */
  registerDeviceToken: async (deviceToken) => {
    try {
      const deviceInfo = deviceTokenService.getDeviceInfo();

      const result = await authService.registerDeviceToken(
        deviceToken,
        deviceInfo.device_type,
        deviceInfo.device_name
      );

      if (result.success) {
        // Store token locally for future reference
        localStorage.setItem('deviceToken', deviceToken);
        localStorage.setItem('deviceInfo', JSON.stringify(deviceInfo));

        console.log('[DeviceToken] Successfully registered:', deviceToken);
      }

      return result;
    } catch (error) {
      console.error('[DeviceToken] Registration failed:', error);
      return {
        success: false,
        error: error.message || 'Failed to register device token'
      };
    }
  },

  /**
   * Remove device token from backend
   * @param {string} deviceToken - Device token to remove
   * @returns {Promise<Object>} Removal result
   */
  removeDeviceToken: async (deviceToken) => {
    try {
      const result = await authService.removeDeviceToken(deviceToken);

      if (result.success) {
        // Remove from local storage
        localStorage.removeItem('deviceToken');
        localStorage.removeItem('deviceInfo');

        console.log('[DeviceToken] Successfully removed:', deviceToken);
      }

      return result;
    } catch (error) {
      console.error('[DeviceToken] Removal failed:', error);
      return {
        success: false,
        error: error.message || 'Failed to remove device token'
      };
    }
  },

  /**
   * Get stored device token
   * @returns {string|null} Stored device token
   */
  getStoredDeviceToken: () => {
    return localStorage.getItem('deviceToken');
  },

  /**
   * Get stored device info
   * @returns {Object|null} Stored device information
   */
  getStoredDeviceInfo: () => {
    const deviceInfo = localStorage.getItem('deviceInfo');
    return deviceInfo ? JSON.parse(deviceInfo) : null;
  },

  /**
   * Initialize device token registration
   * This would typically be called after successful authentication
   * @returns {Promise<Object>} Initialization result
   */
  initializeDeviceToken: async () => {
    try {
      // Check if we already have a token
      let deviceToken = deviceTokenService.getStoredDeviceToken();

      if (!deviceToken) {
        // Generate a mock token for web (in real app, use Firebase)
        deviceToken = deviceTokenService.generateMockDeviceToken();
      }

      // Register with backend
      const result = await deviceTokenService.registerDeviceToken(deviceToken);

      return {
        success: result.success,
        deviceToken: result.success ? deviceToken : null,
        error: result.error
      };
    } catch (error) {
      console.error('[DeviceToken] Initialization failed:', error);
      return {
        success: false,
        error: error.message || 'Failed to initialize device token'
      };
    }
  },

  /**
   * Cleanup device token on logout
   * @returns {Promise<Object>} Cleanup result
   */
  cleanupDeviceToken: async () => {
    try {
      const deviceToken = deviceTokenService.getStoredDeviceToken();

      if (deviceToken) {
        const result = await deviceTokenService.removeDeviceToken(deviceToken);
        return result;
      }

      return { success: true, message: 'No device token to cleanup' };
    } catch (error) {
      console.error('[DeviceToken] Cleanup failed:', error);
      return {
        success: false,
        error: error.message || 'Failed to cleanup device token'
      };
    }
  },

  /**
   * Check if push notifications are supported
   * @returns {boolean} True if supported
   */
  isPushNotificationSupported: () => {
    return 'serviceWorker' in navigator && 'PushManager' in window;
  },

  /**
   * Request notification permission
   * @returns {Promise<string>} Permission status
   */
  requestNotificationPermission: async () => {
    if (!('Notification' in window)) {
      return 'not-supported';
    }

    if (Notification.permission === 'granted') {
      return 'granted';
    }

    if (Notification.permission === 'denied') {
      return 'denied';
    }

    try {
      const permission = await Notification.requestPermission();
      return permission;
    } catch (error) {
      console.error('[DeviceToken] Permission request failed:', error);
      return 'denied';
    }
  },

  /**
   * Show a test notification
   * @param {string} title - Notification title
   * @param {string} body - Notification body
   * @returns {Promise<boolean>} Success status
   */
  showTestNotification: async (title = 'MissionX', body = 'Welcome to MissionX!') => {
    try {
      const permission = await deviceTokenService.requestNotificationPermission();

      if (permission === 'granted') {
        new Notification(title, {
          body,
          icon: '/favicon.ico',
          badge: '/favicon.ico',
          tag: 'missionx-welcome'
        });
        return true;
      }

      return false;
    } catch (error) {
      console.error('[DeviceToken] Test notification failed:', error);
      return false;
    }
  }
};

export default deviceTokenService;
