// src/services/chatWebSocket.js
import io from 'socket.io-client';

// Get WebSocket URL from environment variables or use a default
const WEBSOCKET_URL = process.env.REACT_APP_WEBSOCKET_URL || 'http://localhost:6001';

class ChatWebSocket {
    constructor() {
        this.socket = null;
        this.messageHandlers = new Set();
        this.statusHandlers = new Set();
        this.typingHandlers = new Set();
        this.connectionHandlers = new Set();
        this.errorHandlers = new Set();
        this.readReceiptHandlers = new Set();
        this.giftHandlers = new Set();
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000; // Start with 1 second
        this.maxReconnectDelay = 30000; // Max 30 seconds
    }

    /**
     * Connect to the WebSocket server
     * @param {string} token - Authentication token
     */
    connect(token) {
        if (this.socket && this.isConnected) {
            console.log('WebSocket already connected');
            return;
        }

        // Disconnect existing socket if any
        if (this.socket) {
            this.disconnect();
        }

        // Initialize the socket connection with environment-specific URL
        console.log(`[WEBSOCKET] Connecting to ${WEBSOCKET_URL}`);

        this.socket = io(WEBSOCKET_URL, {
            auth: { token },
            reconnection: true,
            reconnectionAttempts: this.maxReconnectAttempts,
            reconnectionDelay: this.reconnectDelay,
            reconnectionDelayMax: this.maxReconnectDelay,
            timeout: 10000,
            transports: ['websocket']
        });

        // Setup event listeners with enhanced logging
        this.socket.on('connect', () => {
            console.log(`[WEBSOCKET] Connected to ${WEBSOCKET_URL}`);
            this.isConnected = true;
            this.reconnectAttempts = 0;
            this.connectionHandlers.forEach(handler => handler({
                status: 'connected',
                url: WEBSOCKET_URL
            }));
        });

        this.socket.on('connect_error', (error) => {
            console.error(`[WEBSOCKET] Connection error: ${error.message}`);
            this.isConnected = false;
            this.errorHandlers.forEach(handler => handler({
                type: 'connection_error',
                message: `Failed to connect to ${WEBSOCKET_URL}: ${error.message}`,
                error
            }));

            // If we're in development, provide helpful debugging information
            if (process.env.NODE_ENV === 'development') {
                console.info('[WEBSOCKET] Development mode detected. Check that:');
                console.info(`1. WebSocket server is running at ${WEBSOCKET_URL}`);
                console.info('2. CORS is properly configured on the WebSocket server');
                console.info('3. Network connectivity is available');
            }
        });

        this.socket.on('disconnect', (reason) => {
            console.log(`[WEBSOCKET] Disconnected: ${reason}`);
            this.isConnected = false;
            this.connectionHandlers.forEach(handler => handler({
                status: 'disconnected',
                reason,
                url: WEBSOCKET_URL
            }));

            // If the disconnect was not intentional, log additional information
            if (reason !== 'io client disconnect') {
                console.warn(`[WEBSOCKET] Unintentional disconnect: ${reason}`);
            }
        });

        this.socket.on('reconnect_attempt', (attemptNumber) => {
            console.log(`[WEBSOCKET] Reconnection attempt ${attemptNumber} of ${this.maxReconnectAttempts}`);
            this.reconnectAttempts = attemptNumber;
            this.connectionHandlers.forEach(handler => handler({
                status: 'reconnecting',
                attempt: attemptNumber,
                maxAttempts: this.maxReconnectAttempts,
                url: WEBSOCKET_URL
            }));
        });

        this.socket.on('reconnect', (attemptNumber) => {
            console.log(`[WEBSOCKET] Reconnected after ${attemptNumber} attempts`);
            this.isConnected = true;
            this.reconnectAttempts = 0;
            this.connectionHandlers.forEach(handler => handler({
                status: 'reconnected',
                attempts: attemptNumber,
                url: WEBSOCKET_URL
            }));
        });

        this.socket.on('reconnect_failed', () => {
            console.error(`[WEBSOCKET] Reconnection failed after ${this.maxReconnectAttempts} attempts`);
            this.errorHandlers.forEach(handler => handler({
                type: 'connection_failed',
                message: `Failed to reconnect to ${WEBSOCKET_URL} after ${this.maxReconnectAttempts} attempts`,
                attempts: this.maxReconnectAttempts
            }));

            // Provide guidance in development mode
            if (process.env.NODE_ENV === 'development') {
                console.info('[WEBSOCKET] You can manually reconnect by calling chatWebSocket.reconnect(token)');
            }
        });

        this.socket.on('error', (error) => {
            console.error('[WEBSOCKET] Error:', error);
            this.errorHandlers.forEach(handler => handler({
                type: 'socket_error',
                message: error.message || 'Unknown socket error',
                error,
                url: WEBSOCKET_URL
            }));
        });

        // Message event handlers
        this.socket.on('message', (message) => {
            this.messageHandlers.forEach(handler => handler(message));
        });

        this.socket.on('status_update', (status) => {
            this.statusHandlers.forEach(handler => handler(status));
        });

        this.socket.on('typing', (data) => {
            this.typingHandlers.forEach(handler => handler(data));
        });

        this.socket.on('read_receipt', (data) => {
            this.readReceiptHandlers.forEach(handler => handler(data));
        });

        this.socket.on('gift_received', (data) => {
            this.giftHandlers.forEach(handler => handler(data));
        });
    }

    /**
     * Register a handler for new messages
     * @param {Function} handler - Callback function for new messages
     * @returns {Function} Unsubscribe function
     */
    onMessage(handler) {
        this.messageHandlers.add(handler);
        return () => this.messageHandlers.delete(handler);
    }

    /**
     * Register a handler for message status updates
     * @param {Function} handler - Callback function for status updates
     * @returns {Function} Unsubscribe function
     */
    onStatusUpdate(handler) {
        this.statusHandlers.add(handler);
        return () => this.statusHandlers.delete(handler);
    }

    /**
     * Register a handler for typing indicators
     * @param {Function} handler - Callback function for typing indicators
     * @returns {Function} Unsubscribe function
     */
    onTyping(handler) {
        this.typingHandlers.add(handler);
        return () => this.typingHandlers.delete(handler);
    }

    /**
     * Register a handler for connection status changes
     * @param {Function} handler - Callback function for connection events
     * @returns {Function} Unsubscribe function
     */
    onConnection(handler) {
        this.connectionHandlers.add(handler);
        return () => this.connectionHandlers.delete(handler);
    }

    /**
     * Register a handler for errors
     * @param {Function} handler - Callback function for errors
     * @returns {Function} Unsubscribe function
     */
    onError(handler) {
        this.errorHandlers.add(handler);
        return () => this.errorHandlers.delete(handler);
    }

    /**
     * Generic event handler registration (for compatibility with existing code)
     * @param {string} event - Event name
     * @param {Function} handler - Callback function
     */
    on(event, handler) {
        switch (event) {
            case 'message':
                return this.onMessage(handler);
            case 'status_update':
                return this.onStatusUpdate(handler);
            case 'typing':
                return this.onTyping(handler);
            case 'connection':
                return this.onConnection(handler);
            case 'error':
                return this.onError(handler);
            case 'read_receipt':
                // Add read receipt handler support
                this.readReceiptHandlers.add(handler);
                return () => this.readReceiptHandlers.delete(handler);
            case 'gift_received':
                // Add gift received handler support
                this.giftHandlers.add(handler);
                return () => this.giftHandlers.delete(handler);
            default:
                console.warn(`[WEBSOCKET] Unknown event type: ${event}`);
                return () => {}; // Return empty unsubscribe function
        }
    }

    /**
     * Generic event handler removal (for compatibility with existing code)
     * @param {string} event - Event name
     * @param {Function} handler - Callback function to remove
     */
    off(event, handler) {
        switch (event) {
            case 'message':
                this.messageHandlers.delete(handler);
                break;
            case 'status_update':
                this.statusHandlers.delete(handler);
                break;
            case 'typing':
                this.typingHandlers.delete(handler);
                break;
            case 'connection':
                this.connectionHandlers.delete(handler);
                break;
            case 'error':
                this.errorHandlers.delete(handler);
                break;
            case 'read_receipt':
                if (this.readReceiptHandlers) {
                    this.readReceiptHandlers.delete(handler);
                }
                break;
            case 'gift_received':
                if (this.giftHandlers) {
                    this.giftHandlers.delete(handler);
                }
                break;
            default:
                console.warn(`[WEBSOCKET] Unknown event type: ${event}`);
        }
    }

    /**
     * Send typing status to conversation
     * @param {number} conversationId - ID of the conversation
     * @param {boolean} isTyping - Whether the user is typing
     */
    sendTypingStatus(conversationId, isTyping) {
        if (!this.socket || !this.isConnected) {
            console.warn('Cannot send typing status: WebSocket not connected');
            return;
        }

        this.socket.emit('typing', {
            conversation_id: conversationId,
            is_typing: isTyping
        });
    }

    /**
     * Send a read receipt for a message
     * @param {number} messageId - ID of the message
     */
    sendReadReceipt(messageId) {
        if (!this.socket || !this.isConnected) {
            console.warn('Cannot send read receipt: WebSocket not connected');
            return;
        }

        this.socket.emit('read_receipt', {
            message_id: messageId,
            timestamp: new Date().toISOString()
        });
    }

    /**
     * Check if WebSocket is currently connected
     * @returns {boolean} Connection status
     */
    isSocketConnected() {
        return this.isConnected;
    }

    /**
     * Check if WebSocket functionality should be available
     * This can be used for graceful degradation when WebSocket is not available
     * @returns {Object} Status object with availability information
     */
    checkAvailability() {
        const status = {
            available: false,
            connected: this.isConnected,
            reason: null
        };

        // Check if WebSocket is supported by the browser
        if (typeof WebSocket === 'undefined') {
            status.reason = 'WebSocket is not supported by this browser';
            return status;
        }

        // Check if we have a socket instance
        if (!this.socket) {
            status.reason = 'WebSocket connection has not been initialized';
            return status;
        }

        // If we're connected, WebSocket is available
        if (this.isConnected) {
            status.available = true;
            return status;
        }

        // If we've exceeded reconnection attempts, WebSocket is not available
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            status.reason = `Exceeded maximum reconnection attempts (${this.maxReconnectAttempts})`;
            return status;
        }

        // Default: WebSocket is potentially available but not connected
        status.available = true;
        status.reason = 'WebSocket is supported but not currently connected';
        return status;
    }

    /**
     * Manually reconnect to the WebSocket server
     * @param {string} token - Authentication token
     * @returns {boolean} Whether reconnection was attempted
     */
    reconnect(token) {
        if (!token) {
            console.error('[WEBSOCKET] Cannot reconnect: No authentication token provided');
            this.errorHandlers.forEach(handler => handler({
                type: 'reconnect_error',
                message: 'Cannot reconnect: No authentication token provided'
            }));
            return false;
        }

        console.log('[WEBSOCKET] Manual reconnection initiated');

        // Reset reconnection attempts counter
        this.reconnectAttempts = 0;

        // Disconnect if already connected
        if (this.socket) {
            this.disconnect();
        }

        // Connect with the token
        this.connect(token);
        return true;
    }

    /**
     * Disconnect from the WebSocket server
     * @param {boolean} [silent=false] - Whether to suppress logging
     */
    disconnect(silent = false) {
        if (this.socket) {
            if (!silent) {
                console.log('[WEBSOCKET] Disconnecting from server');
            }

            try {
                this.socket.disconnect();
                if (!silent) {
                    console.log('[WEBSOCKET] Successfully disconnected');
                }
            } catch (error) {
                console.error('[WEBSOCKET] Error during disconnect:', error);
            }

            this.socket = null;
        } else if (!silent) {
            console.log('[WEBSOCKET] No active connection to disconnect');
        }

        this.isConnected = false;

        // Notify handlers of manual disconnect
        if (!silent) {
            this.connectionHandlers.forEach(handler => handler({
                status: 'disconnected',
                reason: 'manual_disconnect',
                url: WEBSOCKET_URL
            }));
        }
    }

    /**
     * Clean up all resources
     * @param {boolean} [silent=false] - Whether to suppress logging
     */
    cleanup(silent = false) {
        if (!silent) {
            console.log('[WEBSOCKET] Cleaning up resources');
        }

        // Disconnect with silent flag to avoid duplicate logging
        this.disconnect(true);

        // Clear all event handlers
        this.messageHandlers.clear();
        this.statusHandlers.clear();
        this.typingHandlers.clear();
        this.connectionHandlers.clear();
        this.errorHandlers.clear();
        this.readReceiptHandlers.clear();
        this.giftHandlers.clear();

        if (!silent) {
            console.log('[WEBSOCKET] Cleanup complete');
        }
    }
}

export const chatWebSocket = new ChatWebSocket();

// Connect automatically if token exists, with better error handling
const token = localStorage.getItem('token');
if (token) {
    try {
        console.log('[WEBSOCKET] Auto-connecting with stored token');
        chatWebSocket.connect(token);
    } catch (error) {
        console.error('[WEBSOCKET] Auto-connect failed:', error);

        // Provide helpful debugging information in development mode
        if (process.env.NODE_ENV === 'development') {
            console.info('[WEBSOCKET] Development mode detected. You can manually reconnect using:');
            console.info('chatWebSocket.reconnect(localStorage.getItem("token"))');
        }
    }
} else {
    console.log('[WEBSOCKET] No authentication token found, skipping auto-connect');
}