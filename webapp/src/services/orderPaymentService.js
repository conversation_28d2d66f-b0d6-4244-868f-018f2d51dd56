import walletAPI from './walletService';
import orderAPI from './orderService';
import missionApi from './missionApi';

/**
 * Order Payment Service
 *
 * This service handles all payment-related operations for orders,
 * including processing payments, checking balances, and handling refunds.
 */
const orderPaymentService = {
  /**
   * Check if user has sufficient balance for an order
   * @param {number} amount - The order amount
   * @returns {Promise<{sufficient: boolean, balance: number, shortfall: number}>}
   */
  checkSufficientBalance: async (amount) => {
    try {
      // Get current wallet balance
      const response = await walletAPI.getBalance();
      const balance = response.data.credits_balance;

      return {
        sufficient: balance >= amount,
        balance: balance,
        shortfall: balance >= amount ? 0 : amount - balance
      };
    } catch (error) {
      console.error('Error checking balance for order:', error);
      throw error;
    }
  },

  /**
   * Process payment for creating an order
   * @param {Object} orderData - The order data
   * @param {Object} options - Additional options
   * @param {Function} options.onInsufficientBalance - Called when balance is insufficient
   * @returns {Promise<Object>} - The created order details
   */
  createOrder: async (orderData, options = {}) => {
    try {
      // First check if user has sufficient balance
      const amount = orderData.quantity * (orderData.price || 0);
      const balanceCheck = await orderPaymentService.checkSufficientBalance(amount);

      if (!balanceCheck.sufficient) {
        if (options.onInsufficientBalance) {
          options.onInsufficientBalance(balanceCheck);
        }
        throw new Error(`Insufficient balance. Required: ${amount}, Available: ${balanceCheck.balance}`);
      }

      // Create the order through the order API
      const response = await orderAPI.createOrder(orderData);

      return response;
    } catch (error) {
      console.error('Error creating order:', error);
      throw error;
    }
  },

  /**
   * Process payment for a mission order
   * @param {number} missionId - The ID of the mission
   * @param {number} amount - The order amount
   * @param {Object} options - Additional options
   * @param {Function} options.onInsufficientBalance - Called when balance is insufficient
   * @returns {Promise<Object>} - The payment transaction details
   */
  processMissionOrderPayment: async (missionId, amount, options = {}) => {
    try {
      // First check if user has sufficient balance
      const balanceCheck = await orderPaymentService.checkSufficientBalance(amount);

      if (!balanceCheck.sufficient) {
        if (options.onInsufficientBalance) {
          options.onInsufficientBalance(balanceCheck);
        }
        throw new Error(`Insufficient balance. Required: ${amount}, Available: ${balanceCheck.balance}`);
      }

      // Process the payment through the mission API
      const response = await missionApi.processMissionPayment(missionId, {
        amount: amount,
        payment_type: 'mission_creation'
      });

      return response.data;
    } catch (error) {
      console.error('Error processing mission payment:', error);
      throw error;
    }
  },

  /**
   * Handle payment return for an order
   * @param {string} transactionId - The payment transaction ID
   * @param {number} orderId - The order ID
   * @returns {Promise<Object>} - Updated order details
   */
  handleOrderPaymentReturn: async (transactionId, orderId) => {
    try {
      // Check payment status
      const paymentResponse = await walletAPI.checkPaymentStatus(transactionId);

      if (paymentResponse.data.status === 'success' ||
          paymentResponse.data.status === 'completed') {
        // Update order status if payment was successful
        // This might be handled automatically by the backend
        // but we can check the order status to confirm
        const orderResponse = await orderAPI.getOrderDetails(orderId);

        return {
          success: true,
          message: 'Payment completed successfully',
          order: orderResponse.data
        };
      } else {
        return {
          success: false,
          message: 'Payment was not successful',
          paymentStatus: paymentResponse.data.status
        };
      }
    } catch (error) {
      console.error('Error handling order payment return:', error);
      throw error;
    }
  },

  /**
   * Get order payment history
   * @param {number} orderId - The ID of the order
   * @returns {Promise<Array>} - The order payment history
   */
  getOrderPaymentHistory: async (orderId) => {
    try {
      const response = await orderAPI.getOrderPaymentHistory(orderId);
      return response.data;
    } catch (error) {
      console.error('Error getting order payment history:', error);
      throw error;
    }
  },

  /**
   * Get wallet balance
   * @returns {Promise<number>} - The current wallet balance
   */
  getWalletBalance: async () => {
    try {
      const response = await walletAPI.getBalance();
      return response.data.credits_balance;
    } catch (error) {
      console.error('Error getting wallet balance:', error);
      throw error;
    }
  },

  /**
   * Format currency amount
   * @param {number} amount - The amount to format
   * @param {string} currency - The currency code (default: 'credits')
   * @returns {string} - Formatted currency amount
   */
  formatCurrency: (amount, currency = 'credits') => {
    if (currency.toLowerCase() === 'credits') {
      return `${amount.toLocaleString()} Credits`;
    }

    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase()
    }).format(amount);
  }
};

export default orderPaymentService;