import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8001/api';

// API configuration

// Create API instance with auth headers
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to include auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor to handle common error scenarios
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Handle specific error types
    if (error.response) {
      // Server responded with an error status
      const status = error.response.status;
      const data = error.response.data;

      // Map specific API error codes to user-friendly messages
      switch (status) {
        case 401:
          // Unauthorized - handle authentication errors
          console.error('Authentication error:', data.message);
          // Redirect to login if token is invalid/expired
          if (window.location.pathname !== '/login') {
            localStorage.removeItem('token');
            window.location.href = '/login';
          }
          break;

        case 403:
          // Forbidden - handle permission errors
          console.error('Permission error:', data.message);
          // Handle verification requirements
          if (data.code === 'verification_required') {
            return Promise.reject({
              ...error,
              isVerificationError: true,
              requirements: data.missing_requirements || []
            });
          }
          break;

        case 422:
          // Validation errors
          console.error('Validation error:', data.message);
          break;

        case 429:
          // Rate limiting
          console.error('Too many requests. Please try again later.');
          break;

        case 500:
          // Server error
          console.error('Server error:', data.message);
          break;

        default:
          // Other errors
          console.error(`Unhandled error (${status}):`, data.message);
      }
    }

    return Promise.reject(error);
  }
);

// Wallet API endpoints
export const walletAPI = {
      // Get current credit balance
      getBalance: async () => {
        try {
          const response = await api.get('/credits/balance');
          return response;
        } catch (error) {
          console.error('Error fetching wallet balance:', error);
          throw error;
        }
      },

      // Get transaction history with optional filters
      getTransactions: async (page = 1, filters = {}) => {
        try {
          // Build query parameters
          const params = new URLSearchParams();
          params.append('page', page);

          // Add filters if provided
          if (filters.type === 'credit-in') {
            params.append('type', 'add');
          } else if (filters.type === 'credit-out') {
            params.append('type', 'deduct');
          }

          if (filters.startDate) {
            params.append('start_date', filters.startDate);
          }

          if (filters.endDate) {
            params.append('end_date', filters.endDate);
          }

          const response = await api.get(`/credits/transactions?${params.toString()}`);
          return response;
        } catch (error) {
          console.error('Error fetching transactions:', error);
          throw error;
        }
      },

      // Get credit packages
      getCreditPackages: async (channel = 'default') => {
        try {
          const response = await api.get(`/credits/${channel}/packages`);
          return response;
        } catch (error) {
          console.error('Error fetching credit packages:', error);
          throw error;
        }
      },

      // Create a payment for a credit package
      createPayment: async (creditPackageId, redirectUrl) => {
        try {
          const response = await api.post('/payments', {
            credit_package_id: creditPackageId,
            redirect_url: redirectUrl,
            payment_method: 'billplz'
          });
          return response;
        } catch (error) {
          console.error('Error creating payment:', error);
          throw error;
        }
      },

      // Check payment status
      checkPaymentStatus: async (transactionId) => {
        try {
          const response = await api.get(`/credits/payments/${transactionId}`);
          return response;
        } catch (error) {
          console.error('Error checking payment status:', error);
          throw error;
        }
      },

      // Retry a payment
      retryPayment: async (transactionId) => {
        try {
          const response = await api.post('/credits/payments/retry', {
            transaction_id: transactionId
          });
          return response;
        } catch (error) {
          console.error('Error retrying payment:', error);
          throw error;
        }
      },

      // Request withdrawal (legacy method)
      requestWithdrawal: async (amount, bankDetails = {}) => {
        try {
          const response = await api.post('/credits/withdrawals/request', {
            amount,
            ...bankDetails
          });
          return response;
        } catch (error) {
          console.error('Error requesting withdrawal:', error);
          throw error;
        }
      },

      // Enhanced withdrawal with bank account selection and currency support
      withdraw: async (withdrawData) => {
        try {
          const response = await api.post('/withdrawals/credits', {
            amount: withdrawData.amount,
            bank_account_id: withdrawData.bank_account_id,
            fiat_currency: withdrawData.fiat_currency,
            payment_mode_id: withdrawData.payment_mode_id || 1, // Default payment mode
            recipient_reference: withdrawData.recipient_reference,
            other_payment_details: withdrawData.other_payment_details,
            id_validation: withdrawData.id_validation || false,
            id_validation_type: withdrawData.id_validation_type,
            id_validation_value: withdrawData.id_validation_value,
            transaction_type: withdrawData.transaction_type,
            purpose_of_transfer: withdrawData.purpose_of_transfer,
            email: withdrawData.email
          });
          return response;
        } catch (error) {
          console.error('Error processing withdrawal:', error);
          throw error;
        }
      },

      // Get withdrawal history
      getWithdrawals: async (limit = 10, offset = 0) => {
        try {
          const params = new URLSearchParams();
          params.append('limit', limit);
          params.append('offset', offset);

          const response = await api.get(`/withdrawals/history?${params.toString()}`);
          return response;
        } catch (error) {
          console.error('Error fetching withdrawals:', error);
          throw error;
        }
      },

      // Get currencies for withdrawals
      getWithdrawalCurrencies: async (type = 'credits') => {
        try {
          const response = await api.get(`/withdrawals/currencies?type=${type}`);
          return response;
        } catch (error) {
          console.error('Error fetching withdrawal currencies:', error);
          throw error;
        }
      },

      // Get withdrawal by ID
      getWithdrawal: async (withdrawalId) => {
        try {
          const response = await api.get(`/withdrawals/${withdrawalId}`);
          return response;
        } catch (error) {
          console.error('Error fetching withdrawal details:', error);
          throw error;
        }
      },

      // Note: Cancel withdrawal endpoint doesn't exist in backend
      // cancelWithdrawal functionality would need to be implemented in backend first

      // Note: Get credit channels endpoint is for backend/admin use only
      // getCreditChannels functionality is not exposed to frontend users
    };

export default walletAPI;