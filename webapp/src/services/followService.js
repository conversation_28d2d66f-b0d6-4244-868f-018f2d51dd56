import axios from 'axios';

// Base URL for API requests - using the correct backend URL
const BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8001/api';

// Get auth token from local storage
const getAuthToken = () => {
    return localStorage.getItem('token');
};

// Create axios instance with default config
const apiClient = axios.create({
    baseURL: BASE_URL,
    timeout: 30000,
});

// Add request interceptor to include auth token
apiClient.interceptors.request.use(
    (config) => {
        const token = getAuthToken();
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

// Add response interceptor for error handling
apiClient.interceptors.response.use(
    (response) => response,
    (error) => {
        if (error.response?.status === 401) {
            // Handle unauthorized access
            localStorage.removeItem('token');
            window.location.href = '/';
        }
        return Promise.reject(error);
    }
);

// Follow Service for managing user relationships
const followService = {
    // Follow a user
    followUser: async (userId) => {
        try {
            const response = await apiClient.post(`/users/${userId}/follow`);
            return response.data;
        } catch (error) {
            console.error('Error following user:', error);
            throw error;
        }
    },

    // Unfollow a user
    unfollowUser: async (userId) => {
        try {
            const response = await apiClient.delete(`/users/${userId}/follow`);
            return response.data;
        } catch (error) {
            console.error('Error unfollowing user:', error);
            throw error;
        }
    },

    // Get user's followers
    getFollowers: async (userId, page = 1, limit = 20) => {
        try {
            const response = await apiClient.get(`/users/${userId}/followers`, {
                params: { 
                    page, 
                    per_page: limit 
                }
            });
            
            // Transform backend response
            const followers = response.data.data.map(follower => ({
                id: follower.id,
                username: follower.nickname || follower.name,
                name: follower.name || follower.nickname,
                profile_picture: follower.profile_picture,
                bio: follower.bio || follower.description,
                is_following: follower.is_following || false,
                followed_at: follower.pivot?.created_at
            }));

            return {
                followers,
                has_more: response.data.next_page_url !== null,
                next_page: response.data.current_page + 1,
                total: response.data.total,
                current_page: response.data.current_page,
                last_page: response.data.last_page
            };
        } catch (error) {
            console.error('Error fetching followers:', error);
            throw error;
        }
    },

    // Get users that a user is following
    getFollowing: async (userId, page = 1, limit = 20) => {
        try {
            const response = await apiClient.get(`/users/${userId}/following`, {
                params: { 
                    page, 
                    per_page: limit 
                }
            });
            
            // Transform backend response
            const following = response.data.data.map(user => ({
                id: user.id,
                username: user.nickname || user.name,
                name: user.name || user.nickname,
                profile_picture: user.profile_picture,
                bio: user.bio || user.description,
                is_following: true, // Since this is the following list
                followed_at: user.pivot?.created_at
            }));

            return {
                following,
                has_more: response.data.next_page_url !== null,
                next_page: response.data.current_page + 1,
                total: response.data.total,
                current_page: response.data.current_page,
                last_page: response.data.last_page
            };
        } catch (error) {
            console.error('Error fetching following:', error);
            throw error;
        }
    },

    // Check if current user is following a specific user
    isFollowing: async (userId) => {
        try {
            const response = await apiClient.get(`/users/${userId}/is-following`);
            return response.data.is_following || false;
        } catch (error) {
            console.error('Error checking follow status:', error);
            return false;
        }
    },

    // Get follow statistics for a user
    getFollowStats: async (userId) => {
        try {
            const response = await apiClient.get(`/users/${userId}/follow-stats`);
            return {
                followers_count: response.data.followers_count || 0,
                following_count: response.data.following_count || 0
            };
        } catch (error) {
            console.error('Error fetching follow stats:', error);
            return {
                followers_count: 0,
                following_count: 0
            };
        }
    },

    // Get suggested users to follow
    getSuggestedUsers: async (limit = 10) => {
        try {
            const response = await apiClient.get('/users/suggested', {
                params: { limit }
            });
            
            // Transform backend response
            const users = response.data.map(user => ({
                id: user.id,
                username: user.nickname || user.name,
                name: user.name || user.nickname,
                profile_picture: user.profile_picture,
                bio: user.bio || user.description,
                is_following: false,
                mutual_followers_count: user.mutual_followers_count || 0,
                reason: user.suggestion_reason || 'Suggested for you'
            }));

            return { users };
        } catch (error) {
            console.error('Error fetching suggested users:', error);
            return { users: [] };
        }
    },

    // Search users for following
    searchUsersToFollow: async (query, page = 1, limit = 20) => {
        try {
            const response = await apiClient.get('/users/search', {
                params: { 
                    query,
                    page, 
                    per_page: limit 
                }
            });
            
            // Transform backend response
            const users = response.data.data.map(user => ({
                id: user.id,
                username: user.nickname || user.name,
                name: user.name || user.nickname,
                profile_picture: user.profile_picture,
                bio: user.bio || user.description,
                is_following: user.is_following || false,
                followers_count: user.followers_count || 0
            }));

            return {
                users,
                has_more: response.data.next_page_url !== null,
                next_page: response.data.current_page + 1,
                total: response.data.total,
                current_page: response.data.current_page,
                last_page: response.data.last_page
            };
        } catch (error) {
            console.error('Error searching users:', error);
            throw error;
        }
    }
};

export default followService;
