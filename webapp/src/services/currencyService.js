import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api';

// Helper function to simulate API delay for development
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Create API instance with auth headers
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to include auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Mock data for development
const mockCurrencies = [
  {
    code: 'MYR',
    name: 'Malaysian Ringgit',
    symbol: 'RM',
    conversion_rate: 0.25, // 1 credit = 0.25 MYR
    is_default: true
  },
  {
    code: 'USD',
    name: 'US Dollar',
    symbol: '$',
    conversion_rate: 0.06, // 1 credit = 0.06 USD
    is_default: false
  },
  {
    code: 'SGD',
    name: 'Singapore Dollar',
    symbol: 'S$',
    conversion_rate: 0.08, // 1 credit = 0.08 SGD
    is_default: false
  },
  {
    code: 'EUR',
    name: 'Euro',
    symbol: '€',
    conversion_rate: 0.055, // 1 credit = 0.055 EUR
    is_default: false
  }
];

// Use mock API in development
const isDevelopment = process.env.NODE_ENV === 'development';

// Currency Service
const currencyService = isDevelopment 
  ? {
      // Get available currencies for withdrawal
      getWithdrawalCurrencies: async () => {
        await delay(600);
        return {
          data: {
            currencies: mockCurrencies,
            message: 'Currencies retrieved successfully'
          }
        };
      },
      
      // Get conversion rate for a specific currency
      getConversionRate: async (currencyCode) => {
        await delay(300);
        const currency = mockCurrencies.find(c => c.code === currencyCode);
        
        if (!currency) {
          throw new Error('Currency not found');
        }
        
        return {
          data: {
            currency: currency,
            conversion_rate: currency.conversion_rate,
            message: 'Conversion rate retrieved successfully'
          }
        };
      },
      
      // Convert credits to currency amount
      convertCredits: (credits, conversionRate) => {
        return Number(credits) * conversionRate;
      },
      
      // Get default currency
      getDefaultCurrency: () => {
        return mockCurrencies.find(c => c.is_default) || mockCurrencies[0];
      }
    }
  : {
      // Real API implementations
      getWithdrawalCurrencies: async () => {
        return api.get('/withdrawals/currencies');
      },
      
      getConversionRate: async (currencyCode) => {
        return api.get(`/withdrawals/currencies/${currencyCode}/rate`);
      },
      
      convertCredits: (credits, conversionRate) => {
        return Number(credits) * conversionRate;
      },
      
      getDefaultCurrency: async () => {
        const response = await api.get('/withdrawals/currencies/default');
        return response.data.currency;
      }
    };

export default currencyService; 