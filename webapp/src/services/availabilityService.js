import axios from 'axios';

// Helper function to get the API URL
const getApiUrl = () => {
  return process.env.REACT_APP_API_URL || 'https://api.missionx.com';
};

// Helper function to get the auth token
const getAuthToken = () => {
  return localStorage.getItem('token');
};

// Helper function to create API instance with auth headers
const createApiInstance = () => {
  const token = getAuthToken();
  
  return axios.create({
    baseURL: getApiUrl(),
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': token ? `Bearer ${token}` : ''
    }
  });
};

// Create API instance
const api = createApiInstance();

/**
 * Availability Service
 * 
 * This service handles all availability-related API calls.
 */
export const availabilityAPI = {
  /**
   * Get user availability
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} - Availability data
   */
  getUserAvailability: async (params = {}) => {
    try {
      const response = await api.get('/user/availability', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching user availability:', error);
      throw error;
    }
  },
  
  /**
   * Create user availability
   * @param {Object} availabilityData - The availability data
   * @returns {Promise<Object>} - Created availability data
   */
  createAvailability: async (availabilityData) => {
    try {
      const response = await api.post('/user/availability', availabilityData);
      return response.data;
    } catch (error) {
      console.error('Error creating availability:', error);
      throw error;
    }
  },
  
  /**
   * Get specific availability
   * @param {number} availabilityId - The ID of the availability
   * @returns {Promise<Object>} - Availability details
   */
  getAvailabilityById: async (availabilityId) => {
    try {
      const response = await api.get(`/user/availability/${availabilityId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching availability details for ID ${availabilityId}:`, error);
      throw error;
    }
  },
  
  /**
   * Update availability
   * @param {number} availabilityId - The ID of the availability
   * @param {Object} availabilityData - The updated availability data
   * @returns {Promise<Object>} - Updated availability data
   */
  updateAvailability: async (availabilityId, availabilityData) => {
    try {
      const response = await api.put(`/user/availability/${availabilityId}`, availabilityData);
      return response.data;
    } catch (error) {
      console.error(`Error updating availability ${availabilityId}:`, error);
      throw error;
    }
  },
  
  /**
   * Delete availability
   * @param {number} availabilityId - The ID of the availability
   * @returns {Promise<Object>} - Response data
   */
  deleteAvailability: async (availabilityId) => {
    try {
      const response = await api.delete(`/user/availability/${availabilityId}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting availability ${availabilityId}:`, error);
      throw error;
    }
  },
  
  /**
   * Batch update availability
   * @param {Object} batchData - The batch update data
   * @returns {Promise<Object>} - Response data
   */
  batchUpdateAvailability: async (batchData) => {
    try {
      const response = await api.post('/user/availability/batch', batchData);
      return response.data;
    } catch (error) {
      console.error('Error batch updating availability:', error);
      throw error;
    }
  },
  
  /**
   * Get availability status
   * @returns {Promise<Object>} - Status data
   */
  getAvailabilityStatus: async () => {
    try {
      const response = await api.get('/user/availability/status');
      return response.data;
    } catch (error) {
      console.error('Error fetching availability status:', error);
      throw error;
    }
  },
  
  /**
   * Set availability override
   * @param {Object} overrideData - The override data
   * @returns {Promise<Object>} - Override data
   */
  setAvailabilityOverride: async (overrideData) => {
    try {
      const response = await api.post('/user/availability/override', overrideData);
      return response.data;
    } catch (error) {
      console.error('Error setting availability override:', error);
      throw error;
    }
  },
  
  /**
   * Get availability override
   * @returns {Promise<Object>} - Override data
   */
  getAvailabilityOverride: async () => {
    try {
      const response = await api.get('/user/availability/override');
      return response.data;
    } catch (error) {
      console.error('Error fetching availability override:', error);
      throw error;
    }
  },
  
  /**
   * Remove availability override
   * @returns {Promise<Object>} - Response data
   */
  removeAvailabilityOverride: async () => {
    try {
      const response = await api.delete('/user/availability/override');
      return response.data;
    } catch (error) {
      console.error('Error removing availability override:', error);
      throw error;
    }
  }
};

export default availabilityAPI;
