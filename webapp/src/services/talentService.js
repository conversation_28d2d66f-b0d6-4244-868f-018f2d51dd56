/**
 * Talent Service
 *
 * This service handles API calls related to talent filtering and retrieval.
 * Connects to the real backend API endpoints for talent data.
 */

import axios from 'axios';
import { formatTalentForDisplay } from '../models/TalentModel';
import { convertFiltersToApiFormat } from '../utils/filterUtils';
import { handleApiError } from '../utils/apiErrorHandler';
import { cacheManager } from '../utils/cacheUtils';
import api from './api';

// Base API URL
const API_URL = '/api';

/**
 * Get the authentication token from localStorage
 * @returns {string|null} The authentication token or null if not found
 */
const getAuthToken = () => {
  return localStorage.getItem('token');
};

/**
 * Configure axios with authentication
 */
const apiClient = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Add authentication token to requests
apiClient.interceptors.request.use(
  config => {
    const token = getAuthToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  error => Promise.reject(error)
);

/**
 * Filter talents based on criteria
 * @param {Object} filters - Filter criteria
 * @param {number} page - Page number for pagination
 * @param {number} perPage - Number of results per page
 * @param {boolean} skipCache - Whether to skip the cache and force a fresh request
 * @returns {Promise<Object>} Filtered talents with pagination metadata
 * @throws {Object} Standardized error object
 */
export const filterTalents = async (filters = {}, page = 1, perPage = 15, skipCache = false) => {
  // Only cache certain filter combinations to avoid cache explosion
  // We'll cache:
  // 1. Empty filters (default listing)
  // 2. Single category/type/style filters
  // 3. Common combinations like gender + level
  const shouldCache =
    Object.keys(filters).length === 0 || // Empty filters
    (Object.keys(filters).length === 1 &&
      (filters.serviceCategoryId ||
       filters.serviceTypeId ||
       filters.serviceStyleId ||
       filters.gender)) ||
    (Object.keys(filters).length === 2 &&
      filters.gender &&
      (filters.experienceLevel || filters.serviceTypeId));

  if (shouldCache) {
    const cacheKey = cacheManager.getTalentFiltersKey(filters, page, perPage);

    // Check if data is in cache and we're not skipping cache
    if (!skipCache && cacheManager.has(cacheKey)) {
      return cacheManager.get(cacheKey);
    }
  }

  try {
    const apiFilters = convertFiltersToApiFormat(filters);

    const response = await api.get('/talents', {
      params: {
        ...apiFilters,
        page,
        per_page: perPage,
        current_page: page
      }
    });

    // Format talents for display
    const formattedTalents = response.data.data.map(formatTalentForDisplay);

    const result = {
      ...response.data,
      data: formattedTalents
    };

    // Cache the result if it meets our caching criteria
    if (shouldCache) {
      const cacheKey = cacheManager.getTalentFiltersKey(filters, page, perPage);
      cacheManager.set(cacheKey, result);
    }

    return result;
  } catch (error) {
    // Handle the error and convert it to a standardized format
    const handledError = handleApiError(error);
    console.error('Error filtering talents:', handledError);

    // Return empty result with error information instead of throwing
    return {
      data: [],
      meta: {
        current_page: page,
        from: null,
        last_page: 1,
        path: '/talents',
        per_page: perPage,
        to: null,
        total: 0
      },
      links: {
        first: null,
        last: null,
        prev: null,
        next: null
      },
      error: handledError
    };
  }
};

/**
 * Get service types, optionally filtered by service category
 * @param {number|null} serviceCategoryId - Service category ID to filter by
 * @param {boolean} skipCache - Whether to skip the cache and force a fresh request
 * @returns {Promise<Array>} Array of service types
 * @throws {Object} Standardized error object
 */
export const getServiceTypes = async (serviceCategoryId = null, skipCache = false) => {
  const cacheKey = cacheManager.getServiceTypesKey(serviceCategoryId);

  // Check if data is in cache and we're not skipping cache
  if (!skipCache && cacheManager.has(cacheKey)) {
    return cacheManager.get(cacheKey);
  }

  try {
    const response = await api.get('/talents/service-types', {
      params: {
        service_category_id: serviceCategoryId
      }
    });

    // Cache the response
    cacheManager.set(cacheKey, response.data);

    return response.data;
  } catch (error) {
    // Handle the error and convert it to a standardized format
    const handledError = handleApiError(error);
    console.error('Error fetching service types:', handledError);

    // Return empty array instead of throwing
    return [];
  }
};

/**
 * Get service styles, optionally filtered by service type
 * @param {number|null} serviceTypeId - Service type ID to filter by
 * @param {boolean} skipCache - Whether to skip the cache and force a fresh request
 * @returns {Promise<Array>} Array of service styles
 * @throws {Object} Standardized error object
 */
export const getServiceStyles = async (serviceTypeId = null, skipCache = false) => {
  const cacheKey = cacheManager.getServiceStylesKey(serviceTypeId);

  // Check if data is in cache and we're not skipping cache
  if (!skipCache && cacheManager.has(cacheKey)) {
    return cacheManager.get(cacheKey);
  }

  try {
    const response = await api.get('/talents/service-styles', {
      params: {
        service_type_id: serviceTypeId
      }
    });

    // Cache the response
    cacheManager.set(cacheKey, response.data);

    return response.data;
  } catch (error) {
    // Handle the error and convert it to a standardized format
    const handledError = handleApiError(error);
    console.error('Error fetching service styles:', handledError);

    // Return empty array instead of throwing
    return [];
  }
};

/**
 * Get a single talent by ID
 * @param {number} talentId - Talent ID to fetch
 * @param {boolean} skipCache - Whether to skip the cache and force a fresh request
 * @returns {Promise<Object>} Talent object
 */
export const getTalentById = async (talentId, skipCache = false) => {
  const cacheKey = cacheManager.getTalentKey(talentId);

  // Check if data is in cache and we're not skipping cache
  if (!skipCache && cacheManager.has(cacheKey)) {
    return cacheManager.get(cacheKey);
  }

  try {
    const response = await api.get(`/talents/${talentId}`);

    // Format talent for display
    const formattedTalent = formatTalentForDisplay(response.data);

    // Cache the response
    cacheManager.set(cacheKey, formattedTalent);

    return formattedTalent;
  } catch (error) {
    // Handle the error and convert it to a standardized format
    const handledError = handleApiError(error);
    console.error(`Error fetching talent with ID ${talentId}:`, handledError);

    // Return null instead of throwing
    return null;
  }
};

/**
 * Get service categories
 * @param {boolean} skipCache - Whether to skip the cache and force a fresh request
 * @returns {Promise<Array>} Array of service categories
 */
export const getServiceCategories = async (skipCache = false) => {
  const cacheKey = cacheManager.getKey('serviceCategories');

  // Check if data is in cache and we're not skipping cache
  if (!skipCache && cacheManager.has(cacheKey)) {
    return cacheManager.get(cacheKey);
  }

  try {
    const response = await api.get('/service-configuration/categories');

    // Cache the response
    cacheManager.set(cacheKey, response.data);

    return response.data;
  } catch (error) {
    // Handle the error and convert it to a standardized format
    const handledError = handleApiError(error);
    console.error('Error fetching service categories:', handledError);

    // Return empty array instead of throwing
    return [];
  }
};

/**
 * Get races
 * @param {boolean} skipCache - Whether to skip the cache and force a fresh request
 * @returns {Promise<Array>} Array of races
 */
export const getRaces = async (skipCache = false) => {
  const cacheKey = cacheManager.getKey('races');

  // Check if data is in cache and we're not skipping cache
  if (!skipCache && cacheManager.has(cacheKey)) {
    return cacheManager.get(cacheKey);
  }

  try {
    const response = await api.get('/races');

    // Cache the response
    cacheManager.set(cacheKey, response.data);

    return response.data;
  } catch (error) {
    // Handle the error and convert it to a standardized format
    const handledError = handleApiError(error);
    console.error('Error fetching races:', handledError);

    // Return empty array instead of throwing
    return [];
  }
};

/**
 * Get languages
 * @param {boolean} skipCache - Whether to skip the cache and force a fresh request
 * @returns {Promise<Array>} Array of languages
 */
export const getLanguages = async (skipCache = false) => {
  const cacheKey = cacheManager.getKey('languages');

  // Check if data is in cache and we're not skipping cache
  if (!skipCache && cacheManager.has(cacheKey)) {
    return cacheManager.get(cacheKey);
  }

  try {
    const response = await api.get('/languages');

    // Cache the response
    cacheManager.set(cacheKey, response.data);

    return response.data;
  } catch (error) {
    // Handle the error and convert it to a standardized format
    const handledError = handleApiError(error);
    console.error('Error fetching languages:', handledError);

    // Return empty array instead of throwing
    return [];
  }
};

export default {
  filterTalents,
  getServiceTypes,
  getServiceStyles,
  getTalentById,
  getServiceCategories,
  getRaces,
  getLanguages
};
