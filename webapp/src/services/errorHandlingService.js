import { 
  API_ERROR_TYPES, 
  WALLET_ERROR_TYPES, 
  getErrorMessage, 
  getErrorActions, 
  isRecoverableError, 
  getErrorSeverity 
} from '../utils/errorTypes';

/**
 * Enhanced Error Handling Service
 * 
 * This service provides centralized error processing, user message generation,
 * and error recovery mechanisms for wallet and financial operations.
 */
class ErrorHandlingService {
  constructor() {
    this.errorHistory = [];
    this.maxHistorySize = 50;
    this.retryAttempts = new Map();
    this.maxRetryAttempts = 3;
    
    // Bind methods to preserve context
    this.processError = this.processError.bind(this);
    this.createStandardizedError = this.createStandardizedError.bind(this);
    this.shouldRetry = this.shouldRetry.bind(this);
    this.recordRetryAttempt = this.recordRetryAttempt.bind(this);
  }

  /**
   * Process an error and convert it to a standardized format
   * @param {Error|Object} error - The error object
   * @param {string} context - Context where the error occurred
   * @param {Object} metadata - Additional metadata about the error
   * @returns {Object} Standardized error object
   */
  processError(error, context = 'unknown', metadata = {}) {
    const standardizedError = this.createStandardizedError(error, context, metadata);
    
    // Record error in history
    this.recordError(standardizedError);
    
    // Log error for debugging
    this.logError(standardizedError);
    
    return standardizedError;
  }

  /**
   * Create a standardized error object
   * @param {Error|Object} error - The original error
   * @param {string} context - Context where the error occurred
   * @param {Object} metadata - Additional metadata
   * @returns {Object} Standardized error object
   */
  createStandardizedError(error, context, metadata) {
    const timestamp = new Date().toISOString();
    const errorId = this.generateErrorId();
    
    // Determine error type and details
    const { type, originalMessage, statusCode, validationErrors } = this.analyzeError(error);
    
    // Generate user-friendly message
    const userMessage = getErrorMessage(type, originalMessage);
    
    // Get suggested actions
    const actions = getErrorActions(type);
    
    // Determine if error is recoverable
    const recoverable = isRecoverableError(type);
    
    // Get error severity
    const severity = getErrorSeverity(type);
    
    return {
      id: errorId,
      type,
      severity,
      message: userMessage,
      originalMessage,
      statusCode,
      validationErrors,
      actions,
      recoverable,
      context,
      metadata,
      timestamp,
      retryCount: this.getRetryCount(context),
      canRetry: this.shouldRetry(type, context)
    };
  }

  /**
   * Analyze an error to determine its type and extract details
   * @param {Error|Object} error - The error to analyze
   * @returns {Object} Error analysis results
   */
  analyzeError(error) {
    let type = API_ERROR_TYPES.UNKNOWN_ERROR;
    let originalMessage = 'An unexpected error occurred';
    let statusCode = null;
    let validationErrors = null;

    // Handle network errors (no response)
    if (!error.response && error.request) {
      type = API_ERROR_TYPES.NETWORK_ERROR;
      originalMessage = 'Network connection failed';
      return { type, originalMessage, statusCode, validationErrors };
    }

    // Handle API response errors
    if (error.response) {
      statusCode = error.response.status;
      const data = error.response.data || {};
      originalMessage = data.message || error.message || 'API request failed';

      // Map status codes to error types
      switch (statusCode) {
        case 401:
          type = API_ERROR_TYPES.AUTHENTICATION_ERROR;
          break;
        case 403:
          type = this.mapForbiddenError(data);
          break;
        case 404:
          type = API_ERROR_TYPES.NOT_FOUND_ERROR;
          break;
        case 422:
          type = API_ERROR_TYPES.VALIDATION_ERROR;
          validationErrors = data.errors || data.validationErrors;
          break;
        case 429:
          type = API_ERROR_TYPES.RATE_LIMIT_ERROR;
          break;
        case 500:
        case 502:
        case 503:
        case 504:
          type = API_ERROR_TYPES.SERVER_ERROR;
          break;
        default:
          type = this.mapBusinessLogicError(data, originalMessage);
      }
    }

    // Handle JavaScript errors
    if (error instanceof Error && !error.response) {
      originalMessage = error.message;
      type = API_ERROR_TYPES.UNKNOWN_ERROR;
    }

    return { type, originalMessage, statusCode, validationErrors };
  }

  /**
   * Map 403 Forbidden errors to specific wallet error types
   * @param {Object} data - Response data
   * @returns {string} Specific error type
   */
  mapForbiddenError(data) {
    const errorCode = data.error_code;
    const message = data.message || '';

    switch (errorCode) {
      case 'ekyc_required':
        return WALLET_ERROR_TYPES.EKYC_VERIFICATION_REQUIRED;
      case 'email_verification_required':
        return WALLET_ERROR_TYPES.EMAIL_VERIFICATION_REQUIRED;
      default:
        // Check message content for verification requirements
        if (message.toLowerCase().includes('email verification')) {
          return WALLET_ERROR_TYPES.EMAIL_VERIFICATION_REQUIRED;
        }
        if (message.toLowerCase().includes('e-kyc') || message.toLowerCase().includes('kyc')) {
          return WALLET_ERROR_TYPES.EKYC_VERIFICATION_REQUIRED;
        }
        return API_ERROR_TYPES.AUTHENTICATION_ERROR;
    }
  }

  /**
   * Map business logic errors to specific wallet error types
   * @param {Object} data - Response data
   * @param {string} message - Error message
   * @returns {string} Specific error type
   */
  mapBusinessLogicError(data, message) {
    const lowerMessage = message.toLowerCase();

    // Balance-related errors
    if (lowerMessage.includes('insufficient balance') || lowerMessage.includes('insufficient credits')) {
      return WALLET_ERROR_TYPES.INSUFFICIENT_BALANCE;
    }

    // Payment-related errors
    if (lowerMessage.includes('payment failed') || lowerMessage.includes('payment error')) {
      return WALLET_ERROR_TYPES.PAYMENT_FAILED;
    }

    // Withdrawal-related errors
    if (lowerMessage.includes('withdrawal failed') || lowerMessage.includes('withdrawal error')) {
      return WALLET_ERROR_TYPES.WITHDRAWAL_FAILED;
    }

    if (lowerMessage.includes('withdrawal limit')) {
      return WALLET_ERROR_TYPES.WITHDRAWAL_LIMIT_EXCEEDED;
    }

    // Service availability errors
    if (lowerMessage.includes('service unavailable') || lowerMessage.includes('temporarily unavailable')) {
      return WALLET_ERROR_TYPES.SERVICE_UNAVAILABLE;
    }

    if (lowerMessage.includes('maintenance')) {
      return WALLET_ERROR_TYPES.MAINTENANCE_MODE;
    }

    return API_ERROR_TYPES.UNKNOWN_ERROR;
  }

  /**
   * Determine if an operation should be retried
   * @param {string} errorType - The type of error
   * @param {string} context - Context where the error occurred
   * @returns {boolean} Whether to allow retry
   */
  shouldRetry(errorType, context) {
    const retryCount = this.getRetryCount(context);
    
    if (retryCount >= this.maxRetryAttempts) {
      return false;
    }

    return isRecoverableError(errorType);
  }

  /**
   * Record a retry attempt
   * @param {string} context - Context where the retry is happening
   */
  recordRetryAttempt(context) {
    const currentCount = this.retryAttempts.get(context) || 0;
    this.retryAttempts.set(context, currentCount + 1);
  }

  /**
   * Get the current retry count for a context
   * @param {string} context - The context to check
   * @returns {number} Current retry count
   */
  getRetryCount(context) {
    return this.retryAttempts.get(context) || 0;
  }

  /**
   * Clear retry attempts for a context
   * @param {string} context - The context to clear
   */
  clearRetryAttempts(context) {
    this.retryAttempts.delete(context);
  }

  /**
   * Record an error in history
   * @param {Object} error - The standardized error object
   */
  recordError(error) {
    this.errorHistory.unshift(error);
    
    // Limit history size
    if (this.errorHistory.length > this.maxHistorySize) {
      this.errorHistory = this.errorHistory.slice(0, this.maxHistorySize);
    }
  }

  /**
   * Log error for debugging
   * @param {Object} error - The standardized error object
   */
  logError(error) {
    const logLevel = error.severity === 'critical' ? 'error' : 'warn';
    
    console[logLevel](`[ErrorHandlingService] ${error.context}:`, {
      id: error.id,
      type: error.type,
      message: error.message,
      originalMessage: error.originalMessage,
      statusCode: error.statusCode,
      timestamp: error.timestamp
    });
  }

  /**
   * Generate a unique error ID
   * @returns {string} Unique error ID
   */
  generateErrorId() {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get error history
   * @param {number} limit - Maximum number of errors to return
   * @returns {Array} Array of recent errors
   */
  getErrorHistory(limit = 10) {
    return this.errorHistory.slice(0, limit);
  }

  /**
   * Clear error history
   */
  clearErrorHistory() {
    this.errorHistory = [];
  }

  /**
   * Get error statistics
   * @returns {Object} Error statistics
   */
  getErrorStats() {
    const total = this.errorHistory.length;
    const byType = {};
    const bySeverity = {};

    this.errorHistory.forEach(error => {
      byType[error.type] = (byType[error.type] || 0) + 1;
      bySeverity[error.severity] = (bySeverity[error.severity] || 0) + 1;
    });

    return {
      total,
      byType,
      bySeverity,
      retryAttempts: Object.fromEntries(this.retryAttempts)
    };
  }
}

// Create and export singleton instance
const errorHandlingService = new ErrorHandlingService();

export default errorHandlingService;
