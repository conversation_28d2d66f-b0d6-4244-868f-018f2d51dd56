import React, { createContext, useContext, useState, useEffect } from 'react';
import { addNotificationHandler, removeNotificationHandler, setupMessageListener } from '../firebase/firebaseService';

// Create context
const NotificationContext = createContext();

// Maximum number of notifications to keep
const MAX_NOTIFICATIONS = 50;

export const NotificationProvider = ({ children }) => {
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);

  // Handle incoming notifications
  const handleNotification = (notification) => {
    setNotifications(prev => {
      // Add new notification to the beginning of the array
      const updated = [notification, ...prev];
      
      // Limit the number of notifications
      return updated.slice(0, MAX_NOTIFICATIONS);
    });
    
    // Update unread count
    setUnreadCount(prev => prev + 1);
    
    // Store notifications in localStorage
    saveNotificationsToLocalStorage([notification, ...notifications].slice(0, MAX_NOTIFICATIONS));
  };

  // Mark notification as read
  const markAsRead = (notificationId) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === notificationId 
          ? { ...notification, read: true } 
          : notification
      )
    );
    
    // Update unread count
    calculateUnreadCount();
    
    // Update localStorage
    saveNotificationsToLocalStorage(
      notifications.map(notification => 
        notification.id === notificationId 
          ? { ...notification, read: true } 
          : notification
      )
    );
  };

  // Mark all notifications as read
  const markAllAsRead = () => {
    setNotifications(prev => 
      prev.map(notification => ({ ...notification, read: true }))
    );
    
    // Reset unread count
    setUnreadCount(0);
    
    // Update localStorage
    saveNotificationsToLocalStorage(
      notifications.map(notification => ({ ...notification, read: true }))
    );
  };

  // Remove notification
  const removeNotification = (notificationId) => {
    setNotifications(prev => 
      prev.filter(notification => notification.id !== notificationId)
    );
    
    // Update unread count
    calculateUnreadCount();
    
    // Update localStorage
    saveNotificationsToLocalStorage(
      notifications.filter(notification => notification.id !== notificationId)
    );
  };

  // Clear all notifications
  const clearAllNotifications = () => {
    setNotifications([]);
    setUnreadCount(0);
    localStorage.removeItem('missionx_notifications');
  };

  // Calculate unread count
  const calculateUnreadCount = () => {
    const count = notifications.filter(notification => !notification.read).length;
    setUnreadCount(count);
  };

  // Save notifications to localStorage
  const saveNotificationsToLocalStorage = (notifications) => {
    localStorage.setItem('missionx_notifications', JSON.stringify(notifications));
  };

  // Load notifications from localStorage
  const loadNotificationsFromLocalStorage = () => {
    const stored = localStorage.getItem('missionx_notifications');
    if (stored) {
      try {
        const parsed = JSON.parse(stored);
        setNotifications(parsed);
        
        // Calculate unread count
        const count = parsed.filter(notification => !notification.read).length;
        setUnreadCount(count);
      } catch (error) {
        console.error('Error parsing stored notifications:', error);
      }
    }
  };

  // Set up notification listener
  useEffect(() => {
    // Load stored notifications
    loadNotificationsFromLocalStorage();
    
    // Set up Firebase message listener
    setupMessageListener();
    
    // Register notification handler
    addNotificationHandler(handleNotification);
    
    // Clean up
    return () => {
      removeNotificationHandler(handleNotification);
    };
  }, []);

  const addNotification = (message, type = 'info', duration = 3000) => {
    const id = Date.now().toString();
    const newNotification = {
      id,
      message,
      type,
      duration
    };
    
    setNotifications(prev => [...prev, newNotification]);
    
    // Auto dismiss
    setTimeout(() => {
      dismissNotification(id);
    }, duration);
    
    return id;
  };

  const dismissNotification = (id) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id));
  };

  // Context value
  const value = {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    removeNotification,
    clearAllNotifications,
    addNotification,
    dismissNotification
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
      
      {/* Notification Display */}
      <div className="fixed bottom-5 right-5 z-50 flex flex-col gap-2">
        {notifications.map(notification => (
          <div 
            key={notification.id}
            className={`p-4 rounded-lg shadow-lg flex items-center justify-between max-w-xs animate-fade-in ${
              notification.type === 'success' 
                ? 'bg-green-500 text-white' 
                : notification.type === 'error'
                ? 'bg-red-500 text-white'
                : notification.type === 'warning'
                ? 'bg-yellow-500 text-white'
                : 'bg-blue-500 text-white'
            }`}
          >
            <span>{notification.message}</span>
            <button 
              onClick={() => dismissNotification(notification.id)}
              className="ml-3 text-white hover:text-gray-200"
            >
              &times;
            </button>
          </div>
        ))}
      </div>
    </NotificationContext.Provider>
  );
};

// Custom hook for using notification context
export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};

export default NotificationContext;
