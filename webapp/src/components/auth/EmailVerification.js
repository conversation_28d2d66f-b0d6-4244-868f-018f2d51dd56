/**
 * Email Verification Component
 * 
 * This component handles email verification for users.
 */

import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import useTranslation from '../../hooks/useTranslation';
import { useNavigate, useLocation } from 'react-router-dom';
import axios from 'axios';

/**
 * EmailVerification component
 * 
 * @param {Object} props
 * @param {boolean} props.standalone - Whether the component is rendered standalone or within another component
 */
const EmailVerification = ({ standalone = true }) => {
    // Get auth context
    const { user, updateUser } = useAuth();
    
    // Navigation and location
    const navigate = useNavigate();
    const location = useLocation();
    
    // State management
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');
    const [resendCooldown, setResendCooldown] = useState(0);
    const [verifying, setVerifying] = useState(false);
    
    // Use the translation hook
    const { t } = useTranslation('auth');

    // Parse query parameters for verification token
    useEffect(() => {
        const queryParams = new URLSearchParams(location.search);
        const token = queryParams.get('token');
        const email = queryParams.get('email');
        
        if (token && email) {
            verifyEmail(token, email);
        }
    }, [location.search]);

    // Handle countdown timer for resend
    useEffect(() => {
        let timer;
        if (resendCooldown > 0) {
            timer = setTimeout(() => {
                setResendCooldown(prev => prev - 1);
            }, 1000);
        }
        
        return () => {
            if (timer) clearTimeout(timer);
        };
    }, [resendCooldown]);

    // Verify email with token
    const verifyEmail = useCallback(async (token, email) => {
        setVerifying(true);
        setLoading(true);
        setError('');
        setSuccess('');
        
        try {
            // Use a default API URL for testing if environment variable is not set
            const apiBaseUrl = process.env.REACT_APP_API_URL || '/api';
            
            const response = await axios.get(`${apiBaseUrl}/email/verify`, {
                params: { token, email }
            });
            
            if (response.data.success) {
                setSuccess(t('email.verificationSuccess', 'Email verified successfully'));
                
                // Update user data if available
                if (user) {
                    updateUser({ email_verified: true });
                }
                
                // Redirect to home after a delay if standalone
                if (standalone) {
                    setTimeout(() => {
                        navigate('/home');
                    }, 3000);
                }
            } else {
                setError(response.data.message || t('email.verificationFailed', 'Email verification failed'));
            }
        } catch (err) {
            console.error('Email verification failed:', err);
            setError(err.response?.data?.message || t('email.verificationFailed', 'Email verification failed'));
        } finally {
            setLoading(false);
        }
    }, [user, updateUser, navigate, standalone, t]);

    // Send verification email
    const sendVerificationEmail = useCallback(async () => {
        if (loading || resendCooldown > 0) return;
        
        setLoading(true);
        setError('');
        setSuccess('');
        
        try {
            // Use a default API URL for testing if environment variable is not set
            const apiBaseUrl = process.env.REACT_APP_API_URL || '/api';
            
            const response = await axios.post(
                `${apiBaseUrl}/email/send-verification`,
                {},
                {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    }
                }
            );
            
            if (response.data.success) {
                setSuccess(t('email.sentSuccess', 'Verification email sent successfully'));
                setResendCooldown(60); // 60 seconds cooldown
            } else {
                setError(response.data.message || t('email.sendFailed', 'Failed to send verification email'));
            }
        } catch (err) {
            console.error('Failed to send verification email:', err);
            setError(err.response?.data?.message || t('email.sendFailed', 'Failed to send verification email'));
        } finally {
            setLoading(false);
        }
    }, [loading, resendCooldown, t]);

    // If verifying a token, show a simple loading/result screen
    if (verifying) {
        return (
            <div className={`${standalone ? 'min-h-screen flex items-center justify-center bg-gray-50' : ''}`}>
                <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
                    <div className="text-center">
                        <h2 className="text-2xl font-bold text-gray-800 mb-4">
                            {t('email.verification', 'Email Verification')}
                        </h2>
                        
                        {loading && (
                            <div className="flex justify-center my-6">
                                <svg className="animate-spin h-10 w-10 text-blue-500" fill="none" viewBox="0 0 24 24">
                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                                </svg>
                            </div>
                        )}
                        
                        {error && (
                            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-4">
                                {error}
                            </div>
                        )}
                        
                        {success && (
                            <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-4">
                                {success}
                            </div>
                        )}
                        
                        {standalone && (
                            <button
                                type="button"
                                onClick={() => navigate('/home')}
                                className="mt-4 inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                            >
                                {t('email.goToHome', 'Go to Home')}
                            </button>
                        )}
                    </div>
                </div>
            </div>
        );
    }

    // Regular email verification request UI
    return (
        <div className={`${standalone ? 'min-h-screen flex items-center justify-center bg-gray-50' : ''}`}>
            <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
                <div className="text-center">
                    <h2 className="text-2xl font-bold text-gray-800 mb-2">
                        {t('email.verification', 'Email Verification')}
                    </h2>
                    <p className="text-gray-600 mb-6">
                        {t('email.verifyPrompt', 'Please verify your email address to access all features')}
                    </p>
                    
                    {error && (
                        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-4">
                            {error}
                        </div>
                    )}
                    
                    {success && (
                        <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-4">
                            {success}
                        </div>
                    )}
                    
                    <button
                        type="button"
                        onClick={sendVerificationEmail}
                        disabled={loading || resendCooldown > 0}
                        className="w-full flex justify-center items-center py-3 px-4 rounded-lg text-sm font-semibold text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200 disabled:bg-blue-300 disabled:cursor-not-allowed"
                    >
                        {loading ? (
                            <>
                                <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                                </svg>
                                {t('email.sending', 'Sending...')}
                            </>
                        ) : resendCooldown > 0 ? (
                            `${t('email.resendIn', 'Resend in')} ${resendCooldown}s`
                        ) : (
                            t('email.sendVerification', 'Send Verification Email')
                        )}
                    </button>
                    
                    {standalone && (
                        <button
                            type="button"
                            onClick={() => navigate('/home')}
                            className="mt-4 text-sm text-gray-600 hover:text-gray-800 transition-colors duration-150 focus:outline-none"
                        >
                            {t('email.skipForNow', 'Skip for now')}
                        </button>
                    )}
                </div>
            </div>
        </div>
    );
};

export default EmailVerification;
