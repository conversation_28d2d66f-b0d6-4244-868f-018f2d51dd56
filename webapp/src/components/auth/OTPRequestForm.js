/**
 * OTP Request Form Component
 * First step of registration - phone number entry and OTP request
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '../../contexts/AuthContext';
import Form<PERSON>ield from '../ui/FormField';
import { OTPButton } from '../ui/GradientButton';
import { phoneValidationService } from '../../services/phoneValidationService';
import { PhoneIcon, InformationCircleIcon } from '@heroicons/react/24/outline';
import { cn } from '../../lib/utils';
import useTranslation from '../../hooks/useTranslation';

/**
 * OTPRequestForm Component
 * @param {Object} props
 * @param {Function} props.onSuccess - Callback when OTP is sent successfully
 * @param {Function} props.onBack - Callback to go back
 * @param {string} props.initialPhone - Initial phone number value
 * @param {string} props.className - Additional CSS classes
 */
const OTPRequestForm = ({
  onSuccess,
  onBack,
  initialPhone = '',
  className = ''
}) => {
  const { requestOtp, loading, error } = useAuth();
  const { t } = useTranslation('auth');
  const [authError, setAuthError] = useState(null); // Use local state for specific errors

  // Form state
  const [mobileNumber, setMobileNumber] = useState(initialPhone);
  const [validation, setValidation] = useState({ valid: true, message: '' });

  // Rate limiting state (60 seconds as per backend)
  const [countdown, setCountdown] = useState(0);
  const [lastRequestTime, setLastRequestTime] = useState(null);

  // Success state
  const [otpSent, setOtpSent] = useState(false);
  const [sentToNumber, setSentToNumber] = useState('');

  // Clear authError when component mounts or error prop changes
  useEffect(() => {
    setAuthError(error); // Initialize with global error if any
  }, [error]);

  // Initialize countdown from localStorage
  useEffect(() => {
    const savedRequestTime = localStorage.getItem('lastOtpRequestTime');
    if (savedRequestTime) {
      const timeDiff = Date.now() - parseInt(savedRequestTime);
      const remainingTime = Math.max(0, 60 - Math.floor(timeDiff / 1000));
      if (remainingTime > 0) {
        setCountdown(remainingTime);
        setLastRequestTime(parseInt(savedRequestTime));
      }
    }
  }, []);

  // Countdown timer
  useEffect(() => {
    let timer;
    if (countdown > 0) {
      timer = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            localStorage.removeItem('lastOtpRequestTime');
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => clearInterval(timer);
  }, [countdown]);

  // Handle phone number input
  const handlePhoneChange = (e) => {
    const value = e.target.value;
    const formattedValue = phoneValidationService.autoFormat(value, mobileNumber);
    setMobileNumber(formattedValue);

    // Real-time validation
    const phoneValidation = phoneValidationService.validateWithMessage(formattedValue);
    setValidation(phoneValidation);

    // Reset success state when phone changes
    if (otpSent && formattedValue !== sentToNumber) {
      setOtpSent(false);
      setSentToNumber('');
      setAuthError(null); // Clear previous OTP success/error messages
    }
  };

  // Handle OTP request
  const handleRequestOtp = async () => {
    // Validate phone number
    const phoneValidation = phoneValidationService.validateWithMessage(mobileNumber);
    setValidation(phoneValidation);

    if (!phoneValidation.valid) {
      return;
    }
    setAuthError(null); // Clear previous errors

    try {
      // Normalize phone number for backend
      const normalizedPhone = phoneValidationService.normalizeForBackend(mobileNumber);

      const result = await requestOtp(normalizedPhone);

      if (result.success) {
        // Set countdown and save request time
        const requestTime = Date.now();
        setCountdown(60);
        setLastRequestTime(requestTime);
        localStorage.setItem('lastOtpRequestTime', requestTime.toString());

        // Set success state
        setOtpSent(true);
        setSentToNumber(mobileNumber);

        onSuccess?.(normalizedPhone, mobileNumber);
      } else {
        // Handle specific validation errors from requestOtp
        if (result.validationErrors && result.validationErrors.mobile_number) {
          setAuthError(result.validationErrors.mobile_number[0]);
        } else {
          setAuthError(result.error || 'Failed to send OTP.');
        }
      }
    } catch (err) {
      console.error('OTP request error:', err);
    }
  };

  // Handle resend OTP
  const handleResendOtp = () => {
    if (countdown === 0) {
      handleRequestOtp();
    }
  };

  // Get display phone number
  const getDisplayPhone = () => {
    return phoneValidationService.getDisplayFormat(mobileNumber);
  };

  return (
    <motion.div
      className={cn('space-y-6', className)}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Header */}
      <div className="text-center">
        <h3 className="text-xl font-semibold text-gray-800 mb-2">
          {otpSent ? t('otpRequest.sentTitle', 'Verification Code Sent!') : t('otpRequest.enterMobile', 'Enter Your Mobile Number')}
        </h3>
        <p className="text-gray-600">
          {otpSent
            ? t('otpRequest.sentMessage', "We've sent a 6-digit code to") + ` ${getDisplayPhone()}`
            : t('otpRequest.description', "We'll send you a verification code to get started")
          }
        </p>
      </div>

      {/* Error Display - Use local authError state */}
      {authError && (
        <motion.div
          className="p-4 bg-red-50 border border-red-200 rounded-xl"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
        >
          <p className="text-red-600 text-sm text-center">{authError}</p>
        </motion.div>
      )}

      {/* Success Display */}
      {otpSent && (
        <motion.div
          className="p-4 bg-green-50 border border-green-200 rounded-xl"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
        >
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <div>
              <p className="text-green-700 font-medium">{t('otpRequest.sentNotification', 'Verification code sent!')}</p>
              <p className="text-green-600 text-sm">{t('otpRequest.checkMessages', 'Check your messages for the 6-digit code')}</p>
            </div>
          </div>
        </motion.div>
      )}

      {/* Phone Number Input */}
      <div className="space-y-4">
        <FormField
          label={t('register.mobileNumber', 'Mobile Number')}
          type="tel"
          value={mobileNumber}
          onChange={handlePhoneChange}
          placeholder={t('register.placeholders.mobileNumber', '+60 12-345 6789')}
          required
          icon={<PhoneIcon />}
          validation={validation}
          helperText={t('register.helperText.mobileNumber', 'Enter your Malaysian mobile number')}
          disabled={loading}
        />

        {/* OTP Request Button */}
        <OTPButton
          countdown={countdown}
          onResend={handleResendOtp}
          loading={loading}
          disabled={!validation.valid || !mobileNumber || countdown > 0}
          className="w-full"
        >
          {otpSent ? t('otp.resend', 'Resend Code') : t('otpRequest.sendCode', 'Send Verification Code')}
        </OTPButton>
      </div>

      {/* Information Box */}
      <motion.div
        className="p-4 bg-blue-50 border border-blue-200 rounded-xl"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3 }}
      >
        <div className="flex space-x-3">
          <InformationCircleIcon className="items-start w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
          <div className="space-y-2">
            <h4 className="text-blue-700 font-medium">{t('otpRequest.infoTitle', 'Important Information')}</h4>
            <ul className="text-blue-600 text-left text-sm space-y-1">
              <li>• SMS charges may apply from your mobile provider</li>
              <li>• Code expires in 5 minutes</li>
              <li>• You can request a new code after 60 seconds</li>
              <li>• Make sure your phone can receive SMS messages</li>
            </ul>
          </div>
        </div>
      </motion.div>

      {/* Supported Formats */}
      <motion.div
        className="p-4 bg-gray-50 rounded-xl border border-gray-200"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.4 }}
      >
        <h4 className="text-gray-700 font-medium mb-2">{t('otpRequest.supportedFormats', 'Supported Phone Formats:')}</h4>
        <div className="grid grid-cols-2 gap-2 text-gray-600 text-sm">
          {phoneValidationService.getSupportedFormats().map((format, index) => (
            <div key={index} className="font-mono">
              {format}
            </div>
          ))}
        </div>
      </motion.div>

      {/* Rate Limiting Info */}
      {countdown > 0 && (
        <motion.div
          className="p-3 bg-yellow-50 border border-yellow-200 rounded-xl"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
        >
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse" />
            <p className="text-yellow-700 text-sm">
              {t('otpRequest.wait', 'Please wait {{count}} seconds before requesting another code', { count: countdown })}
            </p>
          </div>
        </motion.div>
      )}

      {/* Back Button */}
      {onBack && (
        <motion.button
          onClick={onBack}
          className="w-full py-3 text-gray-500 bg-transparent hover:bg-transparent hover:text-gray-700 transition-colors text-sm"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          {t('otpRequest.back', '← Back to login')}
        </motion.button>
      )}

      {/* Next Steps */}
      {otpSent && (
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          <p className="text-gray-500 text-sm">
            {t('otpRequest.nextStep', 'Enter the verification code in the next step to continue registration')}
          </p>
        </motion.div>
      )}
    </motion.div>
  );
};

export default OTPRequestForm;
