/**
 * DeviceRegistration Component
 * 
 * This component handles silent registration of device tokens for push notifications.
 * It automatically attempts to register the device when mounted and when the user logs in.
 */

import { useEffect, useState, useCallback } from 'react';
import { getToken } from 'firebase/messaging';
import { messaging } from '../../firebase/config';
import { authService } from '../../services/authService';
import { useAuth } from '../../contexts/AuthContext';

/**
 * DeviceRegistration component
 * 
 * This component doesn't render anything visible but handles device token registration
 * for push notifications in the background.
 */
const DeviceRegistration = () => {
  const { isAuthenticated } = useAuth();
  const [registrationAttempted, setRegistrationAttempted] = useState(false);

  /**
   * Register device token with backend
   */
  const registerDeviceToken = useCallback(async () => {
    if (!isAuthenticated) return;

    try {
      // Check if notifications are supported
      if (!('Notification' in window)) {
        console.log('This browser does not support notifications');
        return;
      }

      // Request permission if not already granted
      if (Notification.permission !== 'granted') {
        const permission = await Notification.requestPermission();
        if (permission !== 'granted') {
          console.log('Notification permission denied');
          return;
        }
      }

      // Get Firebase Cloud Messaging token
      const deviceToken = await getToken(messaging, {
        vapidKey: process.env.REACT_APP_FIREBASE_VAPID_KEY
      });

      if (!deviceToken) {
        console.log('No registration token available');
        return;
      }

      // Register token with backend
      const deviceInfo = {
        device_type: 'web',
        device_name: navigator.userAgent
      };

      const response = await authService.registerDeviceToken(
        deviceToken,
        deviceInfo.device_type,
        deviceInfo.device_name
      );

      if (response.success) {
        console.log('Device token registered successfully');
      } else {
        console.error('Failed to register device token:', response.error);
      }
    } catch (error) {
      console.error('Error registering device token:', error);
    } finally {
      setRegistrationAttempted(true);
    }
  }, [isAuthenticated]);

  // Register device token when component mounts and when auth state changes
  useEffect(() => {
    if (isAuthenticated && !registrationAttempted) {
      registerDeviceToken();
    }
  }, [isAuthenticated, registrationAttempted, registerDeviceToken]);

  // This component doesn't render anything visible
  return null;
};

export default DeviceRegistration;
