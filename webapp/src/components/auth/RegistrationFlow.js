/**
 * Registration Flow Container Component
 * Manages multi-step registration process with step navigation and data persistence
 */

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { StepCard } from '../ui/GlassMorphCard';
import OTPRequestForm from './OTPRequestForm';
import OTPVerificationInput from './OTPVerificationInput';
import RegistrationForm from './RegistrationForm';
import ProfilePictureUpload from './ProfilePictureUpload';
import ReferralInput from './ReferralInput';
import { useAuth } from '../../contexts/AuthContext';
import { deviceTokenService } from '../../services/deviceTokenService'; // Import deviceTokenService
import { cn } from '../../lib/utils';

/**
 * RegistrationFlow Component
 * @param {Object} props
 * @param {Function} props.onSuccess - Callback when registration completes
 * @param {Function} props.onBack - Callback to go back to login
 * @param {string} props.className - Additional CSS classes
 */
const RegistrationFlow = ({
  onSuccess,
  onBack,
  className = ''
}) => {
  const { requestOtp } = useAuth();

  // Step management
  const [currentStep, setCurrentStep] = useState(1);
  const [completedSteps, setCompletedSteps] = useState(new Set());

  // Registration data persistence
  const [registrationData, setRegistrationData] = useState({
    // Step 1: Phone & OTP
    mobileNumber: '',
    normalizedPhone: '',
    otp: '',

    // Step 2: Personal Info
    name: '',
    email: '',
    nickname: '',
    gender: '',
    dateOfBirth: '',
    password: '',
    confirmPassword: '',

    // Step 3: Optional Info
    profilePicture: null,
    referralType: null,
    referralValue: '',

    // Device Info
    deviceToken: null,
    deviceType: 'web',
    deviceName: navigator.userAgent
  });

  // Step configuration
  const steps = [
    {
      id: 1,
      title: 'Phone Verification',
      description: 'Enter your mobile number',
      component: 'OTPRequest'
    },
    {
      id: 2,
      title: 'Verify Code',
      description: 'Enter the 6-digit code',
      component: 'OTPVerification'
    },
    {
      id: 3,
      title: 'Personal Details',
      description: 'Complete your profile',
      component: 'PersonalInfo'
    },
    {
      id: 4,
      title: 'Finish Setup',
      description: 'Optional profile picture',
      component: 'FinalStep'
    }
  ];

  // Load saved registration data
  useEffect(() => {
    const savedData = localStorage.getItem('registrationData');
    if (savedData) {
      try {
        const parsedData = JSON.parse(savedData);
        setRegistrationData(prev => ({ ...prev, ...parsedData }));

        // Determine current step based on saved data
        if (parsedData.otp && parsedData.mobileNumber) {
          setCurrentStep(3);
          setCompletedSteps(new Set([1, 2]));
        } else if (parsedData.mobileNumber) {
          setCurrentStep(2);
          setCompletedSteps(new Set([1]));
        }
      } catch (error) {
        console.error('Error loading registration data:', error);
      }
    }
  }, []);

  // Save registration data to localStorage
  const saveRegistrationData = useCallback((data) => {
    const updatedData = { ...registrationData, ...data };
    setRegistrationData(updatedData);

    // Save to localStorage (exclude sensitive data)
    const dataToSave = { ...updatedData };
    delete dataToSave.password;
    delete dataToSave.confirmPassword;
    delete dataToSave.otp;

    localStorage.setItem('registrationData', JSON.stringify(dataToSave));
  }, [registrationData]);

  // Handle step completion
  const handleStepComplete = useCallback((stepData) => {
    saveRegistrationData(stepData);
    setCompletedSteps(prev => new Set([...prev, currentStep]));

    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
    }
  }, [saveRegistrationData, currentStep, steps.length]);

  // Handle step navigation
  const goToStep = useCallback((stepNumber) => {
    if (stepNumber <= currentStep || completedSteps.has(stepNumber - 1)) {
      setCurrentStep(stepNumber);
    }
  }, [currentStep, completedSteps]);

  // Handle going back
  const handleBack = useCallback(() => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    } else {
      // Clear registration data and go back to login
      localStorage.removeItem('registrationData');
      onBack?.();
    }
  }, [currentStep, onBack]);

  // Handle OTP request success
  const handleOtpRequestSuccess = useCallback((normalizedPhone, displayPhone) => {
    handleStepComplete({
      normalizedPhone,
      mobileNumber: displayPhone
    });
  }, [handleStepComplete]);

  // Handle OTP verification success
  const handleOtpVerificationSuccess = useCallback((otp) => {
    handleStepComplete({ otp });
  }, [handleStepComplete]);

  // Handle personal info completion
  const handlePersonalInfoComplete = useCallback((personalData) => {
    handleStepComplete(personalData);
  }, [handleStepComplete]);

  // Handle final registration
  const handleRegistrationComplete = useCallback((finalData) => {
    const completeData = { ...registrationData, ...finalData };

    // Clear saved data
    localStorage.removeItem('registrationData');

    // Call success callback
    onSuccess?.(completeData);
  }, [registrationData, onSuccess]);

  // Handle OTP resend
  const handleOtpResend = useCallback(async () => {
    try {
      await requestOtp(registrationData.normalizedPhone);
    } catch (error) {
      console.error('OTP resend error:', error);
    }
  }, [requestOtp, registrationData.normalizedPhone]);

  // Handle step-specific back navigation
  const handleOtpVerificationBack = useCallback(() => {
    setCurrentStep(1);
  }, []);

  const handlePersonalInfoBack = useCallback(() => {
    setCurrentStep(2);
  }, []);

  const handleFinalStepBack = useCallback(() => {
    setCurrentStep(3);
  }, []);

  // Final step component with profile picture and referral
  const FinalStepComponent = ({ registrationData, onComplete, onBack }) => {
    const { register, loading, error } = useAuth();
    const [profilePicture, setProfilePicture] = useState(null);
    const [referralType, setReferralType] = useState(null);
    const [referralValue, setReferralValue] = useState('');

    const handleComplete = async () => {
      try {
        // Generate device token and get device info just before sending
        const currentDeviceToken = deviceTokenService.generateMockDeviceToken();
        const deviceInfo = deviceTokenService.getDeviceInfo();

        // Prepare final registration data with proper field mapping
        const payload = {
          // Map frontend state keys to backend expected keys
          name: registrationData.name,
          email: registrationData.email,
          nickname: registrationData.nickname,
          gender: registrationData.gender,
          date_of_birth: registrationData.dateOfBirth, // Key change
          password: registrationData.password,
          // confirmPassword is not sent to backend
          mobile_number: registrationData.normalizedPhone, // Key change
          otp: registrationData.otp,
          country_code: 'MY', // As per backend RegisterRequest

          // Optional fields from current step
          profile_picture: profilePicture,
          ...(referralType && referralValue ? {
            referral_type: referralType,
            referral_value: referralValue
          } : {}),

          // Use freshly generated/retrieved device info
          device_token: currentDeviceToken,
          device_type: deviceInfo.device_type, // Ensure this matches what getDeviceInfo provides
          device_name: deviceInfo.device_name, // Ensure this matches
        };

        // Debug logging to see what data is being sent
        console.log('🔍 Final registration data being sent:', {
          ...payload,
          password: '[HIDDEN]',
          otp: '[HIDDEN]'
        });

        const result = await register(payload); // Use the mapped payload
        if (result.success) {
          onComplete(result);
        } else {
          console.error('❌ Registration failed:', result.error);
          if (result.validationErrors) {
            console.error('📋 Validation errors:', result.validationErrors);
          }
        }
      } catch (error) {
        console.error('💥 Final registration error:', error);
        if (error.response?.data) {
          console.error('🔍 Server response data:', error.response.data);
        }
      }
    };

    return (
      <div className="space-y-4">
        <div className="text-center">
          <h3 className="text-xl font-semibold text-gray-800 mb-2">Almost Done!</h3>
          <p className="text-gray-600">Add a profile picture and referral (optional)</p>
        </div>

        {/* Error Display */}
        {error && (
          <motion.div
            className="p-4 bg-red-500/10 border border-red-500/20 rounded-xl"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
          >
            <p className="text-red-600 text-sm text-center">{error}</p>
          </motion.div>
        )}

        <ProfilePictureUpload
          value={profilePicture}
          onChange={setProfilePicture}
          onRemove={() => setProfilePicture(null)}
        />

        <ReferralInput
          referralType={referralType}
          referralValue={referralValue}
          onTypeChange={setReferralType}
          onValueChange={setReferralValue}
        />

        <div className="flex space-x-3">
          <button
            onClick={onBack}
            className="flex-1 py-3 px-4 text-gray-600 hover:text-gray-800 border border-gray-300 hover:border-gray-400 rounded-xl transition-colors"
            disabled={loading}
          >
            Back
          </button>
          <button
            onClick={handleComplete}
            disabled={loading}
            className="flex-1 py-3 px-4 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white rounded-xl font-medium transition-all duration-300 disabled:opacity-50"
          >
            {loading ? 'Creating Account...' : 'Complete Registration'}
          </button>
        </div>
      </div>
    );
  };

  // Get current step component
  const getCurrentStepComponent = () => {
    const step = steps[currentStep - 1];

    switch (step.component) {
      case 'OTPRequest':
        return (
          <OTPRequestForm
            onSuccess={handleOtpRequestSuccess}
            onBack={handleBack}
            initialPhone={registrationData.mobileNumber}
          />
        );

      case 'OTPVerification':
        return (
          <OTPVerificationInput
            mobileNumber={registrationData.mobileNumber}
            normalizedPhone={registrationData.normalizedPhone}
            onSuccess={handleOtpVerificationSuccess}
            onBack={handleOtpVerificationBack}
            onResend={handleOtpResend}
          />
        );

      case 'PersonalInfo':
        return (
          <RegistrationForm
            initialData={registrationData}
            onSuccess={handlePersonalInfoComplete}
            onBack={handlePersonalInfoBack}
          />
        );

      case 'FinalStep':
        return (
          <FinalStepComponent
            registrationData={registrationData}
            onComplete={handleRegistrationComplete}
            onBack={handleFinalStepBack}
          />
        );

      default:
        return null;
    }
  };

  return (
    <motion.div
      className={cn('space-y-6', className)}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      {/* Progress Indicator */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-800">
            Step {currentStep} of {steps.length}
          </h2>
          <span className="text-gray-600 text-sm">
            {Math.round((currentStep / steps.length) * 100)}% Complete
          </span>
        </div>

        {/* Progress Bar */}
        <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
          <motion.div
            className="h-full bg-gradient-to-r from-indigo-500 to-purple-500"
            initial={{ width: 0 }}
            animate={{ width: `${(currentStep / steps.length) * 100}%` }}
            transition={{ duration: 0.5, ease: "easeOut" }}
          />
        </div>

        {/* Step Indicators */}
        <div className="flex justify-between">
          {steps.map((step) => (
            <button
              key={step.id}
              onClick={() => goToStep(step.id)}
              disabled={step.id > currentStep && !completedSteps.has(step.id - 1)}
              className={cn(
                'flex flex-col items-center bg-transparent space-y-2 p-2 rounded-lg transition-all duration-300',
                'disabled:cursor-not-allowed',
                step.id === currentStep && 'bg-indigo-50',
                step.id < currentStep && 'text-green-600',
                step.id === currentStep && 'text-indigo-700',
                step.id > currentStep && 'text-gray-400'
              )}
            >
              <div className={cn(
                'w-8 h-8 rounded-full flex bg-transparent items-center justify-center text-sm font-medium transition-all duration-300',
                completedSteps.has(step.id) && 'bg-green-500 text-white',
                step.id === currentStep && 'bg-indigo-500 text-white',
                step.id > currentStep && !completedSteps.has(step.id) && 'bg-gray-200 text-gray-500'
              )}>
                {completedSteps.has(step.id) ? (
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                ) : (
                  step.id
                )}
              </div>
              <div className="text-center">
                <div className="text-xs font-medium">{step.title}</div>
                <div className="text-xs opacity-70 hidden sm:block">{step.description}</div>
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Current Step Content */}
      <StepCard isActive={true} className="min-h-[400px]">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentStep}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            {getCurrentStepComponent()}
          </motion.div>
        </AnimatePresence>
      </StepCard>

      {/* Navigation */}
      <div className="flex justify-between items-center">
        <button
          onClick={handleBack}
          className="px-4 py-2 text-gray-600 bg-transparent hover:bg-transparent hover:text-gray-800 transition-colors"
        >
          ← {currentStep === 1 ? 'Back to Login' : 'Previous Step'}
        </button>

        <div className="text-gray-500 text-sm">
          {currentStep === steps.length ? 'Almost done!' : `${steps.length - currentStep} steps remaining`}
        </div>
      </div>
    </motion.div>
  );
};

export default RegistrationFlow;
