/**
 * Password Reset Flow Component
 * Multi-step password reset with OTP verification and security features
 */

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '../../contexts/AuthContext';
import <PERSON><PERSON>ield from '../ui/FormField';
import { SubmitButton, OTPButton } from '../ui/GradientButton';
import { AuthCard } from '../ui/GlassMorphCard';
import OTPVerificationInput from './OTPVerificationInput';
import { phoneValidationService } from '../../services/phoneValidationService';
import { validatePassword } from '../../utils/validationUtils';
import { PhoneIcon, LockClosedIcon, CheckCircleIcon } from '@heroicons/react/24/outline';
import { cn } from '../../lib/utils';
import useTranslation from '../../hooks/useTranslation';

/**
 * PasswordResetFlow Component
 * @param {Object} props
 * @param {Function} props.onSuccess - Callback when password reset succeeds
 * @param {Function} props.onCancel - Callback to cancel and go back
 * @param {string} props.className - Additional CSS classes
 */
const PasswordResetFlow = ({
  onSuccess,
  onCancel,
  className = ''
}) => {
  const { requestOtp, verifyOtpForReset, resetPassword, loading, error } = useAuth();
  const { t } = useTranslation('auth');

  // Flow state
  const [currentStep, setCurrentStep] = useState(1); // 1: Phone, 2: OTP, 3: New Password, 4: Success
  const [resetData, setResetData] = useState({
    mobileNumber: '',
    normalizedPhone: '',
    otp: '',
    newPassword: '',
    confirmPassword: ''
  });

  // Validation state
  const [validation, setValidation] = useState({
    mobileNumber: { valid: true, message: '' },
    newPassword: { valid: true, message: '' },
    confirmPassword: { valid: true, message: '' }
  });

  // Step 1: Phone Number Entry
  const PhoneStep = () => {
    const [phoneNumber, setPhoneNumber] = useState(resetData.mobileNumber);

    const handlePhoneChange = (e) => {
      const value = e.target.value;
      const formattedValue = phoneValidationService.autoFormat(value, phoneNumber);
      setPhoneNumber(formattedValue);

      // Real-time validation
      const phoneValidation = phoneValidationService.validateWithMessage(formattedValue);
      setValidation(prev => ({ ...prev, mobileNumber: phoneValidation }));
    };

    const handleSubmit = async (e) => {
      e.preventDefault();

      const phoneValidation = phoneValidationService.validateWithMessage(phoneNumber);
      setValidation(prev => ({ ...prev, mobileNumber: phoneValidation }));

      if (!phoneValidation.valid) return;

      try {
        const normalizedPhone = phoneValidationService.normalizeForBackend(phoneNumber);
        const result = await requestOtp(normalizedPhone);

        if (result.success) {
          setResetData(prev => ({
            ...prev,
            mobileNumber: phoneNumber,
            normalizedPhone
          }));
          setCurrentStep(2);
        }
      } catch (err) {
        console.error('OTP request error:', err);
      }
    };

    return (
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="text-center">
          <h3 className="text-xl font-semibold text-gray-800 mb-2">{t('forgotPassword.title', 'Reset Your Password')}</h3>
          <p className="text-gray-600">
            {t('forgotPassword.instructions', 'Enter your mobile number to receive a verification code')}
          </p>
        </div>

        <FormField
          label={t('register.mobileNumber', 'Mobile Number')}
          type="tel"
          value={phoneNumber}
          onChange={handlePhoneChange}
          placeholder={t('register.placeholders.mobileNumber', '+60 12-345 6789')}
          required
          icon={<PhoneIcon />}
          validation={validation.mobileNumber}
          helperText={t('forgotPassword.helper', 'Enter the mobile number associated with your account')}
        />

        <SubmitButton
          loading={loading}
          disabled={!validation.mobileNumber.valid || !phoneNumber}
        >
          {t('forgotPassword.sendButton', 'Send Reset Code')}
        </SubmitButton>
      </form>
    );
  };

  // Step 2: OTP Verification
  const OTPStep = () => {
    const handleOtpSuccess = async (otp) => {
      try {
        const result = await verifyOtpForReset(resetData.normalizedPhone, otp);

        if (result.success) {
          setResetData(prev => ({ ...prev, otp }));
          setCurrentStep(3);
        }
      } catch (err) {
        console.error('OTP verification error:', err);
      }
    };

    const handleResend = async () => {
      try {
        await requestOtp(resetData.normalizedPhone);
      } catch (err) {
        console.error('Resend OTP error:', err);
      }
    };

    return (
      <OTPVerificationInput
        mobileNumber={resetData.mobileNumber}
        normalizedPhone={resetData.normalizedPhone}
        onSuccess={handleOtpSuccess}
        onBack={() => setCurrentStep(1)}
        onResend={handleResend}
      />
    );
  };

  // Step 3: New Password
  const PasswordStep = () => {
    const [passwords, setPasswords] = useState({
      newPassword: '',
      confirmPassword: ''
    });

    const handlePasswordChange = (field) => (e) => {
      const value = e.target.value;
      setPasswords(prev => ({ ...prev, [field]: value }));

      // Real-time validation
      if (field === 'newPassword') {
        const passwordValidation = validatePassword(value);
        setValidation(prev => ({ ...prev, newPassword: passwordValidation }));

        // Also validate confirm password if it exists
        if (passwords.confirmPassword) {
          const confirmValidation = value === passwords.confirmPassword
            ? { valid: true, message: 'Passwords match' }
            : { valid: false, message: 'Passwords do not match' };
          setValidation(prev => ({ ...prev, confirmPassword: confirmValidation }));
        }
      } else if (field === 'confirmPassword') {
        const confirmValidation = value === passwords.newPassword
          ? { valid: true, message: 'Passwords match' }
          : { valid: false, message: 'Passwords do not match' };
        setValidation(prev => ({ ...prev, confirmPassword: confirmValidation }));
      }
    };

    const handleSubmit = async (e) => {
      e.preventDefault();

      const passwordValidation = validatePassword(passwords.newPassword);
      const confirmValidation = passwords.newPassword === passwords.confirmPassword
        ? { valid: true, message: 'Passwords match' }
        : { valid: false, message: 'Passwords do not match' };

      setValidation(prev => ({
        ...prev,
        newPassword: passwordValidation,
        confirmPassword: confirmValidation
      }));

      if (!passwordValidation.valid || !confirmValidation.valid) return;

      try {
        const result = await resetPassword(resetData.normalizedPhone, passwords.newPassword);

        if (result.success) {
          setCurrentStep(4);
        }
      } catch (err) {
        console.error('Password reset error:', err);
      }
    };

    return (
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="text-center">
          <h3 className="text-xl font-semibold text-gray-800 mb-2">{t('resetPassword.title', 'Create New Password')}</h3>
          <p className="text-gray-600">
            {t('resetPassword.instructions', 'Choose a strong password for your account')}
          </p>
        </div>

        <div className="space-y-4">
          <FormField
            label={t('resetPassword.newPassword', 'New Password')}
            type="password"
            value={passwords.newPassword}
            onChange={handlePasswordChange('newPassword')}
            placeholder={t('resetPassword.newPassword', 'Enter your new password')}
            required
            icon={<LockClosedIcon />}
            validation={validation.newPassword}
            helperText={t('register.helperText.password', 'Minimum 6 characters with letters and numbers')}
          />

          <FormField
            label={t('resetPassword.confirmPassword', 'Confirm New Password')}
            type="password"
            value={passwords.confirmPassword}
            onChange={handlePasswordChange('confirmPassword')}
            placeholder={t('resetPassword.confirmPassword', 'Confirm your new password')}
            required
            icon={<LockClosedIcon />}
            validation={validation.confirmPassword}
          />
        </div>

        <SubmitButton
          loading={loading}
          disabled={!validation.newPassword.valid || !validation.confirmPassword.valid || !passwords.newPassword || !passwords.confirmPassword}
        >
          {t('resetPassword.resetButton', 'Reset Password')}
        </SubmitButton>
      </form>
    );
  };

  // Step 4: Success
  const SuccessStep = () => (
    <div className="text-center space-y-6">
      <motion.div
        className="mx-auto w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center"
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ type: "spring", stiffness: 500, damping: 30 }}
      >
        <CheckCircleIcon className="w-8 h-8 text-green-400" />
      </motion.div>

      <div>
        <h3 className="text-xl font-semibold text-gray-800 mb-2">{t('resetPassword.success', 'Password Reset Successful!')}</h3>
        <p className="text-gray-600">
          {t('resetPassword.successMessage', 'Your password has been successfully updated. You have been logged out from all devices for security.')}
        </p>
      </div>

      <div className="space-y-3">
        <SubmitButton onClick={() => onSuccess?.()}>
          {t('resetPassword.successContinue', 'Continue to Login')}
        </SubmitButton>

        <p className="text-gray-500 text-sm">
          {t('resetPassword.successInfo', 'Please sign in with your new password')}
        </p>
      </div>
    </div>
  );

  // Get current step component
  const getCurrentStep = () => {
    switch (currentStep) {
      case 1: return <PhoneStep />;
      case 2: return <OTPStep />;
      case 3: return <PasswordStep />;
      case 4: return <SuccessStep />;
      default: return <PhoneStep />;
    }
  };

  return (
    <motion.div
      className={cn('w-full max-w-md mx-auto', className)}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <AuthCard>
        {/* Progress Indicator */}
        {currentStep < 4 && (
          <div className="mb-6">
            <div className="flex justify-between text-sm text-gray-600 mb-2">
              <span>Step {currentStep} of 3</span>
              <span>{Math.round((currentStep / 3) * 100)}% Complete</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <motion.div
                className="h-full bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${(currentStep / 3) * 100}%` }}
                transition={{ duration: 0.5 }}
              />
            </div>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <motion.div
            className="mb-6 p-4 bg-red-50 border border-red-200 rounded-xl"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
          >
            <p className="text-red-600 text-sm text-center">{error}</p>
          </motion.div>
        )}

        {/* Current Step Content */}
        <AnimatePresence mode="wait">
          <motion.div
            key={currentStep}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            {getCurrentStep()}
          </motion.div>
        </AnimatePresence>

        {/* Cancel Button */}
        {currentStep < 4 && (
          <div className="mt-6 text-center">
            <button
              onClick={onCancel}
              className="text-gray-500 hover:text-gray-700 text-sm transition-colors"
              disabled={loading}
            >
              {t('forgotPassword.backToLogin', '← Back to login')}
            </button>
          </div>
        )}
      </AuthCard>
    </motion.div>
  );
};

export default PasswordResetFlow;
