/**
 * Referral Input Component
 * Handles both referral code and phone number types with validation
 */

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import FormField from '../ui/FormField';
import PhoneNumberInput from './PhoneNumberInput';
import { phoneValidationService } from '../../services/phoneValidationService';
import { GiftIcon, UserGroupIcon, PhoneIcon, TicketIcon } from '@heroicons/react/24/outline';
import { cn } from '../../lib/utils';

/**
 * ReferralInput Component
 * @param {Object} props
 * @param {string} props.referralType - Current referral type ('code' or 'phone')
 * @param {string} props.referralValue - Current referral value
 * @param {Function} props.onTypeChange - Callback when type changes
 * @param {Function} props.onValueChange - Callback when value changes
 * @param {boolean} props.disabled - Disabled state
 * @param {string} props.className - Additional CSS classes
 */
const ReferralInput = ({
  referralType = null,
  referralValue = '',
  onTypeChange,
  onValueChange,
  disabled = false,
  className = ''
}) => {
  // Internal state
  const [selectedType, setSelectedType] = useState(referralType);
  const [inputValue, setInputValue] = useState(referralValue);
  const [validation, setValidation] = useState({ valid: true, message: '' });

  // Update internal state when props change
  useEffect(() => {
    setSelectedType(referralType);
    setInputValue(referralValue);
  }, [referralType, referralValue]);

  // Handle type selection
  const handleTypeChange = (type) => {
    setSelectedType(type);
    setInputValue('');
    setValidation({ valid: true, message: '' });
    
    onTypeChange?.(type);
    onValueChange?.('');
  };

  // Handle value change
  const handleValueChange = (value) => {
    setInputValue(value);
    
    // Validate based on type
    let validationResult = { valid: true, message: '' };
    
    if (value && selectedType === 'code') {
      // Validate referral code (alphanumeric, 3-20 characters)
      if (!/^[A-Za-z0-9]{3,20}$/.test(value)) {
        validationResult = {
          valid: false,
          message: 'Referral code must be 3-20 alphanumeric characters'
        };
      }
    } else if (value && selectedType === 'phone') {
      // Validate phone number
      validationResult = phoneValidationService.validateWithMessage(value);
    }
    
    setValidation(validationResult);
    onValueChange?.(value);
  };

  // Handle phone input change (special case for phone type)
  const handlePhoneInputChange = (e) => {
    handleValueChange(e.target.value);
  };

  // Handle regular input change
  const handleInputChange = (e) => {
    const value = e.target.value.toUpperCase(); // Referral codes are typically uppercase
    handleValueChange(value);
  };

  // Clear referral
  const handleClear = () => {
    setSelectedType(null);
    setInputValue('');
    setValidation({ valid: true, message: '' });
    
    onTypeChange?.(null);
    onValueChange?.('');
  };

  return (
    <motion.div
      className={cn('space-y-4', className)}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Header */}
      <div className="text-center">
        <h4 className="text-lg font-medium text-blue- mb-2 flex items-center justify-center space-x-2">
          <GiftIcon className="w-5 h-5" />
          <span>Referral Code (Optional)</span>
        </h4>
        <p className="text-blue-800 text-sm">
          Have a referral? Enter it here to get bonus rewards!
        </p>
      </div>

      {/* Type Selection */}
      {!selectedType && (
        <motion.div
          className="space-y-3"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
        >
          <p className="text-blue-800 text-sm text-center">
            Choose how you want to enter your referral:
          </p>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            {/* Referral Code Option */}
            <motion.button
              onClick={() => handleTypeChange('code')}
              disabled={disabled}
              className={cn(
                'p-4 rounded-xl border-2 border-dashed border-black hover:border-indigo-400',
                'bg-white/5 hover:bg-indigo-500/10 transition-all duration-300',
                'disabled:opacity-50 disabled:cursor-not-allowed'
              )}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <div className="text-center space-y-2">
                <div className="w-10 h-10 bg-indigo-500/20 rounded-full flex items-center justify-center mx-auto">
                  <TicketIcon className="w-5 h-5 text-indigo-400" />
                </div>
                <div>
                  <h5 className="text-blue-800 font-medium">Referral Code</h5>
                  <p className="text-blue-800 text-xs">Enter a friend's code</p>
                </div>
              </div>
            </motion.button>

            {/* Phone Number Option */}
            <motion.button
              onClick={() => handleTypeChange('phone')}
              disabled={disabled}
              className={cn(
                'p-4 rounded-xl border-2 border-dashed border-black hover:border-purple-400',
                'bg-white/5 hover:bg-purple-500/10 transition-all duration-300',
                'disabled:opacity-50 disabled:cursor-not-allowed'
              )}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <div className="text-center space-y-2">
                <div className="w-10 h-10 bg-purple-500/20 rounded-full flex items-center justify-center mx-auto">
                  <PhoneIcon className="w-5 h-5 text-purple-400" />
                </div>
                <div>
                  <h5 className="text-blue-800 font-medium">Phone Number</h5>
                  <p className="text-blue-800 text-xs">Enter friend's number</p>
                </div>
              </div>
            </motion.button>
          </div>
        </motion.div>
      )}

      {/* Input Field */}
      <AnimatePresence mode="wait">
        {selectedType && (
          <motion.div
            key={selectedType}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
            className="space-y-4"
          >
            {selectedType === 'code' ? (
              <FormField
                label="Referral Code"
                type="text"
                value={inputValue}
                onChange={handleInputChange}
                placeholder="Enter referral code (e.g., ABC123)"
                icon={<TicketIcon />}
                validation={validation}
                helperText="Referral codes are 3-20 characters long"
                disabled={disabled}
                className="uppercase"
                maxLength={20}
              />
            ) : (
              <PhoneNumberInput
                label="Referrer's Phone Number"
                value={inputValue}
                onChange={handlePhoneInputChange}
                placeholder="+60 12-345 6789"
                helperText="Enter your friend's Malaysian mobile number"
                disabled={disabled}
                showFormats={false}
              />
            )}

            {/* Action Buttons */}
            <div className="flex space-x-3">
              <button
                onClick={handleClear}
                disabled={disabled}
                className="flex-1 py-2 px-4 text-white/60 hover:text-white border border-white/30 hover:border-white/50 rounded-xl transition-colors text-sm"
              >
                Clear
              </button>
              
              <button
                onClick={() => handleTypeChange(selectedType === 'code' ? 'phone' : 'code')}
                disabled={disabled}
                className="flex-1 py-2 px-4 text-indigo-300 hover:text-indigo-200 border border-indigo-500/30 hover:border-indigo-500/50 rounded-xl transition-colors text-sm"
              >
                Switch to {selectedType === 'code' ? 'Phone' : 'Code'}
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Skip Option */}
      {!selectedType && (
        <motion.div
          className="text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
        >
          <p className="text-white/50 text-sm">
            Don't have a referral? No problem! You can skip this step.
          </p>
        </motion.div>
      )}

      {/* Benefits Info */}
      <motion.div
        className="p-4 bg-gradient-to-r from-indigo-500/10 to-purple-500/10 border border-indigo-500/20 rounded-xl"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.4 }}
      >
        <div className="flex item-center space-x-3">
          <UserGroupIcon className="w-5 h-5 text-indigo-400 mt-0.5 flex-shrink-0" />
          <div>
            <h5 className="text-indigo-800 text-center font-medium mb-1">Referral Benefits:</h5>
            <ul className="text-indigo-600 text-sm space-y-1">
              <li>• Get bonus credits when you join</li>
              <li>• Your friend gets rewards too</li>
              <li>• Unlock exclusive missions together</li>
              <li>• Build your gaming network</li>
            </ul>
          </div>
        </div>
      </motion.div>

      {/* Validation Success */}
      {inputValue && validation.valid && selectedType && (
        <motion.div
          className="p-3 bg-green-500/10 border border-green-500/20 rounded-xl"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
        >
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-400 rounded-full" />
            <span className="text-green-400 text-sm">
              {selectedType === 'code' 
                ? `Referral code "${inputValue}" looks good!`
                : `Phone number ${phoneValidationService.getDisplayFormat(inputValue)} is valid`
              }
            </span>
          </div>
        </motion.div>
      )}
    </motion.div>
  );
};

export default ReferralInput;
