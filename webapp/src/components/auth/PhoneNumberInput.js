/**
 * Phone Number Input Component
 * Specialized Malaysian phone number input with formatting and validation
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import FormField from '../ui/FormField';
import { phoneValidationService } from '../../services/phoneValidationService';
import { PhoneIcon, InformationCircleIcon } from '@heroicons/react/24/outline';
import { cn } from '../../lib/utils';

/**
 * PhoneNumberInput Component
 * @param {Object} props
 * @param {string} props.value - Current phone number value
 * @param {Function} props.onChange - Change handler
 * @param {string} props.label - Field label
 * @param {string} props.placeholder - Placeholder text
 * @param {boolean} props.required - Required field
 * @param {boolean} props.disabled - Disabled state
 * @param {string} props.helperText - Helper text
 * @param {boolean} props.showFormats - Show supported formats
 * @param {string} props.className - Additional CSS classes
 */
const PhoneNumberInput = ({
  value = '',
  onChange,
  label = 'Mobile Number',
  placeholder = '+60 12-345 6789',
  required = false,
  disabled = false,
  helperText = 'Enter your Malaysian mobile number',
  showFormats = true,
  className = ''
}) => {
  // Internal state for formatting
  const [internalValue, setInternalValue] = useState(value);
  const [validation, setValidation] = useState({ valid: true, message: '' });
  const [isFocused, setIsFocused] = useState(false);
  const [previousValue, setPreviousValue] = useState('');

  // Update internal value when prop changes
  useEffect(() => {
    setInternalValue(value);
  }, [value]);

  // Handle input change with formatting
  const handleChange = (e) => {
    const inputValue = e.target.value;
    
    // Auto-format the phone number
    const formattedValue = phoneValidationService.autoFormat(inputValue, previousValue);
    
    // Update internal state
    setInternalValue(formattedValue);
    setPreviousValue(internalValue);
    
    // Validate the phone number
    const phoneValidation = phoneValidationService.validateWithMessage(formattedValue);
    setValidation(phoneValidation);
    
    // Call parent onChange with formatted value
    if (onChange) {
      const syntheticEvent = {
        ...e,
        target: {
          ...e.target,
          value: formattedValue
        }
      };
      onChange(syntheticEvent);
    }
  };

  // Handle focus
  const handleFocus = (e) => {
    setIsFocused(true);
    
    // Auto-add +60 prefix if empty
    if (!internalValue) {
      const newValue = '+60 ';
      setInternalValue(newValue);
      
      if (onChange) {
        const syntheticEvent = {
          ...e,
          target: {
            ...e.target,
            value: newValue
          }
        };
        onChange(syntheticEvent);
      }
    }
  };

  // Handle blur
  const handleBlur = (e) => {
    setIsFocused(false);
    
    // Remove +60 prefix if only that remains
    if (internalValue === '+60 ' || internalValue === '+60') {
      setInternalValue('');
      
      if (onChange) {
        const syntheticEvent = {
          ...e,
          target: {
            ...e.target,
            value: ''
          }
        };
        onChange(syntheticEvent);
      }
    }
  };

  // Get display value for the input
  const getDisplayValue = () => {
    if (isFocused || internalValue) {
      return internalValue;
    }
    return '';
  };

  // Get validation state with enhanced messaging
  const getValidationState = () => {
    if (!internalValue) {
      return required 
        ? { valid: false, message: 'Phone number is required' }
        : { valid: true, message: '' };
    }
    
    return validation;
  };

  const currentValidation = getValidationState();

  return (
    <motion.div
      className={cn('space-y-3', className)}
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* Main Input Field */}
      <FormField
        label={label}
        type="tel"
        value={getDisplayValue()}
        onChange={handleChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
        placeholder={placeholder}
        required={required}
        disabled={disabled}
        icon={<PhoneIcon />}
        validation={currentValidation}
        helperText={helperText}
        className="font-mono"
      />

      {/* Format Examples */}
      {showFormats && (
        <motion.div
          className="p-3 bg-blue-500/10 border border-blue-500/20 rounded-xl"
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <div className="flex items-start space-x-2">
            <InformationCircleIcon className="w-4 h-4 text-blue-400 mt-0.5 flex-shrink-0" />
            <div>
              <h5 className="text-blue-400 font-medium text-sm mb-1">Supported Formats:</h5>
              <div className="grid grid-cols-2 gap-1 text-blue-300/80 text-xs font-mono">
                {phoneValidationService.getSupportedFormats().map((format, index) => (
                  <div key={index} className="truncate">
                    {format}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </motion.div>
      )}

      {/* Real-time Format Preview */}
      {internalValue && validation.valid && (
        <motion.div
          className="p-2 bg-green-500/10 border border-green-500/20 rounded-lg"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.2 }}
        >
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-400 rounded-full" />
            <span className="text-green-400 text-sm">
              Formatted: {phoneValidationService.getDisplayFormat(internalValue)}
            </span>
          </div>
        </motion.div>
      )}

      {/* Series Detection */}
      {internalValue && validation.valid && (
        <motion.div
          className="text-xs text-white/50"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2 }}
        >
          {phoneValidationService.is11Series(internalValue) 
            ? '📱 11-series number detected'
            : '📱 Standard Malaysian number'
          }
        </motion.div>
      )}
    </motion.div>
  );
};

export default PhoneNumberInput;
