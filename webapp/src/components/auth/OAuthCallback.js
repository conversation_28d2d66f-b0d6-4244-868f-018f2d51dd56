/**
 * OAuth Callback Component
 * 
 * This component handles OAuth callback from providers like Google and Apple.
 */

import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import useTranslation from '../../hooks/useTranslation';
import axios from 'axios';

/**
 * OAuthCallback component
 */
const OAuthCallback = () => {
    // Get auth context
    const { checkAuth } = useAuth();
    
    // Navigation and location
    const navigate = useNavigate();
    const location = useLocation();
    
    // State management
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');
    
    // Use the translation hook
    const { t } = useTranslation('auth');

    // Process OAuth callback on component mount
    useEffect(() => {
        const processOAuthCallback = async () => {
            try {
                // Parse query parameters
                const queryParams = new URLSearchParams(location.search);
                const code = queryParams.get('code');
                const state = queryParams.get('state');
                const provider = queryParams.get('provider') || getProviderFromPath();
                const error = queryParams.get('error');
                
                // Handle error from OAuth provider
                if (error) {
                    setError(t('oauth.providerError', 'Authentication failed: {{error}}', { error }));
                    setLoading(false);
                    return;
                }
                
                // Validate required parameters
                if (!code || !provider) {
                    setError(t('oauth.invalidCallback', 'Invalid callback parameters'));
                    setLoading(false);
                    return;
                }
                
                // Use a default API URL for testing if environment variable is not set
                const apiBaseUrl = process.env.REACT_APP_API_URL || '/api';
                
                // Exchange code for token
                const response = await axios.get(`${apiBaseUrl}/auth/oauth/${provider}/callback`, {
                    params: { code, state }
                });
                
                if (response.data.success) {
                    // Store token
                    localStorage.setItem('token', response.data.token);
                    
                    // Check if this is a first-time login
                    const isNewUser = response.data.is_new_user;
                    
                    // Set success message
                    setSuccess(isNewUser 
                        ? t('oauth.signupSuccess', 'Account created successfully') 
                        : t('oauth.loginSuccess', 'Logged in successfully'));
                    
                    // Update auth state
                    await checkAuth();
                    
                    // Redirect after a short delay
                    setTimeout(() => {
                        if (isNewUser) {
                            // For new users, redirect to welcome page
                            navigate('/welcome');
                        } else {
                            // For existing users, redirect to home
                            navigate('/home');
                        }
                    }, 1500);
                } else {
                    setError(response.data.message || t('oauth.authFailed', 'Authentication failed'));
                }
            } catch (err) {
                console.error('OAuth callback processing failed:', err);
                setError(err.response?.data?.message || t('oauth.unexpectedError', 'An unexpected error occurred'));
            } finally {
                setLoading(false);
            }
        };
        
        processOAuthCallback();
    }, [location, navigate, checkAuth, t]);

    // Extract provider from URL path
    const getProviderFromPath = () => {
        const pathParts = location.pathname.split('/');
        const providerIndex = pathParts.findIndex(part => part === 'callback') - 1;
        return providerIndex >= 0 ? pathParts[providerIndex] : null;
    };

    return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
            <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
                <div className="text-center">
                    <h2 className="text-2xl font-bold text-gray-800 mb-4">
                        {t('oauth.processingTitle', 'Processing Authentication')}
                    </h2>
                    
                    {loading && (
                        <div className="flex flex-col items-center justify-center my-6">
                            <svg className="animate-spin h-10 w-10 text-blue-500 mb-4" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                            </svg>
                            <p className="text-gray-600">
                                {t('oauth.processing', 'Processing your authentication...')}
                            </p>
                        </div>
                    )}
                    
                    {error && (
                        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-4">
                            {error}
                        </div>
                    )}
                    
                    {success && (
                        <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-4">
                            {success}
                        </div>
                    )}
                    
                    {!loading && (
                        <div className="mt-6">
                            {error ? (
                                <button
                                    type="button"
                                    onClick={() => navigate('/')}
                                    className="inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                >
                                    {t('oauth.backToLogin', 'Back to Login')}
                                </button>
                            ) : (
                                <p className="text-gray-600 text-sm">
                                    {t('oauth.redirecting', 'Redirecting you shortly...')}
                                </p>
                            )}
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default OAuthCallback;
