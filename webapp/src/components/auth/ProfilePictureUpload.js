/**
 * Profile Picture Upload Component
 * Drag-and-drop image upload with preview, cropping, and validation
 */

import React, { useState, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { PhotoIcon, XMarkIcon, ArrowUpTrayIcon } from '@heroicons/react/24/outline';
import { cn } from '../../lib/utils';

/**
 * ProfilePictureUpload Component
 * @param {Object} props
 * @param {File} props.value - Current profile picture file
 * @param {Function} props.onChange - Callback when file changes
 * @param {Function} props.onRemove - Callback when file is removed
 * @param {boolean} props.required - Whether upload is required
 * @param {string} props.className - Additional CSS classes
 */
const ProfilePictureUpload = ({
  value,
  onChange,
  onRemove,
  required = false,
  className = ''
}) => {
  // Component state
  const [isDragging, setIsDragging] = useState(false);
  const [preview, setPreview] = useState(null);
  const [error, setError] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);

  // File input ref
  const fileInputRef = useRef(null);

  // Supported file types (matching backend validation)
  const supportedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/gif'];
  const maxFileSize = 2 * 1024 * 1024; // 2MB (align with backend: max:2048)

  // Generate preview URL when value changes
  React.useEffect(() => {
    if (value instanceof File) {
      const url = URL.createObjectURL(value);
      setPreview(url);
      return () => URL.revokeObjectURL(url);
    } else {
      setPreview(null);
    }
  }, [value]);

  // Validate file
  const validateFile = (file) => {
    if (!supportedTypes.includes(file.type)) {
      return 'Please select a valid image file (JPEG, PNG, JPG, or GIF)';
    }

    if (file.size > maxFileSize) {
      return 'File size must be less than 5MB';
    }

    return null;
  };

  // Process uploaded file
  const processFile = async (file) => {
    setIsProcessing(true);
    setError('');

    try {
      const validationError = validateFile(file);
      if (validationError) {
        setError(validationError);
        return;
      }

      // Create a new File object to ensure it's properly formatted
      const processedFile = new File([file], file.name, {
        type: file.type,
        lastModified: file.lastModified
      });

      onChange?.(processedFile);
    } catch (err) {
      setError('Failed to process image. Please try again.');
      console.error('File processing error:', err);
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle file selection
  const handleFileSelect = (e) => {
    const file = e.target.files?.[0];
    if (file) {
      processFile(file);
    }
  };

  // Handle drag events
  const handleDragOver = useCallback((e) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback((e) => {
    e.preventDefault();
    setIsDragging(false);

    const files = Array.from(e.dataTransfer.files);
    const imageFile = files.find(file => supportedTypes.includes(file.type));

    if (imageFile) {
      processFile(imageFile);
    } else {
      setError('Please drop a valid image file');
    }
  }, []);

  // Handle remove
  const handleRemove = () => {
    setPreview(null);
    setError('');
    onRemove?.();

    // Clear file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Open file dialog
  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  return (
    <motion.div
      className={cn('space-y-4', className)}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Label */}
      <div className="text-center">
        <h4 className="text-lg font-medium text-blue-800 mb-2">
          Profile Picture {required && <span className="text-red-400">*</span>}
        </h4>
        <p className="text-blue-800 text-sm">
          Upload a photo to personalize your gaming profile
        </p>
      </div>

      {/* Upload Area */}
      <div className="relative">
        <motion.div
          className={cn(
            'relative border-2 border-dashed rounded-2xl p-8 transition-all duration-300',
            'bg-white/5 backdrop-blur-sm',
            isDragging && 'border-indigo-400 bg-indigo-500/10',
            !isDragging && 'border-white/30 hover:border-white/50',
            error && 'border-red-400 bg-red-500/10'
          )}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          whileHover={{ scale: 1.02 }}
          transition={{ type: "spring", stiffness: 300, damping: 20 }}
        >
          <AnimatePresence mode="wait">
            {preview ? (
              // Preview Mode
              <motion.div
                key="preview"
                className="text-center"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                transition={{ duration: 0.3 }}
              >
                <div className="relative inline-block">
                  <img
                    src={preview}
                    alt="Profile preview"
                    className="w-32 h-32 rounded-full object-cover border-4 border-white/20 shadow-lg"
                  />

                  {/* Remove Button */}
                  <button
                    onClick={handleRemove}
                    className="absolute -top-2 -right-2 w-8 h-8 bg-red-500 hover:bg-red-600 rounded-full flex items-center justify-center transition-colors shadow-lg"
                  >
                    <XMarkIcon className="w-4 h-4 text-white" />
                  </button>
                </div>

                <div className="mt-4 space-y-2">
                  <p className="text-gray-800 font-medium">Looking great!</p>
                  <button
                    onClick={openFileDialog}
                    className="text-indigo-600 bg-transparent hover:bg-transparent hover:text-indigo-700 text-sm transition-colors"
                  >
                    Change photo
                  </button>
                </div>
              </motion.div>
            ) : (
              // Upload Mode
              <motion.div
                key="upload"
                className="text-center cursor-pointer"
                onClick={openFileDialog}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
                whileHover={{ y: -5 }}
                whileTap={{ scale: 0.95 }}
              >
                <div className="mx-auto w-16 h-16 bg-gradient-to-br from-indigo-500/20 to-purple-500/20 rounded-full flex items-center justify-center mb-4">
                  {isProcessing ? (
                    <div className="w-8 h-8 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                  ) : (
                    <PhotoIcon className="w-8 h-8 text-white/70" />
                  )}
                </div>

                <div className="space-y-2">
                  <p className="text-gray-800 font-medium">
                    {isProcessing ? 'Processing...' : 'Upload Profile Picture'}
                  </p>
                  <p className="text-gray-600 text-sm">
                    Drag and drop or click to browse
                  </p>
                  <div className="flex items-center justify-center space-x-2 text-xs text-gray-500">
                    <ArrowUpTrayIcon className="w-4 h-4" />
                    <span>JPEG, PNG, JPG, GIF up to 5MB</span>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Drag Overlay */}
          <AnimatePresence>
            {isDragging && (
              <motion.div
                className="absolute inset-0 bg-indigo-500/20 border-2 border-indigo-400 rounded-2xl flex items-center justify-center"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
              >
                <div className="text-center">
                  <ArrowUpTrayIcon className="w-12 h-12 text-indigo-400 mx-auto mb-2" />
                  <p className="text-indigo-300 font-medium">Drop your image here</p>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>

        {/* Hidden File Input */}
        <input
          ref={fileInputRef}
          type="file"
          accept={supportedTypes.join(',')}
          onChange={handleFileSelect}
          className="hidden"
        />
      </div>

      {/* Error Message */}
      <AnimatePresence>
        {error && (
          <motion.div
            className="p-3 bg-red-500/10 border border-red-500/20 rounded-xl"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <p className="text-red-400 text-sm text-center">{error}</p>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Upload Guidelines */}
      <motion.div
        className="p-4 bg-blue-500/10 border border-blue-500/20 rounded-xl"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3 }}
      >
        <h5 className="text-blue-600 font-medium mb-2">Photo Guidelines:</h5>
        <ul className="text-blue-700 text-sm space-y-1">
          <li>• Use a clear, recent photo of yourself</li>
          <li>• Square images work best (will be cropped to circle)</li>
          <li>• Avoid blurry or low-quality images</li>
          <li>• Keep it appropriate for a gaming community</li>
        </ul>
      </motion.div>

      {/* Skip Option */}
      {!required && (
        <div className="text-center">
          <p className="text-gray-500 text-sm">
            You can always add a profile picture later in your settings
          </p>
        </div>
      )}
    </motion.div>
  );
};

export default ProfilePictureUpload;
