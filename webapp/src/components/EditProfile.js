import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { profileAPI } from '../services/api';
import { motion, AnimatePresence } from 'framer-motion';
import EditSkills from './EditSkills';
import VoiceNote from './VoiceNote';
import MediaGallery from './MediaGallery';
import profileService from '../services/profileService';

const SectionHeader = ({ title, onEdit, icon }) => (
    <div className="flex justify-between items-center mb-6">
        <div className="flex items-center space-x-3">
            {icon}
            <h2 className="text-lg font-bold text-gray-900">{title}</h2>
        </div>
        <button
            onClick={onEdit}
            className="px-4 py-2 text-sm bg-gray-900 text-white rounded-lg hover:bg-gray-800
                transition-all duration-200 flex items-center space-x-2 transform hover:scale-105"
        >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                    d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
            </svg>
            <span>Edit</span>
        </button>
    </div>
);

const getGameImagePath = (gameName) => {
    if (!gameName) return '/default-game.png';
    return `/game-icons/${gameName.toLowerCase().replace(/\s+/g, '-')}.png`;
};

const EditProfile = () => {
    const navigate = useNavigate();
    const [loading, setLoading] = useState(true);
    const [saving, setSaving] = useState(false);
    const [activeSection, setActiveSection] = useState(null);
    const [coverMedia, setCoverMedia] = useState({
        photos: [],
        video: null,
        selectedCover: null
    });
    const [profileData, setProfileData] = useState({
        name: '',
        email: '',
        gender: '',
        height: '',
        weight: '',
        birthdate: '',
        biography: '',
        profilePicture: '',
        coverPhoto: '',
        coverVideo: '',
        level: 1,
        race_id: null,
        race_name: '',
        skills: null, // Initialize as null to differentiate between "no data yet" and "empty array"
        voiceNote: null // Initialize voice note as null
    });

    // State for races
    const [races, setRaces] = useState([]);
    const [voiceNoteLoading, setVoiceNoteLoading] = useState(false);
    const [skillsLoading, setSkillsLoading] = useState(false);
    const [skillsError, setSkillsError] = useState(null);
    const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
    const [skillToDelete, setSkillToDelete] = useState(null);

    // Function to fetch voice note
    const fetchVoiceNote = async () => {
        setVoiceNoteLoading(true);
        try {
            const response = await profileService.getVoiceNote();
            if (response.success && response.data) {
                setProfileData(prev => ({
                    ...prev,
                    voiceNote: response.data.url
                }));
            }
        } catch (error) {
            console.error('Error fetching voice note:', error);
        } finally {
            setVoiceNoteLoading(false);
        }
    };

    // Function to fetch races
    const fetchRaces = async () => {
        try {
            const response = await profileService.getRaces();
            if (response.success) {
                setRaces(response.data);
            } else {
                console.error('Failed to fetch races:', response.error);
            }
        } catch (error) {
            console.error('Error fetching races:', error);
        }
    };

    useEffect(() => {
        const fetchProfile = async () => {
            setLoading(true);
            setSkillsLoading(true);
            setVoiceNoteLoading(true);
            setSkillsError(null);
            try {
                const [profileRes, bioRes, racesRes] = await Promise.all([
                    profileAPI.getProfile(),
                    profileAPI.getBiography(),
                    profileService.getRaces()
                ]);

                const levelInfo = profileRes.data.level || { current_level: 1 };
                const currentLevel = typeof levelInfo === 'object' ? levelInfo.current_level : levelInfo;

                // Initialize skills as an empty array if none exist
                const initialSkills = profileRes.data.skills || [];

                // Set races
                if (racesRes.success) {
                    setRaces(racesRes.data);
                }

                setProfileData({
                    ...profileRes.data,
                    level: currentLevel,
                    biography: bioRes.data.bio || bioRes.data.biography || "",
                    skills: initialSkills
                });

                // Fetch voice note separately
                fetchVoiceNote();
            } catch (error) {
                console.error('Error fetching profile:', error);
                setSkillsError('Failed to load skills. Please refresh the page.');
                // Initialize with empty skills array on error
                setProfileData(prev => ({
                    ...prev,
                    skills: []
                }));

                // Fetch races separately if the main fetch fails
                fetchRaces();
            } finally {
                setLoading(false);
                setSkillsLoading(false);
            }
        };

        fetchProfile();
    }, []);

    const handleBack = () => {
        navigate('/profile');
    };

    const handleSave = async () => {
        setSaving(true);
        try {
            // Save profile data and biography
            await Promise.all([
                profileAPI.updateProfile({
                    ...profileData,
                    photos: coverMedia.photos,
                    video: coverMedia.video,
                    coverType: coverMedia.selectedCover?.type,
                    coverPhoto: coverMedia.selectedCover?.type === 'photo'
                        ? coverMedia.photos.find(p => p.id === coverMedia.selectedCover.id)
                        : null,
                    coverVideo: coverMedia.selectedCover?.type === 'video' ? coverMedia.video : null
                }),
                profileAPI.updateBiography({ bio: profileData.biography })
            ]);

            // Show success message
            alert('Profile updated successfully!');

            // Navigate back to profile page
            navigate('/profile');
        } catch (error) {
            console.error('Error saving profile:', error);
            alert('Failed to save profile. Please try again.');
        } finally {
            setSaving(false);
        }
    };

    const handleImageUpload = async (type) => {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = type === 'cover' ? 'image/*,video/*' : 'image/*';
        input.multiple = type === 'cover';

        input.onchange = async (e) => {
            const files = Array.from(e.target.files);

            for (const file of files) {
                if (type === 'cover' && file.type.startsWith('video/') && file.size > 50 * 1024 * 1024) {
                    alert('Video size must be less than 50MB');
                    continue;
                }

                try {
                    const response = await profileAPI.uploadMedia(file);
                    if (type === 'cover') {
                        setCoverMedia(prev => ({
                            ...prev,
                            photos: [...prev.photos, {
                            type: file.type.startsWith('video/') ? 'video' : 'image',
                            url: response.data.url
                            }],
                            selectedCover: {
                                type: file.type.startsWith('video/') ? 'video' : 'image',
                                id: response.data.id
                            }
                        }));
                    } else {
                        setProfileData(prev => ({
                            ...prev,
                            profilePicture: response.data.url
                        }));
                    }
                } catch (error) {
                    console.error('Error uploading media:', error);
                    alert('Failed to upload media. Please try again.');
                }
            }
        };

        input.click();
    };

    const handleDeleteMedia = (index) => {
        setCoverMedia(prev => ({
            ...prev,
            photos: prev.photos.filter((_, i) => i !== index),
            selectedCover: null
        }));
    };

    const handleSaveMedia = async () => {
        try {
            const selectedMedia = coverMedia.photos[0]; // For now, just use the first media
            if (selectedMedia) {
                // Update profile with new media
                const response = await profileAPI.updateProfile({
                    ...profileData,
                    coverPhoto: selectedMedia.type === 'image' ? selectedMedia.url : null,
                    coverVideo: selectedMedia.type === 'video' ? selectedMedia.url : null
                });

                if (response.success) {
                    // Update local state with the response data
                    setProfileData(prev => ({
                        ...prev,
                        coverPhoto: selectedMedia.type === 'image' ? selectedMedia.url : prev.coverPhoto,
                        coverVideo: selectedMedia.type === 'video' ? selectedMedia.url : prev.coverVideo
                    }));

                    // Show success message
                    alert('Media updated successfully!');
                } else {
                    alert('Failed to update media. Please try again.');
                }

                setActiveSection(null);
            }
        } catch (error) {
            console.error('Error saving media:', error);
            alert('Failed to save changes. Please try again.');
        }
    };

    const handleSaveDetails = async (details) => {
        try {
            let response;

            // If race_id is being updated, use the updateRace method
            if (details.race_id !== undefined && details.race_id !== profileData.race_id) {
                // Update race using the dedicated API endpoint
                response = await profileService.updateRace(details.race_id);

                if (!response.success) {
                    throw new Error(response.error || 'Failed to update race');
                }

                // Update other profile details if there are any
                const otherDetails = { ...details };
                delete otherDetails.race_id;
                delete otherDetails.race_name;

                if (Object.keys(otherDetails).length > 0) {
                    // Update profile with other details
                    const profileResponse = await profileAPI.updateProfile({
                        ...profileData,
                        ...otherDetails
                    });

                    if (!profileResponse.success) {
                        throw new Error(profileResponse.error || 'Failed to update profile details');
                    }
                }
            } else {
                // Update profile with new details
                response = await profileAPI.updateProfile({
                    ...profileData,
                    ...details
                });

                if (!response.success) {
                    throw new Error(response.error || 'Failed to update profile details');
                }
            }

            // Update local state with the response data
            setProfileData(prev => ({
                ...prev,
                ...details
            }));

            // Show success message
            alert('Profile details updated successfully!');
            setActiveSection(null);
        } catch (error) {
            console.error('Error saving details:', error);
            alert(error.message || 'Failed to save changes. Please try again.');
        }
    };

    const handleSaveSkills = async (updatedSkills) => {
        setSkillsLoading(true);
        setSkillsError(null);
        try {
            // Make API call first
            const response = await profileAPI.updateSkills(updatedSkills);

            if (response.success) {
                // Update local state after successful API call
                setProfileData(prev => ({
                    ...prev,
                    skills: updatedSkills,
                    selectedSkillForEdit: null
                }));

                // Show success message
                alert('Skills updated successfully!');
                setActiveSection(null);
            } else {
                setSkillsError('Failed to save skills. Please try again.');
            }
        } catch (error) {
            console.error('Error saving skills:', error);
            setSkillsError('Failed to save skills. Please try again.');
        } finally {
            setSkillsLoading(false);
        }
    };

    const handleDeleteSkill = async (skillId) => {
        try {
            setSkillsLoading(true);
            await profileAPI.deleteSkill(skillId);

            // Update local state after successful deletion
            setProfileData(prev => ({
                ...prev,
                skills: prev.skills.filter(skill => skill.id !== skillId)
            }));

            // Close the confirmation modal
            setShowDeleteConfirm(false);
            setSkillToDelete(null);
        } catch (error) {
            console.error('Error deleting skill:', error);
            setSkillsError('Failed to delete skill. Please try again.');
        } finally {
            setSkillsLoading(false);
        }
    };

    const renderModalContent = () => {
        if (!profileData) return null;

        switch (activeSection) {
            case 'skills':
                return (
                    <EditSkills
                        onClose={() => {
                            setActiveSection(null);
                            setProfileData(prev => ({
                                ...prev,
                                selectedSkillForEdit: null
                            }));
                        }}
                        onSave={handleSaveSkills}
                        initialSkill={profileData.selectedSkillForEdit}
                    />
                );

            case 'cover':
                return (
                    <div className="space-y-4">
                        <div className="flex justify-between items-center">
                            <h4 className="text-sm font-medium text-gray-700">Upload Cover Photo/Video</h4>
                            <button
                                onClick={() => handleImageUpload('cover')}
                                className="px-3 py-1.5 text-sm bg-indigo-600 text-white rounded-lg hover:bg-indigo-700"
                            >
                                Upload
                            </button>
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                            {coverMedia.photos.map((media, index) => (
                                <div key={index} className="relative">
                                    {media.type === 'video' ? (
                                        <video
                                            src={media.url}
                                            className="w-full h-32 object-cover rounded-lg"
                                            controls
                                        />
                                    ) : (
                                        <img
                                            src={media.url}
                                            alt={`Cover ${index + 1}`}
                                            className="w-full h-32 object-cover rounded-lg"
                                        />
                                    )}
                                    <button
                                        onClick={() => handleDeleteMedia(index)}
                                        className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600"
                                    >
                                        ×
                                    </button>
                                </div>
                            ))}
                        </div>
                        <div className="pt-4 flex justify-end">
                            <button
                                onClick={handleSaveMedia}
                                className="px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800"
                            >
                                Save Changes
                            </button>
                        </div>
                    </div>
                );

            case 'details':
                return (
                    <div className="space-y-4">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Gender</label>
                            <select
                                value={profileData.gender || ''}
                                onChange={(e) => handleSaveDetails({ gender: e.target.value })}
                                className="w-full px-4 py-2 border border-gray-300 rounded-lg"
                            >
                                <option value="">Select gender</option>
                                <option value="male">Male</option>
                                <option value="female">Female</option>
                            </select>
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">Height (cm)</label>
                                <input
                                    type="number"
                                    value={profileData.height || ''}
                                    onChange={(e) => handleSaveDetails({ height: e.target.value })}
                                    className="w-full px-4 py-2 border border-gray-300 rounded-lg"
                                />
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">Weight (kg)</label>
                                <input
                                    type="number"
                                    value={profileData.weight || ''}
                                    onChange={(e) => handleSaveDetails({ weight: e.target.value })}
                                    className="w-full px-4 py-2 border border-gray-300 rounded-lg"
                                />
                            </div>
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Birth Date</label>
                            <input
                                type="date"
                                value={profileData.birthdate || ''}
                                onChange={(e) => handleSaveDetails({ birthdate: e.target.value })}
                                className="w-full px-4 py-2 border border-gray-300 rounded-lg"
                            />
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                            <input
                                type="email"
                                value={profileData.email || ''}
                                onChange={(e) => handleSaveDetails({ email: e.target.value })}
                                className="w-full px-4 py-2 border border-gray-300 rounded-lg"
                            />
                        </div>

                        {/* Race Selection */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Race</label>
                            {races.length > 0 ? (
                                <>
                                    <select
                                        value={profileData.race_id || ''}
                                        onChange={(e) => setProfileData({
                                            ...profileData,
                                            race_id: e.target.value ? Number(e.target.value) : null,
                                            race_name: e.target.value ? races.find(r => r.id === Number(e.target.value))?.name || '' : ''
                                        })}
                                        className="w-full px-4 py-2 border border-gray-300 rounded-lg"
                                    >
                                        <option value="">Select race</option>
                                        {races.map(race => (
                                            <option key={race.id} value={race.id}>
                                                {race.name}
                                            </option>
                                        ))}
                                    </select>
                                    {profileData.race_id && (
                                        <p className="mt-1 text-sm text-gray-500">
                                            {races.find(r => r.id === profileData.race_id)?.description || ''}
                                        </p>
                                    )}
                                </>
                            ) : (
                                <p className="text-sm text-gray-500">Loading race options from backoffice...</p>
                            )}
                        </div>

                        <div className="pt-4 flex justify-end">
                            <button
                                onClick={() => handleSaveDetails({
                                    gender: profileData.gender,
                                    height: profileData.height,
                                    weight: profileData.weight,
                                    birthdate: profileData.birthdate,
                                    email: profileData.email,
                                    race_id: profileData.race_id
                                })}
                                className="px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800"
                            >
                                Save Changes
                            </button>
                        </div>
                    </div>
                );

            case 'biography':
                return (
                    <div className="space-y-4">
                        <textarea
                            value={profileData.biography || ''}
                            onChange={(e) => setProfileData({ ...profileData, biography: e.target.value })}
                            rows={6}
                            className="w-full px-4 py-2 border border-gray-300 rounded-lg resize-none"
                            placeholder="Tell us about yourself..."
                        />

                        <div className="flex justify-end">
                            <button
                                onClick={() => handleSaveDetails({
                                    bio: profileData.biography
                                })}
                                className="px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800"
                            >
                                Save Changes
                            </button>
                        </div>
                    </div>
                );

            default:
                return null;
        }
    };

    if (loading) {
        return (
            <>
                <div className="flex items-center justify-center min-h-screen bg-gradient-to-b from-indigo-50 to-white">
                    <div className="text-center">
                        <div className="w-16 h-16 border-t-4 border-indigo-600 border-solid rounded-full animate-spin mx-auto mb-4"></div>
                        <p className="text-gray-700 text-lg font-medium">Loading your profile...</p>
                    </div>
                </div>
            </>
        );
    }

    return (
        <>
            <div className="min-h-screen bg-gray-50">
                {/* Enhanced Header */}
                <div className="bg-white shadow-sm sticky top-0 z-50">
                    <div className="max-w-4xl mx-auto px-4 py-4">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-4">
                                <button
                                    onClick={handleBack}
                                    className="p-2 rounded-full hover:bg-gray-100 transition-colors duration-200 group"
                                >
                                    <svg className="w-6 h-6 text-white-600 group-hover:-translate-x-1 transition-transform duration-200"
                                        fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                                    </svg>
                                </button>
                                <h1 className="text-xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-indigo-600 to-blue-500">
                                    Edit Profile
                                </h1>
                            </div>
                            <button
                                onClick={handleSave}
                                disabled={saving}
                                className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700
                    transition-all duration-200 flex items-center space-x-2 disabled:opacity-50 transform hover:scale-105"
                            >
                                {saving ? (
                                    <svg className="animate-spin h-5 w-5" viewBox="0 0 24 24">
                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z" />
                                    </svg>
                                ) : (
                                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                )}
                                <span>{saving ? 'Saving...' : 'Save Changes'}</span>
                            </button>
                        </div>
                    </div>
                </div>

                <div className="max-w-4xl mx-auto px-4 py-8 space-y-6">
                    {/* Section 1: Profile Picture */}
                    <div className="bg-white rounded-xl shadow-sm p-6 hover:shadow-md transition-all duration-300">
                        <SectionHeader
                            title="Profile Picture"
                            onEdit={() => handleImageUpload('profile')}
                            icon={
                                <svg className="w-5 h-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                                        d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            }
                        />
                        <div className="flex justify-center">
                            <div className="relative group">
                                <div className="w-32 h-32 rounded-full overflow-hidden ring-4 ring-white shadow-lg
                transform group-hover:scale-105 transition-all duration-300">
                                    <img
                                        src={profileData.profilePicture || '/default-avatar.png'}
                                        alt="Profile"
                                        className="w-full h-full object-cover"
                                    />
                                </div>
                                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30
                rounded-full flex items-center justify-center transition-all duration-300">
                                    <svg className="w-8 h-8 text-white opacity-0 group-hover:opacity-100 transform
                    scale-50 group-hover:scale-100 transition-all duration-300"
                                        fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                                            d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Cover Media Section */}
                    <MediaGallery
                        onSave={(media) => {
                            setCoverMedia(media);
                        }}
                        initialMedia={coverMedia}
                    />

                    {/* Voice Note Section */}
                    <div className="bg-white rounded-xl shadow-sm p-6">
                        <SectionHeader
                            title="Voice Note"
                            onEdit={() => {}} // No edit button needed as VoiceNote has its own controls
                            icon={
                                <svg className="w-5 h-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                                        d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                                </svg>
                            }
                        />
                        <p className="text-sm text-gray-500 mb-4">
                            Record a short voice note to introduce yourself to potential clients.
                            This helps them get to know you better before booking your services.
                        </p>
                        <VoiceNote
                            onSave={async (audioBlob) => {
                                if (audioBlob) {
                                    // Upload the voice note
                                    setVoiceNoteLoading(true);
                                    try {
                                        const response = await profileService.uploadVoiceNote(audioBlob);
                                        if (response.success) {
                                            setProfileData(prev => ({
                                                ...prev,
                                                voiceNote: response.data.url
                                            }));
                                            console.log('Voice note uploaded successfully');
                                        } else {
                                            console.error('Failed to upload voice note:', response.error);
                                        }
                                    } catch (error) {
                                        console.error('Error uploading voice note:', error);
                                    } finally {
                                        setVoiceNoteLoading(false);
                                    }
                                } else {
                                    // Delete the voice note
                                    setVoiceNoteLoading(true);
                                    try {
                                        const response = await profileService.deleteVoiceNote();
                                        if (response.success) {
                                            setProfileData(prev => ({
                                                ...prev,
                                                voiceNote: null
                                            }));
                                            console.log('Voice note deleted successfully');
                                        } else {
                                            console.error('Failed to delete voice note:', response.error);
                                        }
                                    } catch (error) {
                                        console.error('Error deleting voice note:', error);
                                    } finally {
                                        setVoiceNoteLoading(false);
                                    }
                                }
                            }}
                            initialAudio={profileData.voiceNote}
                        />
                        {voiceNoteLoading && (
                            <div className="mt-4 flex justify-center">
                                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-600"></div>
                            </div>
                        )}
                    </div>

                    {/* Section 3: Details */}
                    <div className="bg-white rounded-xl shadow-sm p-6">
                        <div className="flex justify-between items-center mb-4">
                            <h2 className="text-lg font-semibold">Details</h2>
                            <button
                                onClick={() => setActiveSection('details')}
                                className="px-4 py-2 text-sm bg-black text-white rounded-lg hover:bg-gray-800 transition-colors flex items-center space-x-2"
                            >
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                                </svg>
                                <span>Edit</span>
                            </button>
                        </div>
                        <div className="space-y-4">
                            <div className="flex items-center space-x-3">
                                <span className={`text-xl ${profileData.gender === 'female' ? 'text-pink-500' : 'text-blue-500'}`}>
                                    {profileData.gender === 'male' ? '♂' : '♀'}
                                </span>
                                <span className="text-gray-700">{profileData.gender}</span>
                            </div>
                            <div className="flex items-center space-x-3">
                                <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                                <span className="text-gray-700">{profileData.height}cm, {profileData.weight}kg</span>
                            </div>
                            <div className="flex items-center space-x-3">
                                <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                                <span className="text-gray-700">{profileData.birthdate}</span>
                            </div>
                            <div className="flex items-center space-x-3">
                                <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                </svg>
                                <span className="text-gray-700">{profileData.email}</span>
                            </div>

                            {/* Race Information */}
                            {profileData.race_name && (
                                <div className="flex items-center space-x-3">
                                    <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                                    </svg>
                                    <span className="text-gray-700">{profileData.race_name}</span>
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Section 4: Biography */}
                    <div className="bg-white rounded-xl shadow-sm p-6">
                        <div className="flex justify-between items-center mb-4">
                            <h2 className="text-lg font-semibold">Biography</h2>
                            <button
                                onClick={() => setActiveSection('biography')}
                                className="px-4 py-2 text-sm bg-black text-white rounded-lg hover:bg-gray-800 transition-colors flex items-center space-x-2"
                            >
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                                </svg>
                                <span>Edit</span>
                            </button>
                        </div>
                        <div className="space-y-4">
                            <p className="text-gray-700 whitespace-pre-line">{profileData.biography || 'No biography added yet.'}</p>
                        </div>
                    </div>

                    {/* Section 5: My Skills */}
                    <div className="bg-white rounded-xl shadow-sm p-6">
                        <div className="flex justify-between items-center mb-6">
                            <div className="flex items-center space-x-3">
                                <svg className="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                                        d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                                </svg>
                            <h2 className="text-lg font-semibold">My Skills</h2>
                            </div>
                            <button
                                onClick={() => {
                                    setActiveSection('skills');
                                    setProfileData(prev => ({ ...prev, selectedSkillForEdit: null }));
                                }}
                                disabled={skillsLoading}
                                className="px-4 py-2 text-sm bg-indigo-600 text-white rounded-lg
                                    hover:bg-indigo-700 transition-all duration-200 flex items-center
                                    space-x-2 transform hover:scale-105 disabled:opacity-50"
                            >
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                                        d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                </svg>
                                <span>Add Skills</span>
                            </button>
                        </div>

                        {/* Error Message */}
                        {skillsError && (
                            <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                                <p className="text-red-600">{skillsError}</p>
                            </div>
                        )}

                        {/* Loading State */}
                        {skillsLoading && (
                            <div className="flex items-center justify-center py-8">
                                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
                            </div>
                        )}

                        {/* Skills Grid */}
                        {!skillsLoading && (
                            <div className="space-y-6">
                                {profileData.skills && profileData.skills.length > 0 ? (
                                    profileData.skills.map((skill) => (
                                        <div
                                            key={skill.id}
                                            className="bg-gray-50 rounded-xl p-6 hover:bg-gray-100 transition-all duration-200"
                                        >
                                <div className="flex items-center justify-between mb-4">
                                    <div className="flex items-center space-x-3">
                                        <img
                                                        src={getGameImagePath(skill.game)}
                                            alt={skill.game}
                                                        className="w-12 h-12 rounded-lg object-cover"
                                                        onError={(e) => {
                                                            e.target.src = '/default-game.png';
                                                        }}
                                                    />
                                                    <div>
                                                        <h3 className="text-lg font-semibold text-gray-900">{skill.game}</h3>
                                                        <p className="text-sm text-gray-500">
                                                            {skill.rates?.filter(r => r.enabled).length || 0} active service types
                                                        </p>
                                                    </div>
                                    </div>
                                                <div className="flex items-center space-x-2">
                                                    <button
                                                        onClick={() => {
                                                            setActiveSection('skills');
                                                            setProfileData(prev => ({
                                                                ...prev,
                                                                selectedSkillForEdit: skill
                                                            }));
                                                        }}
                                                        className="p-2 text-indigo-600 hover:bg-indigo-50 rounded-lg
                                                            transition-all duration-200"
                                                    >
                                                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                                                                d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                                                        </svg>
                                                    </button>
                                                    <button
                                                        onClick={() => {
                                                            setSkillToDelete(skill);
                                                            setShowDeleteConfirm(true);
                                                        }}
                                                        className="p-2 text-red-500 hover:bg-red-50 rounded-lg
                                                            transition-all duration-200"
                                                    >
                                                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                                                                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                        </svg>
                                                    </button>
                                </div>
                                            </div>

                                            {/* Service Rates */}
                                            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mt-4">
                                                {skill.rates && skill.rates.map((rate) => (
                                                    <div
                                                        key={rate.type}
                                                        className="bg-white rounded-lg p-4 shadow-sm border border-indigo-100"
                                                    >
                                                        <div className="flex items-center justify-between mb-2">
                                                            <span className="text-sm font-medium text-gray-600">
                                                {rate.type}
                                                            </span>
                                                            <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                                                                Active
                                                            </span>
                                                        </div>
                                                        <div className="flex items-center space-x-1">
                                                            <span className="text-lg font-bold text-gray-900">
                                                                ₱{rate.price || '-'}
                                                            </span>
                                                            <span className="text-sm text-gray-500">/hour</span>
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    ))
                                ) : (
                                    <div className="text-center py-8">
                                        <svg className="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                                                d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                        </svg>
                                        <p className="text-gray-500 mb-4">No skills added yet</p>
                                        <button
                                            onClick={() => {
                                                setActiveSection('skills');
                                                setProfileData(prev => ({
                                                    ...prev,
                                                    selectedSkillForEdit: null
                                                }));
                                            }}
                                            className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700
                                                transition-all duration-200 inline-flex items-center space-x-2"
                                        >
                                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                                                    d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                            </svg>
                                            <span>Add Your First Skill</span>
                                        </button>
                                </div>
                                )}
                            </div>
                        )}
                    </div>
                </div>
            </div>

            <AnimatePresence>
                {activeSection && (
                    <motion.div
                        className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center backdrop-blur-sm"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        onClick={() => setActiveSection(null)}
                    >
                        <motion.div
                            className="bg-white rounded-xl p-6 w-full max-w-lg mx-4 shadow-2xl"
                            initial={{ scale: 0.95, opacity: 0, y: 20 }}
                            animate={{ scale: 1, opacity: 1, y: 0 }}
                            exit={{ scale: 0.95, opacity: 0, y: 20 }}
                            onClick={e => e.stopPropagation()}
                        >
                            <div className="flex justify-between items-center mb-6">
                                <h3 className="text-xl font-bold text-gray-900">
                                    {activeSection.charAt(0).toUpperCase() + activeSection.slice(1)}
                                </h3>
                                <button
                                    onClick={() => setActiveSection(null)}
                                    className="p-2 hover:bg-gray-100 rounded-full transition-colors duration-200"
                                >
                                    <svg className="w-6 h-6 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </button>
                            </div>
                            {renderModalContent()}
                        </motion.div>
                    </motion.div>
                )}
            </AnimatePresence>

            {/* Delete Confirmation Modal */}
            <AnimatePresence>
                {showDeleteConfirm && skillToDelete && (
                    <motion.div
                        className="fixed inset-0 z-50 overflow-y-auto bg-black bg-opacity-50 backdrop-blur-sm"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                    >
                        <div className="min-h-screen px-4 text-center flex items-center justify-center">
                            <motion.div
                                className="bg-white rounded-xl p-6 w-full max-w-md mx-auto shadow-xl"
                                initial={{ scale: 0.95, opacity: 0 }}
                                animate={{ scale: 1, opacity: 1 }}
                                exit={{ scale: 0.95, opacity: 0 }}
                            >
                                <h3 className="text-lg font-semibold mb-4">Delete Skill</h3>
                                <p className="text-gray-600 mb-6">
                                    Are you sure you want to delete your {skillToDelete.game} skill?
                                    This action cannot be undone.
                                </p>
                                <div className="flex space-x-3">
                                    <button
                                        onClick={() => {
                                            setShowDeleteConfirm(false);
                                            setSkillToDelete(null);
                                        }}
                                        className="flex-1 px-4 py-2 text-gray-700 bg-gray-100 rounded-lg
                                            hover:bg-gray-200 transition-all duration-200"
                                    >
                                        Cancel
                                    </button>
                                    <button
                                        onClick={() => handleDeleteSkill(skillToDelete.id)}
                                        className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg
                                            hover:bg-red-700 transition-all duration-200"
                                    >
                                        Delete
                                    </button>
                                </div>
                            </motion.div>
                        </div>
                    </motion.div>
                )}
            </AnimatePresence>
        </>
    );
};

export default EditProfile;