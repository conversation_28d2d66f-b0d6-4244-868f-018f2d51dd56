import React from 'react';
import { motion } from 'framer-motion';

const CreditPackageCard = ({ 
  package: creditPackage, 
  isSelected, 
  onSelect,
  isLoading = false,
  disabled = false
}) => {
  // Helper function to format price
  const formatPrice = (price) => {
    const numericPrice = typeof price === 'string' ? parseFloat(price) : price;
    return typeof numericPrice === 'number' && !isNaN(numericPrice) 
      ? numericPrice.toFixed(2) 
      : '0.00';
  };

  // Calculate price per credit safely
  const calculatePricePerCredit = (price, credits) => {
    const numericPrice = typeof price === 'string' ? parseFloat(price) : price;
    if (typeof numericPrice !== 'number' || isNaN(numericPrice) || credits <= 0) {
      return 0;
    }
    return numericPrice / credits;
  };

  const pricePerCredit = calculatePricePerCredit(creditPackage.price, creditPackage.credits);

  return (
    <motion.div
      whileTap={{ scale: disabled ? 1 : 0.98 }}
      onClick={() => !disabled && onSelect(creditPackage)}
      className={`
        relative cursor-pointer rounded-xl border-2 p-4 transition-all duration-300
        ${isSelected 
          ? 'border-indigo-500 bg-indigo-50' 
          : 'border-gray-200 bg-white hover:border-indigo-200'}
        ${disabled ? 'opacity-60 cursor-not-allowed' : ''}
      `}
    >
      {isSelected && (
        <div className="absolute -top-2 -right-2 h-6 w-6 rounded-full bg-indigo-500 text-white flex items-center justify-center shadow-md">
          <svg className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
          </svg>
        </div>
      )}

      <div className="flex flex-col space-y-2">
        <div className="flex justify-between items-center">
          <span className="text-xl font-bold text-indigo-600">{creditPackage.credits} Credits</span>
          <span className="text-lg font-semibold text-gray-800">
            {creditPackage.currency_code} {formatPrice(creditPackage.price)}
          </span>
        </div>

        <div className="flex justify-between items-center text-sm text-gray-500">
          <span>Price per credit: {creditPackage.currency_code} {formatPrice(pricePerCredit)}</span>
          <span className="flex items-center">
            <svg className="w-4 h-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clipRule="evenodd" />
            </svg>
            Billplz
          </span>
        </div>
      </div>
    </motion.div>
  );
};

export default CreditPackageCard; 