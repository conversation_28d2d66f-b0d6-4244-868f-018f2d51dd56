import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import talentService from '../services/talentService';
import { useAuth } from '../contexts/AuthContext';

// Import new components
import ProfileHeader from './profile/ProfileHeader';
import StatisticsBar from './profile/StatisticsBar';
import ActionButtons from './profile/ActionButtons';
import TabNavigation from './profile/TabNavigation';
import TabContent from './profile/TabContent';

// Import tab content components
import AboutTab from './profile/AboutTab';
import ReviewTab from './profile/ReviewTab';
import SkillsTab from './profile/SkillsTab';
import PostTab from './profile/PostTab';
import MissionTab from './profile/MissionTab';
import OrderConfirmationPage from './profile/OrderConfirmationPage';

const TalentProfile = () => {
    const { talentId } = useParams();
    const { isAuthenticated } = useAuth();
    const [talent, setTalent] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [activeTab, setActiveTab] = useState('about');
    const [placedOrder, setPlacedOrder] = useState(null);
    const [showOrderConfirmation, setShowOrderConfirmation] = useState(false);
    const navigate = useNavigate();

    // Handle tab change
    const handleTabChange = (tab) => {
        setActiveTab(tab);
    };

    // Handle follow action
    const handleFollow = (isFollowing) => {
        console.log(`User ${isFollowing ? 'followed' : 'unfollowed'} talent ${talentId}`);
        // In a real app, this would make an API call
    };

    // Handle send gift action
    const handleSendGift = () => {
        console.log(`User wants to send gift to talent ${talentId}`);
        // In a real app, this would open a gift selection modal
    };

    // Handle chat action
    const handleChat = () => {
        console.log(`User wants to chat with talent ${talentId}`);
        // In a real app, this would navigate to the chat page or open a chat modal
    };

    // Handle order action
    const handleOrder = (skillOrOrder, rate) => {
        console.log('TalentProfile - handleOrder called with:', { skillOrOrder, rate });

        // Check if this is an order object (from order completion)
        if (skillOrOrder && skillOrOrder.id && skillOrOrder.price) {
            console.log('TalentProfile - This is an order object, showing confirmation');
            // This is an order object
            setPlacedOrder(skillOrOrder);
            setShowOrderConfirmation(true);
            return;
        }

        // Otherwise, this is a skill object (from initial order click)
        console.log(`TalentProfile - User wants to order from talent ${talentId}`, { skill: skillOrOrder, rate });

        // If we're in the skills tab, the order flow is handled there
        if (activeTab === 'skills') {
            console.log('TalentProfile - In skills tab, order flow handled by SkillsTab');
            // Do nothing, let SkillsTab handle it
            return;
        }

        // If we're not in the skills tab, switch to it and add a URL parameter
        console.log('TalentProfile - Not in skills tab, switching to skills tab');
        setActiveTab('skills');

        // Add a URL parameter to indicate that we want to auto-open the order modal
        const url = new URL(window.location.href);
        url.searchParams.set('order', 'true');
        window.history.replaceState({}, '', url);

        // We'll let the SkillsTab component handle the order flow from here
    };

    // Handle view order click from confirmation page
    const handleViewOrder = (order) => {
        navigate(`/orders/${order.id}`);
    };

    // Handle back to profile click from confirmation page
    const handleBackToProfile = () => {
        setShowOrderConfirmation(false);
        setPlacedOrder(null);
    };

    useEffect(() => {
        // Only fetch if talentId is defined
        if (!talentId) {
            setError('Talent ID is required');
            setLoading(false);
            return;
        }

        const fetchTalentProfile = async () => {
            setLoading(true);
            setError(null);

            try {
                // Fetch talent profile from the API
                const talentData = await talentService.getTalentById(talentId);

                if (talentData) {
                    // Use the talent data from the API
                    setTalent(talentData);
                } else {
                    // Fallback to mock data if API returns null
                    // This is just for development purposes and should be removed in production
                    const mockTalent = {
                        id: talentId,
                        name: 'Stella Ong',
                        userId: 'mx12345_20250203',
                        level: 88,
                        gender: 'female',
                        // Use stable, cacheable URLs for images to prevent flickering
                        profileImage: `https://via.placeholder.com/150?text=Stella&cache=${talentId}`,
                        coverImage: `https://via.placeholder.com/1200x400?text=Cover+Image&cache=${talentId}`,
                        bio: "Hi, I'm Stella Ong, a creative soul with a passion for gaming, drawing, and storytelling. I thrive on adventures— it's exploring immersive g worlds, creating art that tells a story, or diving into captivating movies and books that spark my imagination. Gaming fuels my competitive spirit and love for teamwork.",
                        followers: 10,
                        missionsCompleted: 40,
                        rating: 4.5,
                        reviewCount: 20,
                        zodiac: 'Taurus',
                        isOnline: true,
                        lastActive: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
                        isFollowing: false,
                        skills: [
                            {
                                id: 1,
                                name: 'League of Legend',
                                image: `https://via.placeholder.com/100?text=LoL&cache=${talentId}-1`,
                                ordersCount: 20,
                                rates: [
                                    { type: 'Professional', price: 200, unit: 'round' },
                                    { type: 'Casual', price: 80, unit: 'round' }
                                ]
                            },
                            {
                                id: 2,
                                name: 'PUBG',
                                image: `https://via.placeholder.com/100?text=PUBG&cache=${talentId}-2`,
                                ordersCount: 20,
                                rates: [
                                    { type: 'Professional', price: 200, unit: 'round' },
                                    { type: 'Casual', price: 80, unit: 'round' }
                                ]
                            }
                        ],
                        reviews: [
                            {
                                id: 1,
                                name: 'Kar Min',
                                date: '12/12/2024',
                                rating: 4,
                                comment: 'Fun, kind, and full of energy. Sammy makes any moment better!'
                            }
                        ],
                        strengths: ['Fast Learner', 'Focused', 'Team Player', 'Great Listener']
                    };

                    setTalent(mockTalent);
                }
                setLoading(false);
            } catch (err) {
                console.error('Error fetching talent profile:', err);
                setError('Failed to load talent profile');
                setLoading(false);
            }
        };

        fetchTalentProfile();
    }, [talentId]);

    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-screen bg-gray-50">
                <div className="text-center">
                    <div className="w-16 h-16 border-t-4 border-blue-600 border-solid rounded-full animate-spin mx-auto mb-4"></div>
                    <p className="text-gray-700 text-lg">Loading profile...</p>
                </div>
            </div>
        );
    }

    if (error || !talent) {
        return (
            <div className="flex items-center justify-center min-h-screen bg-gray-50">
                <div className="text-center p-8 bg-white rounded-2xl shadow-lg">
                    <p className="text-red-500 text-xl mb-4">{error || 'Talent not found'}</p>
                    <button
                        onClick={() => navigate(-1)}
                        className="px-6 py-2 rounded-lg font-medium text-white bg-blue-600 hover:bg-blue-700"
                    >
                        Go Back
                    </button>
                </div>
            </div>
        );
    }

    // If showing order confirmation, render it
    if (showOrderConfirmation && placedOrder) {
        return (
            <OrderConfirmationPage
                order={placedOrder}
                talent={talent}
                onViewOrder={handleViewOrder}
                onBackToProfile={handleBackToProfile}
            />
        );
    }

    return (
        <div className="min-h-screen bg-gray-50">
            {/* Top Navigation */}
            <div className="bg-white p-4 flex items-center shadow-sm sticky top-0 z-30">
                <button
                    onClick={() => navigate(-1)}
                    className="mr-4 bg-transparent text-blue-500 hover:bg-transparent hover:underline hover:text-blue-800"
                    aria-label="Go back"
                >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                    </svg>
                </button>
                <h1 className="text-lg font-bold">Talent Profile</h1>
            </div>

            {/* Profile Header */}
            <ProfileHeader talent={talent} />

            {/* Statistics Bar */}
            <StatisticsBar talent={talent} />

            {/* Action Buttons */}
            <ActionButtons
                talent={talent}
                onFollow={handleFollow}
                onSendGift={handleSendGift}
                onChat={handleChat}
                onOrder={handleOrder}
            />

            {/* Tab Navigation */}
            <TabNavigation
                activeTab={activeTab}
                onTabChange={handleTabChange}
            />

            {/* Tab Content */}
            <TabContent activeTab={activeTab}>
                {activeTab === 'about' && <AboutTab talent={talent} />}
                {activeTab === 'review' && <ReviewTab talent={talent} />}
                {activeTab === 'skills' && <SkillsTab talent={talent} onOrder={handleOrder} />}
                {activeTab === 'post' && <PostTab talent={talent} />}
                {activeTab === 'mission' && <MissionTab talent={talent} />}
            </TabContent>
        </div>
    );
};

export default TalentProfile;