import React, { useState, useEffect } from 'react';
import { 
  filterTalents, 
  getServiceTypes, 
  getServiceStyles 
} from '../services/talentService';
import { cacheManager } from '../utils/cacheUtils';
import ErrorMessage from './common/ErrorMessage';

/**
 * A component to test caching functionality
 */
const CacheTestComponent = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [result, setResult] = useState(null);
  const [testType, setTestType] = useState('getServiceTypes');
  const [skipCache, setSkipCache] = useState(false);
  const [timings, setTimings] = useState({
    firstCall: null,
    secondCall: null
  });
  const [serviceTypeId, setServiceTypeId] = useState('');
  const [serviceCategoryId, setServiceCategoryId] = useState('');
  const [filters, setFilters] = useState({});
  const [availableServiceTypes, setAvailableServiceTypes] = useState([]);

  // Fetch service types for the dropdown
  useEffect(() => {
    const fetchServiceTypes = async () => {
      try {
        const types = await getServiceTypes();
        setAvailableServiceTypes(types);
      } catch (error) {
        console.error('Error fetching service types:', error);
      }
    };

    fetchServiceTypes();
  }, []);

  // Function to run the selected test
  const runTest = async () => {
    setLoading(true);
    setError(null);
    setResult(null);
    setTimings({ firstCall: null, secondCall: null });

    try {
      // First call (should be uncached)
      const startTime1 = performance.now();
      let response1;

      switch (testType) {
        case 'getServiceTypes':
          response1 = await getServiceTypes(
            serviceCategoryId ? parseInt(serviceCategoryId) : null, 
            skipCache
          );
          break;
        case 'getServiceStyles':
          response1 = await getServiceStyles(
            serviceTypeId ? parseInt(serviceTypeId) : null, 
            skipCache
          );
          break;
        case 'filterTalents':
          response1 = await filterTalents(
            filters, 
            1, 
            15, 
            skipCache
          );
          break;
        default:
          throw new Error('Invalid test type');
      }
      const endTime1 = performance.now();

      // Second call (should be cached if skipCache is false)
      const startTime2 = performance.now();
      let response2;

      switch (testType) {
        case 'getServiceTypes':
          response2 = await getServiceTypes(
            serviceCategoryId ? parseInt(serviceCategoryId) : null, 
            skipCache
          );
          break;
        case 'getServiceStyles':
          response2 = await getServiceStyles(
            serviceTypeId ? parseInt(serviceTypeId) : null, 
            skipCache
          );
          break;
        case 'filterTalents':
          response2 = await filterTalents(
            filters, 
            1, 
            15, 
            skipCache
          );
          break;
        default:
          throw new Error('Invalid test type');
      }
      const endTime2 = performance.now();

      setTimings({
        firstCall: (endTime1 - startTime1).toFixed(2),
        secondCall: (endTime2 - startTime2).toFixed(2)
      });

      setResult(response2);
    } catch (err) {
      setError(err);
    } finally {
      setLoading(false);
    }
  };

  // Function to clear the cache
  const clearCache = () => {
    cacheManager.clear();
    alert('Cache cleared');
  };

  // Function to update filters for filterTalents
  const updateFilters = (key, value) => {
    if (value === '' || value === null) {
      const newFilters = { ...filters };
      delete newFilters[key];
      setFilters(newFilters);
    } else {
      setFilters({
        ...filters,
        [key]: value
      });
    }
  };

  return (
    <div className="p-4 max-w-4xl mx-auto">
      <h2 className="text-2xl font-bold mb-4">Cache Testing</h2>
      
      <div className="bg-white p-4 rounded-lg shadow mb-6">
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            API Function
          </label>
          <select
            value={testType}
            onChange={(e) => setTestType(e.target.value)}
            className="w-full p-2 border border-gray-300 rounded-md"
          >
            <option value="getServiceTypes">Get Service Types</option>
            <option value="getServiceStyles">Get Service Styles</option>
            <option value="filterTalents">Filter Talents</option>
          </select>
        </div>
        
        {testType === 'getServiceTypes' && (
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Service Category ID (optional)
            </label>
            <input
              type="number"
              value={serviceCategoryId}
              onChange={(e) => setServiceCategoryId(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md"
              placeholder="Leave empty for all"
            />
          </div>
        )}
        
        {testType === 'getServiceStyles' && (
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Service Type ID (optional)
            </label>
            <select
              value={serviceTypeId}
              onChange={(e) => setServiceTypeId(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md"
            >
              <option value="">All Service Types</option>
              {availableServiceTypes.map(type => (
                <option key={type.id} value={type.id}>
                  {type.name}
                </option>
              ))}
            </select>
          </div>
        )}
        
        {testType === 'filterTalents' && (
          <div className="mb-4 space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Gender (optional)
              </label>
              <select
                value={filters.gender || ''}
                onChange={(e) => updateFilters('gender', e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="">Any Gender</option>
                <option value="male">Male</option>
                <option value="female">Female</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Service Type (optional)
              </label>
              <select
                value={filters.serviceTypeId || ''}
                onChange={(e) => updateFilters('serviceTypeId', e.target.value ? parseInt(e.target.value) : '')}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="">Any Service Type</option>
                {availableServiceTypes.map(type => (
                  <option key={type.id} value={type.id}>
                    {type.name}
                  </option>
                ))}
              </select>
            </div>
          </div>
        )}
        
        <div className="mb-4">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={skipCache}
              onChange={(e) => setSkipCache(e.target.checked)}
              className="mr-2"
            />
            <span className="text-sm font-medium text-gray-700">Skip Cache</span>
          </label>
        </div>
        
        <div className="flex space-x-2">
          <button
            onClick={runTest}
            disabled={loading}
            className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:bg-blue-300"
          >
            {loading ? 'Running...' : 'Run Test'}
          </button>
          
          <button
            onClick={clearCache}
            className="bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700"
          >
            Clear Cache
          </button>
        </div>
      </div>
      
      {timings.firstCall && (
        <div className="bg-white p-4 rounded-lg shadow mb-6">
          <h3 className="text-lg font-medium mb-2">Timing Results</h3>
          <div className="grid grid-cols-2 gap-4">
            <div className="p-3 bg-gray-50 rounded-md">
              <p className="text-sm font-medium text-gray-700">First Call</p>
              <p className="text-xl font-bold">{timings.firstCall} ms</p>
            </div>
            <div className="p-3 bg-gray-50 rounded-md">
              <p className="text-sm font-medium text-gray-700">Second Call</p>
              <p className="text-xl font-bold">{timings.secondCall} ms</p>
              {!skipCache && timings.secondCall < timings.firstCall && (
                <p className="text-xs text-green-600 mt-1">
                  {((1 - timings.secondCall / timings.firstCall) * 100).toFixed(0)}% faster (cached)
                </p>
              )}
            </div>
          </div>
        </div>
      )}
      
      {error && (
        <div className="mb-6">
          <h3 className="text-lg font-medium mb-2">Error</h3>
          <ErrorMessage error={error} onRetry={runTest} />
        </div>
      )}
      
      {result && (
        <div>
          <h3 className="text-lg font-medium mb-2">Result</h3>
          <div className="bg-gray-50 p-4 rounded-md">
            <pre className="text-xs overflow-auto p-2 bg-gray-100 rounded">
              {JSON.stringify(result, null, 2)}
            </pre>
          </div>
        </div>
      )}
    </div>
  );
};

export default CacheTestComponent;
