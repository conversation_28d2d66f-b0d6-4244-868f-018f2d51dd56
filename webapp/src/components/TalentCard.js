import React from 'react';
import { useNavigate } from 'react-router-dom';
import { formatTalentForDisplay } from '../models/TalentModel';

const TalentCard = ({ talent }) => {
    // Format the talent data for display
    const formattedTalent = formatTalentForDisplay(talent);

    const {
        id,
        displayName,
        gender,
        levelNumber,
        levelName,
        profileImage,
        serviceTitle,
        serviceDescription,
        serviceType,
        serviceStyle,
        lowestPrice,
        isFeatured
    } = formattedTalent;

    const navigate = useNavigate();

    const handleCardClick = () => {
        navigate(`/talents/${id}`);
    };

    return (
        <div
            className="talent-card bg-white rounded-xl overflow-hidden shadow-sm relative cursor-pointer"
            onClick={handleCardClick}
        >
            {/* Featured Tag */}
            {isFeatured && (
                <div className="absolute top-0 left-0 bg-gradient-to-r from-orange-500 to-red-500 text-white text-xs font-bold py-1 px-3 rotate-[-45deg] translate-x-[-18px] translate-y-[10px] z-10 shadow-md">
                    HOT
                </div>
            )}

            {/* Profile Image */}
            <div className="relative aspect-square overflow-hidden group">
                <img
                    src={profileImage}
                    alt={displayName}
                    className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                    onError={(e) => {
                        e.target.onerror = null;
                        e.target.src = 'https://via.placeholder.com/150?text=No+Image';
                    }}
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                {/* Quick View Button - Only visible on hover */}
                <div className="absolute bottom-0 left-0 right-0 p-3 transform translate-y-full group-hover:translate-y-0 transition-transform duration-300">
                    <button className="w-full bg-white/90 backdrop-blur-sm text-indigo-600 py-1.5 rounded-lg text-sm font-medium hover:bg-white transition-colors">
                        View Profile
                    </button>
                </div>
            </div>

            {/* Content */}
            <div className="p-3 sm:p-4">
                <div className="flex items-center gap-2 mb-1.5">
                    <div className="flex items-center">
                        <span className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white text-xs font-bold px-2 py-0.5 rounded-full shadow-sm">
                            LV{levelNumber}
                        </span>
                    </div>
                    <h3 className="font-bold text-sm sm:text-base text-gray-800 flex-1 truncate">{displayName}</h3>
                    <span className={`text-sm sm:text-base font-bold ${
                        gender === 'male' ? 'text-blue-600' :
                        gender === 'female' ? 'text-pink-500' :
                        'text-purple-600'
                    }`}>
                        {gender === 'male' ? '♂' : gender === 'female' ? '♀' : '⚧'}
                    </span>
                </div>

                <div className="flex items-center text-xs sm:text-sm text-gray-600 mb-1.5">
                    <span className="font-medium">{serviceType}</span>
                    {serviceStyle && (
                        <>
                            <span className="mx-1.5 text-gray-400">•</span>
                            <span className="text-gray-500">{serviceStyle}</span>
                        </>
                    )}
                </div>

                <p className="text-xs text-gray-600 line-clamp-2 mb-2 hidden sm:block">
                    {serviceDescription || serviceTitle}
                </p>

                <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-500 bg-gray-100 px-2 py-0.5 rounded-full">
                        {levelName}
                    </span>
                    {lowestPrice !== null && (
                        <span className="font-medium text-sm text-green-600 bg-green-50 px-2 py-0.5 rounded-full flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            {lowestPrice}
                        </span>
                    )}
                </div>
            </div>
        </div>
    );
};

export default TalentCard;