import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';

const MissionFooter = () => {
  const navigate = useNavigate();
  const location = useLocation();
  
  // Determine active tab
  const isOnMissions = location.pathname === '/missions';
  const isOnMyMissions = location.pathname.includes('/missions/my-missions');
  const isOnHostDashboard = location.pathname.includes('/host/dashboard');
  const isOnCreatePage = location.pathname.includes('/missions/create');
  
  return (
    <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 md:hidden z-40">
      <div className="flex justify-around items-center h-16">
        <button
          onClick={() => navigate('/missions')}
          className={`flex flex-col items-center justify-center w-full h-full ${
            isOnMissions ? 'text-indigo-600' : 'text-gray-500'
          }`}
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16" />
          </svg>
          <span className="text-xs mt-1">Browse</span>
        </button>
        
        <button
          onClick={() => navigate('/missions/my-missions')}
          className={`flex flex-col items-center justify-center w-full h-full ${
            isOnMyMissions ? 'text-indigo-600' : 'text-gray-500'
          }`}
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
          </svg>
          <span className="text-xs mt-1">My Missions</span>
        </button>
        
        <button
          onClick={() => navigate('/missions/create')}
          className={`flex flex-col items-center justify-center w-full h-full ${
            isOnCreatePage ? 'text-indigo-600' : 'text-gray-500'
          }`}
        >
          <div className={`rounded-full p-2 ${isOnCreatePage ? 'bg-indigo-100' : 'bg-gray-100'}`}>
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
          </div>
          <span className="text-xs mt-1">Create</span>
        </button>
        
        <button
          onClick={() => navigate('/host/dashboard')}
          className={`flex flex-col items-center justify-center w-full h-full ${
            isOnHostDashboard ? 'text-indigo-600' : 'text-gray-500'
          }`}
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
          </svg>
          <span className="text-xs mt-1">Host</span>
        </button>
        
        <button
          onClick={() => navigate('/profile')}
          className={`flex flex-col items-center justify-center w-full h-full ${
            location.pathname.includes('/profile') ? 'text-indigo-600' : 'text-gray-500'
          }`}
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
          <span className="text-xs mt-1">Profile</span>
        </button>
      </div>
    </div>
  );
};

export default MissionFooter;
