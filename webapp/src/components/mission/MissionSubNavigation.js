import React from 'react';
import { Link, useLocation } from 'react-router-dom';

const MissionSubNavigation = ({
  title,
  showBackButton = true,
  backPath = '/missions',
  actions = null
}) => {
  const location = useLocation();

  // Determine active tab
  const isOnMissions = location.pathname === '/missions';
  const isOnMyMissions = location.pathname.includes('/missions/my-missions');
  const isOnCreatePage = location.pathname.includes('/missions/create');

  return (
    <div className="bg-white/80 shadow-sm backdrop-filter backdrop-blur-md border-b border-indigo-100/50">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between py-3">
          {/* Breadcrumb Navigation */}
          <div className="flex items-center">
            <Link
              to="/"
              className="text-gray-500 hover:text-indigo-600 transition-colors text-sm font-medium"
            >
              Home
            </Link>
            <svg className="w-4 h-4 mx-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
            </svg>
            <Link
              to="/missions"
              className="text-gray-500 hover:text-indigo-600 transition-colors text-sm font-medium"
            >
              Missions
            </Link>
            {title && title !== 'Missions' && (
              <>
                <svg className="w-4 h-4 mx-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                </svg>
                <span className="text-indigo-600 text-sm font-medium">{title}</span>
              </>
            )}
          </div>

          <div className="flex items-center space-x-3">
            {/* Action Buttons */}
            {actions}
          </div>
        </div>

        {/* Mission Navigation Links */}
        <div className="hidden md:flex items-center space-x-1 py-2">
          <Link
            to="/missions"
            className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
              isOnMissions
                ? 'bg-indigo-50 text-indigo-700'
                : 'text-gray-600 hover:bg-indigo-50 hover:text-indigo-700'
            }`}
          >
            Browse Missions
          </Link>

          <Link
            to="/missions/my-missions"
            className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
              isOnMyMissions
                ? 'bg-indigo-50 text-indigo-700'
                : 'text-gray-600 hover:bg-indigo-50 hover:text-indigo-700'
            }`}
          >
            My Missions
          </Link>

          <Link
            to="/missions/create"
            className={`px-4 py-2 bg-indigo-600 text-white rounded-lg text-sm font-medium hover:bg-indigo-700 transition-colors shadow-sm ${
              isOnCreatePage
                ? 'bg-indigo-700'
                : ''
            }`}
          >
            <svg className="w-4 h-4 mr-1 inline-block" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Create Mission
          </Link>
        </div>
      </div>
    </div>
  );
};

export default MissionSubNavigation;
