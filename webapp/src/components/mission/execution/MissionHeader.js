import React from 'react';
import { motion } from 'framer-motion';

const MissionHeader = ({ mission }) => {
  // Format date for display
  const formatDate = (dateString) => {
    const options = { 
      weekday: 'short', 
      month: 'short', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    };
    return new Date(dateString).toLocaleDateString('en-US', options);
  };
  
  // Calculate progress percentage
  const calculateProgress = () => {
    if (!mission?.tasks?.length) return 0;
    
    const completedTasks = mission.tasks.filter(task => task.completed).length;
    return Math.round((completedTasks / mission.tasks.length) * 100);
  };
  
  // Get status color
  const getStatusColor = () => {
    if (!mission?.status) return 'gray';
    
    switch (mission.status.toLowerCase()) {
      case 'scheduled':
        return 'blue';
      case 'in progress':
        return 'green';
      case 'completed':
        return 'purple';
      case 'cancelled':
        return 'red';
      default:
        return 'gray';
    }
  };
  
  // Get time remaining
  const getTimeRemaining = () => {
    if (!mission?.date) return 'No date set';
    
    const missionDate = new Date(mission.date);
    const now = new Date();
    
    if (mission.status?.toLowerCase() === 'completed') {
      return 'Mission completed';
    }
    
    if (mission.status?.toLowerCase() === 'cancelled') {
      return 'Mission cancelled';
    }
    
    if (missionDate < now) {
      if (mission.status?.toLowerCase() === 'in progress') {
        return 'Mission in progress';
      }
      return 'Mission date passed';
    }
    
    const diffTime = Math.abs(missionDate - now);
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor((diffTime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const diffMinutes = Math.floor((diffTime % (1000 * 60 * 60)) / (1000 * 60));
    
    if (diffDays > 0) {
      return `${diffDays} day${diffDays !== 1 ? 's' : ''} ${diffHours} hour${diffHours !== 1 ? 's' : ''} remaining`;
    }
    
    if (diffHours > 0) {
      return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ${diffMinutes} minute${diffMinutes !== 1 ? 's' : ''} remaining`;
    }
    
    return `${diffMinutes} minute${diffMinutes !== 1 ? 's' : ''} remaining`;
  };
  
  return (
    <div className="bg-white rounded-xl shadow-sm border border-indigo-100/50 overflow-hidden">
      {/* Mission Image with Overlay */}
      <div className="relative h-48 md:h-64 overflow-hidden">
        <img 
          src={mission?.image || '/images/mission-default.jpg'} 
          alt={mission?.title || 'Mission'}
          className="w-full h-full object-cover"
        />
        
        {/* Overlay gradient */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent"></div>
        
        {/* Mission title and details */}
        <div className="absolute bottom-0 left-0 right-0 p-6 text-white">
          <div className="flex items-center mb-2">
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-${getStatusColor()}-500/20 text-${getStatusColor()}-100 backdrop-blur-sm border border-${getStatusColor()}-500/30`}>
              <span className={`w-2 h-2 mr-1.5 rounded-full bg-${getStatusColor()}-500 animate-pulse`}></span>
              {mission?.status || 'Unknown'}
            </span>
            
            <span className="mx-2 text-gray-400">•</span>
            
            <span className="text-sm text-gray-300 flex items-center">
              <svg className="w-4 h-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              {mission?.date ? formatDate(mission.date) : 'Date TBD'}
            </span>
          </div>
          
          <h1 className="text-2xl md:text-3xl font-bold">{mission?.title || 'Mission Title'}</h1>
          
          {/* Host information */}
          <div className="flex items-center mt-3">
            <div className="relative">
              <div className="w-10 h-10 rounded-full overflow-hidden border-2 border-white">
                <img 
                  src={mission?.host?.avatar || '/images/default-avatar.jpg'} 
                  alt={mission?.host?.name || 'Host'} 
                  className="w-full h-full object-cover"
                />
              </div>
              {mission?.host?.online && (
                <span className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></span>
              )}
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-white">
                Hosted by {mission?.host?.name || 'Anonymous Host'}
              </p>
              <p className="text-xs text-gray-400 flex items-center">
                <svg className="w-3 h-3 mr-1 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
                LV{mission?.host?.level || '??'}
              </p>
            </div>
          </div>
        </div>
      </div>
      
      {/* Mission Details */}
      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Mission Progress */}
          <div className="bg-indigo-50 rounded-lg p-4 border border-indigo-100">
            <h3 className="text-sm font-medium text-indigo-800 mb-2 flex items-center">
              <svg className="w-4 h-4 mr-1.5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              Mission Progress
            </h3>
            
            <div className="mb-2">
              <div className="flex justify-between items-center mb-1">
                <span className="text-xs text-indigo-700 font-medium">{calculateProgress()}% Complete</span>
                <span className="text-xs text-indigo-700">
                  {mission?.tasks?.filter(task => task.completed).length || 0}/{mission?.tasks?.length || 0} Tasks
                </span>
              </div>
              <div className="w-full bg-white rounded-full h-2.5 overflow-hidden">
                <motion.div 
                  className="bg-gradient-to-r from-indigo-600 to-blue-600 h-2.5 rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: `${calculateProgress()}%` }}
                  transition={{ duration: 1, delay: 0.2 }}
                ></motion.div>
              </div>
            </div>
            
            <p className="text-sm text-indigo-700">
              {mission?.status?.toLowerCase() === 'completed' 
                ? 'All tasks completed!' 
                : mission?.tasks?.filter(task => task.completed).length === 0 
                  ? 'No tasks completed yet' 
                  : `${mission?.tasks?.filter(task => task.completed).length} task${mission?.tasks?.filter(task => task.completed).length !== 1 ? 's' : ''} completed`
              }
            </p>
          </div>
          
          {/* Time Remaining */}
          <div className="bg-blue-50 rounded-lg p-4 border border-blue-100">
            <h3 className="text-sm font-medium text-blue-800 mb-2 flex items-center">
              <svg className="w-4 h-4 mr-1.5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Time Remaining
            </h3>
            
            <p className="text-lg font-semibold text-blue-700">{getTimeRemaining()}</p>
            
            <p className="text-sm text-blue-700 mt-1">
              {mission?.date 
                ? `Mission ${mission?.status?.toLowerCase() === 'completed' ? 'was' : 'is'} scheduled for ${formatDate(mission.date)}` 
                : 'Mission date not set'
              }
            </p>
          </div>
          
          {/* Rewards */}
          <div className="bg-green-50 rounded-lg p-4 border border-green-100">
            <h3 className="text-sm font-medium text-green-800 mb-2 flex items-center">
              <svg className="w-4 h-4 mr-1.5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Rewards
            </h3>
            
            <div className="flex items-center">
              <div className="w-10 h-10 rounded-full bg-gradient-to-br from-green-500 to-emerald-500 flex items-center justify-center text-white shadow-sm">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-lg font-semibold text-green-700">{mission?.bounty || 0} Credits</p>
                <p className="text-sm text-green-700">
                  {mission?.status?.toLowerCase() === 'completed' 
                    ? 'Reward claimed' 
                    : mission?.status?.toLowerCase() === 'in progress' 
                      ? 'Reward pending completion' 
                      : 'Available upon completion'
                  }
                </p>
              </div>
            </div>
          </div>
        </div>
        
        {/* Mission Description */}
        <div className="mt-6">
          <h3 className="text-sm font-medium text-gray-700 mb-2">Mission Description</h3>
          <p className="text-gray-600">
            {mission?.description || 'No description provided for this mission.'}
          </p>
        </div>
      </div>
    </div>
  );
};

export default MissionHeader;
