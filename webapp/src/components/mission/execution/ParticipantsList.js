import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useToast } from '../../common/ToastProvider';

const ParticipantsList = ({ 
  participants = [], 
  host = null,
  isHost = false,
  missionStatus = 'in progress',
  onRemoveParticipant,
  onPromoteToHost
}) => {
  const toast = useToast();
  const [expandedParticipant, setExpandedParticipant] = useState(null);
  const [showRemoveConfirm, setShowRemoveConfirm] = useState(null);
  
  // Handle participant removal
  const handleRemoveParticipant = (participantId) => {
    if (missionStatus.toLowerCase() !== 'in progress') {
      toast.info('Participants can only be removed when the mission is in progress');
      return;
    }
    
    onRemoveParticipant(participantId);
    setShowRemoveConfirm(null);
    toast.success('Participant removed from mission');
  };
  
  // Handle host promotion
  const handlePromoteToHost = (participantId) => {
    if (missionStatus.toLowerCase() !== 'in progress') {
      toast.info('Host can only be changed when the mission is in progress');
      return;
    }
    
    if (window.confirm('Are you sure you want to transfer host privileges to this participant? You will no longer be the host.')) {
      onPromoteToHost(participantId);
      toast.success('Host privileges transferred successfully');
    }
  };
  
  // Get participant status color
  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'active':
        return 'green';
      case 'inactive':
        return 'gray';
      case 'pending':
        return 'yellow';
      default:
        return 'gray';
    }
  };
  
  // Get participant role color
  const getRoleColor = (role) => {
    switch (role?.toLowerCase()) {
      case 'host':
        return 'purple';
      case 'co-host':
        return 'indigo';
      case 'participant':
        return 'blue';
      default:
        return 'gray';
    }
  };
  
  return (
    <div className="bg-white rounded-xl shadow-sm border border-indigo-100/50 overflow-hidden">
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-lg font-semibold text-gray-800 flex items-center">
            <svg className="w-5 h-5 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
            </svg>
            Participants
          </h2>
          
          <div className="text-sm font-medium text-gray-600">
            {participants.length} {participants.length === 1 ? 'Participant' : 'Participants'}
          </div>
        </div>
        
        {/* Host Section */}
        {host && (
          <div className="mb-6">
            <h3 className="text-sm font-medium text-gray-500 mb-3">Host</h3>
            <div className="bg-purple-50 rounded-lg p-4 border border-purple-100">
              <div className="flex items-center">
                <div className="relative">
                  <div className="w-12 h-12 rounded-full overflow-hidden border-2 border-purple-200">
                    <img 
                      src={host.avatar || '/images/default-avatar.jpg'} 
                      alt={host.name} 
                      className="w-full h-full object-cover"
                    />
                  </div>
                  {host.online && (
                    <span className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></span>
                  )}
                </div>
                
                <div className="ml-4 flex-grow">
                  <div className="flex items-center">
                    <h4 className="font-medium text-gray-800">{host.name}</h4>
                    <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                      Host
                    </span>
                    {host.online && (
                      <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Online
                      </span>
                    )}
                  </div>
                  
                  <div className="flex items-center mt-1">
                    <span className="text-sm text-gray-500 flex items-center">
                      <svg className="w-4 h-4 mr-1 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                      </svg>
                      Level {host.level || '??'}
                    </span>
                    
                    {host.completedMissions > 0 && (
                      <>
                        <span className="mx-2 text-gray-300">•</span>
                        <span className="text-sm text-gray-500 flex items-center">
                          <svg className="w-4 h-4 mr-1 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                          </svg>
                          {host.completedMissions} {host.completedMissions === 1 ? 'mission' : 'missions'} completed
                        </span>
                      </>
                    )}
                  </div>
                </div>
                
                {/* Contact Host Button */}
                <button className="px-3 py-1.5 bg-purple-100 text-purple-700 rounded-lg hover:bg-purple-200 transition-colors text-sm font-medium flex items-center">
                  <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  </svg>
                  Message
                </button>
              </div>
            </div>
          </div>
        )}
        
        {/* Participants List */}
        <div>
          <h3 className="text-sm font-medium text-gray-500 mb-3">Participants</h3>
          
          {participants.length === 0 ? (
            <div className="text-center py-8 bg-gray-50 rounded-lg border border-gray-200">
              <svg className="w-12 h-12 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
              </svg>
              <h3 className="text-lg font-medium text-gray-700 mb-2">No Participants Yet</h3>
              <p className="text-gray-500 max-w-md mx-auto">
                {isHost 
                  ? "There are no participants in this mission yet. Share the mission link to invite others to join."
                  : "You're the first participant in this mission. The host may add more participants later."
                }
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {participants.map((participant) => (
                <div 
                  key={participant.id}
                  className="border border-gray-200 rounded-lg overflow-hidden"
                >
                  <div 
                    className="flex items-center p-4 cursor-pointer bg-white hover:bg-gray-50 transition-colors"
                    onClick={() => setExpandedParticipant(expandedParticipant === participant.id ? null : participant.id)}
                  >
                    <div className="relative">
                      <div className="w-10 h-10 rounded-full overflow-hidden">
                        <img 
                          src={participant.avatar || '/images/default-avatar.jpg'} 
                          alt={participant.name} 
                          className="w-full h-full object-cover"
                        />
                      </div>
                      {participant.online && (
                        <span className="absolute bottom-0 right-0 w-2.5 h-2.5 bg-green-500 rounded-full border-2 border-white"></span>
                      )}
                    </div>
                    
                    <div className="ml-3 flex-grow">
                      <div className="flex items-center">
                        <h4 className="font-medium text-gray-800">{participant.name}</h4>
                        {participant.role && (
                          <span className={`ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-${getRoleColor(participant.role)}-100 text-${getRoleColor(participant.role)}-800`}>
                            {participant.role}
                          </span>
                        )}
                        {participant.status && (
                          <span className={`ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-${getStatusColor(participant.status)}-100 text-${getStatusColor(participant.status)}-800`}>
                            {participant.status}
                          </span>
                        )}
                      </div>
                      
                      <div className="text-sm text-gray-500 flex items-center mt-0.5">
                        <svg className="w-3.5 h-3.5 mr-1 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                        Level {participant.level || '??'}
                      </div>
                    </div>
                    
                    {/* Expand/Collapse Icon */}
                    <div className="ml-4">
                      <svg 
                        className={`w-5 h-5 text-gray-400 transform transition-transform ${expandedParticipant === participant.id ? 'rotate-180' : ''}`} 
                        fill="none" 
                        stroke="currentColor" 
                        viewBox="0 0 24 24"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </div>
                  
                  {/* Participant Details (Expanded) */}
                  <AnimatePresence>
                    {expandedParticipant === participant.id && (
                      <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: 'auto', opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={{ duration: 0.2 }}
                        className="border-t border-gray-200 overflow-hidden"
                      >
                        <div className="p-4 bg-gray-50">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            {participant.completedMissions !== undefined && (
                              <div>
                                <h4 className="text-xs font-medium text-gray-500 mb-1">Completed Missions</h4>
                                <div className="text-sm text-gray-700 flex items-center">
                                  <svg className="w-4 h-4 mr-1.5 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                                  </svg>
                                  {participant.completedMissions} {participant.completedMissions === 1 ? 'mission' : 'missions'}
                                </div>
                              </div>
                            )}
                            
                            {participant.joinedAt && (
                              <div>
                                <h4 className="text-xs font-medium text-gray-500 mb-1">Joined Mission</h4>
                                <div className="text-sm text-gray-700 flex items-center">
                                  <svg className="w-4 h-4 mr-1.5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                  </svg>
                                  {new Date(participant.joinedAt).toLocaleDateString()}
                                </div>
                              </div>
                            )}
                          </div>
                          
                          {/* Action Buttons */}
                          <div className="flex justify-end space-x-2">
                            <button className="px-3 py-1.5 bg-white border border-gray-300 rounded text-sm text-gray-700 hover:bg-gray-50 transition-colors flex items-center">
                              <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                              </svg>
                              Message
                            </button>
                            
                            {isHost && missionStatus.toLowerCase() === 'in progress' && (
                              <>
                                <button 
                                  className="px-3 py-1.5 bg-indigo-50 border border-indigo-200 rounded text-sm text-indigo-600 hover:bg-indigo-100 transition-colors flex items-center"
                                  onClick={() => handlePromoteToHost(participant.id)}
                                >
                                  <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                                  </svg>
                                  Make Host
                                </button>
                                
                                <button 
                                  className="px-3 py-1.5 bg-red-50 border border-red-200 rounded text-sm text-red-600 hover:bg-red-100 transition-colors flex items-center"
                                  onClick={() => setShowRemoveConfirm(participant.id)}
                                >
                                  <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                  </svg>
                                  Remove
                                </button>
                              </>
                            )}
                          </div>
                          
                          {/* Remove Confirmation */}
                          <AnimatePresence>
                            {showRemoveConfirm === participant.id && (
                              <motion.div
                                initial={{ opacity: 0, y: -10 }}
                                animate={{ opacity: 1, y: 0 }}
                                exit={{ opacity: 0, y: -10 }}
                                className="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg"
                              >
                                <p className="text-sm text-red-700 mb-2">
                                  Are you sure you want to remove this participant from the mission?
                                </p>
                                <div className="flex justify-end space-x-2">
                                  <button 
                                    className="px-3 py-1 bg-white border border-gray-300 rounded text-xs text-gray-700 hover:bg-gray-50 transition-colors"
                                    onClick={() => setShowRemoveConfirm(null)}
                                  >
                                    Cancel
                                  </button>
                                  <button 
                                    className="px-3 py-1 bg-red-600 rounded text-xs text-white hover:bg-red-700 transition-colors"
                                    onClick={() => handleRemoveParticipant(participant.id)}
                                  >
                                    Remove
                                  </button>
                                </div>
                              </motion.div>
                            )}
                          </AnimatePresence>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ParticipantsList;
