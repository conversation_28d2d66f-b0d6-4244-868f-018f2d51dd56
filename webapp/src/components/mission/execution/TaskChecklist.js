import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useToast } from '../../common/ToastProvider';

const TaskChecklist = ({ 
  tasks = [], 
  onTaskComplete, 
  onTaskUncomplete,
  isHost = false,
  missionStatus = 'in progress'
}) => {
  const toast = useToast();
  const [expandedTask, setExpandedTask] = useState(null);
  
  // Calculate progress percentage
  const calculateProgress = () => {
    if (!tasks.length) return 0;
    const completedTasks = tasks.filter(task => task.completed).length;
    return Math.round((completedTasks / tasks.length) * 100);
  };
  
  // Handle task toggle
  const handleTaskToggle = (taskId, currentStatus) => {
    // If mission is not in progress, don't allow changes
    if (missionStatus.toLowerCase() !== 'in progress') {
      toast.info('Tasks can only be updated when the mission is in progress');
      return;
    }
    
    if (currentStatus) {
      // If task is already completed, confirm before uncompleting
      if (window.confirm('Are you sure you want to mark this task as incomplete?')) {
        onTaskUncomplete(taskId);
        toast.info('Task marked as incomplete');
      }
    } else {
      onTaskComplete(taskId);
      toast.success('Task completed!');
      
      // Check if all tasks are completed
      const updatedTasks = tasks.map(t => 
        t.id === taskId ? { ...t, completed: true } : t
      );
      
      if (updatedTasks.every(t => t.completed)) {
        toast.success('All tasks completed! Mission can now be finalized.', 5000);
      }
    }
  };
  
  // Get task icon based on type
  const getTaskIcon = (type) => {
    switch (type) {
      case 'setup':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
        );
      case 'gameplay':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z" />
          </svg>
        );
      case 'achievement':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
          </svg>
        );
      case 'communication':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
        );
      case 'verification':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
          </svg>
        );
      default:
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
          </svg>
        );
    }
  };
  
  // Get task priority color
  const getPriorityColor = (priority) => {
    switch (priority?.toLowerCase()) {
      case 'high':
        return 'red';
      case 'medium':
        return 'yellow';
      case 'low':
        return 'green';
      default:
        return 'gray';
    }
  };
  
  // Get task status color
  const getStatusColor = (completed) => {
    return completed ? 'green' : 'gray';
  };
  
  return (
    <div className="bg-white rounded-xl shadow-sm border border-indigo-100/50 overflow-hidden">
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-lg font-semibold text-gray-800 flex items-center">
            <svg className="w-5 h-5 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
            </svg>
            Mission Tasks
          </h2>
          
          <div className="flex items-center">
            <div className="text-sm font-medium text-gray-600 mr-3">
              {tasks.filter(task => task.completed).length}/{tasks.length} Completed
            </div>
            <div className="w-20 h-2 bg-gray-200 rounded-full overflow-hidden">
              <motion.div 
                className="h-full bg-gradient-to-r from-indigo-600 to-blue-600"
                initial={{ width: 0 }}
                animate={{ width: `${calculateProgress()}%` }}
                transition={{ duration: 0.5 }}
              ></motion.div>
            </div>
          </div>
        </div>
        
        {tasks.length === 0 ? (
          <div className="text-center py-8">
            <svg className="w-12 h-12 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
            <h3 className="text-lg font-medium text-gray-700 mb-2">No Tasks Available</h3>
            <p className="text-gray-500 max-w-md mx-auto">
              {isHost 
                ? "You haven't added any tasks to this mission yet. Tasks help participants understand what needs to be done."
                : "The host hasn't added any tasks to this mission yet. Check back later for updates."
              }
            </p>
            {isHost && missionStatus.toLowerCase() === 'in progress' && (
              <button className="mt-4 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">
                Add Task
              </button>
            )}
          </div>
        ) : (
          <div className="space-y-3">
            {tasks.map((task, index) => (
              <div 
                key={task.id || index}
                className="border border-gray-200 rounded-lg overflow-hidden"
              >
                <div 
                  className={`flex items-center p-4 cursor-pointer ${
                    task.completed ? 'bg-green-50/50' : 'bg-white'
                  }`}
                  onClick={() => setExpandedTask(expandedTask === task.id ? null : task.id)}
                >
                  {/* Checkbox */}
                  <div className="mr-4">
                    <button
                      className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${
                        task.completed 
                          ? 'bg-green-500 border-green-500 text-white' 
                          : 'border-gray-300 hover:border-indigo-500'
                      } transition-colors`}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleTaskToggle(task.id, task.completed);
                      }}
                      disabled={missionStatus.toLowerCase() !== 'in progress'}
                    >
                      {task.completed && (
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                        </svg>
                      )}
                    </button>
                  </div>
                  
                  {/* Task Icon */}
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${
                    task.completed 
                      ? 'bg-green-100 text-green-600' 
                      : 'bg-indigo-100 text-indigo-600'
                  }`}>
                    {getTaskIcon(task.type)}
                  </div>
                  
                  {/* Task Title and Status */}
                  <div className="flex-grow">
                    <h3 className={`font-medium ${task.completed ? 'text-green-800 line-through opacity-70' : 'text-gray-800'}`}>
                      {task.title}
                    </h3>
                    <div className="flex items-center mt-1">
                      <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-${getStatusColor(task.completed)}-100 text-${getStatusColor(task.completed)}-800 mr-2`}>
                        {task.completed ? 'Completed' : 'Pending'}
                      </span>
                      
                      {task.priority && (
                        <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-${getPriorityColor(task.priority)}-100 text-${getPriorityColor(task.priority)}-800`}>
                          {task.priority} Priority
                        </span>
                      )}
                    </div>
                  </div>
                  
                  {/* Expand/Collapse Icon */}
                  <div className="ml-4">
                    <svg 
                      className={`w-5 h-5 text-gray-400 transform transition-transform ${expandedTask === task.id ? 'rotate-180' : ''}`} 
                      fill="none" 
                      stroke="currentColor" 
                      viewBox="0 0 24 24"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </div>
                
                {/* Task Details (Expanded) */}
                <AnimatePresence>
                  {expandedTask === task.id && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.2 }}
                      className="border-t border-gray-200 overflow-hidden"
                    >
                      <div className="p-4 bg-gray-50">
                        {task.description && (
                          <div className="mb-4">
                            <h4 className="text-sm font-medium text-gray-700 mb-1">Description</h4>
                            <p className="text-sm text-gray-600">{task.description}</p>
                          </div>
                        )}
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {task.assignedTo && (
                            <div>
                              <h4 className="text-xs font-medium text-gray-500 mb-1">Assigned To</h4>
                              <div className="flex items-center">
                                <div className="w-6 h-6 rounded-full overflow-hidden mr-2">
                                  <img 
                                    src={task.assignedTo.avatar || '/images/default-avatar.jpg'} 
                                    alt={task.assignedTo.name} 
                                    className="w-full h-full object-cover"
                                  />
                                </div>
                                <span className="text-sm text-gray-700">{task.assignedTo.name}</span>
                              </div>
                            </div>
                          )}
                          
                          {task.dueDate && (
                            <div>
                              <h4 className="text-xs font-medium text-gray-500 mb-1">Due Date</h4>
                              <div className="flex items-center text-sm text-gray-700">
                                <svg className="w-4 h-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                                {new Date(task.dueDate).toLocaleDateString()}
                              </div>
                            </div>
                          )}
                        </div>
                        
                        {isHost && missionStatus.toLowerCase() === 'in progress' && (
                          <div className="mt-4 flex justify-end space-x-2">
                            <button className="px-3 py-1.5 bg-white border border-gray-300 rounded text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                              Edit Task
                            </button>
                            <button className="px-3 py-1.5 bg-red-50 border border-red-200 rounded text-sm text-red-600 hover:bg-red-100 transition-colors">
                              Delete Task
                            </button>
                          </div>
                        )}
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            ))}
          </div>
        )}
        
        {/* Add Task Button (for host) */}
        {isHost && tasks.length > 0 && missionStatus.toLowerCase() === 'in progress' && (
          <div className="mt-6 flex justify-center">
            <button className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors flex items-center">
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              Add New Task
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default TaskChecklist;
