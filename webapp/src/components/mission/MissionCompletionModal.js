import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import MissionPaymentSummary from './MissionPaymentSummary';

const MissionCompletionModal = ({ isOpen, onClose, onSubmit, mission, participants }) => {
  const [ratings, setRatings] = useState({});
  const [feedback, setFeedback] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [payoutDistribution, setPayoutDistribution] = useState([]);
  const [totalBounty, setTotalBounty] = useState(0);
  const [platformFee, setPlatformFee] = useState(0);

  // Initialize ratings for all participants
  useEffect(() => {
    if (participants && participants.length > 0) {
      const initialRatings = {};
      participants.forEach(participant => {
        initialRatings[participant.id] = 5; // Default rating
      });
      setRatings(initialRatings);
    }
  }, [participants]);

  // Calculate payout distribution based on ratings
  useEffect(() => {
    if (participants && participants.length > 0 && mission) {
      // Calculate total rating points
      let totalRatingPoints = 0;
      Object.values(ratings).forEach(rating => {
        totalRatingPoints += rating;
      });

      // Calculate bounty and platform fee
      const bounty = mission.bounty || 0;
      const fee = Math.ceil(bounty * 0.05); // 5% platform fee

      setTotalBounty(bounty);
      setPlatformFee(fee);

      // Calculate distribution
      const distribution = participants.map(participant => {
        const rating = ratings[participant.id] || 5;
        const sharePercentage = totalRatingPoints > 0 ? rating / totalRatingPoints : 1 / participants.length;
        const amount = Math.floor(bounty * sharePercentage);

        return {
          id: participant.id,
          name: participant.name,
          avatar: participant.avatar,
          rating: rating,
          amount: amount
        };
      });

      setPayoutDistribution(distribution);
    }
  }, [ratings, participants, mission]);

  // Handle rating change
  const handleRatingChange = (participantId, rating) => {
    setRatings(prevRatings => ({
      ...prevRatings,
      [participantId]: rating
    }));
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    setIsSubmitting(true);

    // Prepare data for submission
    const completionData = {
      missionId: mission?.id,
      ratings,
      feedback,
      payoutDistribution,
      totalBounty,
      platformFee
    };

    try {
      // Submit the completion data
      await onSubmit(completionData);
    } catch (error) {
      console.error('Error completing mission:', error);
      setIsSubmitting(false);
    }
  };

  // Render star rating input
  const renderStarRating = (participantId, currentRating) => {
    const stars = [];

    for (let i = 1; i <= 5; i++) {
      stars.push(
        <button
          key={i}
          type="button"
          onClick={() => handleRatingChange(participantId, i)}
          className={`w-8 h-8 ${i <= currentRating ? 'text-yellow-400' : 'text-gray-300'} focus:outline-none`}
        >
          <svg fill="currentColor" viewBox="0 0 20 20">
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
          </svg>
        </button>
      );
    }

    return (
      <div className="flex items-center">
        {stars}
      </div>
    );
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={onClose}
        >
          <motion.div
            className="bg-white rounded-2xl shadow-xl w-full max-w-lg overflow-hidden"
            variants={{
              hidden: { opacity: 0, y: 50, scale: 0.95 },
              visible: { opacity: 1, y: 0, scale: 1, transition: { duration: 0.3, ease: "easeOut" } },
              exit: { opacity: 0, y: 50, scale: 0.95, transition: { duration: 0.2, ease: "easeIn" } }
            }}
            initial="hidden"
            animate="visible"
            exit="exit"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="bg-gradient-to-r from-indigo-500 to-indigo-600 px-6 py-4 text-white">
              <div className="flex justify-between items-center">
                <h3 className="text-xl font-bold">Complete Mission</h3>
                <button
                  onClick={onClose}
                  className="text-white/80 hover:text-white transition-colors"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>

            {/* Content */}
            <form onSubmit={handleSubmit}>
              <div className="p-6">
                <div className="mb-6">
                  <h4 className="font-medium text-gray-800 mb-2">Mission Details</h4>
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <p className="font-medium text-gray-800">{mission?.title}</p>
                    <div className="flex justify-between text-sm text-gray-600 mt-1">
                      <span>Participants: {participants?.length || 0}</span>
                    </div>
                  </div>
                </div>

                {/* Rate Participants */}
                <div className="mb-6">
                  <h4 className="font-medium text-gray-800 mb-4">Rate Participants</h4>
                  <div className="space-y-4">
                    {participants?.map(participant => (
                      <div key={participant.id} className="bg-gray-50 p-3 rounded-lg">
                        <div className="flex items-center mb-2">
                          <div className="w-8 h-8 rounded-full overflow-hidden mr-2 bg-gray-200">
                            <img
                              src={participant.avatar || '/images/default-avatar.jpg'}
                              alt={participant.name}
                              className="w-full h-full object-cover"
                            />
                          </div>
                          <p className="font-medium text-gray-800">{participant.name}</p>
                        </div>
                        {renderStarRating(participant.id, ratings[participant.id] || 5)}
                      </div>
                    ))}
                  </div>
                </div>

                {/* Feedback */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Feedback (Optional)
                  </label>
                  <textarea
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 min-h-[100px] resize-none"
                    placeholder="Share your thoughts about the mission and participants..."
                    value={feedback}
                    onChange={(e) => setFeedback(e.target.value)}
                  />
                </div>

                {/* Payment Distribution */}
                <div className="mb-6">
                  <h4 className="font-medium text-gray-800 mb-4">Payment Distribution</h4>

                  {/* Payment Summary */}
                  <MissionPaymentSummary
                    bounty={totalBounty}
                    platformFee={platformFee}
                    totalAmount={totalBounty}
                    paymentType="completion"
                  />

                  {/* Distribution Table */}
                  <div className="mt-4 bg-gray-50 rounded-lg overflow-hidden">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-100">
                        <tr>
                          <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Participant
                          </th>
                          <th scope="col" className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Rating
                          </th>
                          <th scope="col" className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Amount
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {payoutDistribution.map((payout) => (
                          <tr key={payout.id}>
                            <td className="px-4 py-3 whitespace-nowrap">
                              <div className="flex items-center">
                                <div className="flex-shrink-0 h-8 w-8 rounded-full overflow-hidden bg-gray-200">
                                  <img
                                    src={payout.avatar || '/images/default-avatar.jpg'}
                                    alt={payout.name}
                                    className="h-8 w-8 object-cover"
                                  />
                                </div>
                                <div className="ml-3">
                                  <div className="text-sm font-medium text-gray-900">{payout.name}</div>
                                </div>
                              </div>
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap text-center">
                              <div className="text-sm text-gray-900">{payout.rating} / 5</div>
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap text-right">
                              <div className="text-sm font-medium text-indigo-600">{payout.amount} credits</div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>

                <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm text-yellow-700">
                        Once you complete this mission, all participants will be notified and rewards will be distributed according to your ratings.
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Footer */}
              <div className="px-6 py-4 bg-gray-50 border-t border-gray-100 flex justify-end space-x-3">
                <button
                  type="button"
                  className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                  onClick={onClose}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className={`px-4 py-2 bg-indigo-600 text-white rounded-lg font-medium hover:bg-indigo-700 transition-colors flex items-center ${
                    isSubmitting ? 'opacity-75 cursor-not-allowed' : ''
                  }`}
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Completing...
                    </>
                  ) : (
                    'Complete Mission'
                  )}
                </button>
              </div>
            </form>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default MissionCompletionModal;
