import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import MissionCard from './MissionCard';
import AdvancedSearchBar from './AdvancedSearchBar';
import EnhancedFilterPanel from './EnhancedFilterPanel';
import EnhancedActiveFilters from './EnhancedActiveFilters';
import ResultsHeader from './ResultsHeader';
import NoResults from './NoResults';
import { useToast } from '../common/ToastProvider';
import { missionApi } from '../../services/missionApi';
import { useAuth } from '../../contexts/AuthContext';
import '../../styles/customScrollbar.css';

// Fallback mock data in case no missions are passed
const FALLBACK_MISSIONS = [
  {
    id: 1,
    title: 'MLBB - Tank, Mage Support Needed!',
    image: '/images/mobile_legend.jpg',
    bounty: 120,
    date: '2023-07-15T18:00:00',
    slots_total: 5,
    slots_filled: 2,
    level_requirement: { min: 1, max: 99 },
    style: 'Competitive',
    theme: 'Mobile Legend Bang Bang',
    status: 'Open',
    bookmarked: true,
    host: {
      name: '<PERSON> Cheah',
      level: 88,
      avatar: '/images/profile-1.jpg',
      online: true
    }
  },
  {
    id: 2,
    title: 'Valorant Team Practice Session',
    image: '/images/valorant.jpg',
    bounty: 150,
    date: '2023-07-16T20:30:00',
    slots_total: 4,
    slots_filled: 1,
    level_requirement: { min: 10, max: 50 },
    style: 'Casual',
    theme: 'Valorant',
    status: 'Open',
    bookmarked: false,
    host: {
      name: 'Stella Ong',
      level: 65,
      avatar: '/images/profile-2.jpeg',
      online: false
    }
  },
  {
    id: 3,
    title: 'League of Legends Ranked Duo',
    image: '/images/league_of_legends.jpg',
    bounty: 200,
    date: '2023-07-17T19:00:00',
    slots_total: 2,
    slots_filled: 0,
    level_requirement: { min: 30, max: 99 },
    style: 'Competitive',
    theme: 'League of Legends',
    status: 'Open',
    bookmarked: false,
    host: {
      name: 'Amy',
      level: 92,
      avatar: '/images/profile-3.jpg',
      online: true
    }
  },
  {
    id: 4,
    title: 'PUBG Mobile Squad',
    image: '/images/popular-games-pubg.jpg',
    bounty: 100,
    date: '2023-07-18T21:00:00',
    slots_total: 3,
    slots_filled: 1,
    level_requirement: { min: 5, max: 99 },
    style: 'Casual',
    theme: 'PUBG Mobile',
    status: 'In Progress',
    bookmarked: false,
    host: {
      name: 'John Doe',
      level: 45,
      avatar: '/images/profile-5.jpg',
      online: false
    }
  },
  {
    id: 5,
    title: 'Genshin Impact Co-op Adventure',
    image: '/images/genshin.jpg',
    bounty: 90,
    date: '2023-07-20T15:00:00',
    slots_total: 3,
    slots_filled: 3,
    level_requirement: { min: 1, max: 50 },
    style: 'Casual',
    theme: 'Genshin Impact',
    status: 'Completed',
    bookmarked: true,
    host: {
      name: 'Alex Tan',
      level: 75,
      avatar: '/images/profile-4.jpg',
      online: false
    }
  },
  {
    id: 6,
    title: 'Apex Legends Ranked Push',
    image: '/images/apex.jpg',
    bounty: 180,
    date: '2023-07-21T19:30:00',
    slots_total: 2,
    slots_filled: 0,
    level_requirement: { min: 20, max: 99 },
    style: 'Competitive',
    theme: 'Apex Legends',
    status: 'Open',
    bookmarked: false,
    host: {
      name: 'Jane Smith',
      level: 82,
      avatar: '/images/profile-6.jpg',
      online: true
    }
  }
];

const MissionListing = ({
  missions = [],
  isLoading = false,
  onMissionsUpdate,
  showViewToggle = false,
  viewToggleOptions = [],
  activeView = 'browse',
  onViewChange
}) => {
  const toast = useToast();
  const { isAuthenticated, user } = useAuth();

  // Search states
  const [searchQuery, setSearchQuery] = useState('');
  const [advancedSearchOptions, setAdvancedSearchOptions] = useState({
    searchIn: {
      title: true,
      description: true,
      host: true,
      tags: true
    },
    exactMatch: false,
    includeCompleted: false,
    autoSearch: true
  });
  const [isAdvancedMode, setIsAdvancedMode] = useState(false);
  const [isApplying, setIsApplying] = useState(false);

  // Filter states
  const [showFilters, setShowFilters] = useState(false);
  const [recentSearches, setRecentSearches] = useState(['Mobile Legends', 'Valorant', 'League of Legends']);
  const [popularSearches, setPopularSearches] = useState(['Mobile Legends', 'PUBG Mobile', 'Valorant', 'League of Legends', 'Genshin Impact']);
  const [sortOption, setSortOption] = useState('newest');

  // Enhanced filter states
  const [activeFilters, setActiveFilters] = useState({
    serviceCategories: [],
    priceRange: { min: 0, max: 500 },
    levelRequirement: { min: 1, max: 99 },
    missionStyles: [],
    platforms: [],
    languages: [],
    dateRange: { start: null, end: null }
  });

  // Extract unique service categories from missions
  const serviceCategories = Array.from(
    new Set(missions.map(mission => mission.theme).filter(Boolean))
  );

  // Extract unique mission styles from missions
  const missionStyles = Array.from(
    new Set(missions.map(mission => mission.style).filter(Boolean))
  );

  // Extract unique platforms from missions
  const platforms = Array.from(
    new Set(missions.map(mission => mission.platform).filter(Boolean))
  ) || ['PC', 'Mobile', 'Console']; // Fallback if no platforms in data

  // Extract unique languages from missions
  const languages = Array.from(
    new Set(missions.map(mission => mission.language).filter(Boolean))
  ) || ['English', 'Chinese', 'Japanese', 'Korean']; // Fallback if no languages in data

  // Handle search submission
  const handleSearch = (query, options) => {
    setSearchQuery(query);

    // Update advanced search options if provided
    if (options) {
      setAdvancedSearchOptions(options);
    }

    // Add to recent searches if not already there
    if (query && !recentSearches.includes(query)) {
      const updatedRecentSearches = [query, ...recentSearches.slice(0, 4)];
      setRecentSearches(updatedRecentSearches);
      // In a real app, we would save this to localStorage or user preferences
    }
  };

  // Clear search query
  const clearSearch = () => {
    setSearchQuery('');
  };

  // Handle filter changes
  const handleFilterChange = (filterType, value) => {
    setActiveFilters(prev => ({
      ...prev,
      [filterType]: value
    }));
  };

  // Toggle service category filter
  const toggleServiceCategory = (category) => {
    setActiveFilters(prev => {
      const categories = [...prev.serviceCategories];
      const index = categories.indexOf(category);

      if (index === -1) {
        categories.push(category);
      } else {
        categories.splice(index, 1);
      }

      return {
        ...prev,
        serviceCategories: categories
      };
    });
  };

  // Toggle mission style filter
  const toggleMissionStyle = (style) => {
    setActiveFilters(prev => {
      const styles = [...prev.missionStyles];
      const index = styles.indexOf(style);

      if (index === -1) {
        styles.push(style);
      } else {
        styles.splice(index, 1);
      }

      return {
        ...prev,
        missionStyles: styles
      };
    });
  };

  // Toggle platform filter
  const togglePlatform = (platform) => {
    setActiveFilters(prev => {
      const currentPlatforms = [...prev.platforms];
      const index = currentPlatforms.indexOf(platform);

      if (index === -1) {
        currentPlatforms.push(platform);
      } else {
        currentPlatforms.splice(index, 1);
      }

      return {
        ...prev,
        platforms: currentPlatforms
      };
    });
  };

  // Toggle language filter
  const toggleLanguage = (language) => {
    setActiveFilters(prev => {
      const currentLanguages = [...prev.languages];
      const index = currentLanguages.indexOf(language);

      if (index === -1) {
        currentLanguages.push(language);
      } else {
        currentLanguages.splice(index, 1);
      }

      return {
        ...prev,
        languages: currentLanguages
      };
    });
  };

  // Clear price range filter
  const clearPriceRange = () => {
    setActiveFilters(prev => ({
      ...prev,
      priceRange: { min: 0, max: 500 }
    }));
  };

  // Clear level requirement filter
  const clearLevelRequirement = () => {
    setActiveFilters(prev => ({
      ...prev,
      levelRequirement: { min: 1, max: 99 }
    }));
  };

  // Clear date range filter
  const clearDateRange = () => {
    setActiveFilters(prev => ({
      ...prev,
      dateRange: { start: null, end: null }
    }));
  };

  // Handle apply filters button click
  const handleApplyFilters = () => {
    // In a real app, this would trigger an API call with the filter parameters
    console.log('Applying filters:', activeFilters);
  };

  // Reset all filters
  const resetFilters = () => {
    setActiveFilters({
      serviceCategories: [],
      priceRange: { min: 0, max: 500 },
      levelRequirement: { min: 1, max: 99 },
      missionStyles: [],
      platforms: [],
      languages: [],
      dateRange: { start: null, end: null }
    });
    setSearchQuery('');
    setAdvancedSearchOptions({
      searchIn: {
        title: true,
        description: true,
        host: true,
        tags: true
      },
      exactMatch: false,
      includeCompleted: false
    });
    setIsAdvancedMode(false);
  };

  // Handle mission application
  const handleApplyToMission = useCallback(async (missionId) => {
    if (!isAuthenticated) {
      toast.error('Please log in to apply for missions');
      return Promise.reject(new Error('Authentication required'));
    }

    if (isApplying) {
      return Promise.reject(new Error('Already processing an application'));
    }

    setIsApplying(true);

    try {
      // Call the API to apply for the mission
      const response = await missionApi.applyToMission(missionId);

      // If onMissionsUpdate callback is provided, call it to refresh the missions list
      if (onMissionsUpdate && typeof onMissionsUpdate === 'function') {
        onMissionsUpdate();
      }

      return Promise.resolve(response);
    } catch (error) {
      console.error('Error applying to mission:', error);
      return Promise.reject(new Error(error.message || 'Failed to apply for mission'));
    } finally {
      setIsApplying(false);
    }
  }, [isAuthenticated, isApplying, toast, onMissionsUpdate]);

  // Apply filters to missions
  const applyFilters = (missions) => {
    return missions.filter(mission => {
      // Filter by service category if any selected
      if (activeFilters.serviceCategories.length > 0 &&
          !activeFilters.serviceCategories.includes(mission.theme)) {
        return false;
      }

      // Filter by price range
      if (mission.bounty < activeFilters.priceRange.min ||
          mission.bounty > activeFilters.priceRange.max) {
        return false;
      }

      // Filter by level requirement
      const missionMinLevel = mission.level_requirement?.min || 1;
      const missionMaxLevel = mission.level_requirement?.max || 99;

      if (missionMaxLevel < activeFilters.levelRequirement.min ||
          missionMinLevel > activeFilters.levelRequirement.max) {
        return false;
      }

      // Filter by mission style if any selected
      if (activeFilters.missionStyles.length > 0 &&
          !activeFilters.missionStyles.includes(mission.style)) {
        return false;
      }

      // Filter by platform if any selected
      if (activeFilters.platforms.length > 0 &&
          !activeFilters.platforms.includes(mission.platform)) {
        return false;
      }

      // Filter by language if any selected
      if (activeFilters.languages.length > 0 &&
          !activeFilters.languages.includes(mission.language)) {
        return false;
      }

      // Filter by date range
      if (activeFilters.dateRange.start && activeFilters.dateRange.end) {
        const missionDate = new Date(mission.date);
        const startDate = new Date(activeFilters.dateRange.start);
        const endDate = new Date(activeFilters.dateRange.end);

        // Set time to beginning and end of day for accurate comparison
        startDate.setHours(0, 0, 0, 0);
        endDate.setHours(23, 59, 59, 999);

        if (missionDate < startDate || missionDate > endDate) {
          return false;
        }
      }

      return true;
    });
  };

  // Get active filter count
  const getActiveFilterCount = () => {
    let count = 0;

    if (activeFilters.serviceCategories.length > 0) count += activeFilters.serviceCategories.length;
    if (activeFilters.missionStyles.length > 0) count += activeFilters.missionStyles.length;
    if (activeFilters.platforms.length > 0) count += activeFilters.platforms.length;
    if (activeFilters.languages.length > 0) count += activeFilters.languages.length;

    // Count price range if it's not the default
    if (activeFilters.priceRange.min > 0 || activeFilters.priceRange.max < 500) count++;

    // Count level requirement if it's not the default
    if (activeFilters.levelRequirement.min > 1 || activeFilters.levelRequirement.max < 99) count++;

    // Count date range if it's set
    if (activeFilters.dateRange.start && activeFilters.dateRange.end) count++;

    // Count advanced search options
    if (isAdvancedMode) {
      if (advancedSearchOptions.exactMatch) count++;
      if (advancedSearchOptions.includeCompleted) count++;

      // Count search in options that are disabled
      const searchInOptions = advancedSearchOptions.searchIn;
      if (!searchInOptions.title) count++;
      if (!searchInOptions.description) count++;
      if (!searchInOptions.host) count++;
      if (!searchInOptions.tags) count++;
    }

    return count;
  };

  // Filter missions based on search query and other filters
  const filteredBySearch = missions.filter(mission => {
    // Skip if no search query
    if (!searchQuery) return true;

    // Skip completed missions unless includeCompleted is true
    if (mission.status === 'completed' && !advancedSearchOptions.includeCompleted) {
      return false;
    }

    const query = advancedSearchOptions.exactMatch
      ? searchQuery
      : searchQuery.toLowerCase();

    const searchInOptions = advancedSearchOptions.searchIn;

    // Check if mission matches search query based on searchIn options
    let matchesQuery = false;

    if (searchInOptions.title && mission.title) {
      const title = advancedSearchOptions.exactMatch
        ? mission.title
        : mission.title.toLowerCase();

      if (advancedSearchOptions.exactMatch
          ? title === query
          : title.includes(query)) {
        matchesQuery = true;
      }
    }

    if (!matchesQuery && searchInOptions.description && mission.description) {
      const description = advancedSearchOptions.exactMatch
        ? mission.description
        : mission.description.toLowerCase();

      if (advancedSearchOptions.exactMatch
          ? description === query
          : description.includes(query)) {
        matchesQuery = true;
      }
    }

    if (!matchesQuery && searchInOptions.host && mission.host?.name) {
      const hostName = advancedSearchOptions.exactMatch
        ? mission.host.name
        : mission.host.name.toLowerCase();

      if (advancedSearchOptions.exactMatch
          ? hostName === query
          : hostName.includes(query)) {
        matchesQuery = true;
      }
    }

    if (!matchesQuery && searchInOptions.tags && mission.tags) {
      const tags = mission.tags || [];

      for (const tag of tags) {
        const tagText = advancedSearchOptions.exactMatch
          ? tag
          : tag.toLowerCase();

        if (advancedSearchOptions.exactMatch
            ? tagText === query
            : tagText.includes(query)) {
          matchesQuery = true;
          break;
        }
      }
    }

    return matchesQuery;
  });

  // Apply additional filters
  const filteredMissions = applyFilters(filteredBySearch);

  // Sort missions based on selected option
  const sortedMissions = [...filteredMissions].sort((a, b) => {
    switch (sortOption) {
      case 'newest':
        return new Date(b.date) - new Date(a.date);
      case 'oldest':
        return new Date(a.date) - new Date(b.date);
      case 'highest_bounty':
        return b.bounty - a.bounty;
      case 'lowest_bounty':
        return a.bounty - b.bounty;
      default:
        return 0;
    }
  });

  // Animation variants for staggered list
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <div className="mb-12">
      {/* Section Header */}
      <div className="flex flex-col md:flex-row md:items-center justify-between mb-6 gap-4">
        <div className="flex items-center space-x-3">
          <motion.div
            className="flex items-center justify-center w-12 h-12 rounded-xl bg-gradient-to-br from-indigo-500 to-blue-600 shadow-md text-white"
            whileHover={{ scale: 1.05, rotate: 5 }}
            whileTap={{ scale: 0.95 }}
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          </motion.div>
          <div className="text-left">
            <h2 className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-blue-600">Missions For You</h2>
            <p className="text-sm text-gray-500">Find your next gaming adventure</p>
          </div>
        </div>

        {/* Search and Filter */}
        <div className="flex space-x-2 w-full md:w-auto">
          <AdvancedSearchBar
            searchQuery={searchQuery}
            setSearchQuery={setSearchQuery}
            onSearch={handleSearch}
            onClearSearch={clearSearch}
            recentSearches={recentSearches}
            popularSearches={popularSearches}
            advancedOptions={advancedSearchOptions}
            onAdvancedOptionsChange={setAdvancedSearchOptions}
            isAdvancedMode={isAdvancedMode}
            setIsAdvancedMode={setIsAdvancedMode}
          />

          <motion.button
            className={`p-4 border-2 ${showFilters ? 'bg-indigo-100 border-indigo-400 text-indigo-700' : 'bg-white border-gray-200 text-gray-600'} rounded-2xl hover:bg-indigo-50 hover:border-indigo-300 hover:text-indigo-700 transition-all duration-300 relative flex items-center justify-center shadow-lg hover:shadow-xl`}
            onClick={() => setShowFilters(!showFilters)}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            aria-label="Filter missions"
          >
            <svg className={`w-5 h-5 ${showFilters ? 'text-indigo-600' : 'text-gray-600'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
            </svg>

            {/* Filter indicator */}
            {getActiveFilterCount() > 0 && (
              <motion.span
                className="absolute -top-1 -right-1 flex h-5 w-5 items-center justify-center"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
              >
                <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-indigo-400 opacity-75"></span>
                <span className="relative flex rounded-full h-5 w-5 bg-indigo-500 text-[10px] text-white items-center justify-center font-bold">
                  {getActiveFilterCount()}
                </span>
              </motion.span>
            )}
          </motion.button>
        </div>
      </div>

      {/* View Toggle Options */}
      {showViewToggle && viewToggleOptions.length > 0 && (
        <div className="mb-6">
          <div className="flex bg-gradient-to-r from-gray-100 to-gray-50 rounded-2xl p-1.5 shadow-inner">
            {viewToggleOptions.map((option) => (
              <motion.button
                key={option.id}
                onClick={() => onViewChange && onViewChange(option.id)}
                className={`flex-1 flex items-center justify-center px-6 py-3 rounded-xl text-sm font-medium transition-all duration-300 ${
                  activeView === option.id
                    ? 'bg-white text-indigo-600 shadow-lg transform scale-105'
                    : 'text-gray-600 hover:text-gray-800 hover:bg-white/50'
                }`}
                whileHover={{ scale: activeView === option.id ? 1.05 : 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <svg className="w-4 h-4 mr-2" fill={option.id === 'featured' ? 'currentColor' : 'none'} stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d={option.icon} />
                </svg>
                {option.label}
                {option.count !== undefined && (
                  <span className={`ml-2 px-2.5 py-1 rounded-full text-xs font-semibold ${
                    activeView === option.id
                      ? 'bg-indigo-100 text-indigo-700'
                      : 'bg-gray-200 text-gray-600'
                  }`}>
                    {option.count}
                  </span>
                )}
              </motion.button>
            ))}
          </div>

          {/* Featured missions notice */}
          {activeView === 'featured' && (
            <div className="mt-4 p-4 bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-lg">
              <div className="flex items-center">
                <svg className="w-5 h-5 text-yellow-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
                <span className="text-yellow-800 font-medium">Featured Missions</span>
              </div>
              <p className="text-yellow-700 text-sm mt-1">
                High-reward missions and popular gaming sessions
              </p>
            </div>
          )}
        </div>
      )}

      {/* Enhanced Filter Panel */}
      <EnhancedFilterPanel
        isVisible={showFilters}
        activeFilters={activeFilters}
        onFilterChange={handleFilterChange}
        onToggleCategory={toggleServiceCategory}
        onToggleStyle={toggleMissionStyle}
        onTogglePlatform={togglePlatform}
        onToggleLanguage={toggleLanguage}
        onResetFilters={resetFilters}
        onApplyFilters={handleApplyFilters}
        serviceCategories={serviceCategories}
        missionStyles={missionStyles}
        platforms={platforms}
        languages={languages}
        getActiveFilterCount={getActiveFilterCount}
      />

      {/* Enhanced Active Filters Display */}
      <EnhancedActiveFilters
        searchQuery={searchQuery}
        advancedSearchOptions={advancedSearchOptions}
        activeFilters={activeFilters}
        onClearSearch={clearSearch}
        onToggleCategory={toggleServiceCategory}
        onToggleStyle={toggleMissionStyle}
        onTogglePlatform={togglePlatform}
        onToggleLanguage={toggleLanguage}
        onClearPriceRange={clearPriceRange}
        onClearLevelRequirement={clearLevelRequirement}
        onClearDateRange={clearDateRange}
        onResetFilters={resetFilters}
        getActiveFilterCount={getActiveFilterCount}
      />

      {/* Results Count and Sort Options */}
      {!isLoading && sortedMissions.length > 0 && (
        <ResultsHeader
          resultsCount={sortedMissions.length}
          sortOption={sortOption}
          onSortChange={setSortOption}
        />
      )}

      {/* Mission Grid */}
      {isLoading ? (
        <div className="flex justify-center items-center py-20">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
        </div>
      ) : sortedMissions.length > 0 ? (
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {sortedMissions.map(mission => (
            <motion.div key={mission.id} variants={itemVariants}>
              <MissionCard
                mission={mission}
                onApply={handleApplyToMission}
              />
            </motion.div>
          ))}
        </motion.div>
      ) : (
        <NoResults
          hasActiveFilters={getActiveFilterCount() > 0 || (filteredBySearch.length > 0 && sortedMissions.length === 0)}
          hasSearchResults={filteredBySearch.length > 0}
          searchQuery={searchQuery}
          onResetFilters={resetFilters}
          onClearSearch={clearSearch}
        />
      )}
    </div>
  );
};

export default MissionListing;
