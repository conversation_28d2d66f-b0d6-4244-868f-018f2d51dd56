import React from 'react';
import { motion } from 'framer-motion';

const FilterChip = ({
  label,
  onRemove,
  color = 'indigo',
  icon = null,
  removable = true
}) => {
  // Define color variants
  const colorVariants = {
    indigo: {
      bg: 'bg-indigo-50',
      text: 'text-indigo-800',
      border: 'border-indigo-100',
      hover: 'hover:bg-indigo-100',
      iconBg: 'bg-indigo-100',
      iconText: 'text-indigo-600'
    },
    blue: {
      bg: 'bg-blue-50',
      text: 'text-blue-800',
      border: 'border-blue-100',
      hover: 'hover:bg-blue-100',
      iconBg: 'bg-blue-100',
      iconText: 'text-blue-600'
    },
    green: {
      bg: 'bg-green-50',
      text: 'text-green-800',
      border: 'border-green-100',
      hover: 'hover:bg-green-100',
      iconBg: 'bg-green-100',
      iconText: 'text-green-600'
    },
    purple: {
      bg: 'bg-purple-50',
      text: 'text-purple-800',
      border: 'border-purple-100',
      hover: 'hover:bg-purple-100',
      iconBg: 'bg-purple-100',
      iconText: 'text-purple-600'
    },
    gray: {
      bg: 'bg-gray-50',
      text: 'text-gray-800',
      border: 'border-gray-100',
      hover: 'hover:bg-gray-100',
      iconBg: 'bg-gray-100',
      iconText: 'text-gray-600'
    },
    teal: {
      bg: 'bg-teal-50',
      text: 'text-teal-800',
      border: 'border-teal-100',
      hover: 'hover:bg-teal-100',
      iconBg: 'bg-teal-100',
      iconText: 'text-teal-600'
    },
    amber: {
      bg: 'bg-amber-50',
      text: 'text-amber-800',
      border: 'border-amber-100',
      hover: 'hover:bg-amber-100',
      iconBg: 'bg-amber-100',
      iconText: 'text-amber-600'
    },
    rose: {
      bg: 'bg-rose-50',
      text: 'text-rose-800',
      border: 'border-rose-100',
      hover: 'hover:bg-rose-100',
      iconBg: 'bg-rose-100',
      iconText: 'text-rose-600'
    },
    cyan: {
      bg: 'bg-cyan-50',
      text: 'text-cyan-800',
      border: 'border-cyan-100',
      hover: 'hover:bg-cyan-100',
      iconBg: 'bg-cyan-100',
      iconText: 'text-cyan-600'
    },
    orange: {
      bg: 'bg-orange-50',
      text: 'text-orange-800',
      border: 'border-orange-100',
      hover: 'hover:bg-orange-100',
      iconBg: 'bg-orange-100',
      iconText: 'text-orange-600'
    }
  };

  // Get color classes
  const colors = colorVariants[color] || colorVariants.indigo;

  return (
    <motion.div
      className={`flex items-center ${colors.bg} ${colors.text} px-3 py-1.5 rounded-full text-sm border ${colors.border} shadow-sm group`}
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.8 }}
      transition={{ duration: 0.2 }}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
    >
      {icon && (
        <span className={`w-5 h-5 rounded-full ${colors.iconBg} ${colors.iconText} flex items-center justify-center mr-1.5`}>
          {icon}
        </span>
      )}
      <span>{label}</span>
      {removable && onRemove && (
        <button
          className={`ml-2 ${colors.iconText} ${colors.hover} rounded-full w-5 h-5 flex items-center justify-center transition-colors`}
          onClick={onRemove}
          aria-label={`Remove ${label} filter`}
        >
          <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      )}
    </motion.div>
  );
};

export default FilterChip;
