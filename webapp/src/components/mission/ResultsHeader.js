import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const ResultsHeader = ({
  resultsCount,
  sortOption,
  onSortChange
}) => {
  const [showSortOptions, setShowSortOptions] = useState(false);

  // Sort options
  const sortOptions = [
    { value: 'newest', label: 'Newest First', icon: 'clock' },
    { value: 'oldest', label: 'Oldest First', icon: 'clock-reverse' },
    { value: 'highest_bounty', label: 'Highest Bounty', icon: 'trending-up' },
    { value: 'lowest_bounty', label: 'Lowest Bounty', icon: 'trending-down' }
  ];

  // Get icon for sort option
  const getSortIcon = (icon) => {
    switch (icon) {
      case 'clock':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      case 'clock-reverse':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l-3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      case 'trending-up':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
          </svg>
        );
      case 'trending-down':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 17h8m0 0v-8m0 8l-8-8-4 4-6-6" />
          </svg>
        );
      default:
        return null;
    }
  };

  // Get current sort option
  const currentSort = sortOptions.find(option => option.value === sortOption) || sortOptions[0];

  return (
    <motion.div 
      className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 bg-white rounded-xl shadow-sm p-4 border border-indigo-50"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="text-gray-600 mb-3 sm:mb-0 flex items-center">
        <svg className="w-5 h-5 text-indigo-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
        </svg>
        <span className="font-medium text-gray-800">{resultsCount}</span> {resultsCount === 1 ? 'mission' : 'missions'} found
      </div>

      <div className="flex items-center">
        <span className="text-gray-600 mr-3 flex items-center">
          <svg className="w-4 h-4 mr-1 text-indigo-500" fill="currentColor" viewBox="0 0 20 20">
            <path d="M5 4a1 1 0 00-2 0v7.268a2 2 0 000 3.464V16a1 1 0 102 0v-1.268a2 2 0 000-3.464V4zM11 4a1 1 0 10-2 0v1.268a2 2 0 000 3.464V16a1 1 0 102 0V8.732a2 2 0 000-3.464V4zM16 3a1 1 0 011 1v7.268a2 2 0 010 3.464V16a1 1 0 11-2 0v-1.268a2 2 0 010-3.464V4a1 1 0 011-1z" />
          </svg>
          Sort by:
        </span>
        <div className="relative">
          <motion.button
            className="appearance-none bg-gray-50 border border-gray-200 text-gray-700 py-2 pl-3 pr-10 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 flex items-center min-w-[160px]"
            onClick={() => setShowSortOptions(!showSortOptions)}
            whileHover={{ backgroundColor: '#F5F7FF' }}
          >
            <span className="flex items-center">
              <span className="w-6 h-6 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-600 mr-2">
                {getSortIcon(currentSort.icon)}
              </span>
              <span>{currentSort.label}</span>
            </span>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          </motion.button>

          <AnimatePresence>
            {showSortOptions && (
              <motion.div
                className="absolute right-0 mt-2 w-56 rounded-lg shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-10"
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.2 }}
              >
                <div className="py-1">
                  {sortOptions.map((option) => (
                    <motion.button
                      key={option.value}
                      className={`flex items-center w-full text-left px-4 py-2 text-sm ${
                        option.value === sortOption
                          ? 'bg-indigo-50 text-indigo-700'
                          : 'text-gray-700 hover:bg-gray-50'
                      }`}
                      onClick={() => {
                        onSortChange(option.value);
                        setShowSortOptions(false);
                      }}
                      whileHover={{ x: 5 }}
                    >
                      <span className="w-6 h-6 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-600 mr-2">
                        {getSortIcon(option.icon)}
                      </span>
                      {option.label}
                    </motion.button>
                  ))}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </motion.div>
  );
};

export default ResultsHeader;
