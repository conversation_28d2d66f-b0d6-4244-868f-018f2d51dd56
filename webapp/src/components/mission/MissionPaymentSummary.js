import React from 'react';
import { motion } from 'framer-motion';

/**
 * MissionPaymentSummary Component
 * 
 * Displays a summary of the payment details for a mission
 * Used in mission creation, completion, and other payment-related flows
 * 
 * @param {Object} props - Component props
 * @param {number} props.bounty - The mission bounty amount
 * @param {number} props.platformFee - The platform fee amount
 * @param {number} props.totalAmount - The total amount to be paid
 * @param {string} props.paymentType - The type of payment (e.g., 'creation', 'completion')
 */
const MissionPaymentSummary = ({ 
  bounty = 0, 
  platformFee = 0, 
  totalAmount = 0,
  paymentType = 'creation'
}) => {
  // Format currency
  const formatCurrency = (amount) => {
    return amount.toLocaleString();
  };

  // Get title and description based on payment type
  const getPaymentTypeInfo = () => {
    switch (paymentType) {
      case 'creation':
        return {
          title: 'Mission Creation Payment',
          description: 'This amount will be deducted from your X Wallet balance when you create this mission.'
        };
      case 'completion':
        return {
          title: 'Mission Completion Payout',
          description: 'This amount will be distributed to participants upon successful completion of the mission.'
        };
      case 'refund':
        return {
          title: 'Mission Refund',
          description: 'This amount will be refunded to your X Wallet balance.'
        };
      default:
        return {
          title: 'Payment Summary',
          description: 'Summary of payment details for this mission.'
        };
    }
  };

  const { title, description } = getPaymentTypeInfo();

  return (
    <motion.div
      className="bg-white rounded-xl shadow-md overflow-hidden"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-2">{title}</h3>
        <p className="text-gray-600 text-sm mb-6">{description}</p>
        
        <div className="space-y-4">
          {/* Bounty */}
          <div className="flex justify-between items-center">
            <div className="text-gray-700">
              Mission Bounty
              <div className="text-xs text-gray-500">
                {paymentType === 'completion' 
                  ? 'Amount to be distributed to participants' 
                  : 'Amount to be paid to participants'}
              </div>
            </div>
            <div className="font-medium text-gray-900">
              {formatCurrency(bounty)} credits
            </div>
          </div>
          
          {/* Platform Fee */}
          <div className="flex justify-between items-center">
            <div className="text-gray-700">
              Platform Fee
              <div className="text-xs text-gray-500">
                {platformFee > 0 
                  ? `${Math.round((platformFee / bounty) * 100)}% service fee` 
                  : 'No platform fee for this transaction'}
              </div>
            </div>
            <div className="font-medium text-gray-900">
              {formatCurrency(platformFee)} credits
            </div>
          </div>
          
          {/* Divider */}
          <div className="border-t border-gray-200 my-2"></div>
          
          {/* Total */}
          <div className="flex justify-between items-center">
            <div className="text-gray-800 font-medium">
              Total {paymentType === 'refund' ? 'Refund' : 'Amount'}
            </div>
            <div className="font-bold text-lg text-indigo-600">
              {formatCurrency(totalAmount)} credits
            </div>
          </div>
        </div>
        
        {/* Additional Info */}
        {paymentType === 'creation' && (
          <div className="mt-6 bg-blue-50 p-4 rounded-lg">
            <div className="flex items-start">
              <svg className="w-5 h-5 text-blue-500 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p className="text-blue-800 text-sm">
                The mission bounty will be held in escrow until the mission is completed or cancelled. 
                If cancelled, the bounty will be refunded to your wallet.
              </p>
            </div>
          </div>
        )}
        
        {paymentType === 'completion' && (
          <div className="mt-6 bg-green-50 p-4 rounded-lg">
            <div className="flex items-start">
              <svg className="w-5 h-5 text-green-500 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p className="text-green-800 text-sm">
                Participants will receive their share of the bounty based on your ratings. 
                Higher-rated participants will receive a larger share.
              </p>
            </div>
          </div>
        )}
      </div>
    </motion.div>
  );
};

export default MissionPaymentSummary;
