import React from 'react';
import MainNavigation from '../../components/navigation/MainNavigation';
import MobileNavigation from '../../components/navigation/MobileNavigation';
import MissionNavigation from './MissionNavigation';
import MissionSubNavigation from './MissionSubNavigation';
import MissionMobileNavigation from './MissionMobileNavigation';
import MissionFooter from './MissionFooter';

const MissionLayout = ({
  children,
  title,
  showBackButton = true,
  backPath = '/missions',
  actions = null,
  showFooter = true,
  useMainNav = true,
  useMissionMobileNav = false
}) => {
  return (
    <div className="min-h-screen bg-gradient-to-b from-indigo-50 to-white">
      {useMainNav ? (
        // Use the main navigation from Home.js
        <MainNavigation activeItem="/missions" />
      ) : (
        // Use the original mission navigation
        <MissionNavigation
          title={title}
          showBackButton={showBackButton}
          backPath={backPath}
          actions={actions}
        />
      )}

      {/* Mission-specific sub-navigation */}
      {useMainNav && (
        <MissionSubNavigation
          title={title}
          showBackButton={showBackButton}
          backPath={backPath}
          actions={actions}
        />
      )}

      <main className="container mx-auto max-w-7xl px-5 py-8 mb-20 md:mb-0">
        {children}
      </main>

      {showFooter && (
        useMissionMobileNav ? (
          <MissionMobileNavigation />
        ) : useMainNav ? (
          <MobileNavigation activeItem="/missions" />
        ) : (
          <MissionFooter />
        )
      )}
    </div>
  );
};

export default MissionLayout;
