import React from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';

const SimilarMissionCard = ({ mission }) => {
  const navigate = useNavigate();
  
  // Format date for display
  const formatDate = (dateString) => {
    const options = { 
      month: 'short', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    };
    return new Date(dateString).toLocaleDateString('en-US', options);
  };

  return (
    <motion.div 
      className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 overflow-hidden border border-gray-100 h-full flex flex-col"
      whileHover={{ y: -5 }}
      onClick={() => navigate(`/missions/${mission.id}`)}
    >
      {/* Mission Image with Overlay */}
      <div className="relative h-32 overflow-hidden">
        <img 
          src={mission.image || '/images/mission-default.jpg'} 
          alt={mission.title}
          className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
        />
        
        {/* Tags overlay */}
        <div className="absolute top-0 left-0 w-full p-2 flex flex-wrap gap-1">
          {mission.level_requirement && (
            <span className="bg-indigo-100 text-indigo-800 text-xs font-medium px-2 py-0.5 rounded-full">
              LV{mission.level_requirement.min || 1}-LV{mission.level_requirement.max || 99}
            </span>
          )}
          
          {mission.style && (
            <span className="bg-purple-100 text-purple-800 text-xs font-medium px-2 py-0.5 rounded-full">
              {mission.style}
            </span>
          )}
        </div>
      </div>
      
      {/* Mission Content */}
      <div className="p-3 flex-grow flex flex-col">
        <h3 className="font-bold text-gray-800 text-sm mb-1 line-clamp-2">{mission.title}</h3>
        
        {/* Bounty and Date */}
        <div className="flex justify-between items-center mb-2 mt-auto">
          <div className="flex items-center text-indigo-600 font-medium text-sm">
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            {mission.bounty}
          </div>
          <div className="text-xs text-gray-500">
            {formatDate(mission.date)}
          </div>
        </div>
        
        {/* Host information */}
        <div className="flex items-center mt-2 pt-2 border-t border-gray-100">
          <div className="w-6 h-6 rounded-full overflow-hidden mr-2 bg-gray-200">
            <img 
              src={mission.host?.avatar || '/images/default-avatar.jpg'} 
              alt={mission.host?.name || 'Host'} 
              className="w-full h-full object-cover"
            />
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-xs font-medium text-gray-800 truncate">
              {mission.host?.name || 'Anonymous Host'}
            </p>
            <p className="text-xs text-gray-500">
              LV{mission.host?.level || '??'}
            </p>
          </div>
          <div className="bg-indigo-50 px-2 py-1 rounded text-xs text-indigo-800 font-medium">
            {mission.slots_total - mission.slots_filled}/{mission.slots_total}
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default SimilarMissionCard;
