import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import RangeSlider from './RangeSlider';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { referenceDataApi } from '../../services/referenceDataApi';

const EnhancedFilterPanel = ({
  isVisible,
  activeFilters,
  onFilterChange,
  onToggleCategory,
  onToggleStyle,
  onTogglePlatform,
  onToggleLanguage,
  onResetFilters,
  onApplyFilters,
  serviceCategories: propServiceCategories,
  missionStyles: propMissionStyles,
  platforms: propPlatforms,
  languages: propLanguages,
  getActiveFilterCount
}) => {
  const [startDate, setStartDate] = useState(activeFilters.dateRange?.start || null);
  const [endDate, setEndDate] = useState(activeFilters.dateRange?.end || null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // State for dynamic filter options
  const [serviceCategories, setServiceCategories] = useState(propServiceCategories || []);
  const [missionStyles, setMissionStyles] = useState(propMissionStyles || []);
  const [platforms, setPlatforms] = useState(propPlatforms || []);
  const [languages, setLanguages] = useState(propLanguages || []);
  const [levels, setLevels] = useState([]);

  // Fetch reference data when component mounts
  useEffect(() => {
    const fetchReferenceData = async () => {
      if (
        (propServiceCategories && propServiceCategories.length > 0) &&
        (propMissionStyles && propMissionStyles.length > 0) &&
        (propPlatforms && propPlatforms.length > 0) &&
        (propLanguages && propLanguages.length > 0)
      ) {
        // Use props data if available
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        // Fetch service types, styles, platforms, languages, and levels
        const [typesResponse, stylesResponse, platformsResponse, languagesResponse, levelsResponse] = await Promise.all([
          referenceDataApi.getServiceTypes(),
          referenceDataApi.getServiceStyles(),
          referenceDataApi.getPlatforms(),
          referenceDataApi.getLanguages(),
          referenceDataApi.getLevels()
        ]);

        // Set reference data if not provided in props
        if (!propServiceCategories || propServiceCategories.length === 0) {
          const categories = typesResponse.data?.map(type => type.name) || [];
          setServiceCategories(categories);
        }

        if (!propMissionStyles || propMissionStyles.length === 0) {
          const styles = stylesResponse.data?.map(style => style.name) || [];
          setMissionStyles(styles);
        }

        if (!propPlatforms || propPlatforms.length === 0) {
          const platformList = platformsResponse.data?.map(platform => platform.name) || ['PC', 'Mobile', 'Console'];
          setPlatforms(platformList);
        }

        if (!propLanguages || propLanguages.length === 0) {
          const languageList = languagesResponse.data?.map(language => language.name) || ['English', 'Chinese', 'Japanese', 'Korean'];
          setLanguages(languageList);
        }

        // Set levels data
        setLevels(levelsResponse.data || []);
      } catch (error) {
        console.error('Error fetching reference data:', error);
        setError('Failed to load filter options. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchReferenceData();
  }, [propServiceCategories, propMissionStyles, propPlatforms, propLanguages]);

  // Handle date change
  const handleDateChange = (dates) => {
    const [start, end] = dates;
    setStartDate(start);
    setEndDate(end);

    if (start && end) {
      onFilterChange('dateRange', { start, end });
    }
  };

  // Animation variants
  const panelVariants = {
    hidden: {
      opacity: 0,
      height: 0,
      transition: {
        duration: 0.3,
        ease: 'easeInOut'
      }
    },
    visible: {
      opacity: 1,
      height: 'auto',
      transition: {
        duration: 0.3,
        ease: 'easeInOut'
      }
    }
  };

  // Category icons
  const getCategoryIcon = (category) => {
    const icons = {
      'Mobile Legend Bang Bang': (
        <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
          <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" />
        </svg>
      ),
      'PUBG Mobile': (
        <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.707l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z" clipRule="evenodd" />
        </svg>
      ),
      'Valorant': (
        <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
        </svg>
      ),
      'League of Legends': (
        <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
          <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
        </svg>
      ),
      'Genshin Impact': (
        <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
        </svg>
      )
    };

    return icons[category] || (
      <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clipRule="evenodd" />
      </svg>
    );
  };

  // Style icons
  const getStyleIcon = (style) => {
    const icons = {
      'Casual': (
        <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
          <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" />
        </svg>
      ),
      'Competitive': (
        <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M3 6a3 3 0 013-3h10a1 1 0 01.8 1.6L14.25 8l2.55 3.4A1 1 0 0116 13H6a1 1 0 00-1 1v3a1 1 0 11-2 0V6z" clipRule="evenodd" />
        </svg>
      ),
      'Professional': (
        <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clipRule="evenodd" />
          <path d="M2 13.692V16a2 2 0 002 2h12a2 2 0 002-2v-2.308A24.974 24.974 0 0110 15c-2.796 0-5.487-.46-8-1.308z" />
        </svg>
      )
    };

    return icons[style] || (
      <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clipRule="evenodd" />
      </svg>
    );
  };

  // Platform icons
  const getPlatformIcon = (platform) => {
    const icons = {
      'PC': (
        <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z" clipRule="evenodd" />
        </svg>
      ),
      'Mobile': (
        <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M7 2a2 2 0 00-2 2v12a2 2 0 002 2h6a2 2 0 002-2V4a2 2 0 00-2-2H7zm3 14a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
        </svg>
      ),
      'Console': (
        <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
        </svg>
      )
    };

    return icons[platform] || (
      <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clipRule="evenodd" />
      </svg>
    );
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          variants={panelVariants}
          initial="hidden"
          animate="visible"
          exit="hidden"
          className="bg-white rounded-xl shadow-lg border border-indigo-100/50 p-6 mb-6 overflow-hidden"
        >
          <div className="flex justify-between items-center mb-6">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-600">
                {isLoading ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-indigo-600"></div>
                ) : (
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                  </svg>
                )}
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-800">Advanced Filters</h3>
                {error && <p className="text-xs text-red-500 mt-1">{error}</p>}
              </div>
            </div>
            <div className="flex items-center">
              <span className="text-sm text-gray-500 mr-3">
                {getActiveFilterCount()} filter{getActiveFilterCount() !== 1 ? 's' : ''} applied
              </span>
              <motion.button
                className="text-sm text-indigo-600 bg-transparent hover:text-white hover:bg-indigo-600 font-medium flex items-center px-2 py-1 rounded-md"
                onClick={onResetFilters}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                disabled={isLoading}
              >
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                Reset All
              </motion.button>
            </div>
          </div>

          {/* Global error message */}
          {error && (
            <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-red-700">
                    {error}
                    <button
                      className="ml-2 text-red-700 underline font-medium"
                      onClick={() => window.location.reload()}
                    >
                      Retry
                    </button>
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Loading state */}
          {isLoading && (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Service Category */}
            <div>
              <h4 className="font-medium text-gray-700 mb-4 flex items-center">
                <svg className="w-4 h-4 mr-2 text-indigo-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
                Game Category
              </h4>
              <div className="space-y-3 max-h-48 overflow-y-auto pr-2 custom-scrollbar">
                {serviceCategories.map((category, index) => (
                  <motion.div
                    key={`category-${index}`}
                    whileHover={{ x: 5 }}
                    className="flex items-center"
                  >
                    <label className="flex items-center space-x-3 w-full cursor-pointer">
                      <div className="relative">
                        <input
                          type="checkbox"
                          className="peer sr-only"
                          checked={activeFilters.serviceCategories.includes(category)}
                          onChange={() => onToggleCategory(category)}
                        />
                        <div className="w-5 h-5 border-2 border-gray-300 rounded peer-checked:bg-indigo-600 peer-checked:border-indigo-600 transition-colors"></div>
                        <svg
                          className="absolute w-3 h-3 text-white top-1 left-1 opacity-0 peer-checked:opacity-100 transition-opacity"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M5 13l4 4L19 7" />
                        </svg>
                      </div>
                      <div className="flex items-center justify-between w-full">
                        <span className="text-gray-700 font-medium">{category}</span>
                        <div className="w-6 h-6 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-600">
                          {getCategoryIcon(category)}
                        </div>
                      </div>
                    </label>
                  </motion.div>
                ))}
              </div>
            </div>

            {/* Price Range */}
            <div>
              <RangeSlider
                label={
                  <div className="flex items-center">
                    <svg className="w-4 h-4 mr-2 text-indigo-500" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clipRule="evenodd" />
                    </svg>
                    Price Range (Credits)
                  </div>
                }
                min={0}
                max={500}
                step={10}
                value={activeFilters.priceRange}
                onChange={(value) => onFilterChange('priceRange', value)}
                prefix=""
                suffix=""
                color="indigo"
              />
            </div>

            {/* Level Requirement */}
            <div>
              <h4 className="font-medium text-gray-700 mb-4 flex items-center">
                <svg className="w-4 h-4 mr-2 text-indigo-500" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" />
                </svg>
                Level Requirement
              </h4>

              {isLoading ? (
                <div className="flex justify-center py-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-indigo-500"></div>
                </div>
              ) : error ? (
                <div className="text-red-500 text-sm py-2">{error}</div>
              ) : (
                <>
                  {/* Level selection with visual indicators */}
                  <div className="mb-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label htmlFor="levelMin" className="block text-xs text-gray-500 mb-1">
                          Minimum Level
                        </label>
                        <select
                          id="levelMin"
                          value={activeFilters.levelRequirement.min}
                          onChange={(e) => {
                            const minLevel = parseInt(e.target.value);
                            const maxLevel = Math.max(minLevel, activeFilters.levelRequirement.max);
                            onFilterChange('levelRequirement', { min: minLevel, max: maxLevel });
                          }}
                          className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        >
                          {levels.length > 0 ? (
                            levels.map(level => (
                              <option key={`min-${level.id}`} value={level.level}>
                                {level.name} (Level {level.level})
                              </option>
                            ))
                          ) : (
                            <>
                              <option value="1">Beginner (Level 1)</option>
                              <option value="10">Intermediate (Level 10)</option>
                              <option value="20">Advanced (Level 20)</option>
                              <option value="30">Expert (Level 30)</option>
                            </>
                          )}
                        </select>
                      </div>
                      <div>
                        <label htmlFor="levelMax" className="block text-xs text-gray-500 mb-1">
                          Maximum Level
                        </label>
                        <select
                          id="levelMax"
                          value={activeFilters.levelRequirement.max}
                          onChange={(e) => {
                            const maxLevel = parseInt(e.target.value);
                            const minLevel = Math.min(maxLevel, activeFilters.levelRequirement.min);
                            onFilterChange('levelRequirement', { min: minLevel, max: maxLevel });
                          }}
                          className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        >
                          {levels.length > 0 ? (
                            levels.filter(level => level.level >= activeFilters.levelRequirement.min).map(level => (
                              <option key={`max-${level.id}`} value={level.level}>
                                {level.name} (Level {level.level})
                              </option>
                            ))
                          ) : (
                            <>
                              <option value="10">Intermediate (Level 10)</option>
                              <option value="20">Advanced (Level 20)</option>
                              <option value="30">Expert (Level 30)</option>
                              <option value="99">Master (Level 99)</option>
                            </>
                          )}
                        </select>
                      </div>
                    </div>
                  </div>

                  {/* Visual level range slider */}
                  <RangeSlider
                    min={1}
                    max={99}
                    step={1}
                    value={activeFilters.levelRequirement}
                    onChange={(value) => onFilterChange('levelRequirement', value)}
                    prefix="LV"
                    suffix=""
                    color="blue"
                  />
                </>
              )}
            </div>

            {/* Date Range */}
            <div>
              <h4 className="font-medium text-gray-700 mb-4 flex items-center">
                <svg className="w-4 h-4 mr-2 text-indigo-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                </svg>
                Date Range
              </h4>
              <div className="bg-gray-50 rounded-lg p-3">
                <DatePicker
                  selected={startDate}
                  onChange={handleDateChange}
                  startDate={startDate}
                  endDate={endDate}
                  selectsRange
                  inline
                  className="w-full"
                />
              </div>
            </div>

            {/* Mission Style */}
            <div>
              <h4 className="font-medium text-gray-700 mb-4 flex items-center">
                <svg className="w-4 h-4 mr-2 text-indigo-500" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" />
                </svg>
                Mission Style
              </h4>
              <div className="flex flex-wrap gap-2">
                {missionStyles.map((style, index) => (
                  <motion.button
                    key={`style-${index}`}
                    className={`px-4 py-2 rounded-full text-sm font-medium transition-colors flex items-center space-x-2 ${
                      activeFilters.missionStyles.includes(style)
                        ? 'bg-indigo-100 text-indigo-800 border-2 border-indigo-300 shadow-sm'
                        : 'bg-gray-100 text-gray-700 border-2 border-transparent hover:bg-gray-200'
                    }`}
                    onClick={() => onToggleStyle(style)}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <span className="w-5 h-5 rounded-full bg-white flex items-center justify-center">
                      {getStyleIcon(style)}
                    </span>
                    <span>{style}</span>
                  </motion.button>
                ))}
              </div>
            </div>

            {/* Platform */}
            <div>
              <h4 className="font-medium text-gray-700 mb-4 flex items-center">
                <svg className="w-4 h-4 mr-2 text-indigo-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z" clipRule="evenodd" />
                </svg>
                Platform
              </h4>
              <div className="flex flex-wrap gap-2">
                {platforms.map((platform, index) => (
                  <motion.button
                    key={`platform-${index}`}
                    className={`px-4 py-2 rounded-full text-sm font-medium transition-colors flex items-center space-x-2 ${
                      activeFilters.platforms.includes(platform)
                        ? 'bg-blue-100 text-blue-800 border-2 border-blue-300 shadow-sm'
                        : 'bg-gray-100 text-gray-700 border-2 border-transparent hover:bg-gray-200'
                    }`}
                    onClick={() => onTogglePlatform(platform)}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <span className="w-5 h-5 rounded-full bg-white flex items-center justify-center">
                      {getPlatformIcon(platform)}
                    </span>
                    <span>{platform}</span>
                  </motion.button>
                ))}
              </div>
            </div>

            {/* Language */}
            <div>
              <h4 className="font-medium text-gray-700 mb-4 flex items-center">
                <svg className="w-4 h-4 mr-2 text-indigo-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M7 2a1 1 0 011 1v1h3a1 1 0 110 2H9.578a18.87 18.87 0 01-1.724 4.78c.29.354.596.696.914 1.026a1 1 0 11-1.44 1.389c-.188-.196-.373-.396-.554-.6a19.098 19.098 0 01-3.107 3.567 1 1 0 01-1.334-1.49 17.087 17.087 0 003.13-3.733 18.992 18.992 0 01-1.487-2.494 1 1 0 111.79-.89c.234.47.489.928.764 1.372.417-.934.752-1.913.997-2.927H3a1 1 0 110-2h3V3a1 1 0 011-1zm6 6a1 1 0 01.894.553l2.991 5.982a.869.869 0 01.02.037l.99 1.98a1 1 0 11-1.79.895L15.383 16h-4.764l-.724 1.447a1 1 0 11-1.788-.894l.99-1.98.019-.038 2.99-5.982A1 1 0 0113 8zm-1.382 6h2.764L13 11.236 11.618 14z" clipRule="evenodd" />
                </svg>
                Language
              </h4>
              <div className="flex flex-wrap gap-2">
                {languages.map((language, index) => (
                  <motion.button
                    key={`language-${index}`}
                    className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                      activeFilters.languages.includes(language)
                        ? 'bg-green-100 text-green-800 border-2 border-green-300 shadow-sm'
                        : 'bg-gray-100 text-gray-700 border-2 border-transparent hover:bg-gray-200'
                    }`}
                    onClick={() => onToggleLanguage(language)}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    {language}
                  </motion.button>
                ))}
              </div>
            </div>
          </div>

          {/* Apply Filter Button */}
          <motion.div
            className="mt-8 flex justify-end"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <motion.button
              className="px-6 py-2.5 bg-gradient-to-r from-indigo-600 to-blue-600 text-white rounded-lg font-medium shadow-md hover:shadow-lg transition-all flex items-center justify-center min-w-[150px]"
              whileHover={{ scale: 1.05, boxShadow: "0 10px 15px -3px rgba(79, 70, 229, 0.2)" }}
              whileTap={{ scale: 0.95 }}
              onClick={onApplyFilters}
              disabled={isLoading || error}
            >
              {isLoading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Applying...
                </>
              ) : (
                <>
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                  </svg>
                  Apply Filters
                </>
              )}
            </motion.button>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default EnhancedFilterPanel;
