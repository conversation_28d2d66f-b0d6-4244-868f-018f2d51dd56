import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';

const RangeSlider = ({
  min = 0,
  max = 100,
  step = 1,
  value = { min: 0, max: 100 },
  onChange,
  label = '',
  prefix = '',
  suffix = '',
  color = 'indigo'
}) => {
  const [localValue, setLocalValue] = useState(value);
  const [isDragging, setIsDragging] = useState(null); // 'min', 'max', or null
  const sliderRef = useRef(null);

  // Define color variants
  const colorVariants = {
    indigo: {
      track: 'bg-indigo-600',
      thumb: 'bg-indigo-600 border-indigo-600',
      thumbHover: 'hover:bg-indigo-700 hover:border-indigo-700',
      thumbShadow: 'shadow-indigo-500/20',
      thumbActive: 'ring-indigo-200',
      trackBg: 'bg-indigo-100'
    },
    blue: {
      track: 'bg-blue-600',
      thumb: 'bg-blue-600 border-blue-600',
      thumbHover: 'hover:bg-blue-700 hover:border-blue-700',
      thumbShadow: 'shadow-blue-500/20',
      thumbActive: 'ring-blue-200',
      trackBg: 'bg-blue-100'
    },
    green: {
      track: 'bg-green-600',
      thumb: 'bg-green-600 border-green-600',
      thumbHover: 'hover:bg-green-700 hover:border-green-700',
      thumbShadow: 'shadow-green-500/20',
      thumbActive: 'ring-green-200',
      trackBg: 'bg-green-100'
    },
    purple: {
      track: 'bg-purple-600',
      thumb: 'bg-purple-600 border-purple-600',
      thumbHover: 'hover:bg-purple-700 hover:border-purple-700',
      thumbShadow: 'shadow-purple-500/20',
      thumbActive: 'ring-purple-200',
      trackBg: 'bg-purple-100'
    }
  };

  // Get color classes
  const colors = colorVariants[color] || colorVariants.indigo;

  // Update local value when prop changes
  useEffect(() => {
    setLocalValue(value);
  }, [value]);

  // Handle mouse/touch events for dragging
  useEffect(() => {
    if (!isDragging) return;

    const handleMove = (e) => {
      if (!sliderRef.current) return;

      const rect = sliderRef.current.getBoundingClientRect();
      const width = rect.width;
      const left = rect.left;
      
      // Get clientX for both mouse and touch events
      const clientX = e.touches ? e.touches[0].clientX : e.clientX;
      
      // Calculate position as percentage
      let position = (clientX - left) / width;
      position = Math.max(0, Math.min(1, position));
      
      // Calculate value based on position
      const rawValue = min + position * (max - min);
      const steppedValue = Math.round(rawValue / step) * step;
      
      // Update the appropriate thumb
      setLocalValue(prev => {
        let newValue = { ...prev };
        
        if (isDragging === 'min') {
          newValue.min = Math.min(steppedValue, prev.max - step);
        } else if (isDragging === 'max') {
          newValue.max = Math.max(steppedValue, prev.min + step);
        }
        
        return newValue;
      });
    };

    const handleEnd = () => {
      setIsDragging(null);
      // Notify parent component of change
      onChange(localValue);
    };

    document.addEventListener('mousemove', handleMove);
    document.addEventListener('mouseup', handleEnd);
    document.addEventListener('touchmove', handleMove);
    document.addEventListener('touchend', handleEnd);

    return () => {
      document.removeEventListener('mousemove', handleMove);
      document.removeEventListener('mouseup', handleEnd);
      document.removeEventListener('touchmove', handleMove);
      document.removeEventListener('touchend', handleEnd);
    };
  }, [isDragging, min, max, step, onChange, localValue]);

  // Calculate positions for thumbs and track
  const range = max - min;
  const minPos = ((localValue.min - min) / range) * 100;
  const maxPos = ((localValue.max - min) / range) * 100;

  // Handle input changes
  const handleInputChange = (type, e) => {
    const newValue = parseInt(e.target.value, 10) || 0;
    
    if (type === 'min') {
      const validValue = Math.max(min, Math.min(newValue, localValue.max - step));
      setLocalValue(prev => ({ ...prev, min: validValue }));
    } else {
      const validValue = Math.min(max, Math.max(newValue, localValue.min + step));
      setLocalValue(prev => ({ ...prev, max: validValue }));
    }
  };

  // Handle input blur (commit changes)
  const handleInputBlur = () => {
    onChange(localValue);
  };

  return (
    <div className="space-y-4">
      {label && <h4 className="font-medium text-gray-700 mb-3">{label}</h4>}
      
      {/* Input fields */}
      <div className="flex items-center justify-between">
        <div className="w-1/2 pr-2">
          <label className="block text-sm text-gray-500 mb-1">Min</label>
          <div className="relative">
            {prefix && (
              <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500">
                {prefix}
              </span>
            )}
            <input
              type="number"
              className={`w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:${colors.thumbActive} focus:border-${color}-500 ${prefix ? 'pl-7' : ''}`}
              value={localValue.min}
              onChange={(e) => handleInputChange('min', e)}
              onBlur={handleInputBlur}
              min={min}
              max={localValue.max - step}
              step={step}
            />
            {suffix && (
              <span className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500">
                {suffix}
              </span>
            )}
          </div>
        </div>
        <div className="w-1/2 pl-2">
          <label className="block text-sm text-gray-500 mb-1">Max</label>
          <div className="relative">
            {prefix && (
              <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500">
                {prefix}
              </span>
            )}
            <input
              type="number"
              className={`w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:${colors.thumbActive} focus:border-${color}-500 ${prefix ? 'pl-7' : ''}`}
              value={localValue.max}
              onChange={(e) => handleInputChange('max', e)}
              onBlur={handleInputBlur}
              min={localValue.min + step}
              max={max}
              step={step}
            />
            {suffix && (
              <span className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500">
                {suffix}
              </span>
            )}
          </div>
        </div>
      </div>
      
      {/* Slider */}
      <div className="px-1 py-4">
        <div 
          className="relative h-2 bg-gray-200 rounded-full"
          ref={sliderRef}
        >
          {/* Active track */}
          <div
            className={`absolute h-2 ${colors.track} rounded-full`}
            style={{
              left: `${minPos}%`,
              width: `${maxPos - minPos}%`
            }}
          ></div>
          
          {/* Min thumb */}
          <motion.div
            className={`absolute w-6 h-6 -mt-2 -ml-3 ${colors.thumb} border-2 rounded-full shadow-md cursor-pointer ${colors.thumbHover} ${isDragging === 'min' ? `ring-4 ${colors.thumbActive}` : ''} ${colors.thumbShadow}`}
            style={{ left: `${minPos}%` }}
            whileTap={{ scale: 1.1 }}
            onMouseDown={() => setIsDragging('min')}
            onTouchStart={() => setIsDragging('min')}
            role="slider"
            aria-valuemin={min}
            aria-valuemax={localValue.max}
            aria-valuenow={localValue.min}
            tabIndex={0}
            onKeyDown={(e) => {
              if (e.key === 'ArrowRight') {
                const newValue = Math.min(localValue.min + step, localValue.max - step);
                setLocalValue(prev => ({ ...prev, min: newValue }));
                onChange({ ...localValue, min: newValue });
              } else if (e.key === 'ArrowLeft') {
                const newValue = Math.max(localValue.min - step, min);
                setLocalValue(prev => ({ ...prev, min: newValue }));
                onChange({ ...localValue, min: newValue });
              }
            }}
          >
            <span className="sr-only">Minimum value thumb</span>
          </motion.div>
          
          {/* Max thumb */}
          <motion.div
            className={`absolute w-6 h-6 -mt-2 -ml-3 ${colors.thumb} border-2 rounded-full shadow-md cursor-pointer ${colors.thumbHover} ${isDragging === 'max' ? `ring-4 ${colors.thumbActive}` : ''} ${colors.thumbShadow}`}
            style={{ left: `${maxPos}%` }}
            whileTap={{ scale: 1.1 }}
            onMouseDown={() => setIsDragging('max')}
            onTouchStart={() => setIsDragging('max')}
            role="slider"
            aria-valuemin={localValue.min}
            aria-valuemax={max}
            aria-valuenow={localValue.max}
            tabIndex={0}
            onKeyDown={(e) => {
              if (e.key === 'ArrowRight') {
                const newValue = Math.min(localValue.max + step, max);
                setLocalValue(prev => ({ ...prev, max: newValue }));
                onChange({ ...localValue, max: newValue });
              } else if (e.key === 'ArrowLeft') {
                const newValue = Math.max(localValue.max - step, localValue.min + step);
                setLocalValue(prev => ({ ...prev, max: newValue }));
                onChange({ ...localValue, max: newValue });
              }
            }}
          >
            <span className="sr-only">Maximum value thumb</span>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default RangeSlider;
