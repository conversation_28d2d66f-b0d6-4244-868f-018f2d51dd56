import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import useGlobalBalance from '../../hooks/useGlobalBalance';

/**
 * WalletBalanceCard Component
 *
 * Displays the user's wallet balance and provides options to add credits
 * Used in mission creation and other payment-related flows
 *
 * @param {Object} props - Component props
 * @param {number} props.requiredAmount - The amount required for the current action
 * @param {boolean} props.showAddCredits - Whether to show the add credits button
 * @param {string} props.actionType - The type of action (e.g., 'create', 'join')
 */
const WalletBalanceCard = ({
  requiredAmount = 0,
  showAddCredits = true,
  actionType = 'create'
}) => {
  // Use global balance hook for synchronized balance
  const {
    balance,
    loading: isLoading,
    error,
    formatBalance
  } = useGlobalBalance({
    autoLoad: true,
    enablePolling: false
  });

  const [hasSufficientBalance, setHasSufficientBalance] = useState(true);

  // Update sufficient balance check when balance or required amount changes
  useEffect(() => {
    if (balance !== null) {
      setHasSufficientBalance(balance >= requiredAmount);
    }
  }, [balance, requiredAmount]);

  // Format currency
  const formatCurrency = (amount) => {
    return amount?.toLocaleString() || '0';
  };

  return (
    <motion.div
      className={`rounded-xl shadow-md overflow-hidden ${
        hasSufficientBalance ? 'bg-white' : 'bg-red-50'
      }`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-800">X Wallet Balance</h3>
          {isLoading ? (
            <div className="w-5 h-5 rounded-full border-2 border-indigo-500 border-t-transparent animate-spin"></div>
          ) : error ? (
            <div className="text-red-500 text-sm">Error loading balance</div>
          ) : (
            <div className="flex items-center">
              <svg className="w-5 h-5 text-indigo-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="font-bold text-lg">{formatBalance(balance)}</span>
            </div>
          )}
        </div>

        {!isLoading && !error && (
          <>
            {hasSufficientBalance ? (
              <div className="bg-green-50 p-4 rounded-lg mb-4">
                <div className="flex items-start">
                  <svg className="w-5 h-5 text-green-500 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <div>
                    <p className="text-green-800 text-sm">
                      You have sufficient balance to {actionType} this mission.
                    </p>
                    <p className="text-green-700 text-xs mt-1">
                      Required: <span className="font-medium">{formatCurrency(requiredAmount)} credits</span>
                    </p>
                  </div>
                </div>
              </div>
            ) : (
              <div className="bg-red-100 p-4 rounded-lg mb-4">
                <div className="flex items-start">
                  <svg className="w-5 h-5 text-red-500 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <div>
                    <p className="text-red-800 text-sm">
                      Insufficient balance to {actionType} this mission.
                    </p>
                    <div className="flex justify-between items-center mt-1">
                      <p className="text-red-700 text-xs">
                        Required: <span className="font-medium">{formatCurrency(requiredAmount)} credits</span>
                      </p>
                      <p className="text-red-700 text-xs">
                        Missing: <span className="font-medium">{formatCurrency(requiredAmount - balance)} credits</span>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {showAddCredits && (
              <div className="flex justify-between items-center">
                <Link
                  to="/wallet"
                  className="text-indigo-600 text-sm hover:text-indigo-800 transition-colors"
                >
                  View Wallet
                </Link>

                <Link
                  to="/wallet/deposit"
                  className="px-4 py-2 bg-indigo-600 text-white rounded-lg text-sm font-medium hover:bg-indigo-700 transition-colors flex items-center"
                >
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  Add Credits
                </Link>
              </div>
            )}
          </>
        )}
      </div>
    </motion.div>
  );
};

export default WalletBalanceCard;
