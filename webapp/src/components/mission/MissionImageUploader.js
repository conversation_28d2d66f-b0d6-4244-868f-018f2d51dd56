import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useToast } from '../common/ToastProvider';
import { missionApi } from '../../services/missionApi';

/**
 * MissionImageUploader Component
 *
 * A component for uploading and managing mission images with drag-and-drop support,
 * image preview, and multiple image handling.
 *
 * @param {Object} props - Component props
 * @param {Array} props.images - Array of current images
 * @param {Function} props.onImagesChange - Callback when images change
 * @param {boolean} props.disabled - Whether the uploader is disabled
 * @param {number} props.maxImages - Maximum number of images allowed (default: 5)
 * @param {number} props.maxSizeMB - Maximum file size in MB (default: 5)
 * @param {string|number} props.missionId - ID of the mission (required for API image deletion)
 */
const MissionImageUploader = ({
  images = [],
  onImagesChange,
  disabled = false,
  maxImages = 5,
  maxSizeMB = 5,
  missionId = null
}) => {
  const toast = useToast();
  const fileInputRef = useRef(null);
  const [isDragging, setIsDragging] = useState(false);
  const [uploadProgress, setUploadProgress] = useState({});
  const [previewImages, setPreviewImages] = useState([]);

  // Convert maxSizeMB to bytes
  const maxSizeBytes = maxSizeMB * 1024 * 1024;

  // Allowed file types
  const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];

  // Initialize preview images from props
  useEffect(() => {
    if (images && images.length > 0) {
      const previews = images.map(image => ({
        id: image.id || `existing-${Math.random().toString(36).substr(2, 9)}`,
        url: image.url || image,
        file: null,
        isExisting: true
      }));
      setPreviewImages(previews);
    }
  }, []);

  // Handle file selection
  const handleFileSelect = (e) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    processFiles(Array.from(files));

    // Reset the file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Process selected files
  const processFiles = (files) => {
    // Check if adding these files would exceed the maximum
    if (previewImages.length + files.length > maxImages) {
      toast.error(`You can only upload a maximum of ${maxImages} images`);
      return;
    }

    // Process each file
    const newFiles = files.filter(file => {
      // Check file type
      if (!allowedTypes.includes(file.type)) {
        toast.error(`File type not supported: ${file.name}`);
        return false;
      }

      // Check file size
      if (file.size > maxSizeBytes) {
        toast.error(`File too large: ${file.name} (max ${maxSizeMB}MB)`);
        return false;
      }

      return true;
    });

    // Create preview for each valid file
    const newPreviews = newFiles.map(file => {
      const id = Math.random().toString(36).substr(2, 9);

      // Simulate upload progress
      setUploadProgress(prev => ({
        ...prev,
        [id]: 0
      }));

      // Simulate progress updates
      let progress = 0;
      const interval = setInterval(() => {
        progress += Math.floor(Math.random() * 20);
        if (progress >= 100) {
          progress = 100;
          clearInterval(interval);

          // Clear progress after a delay
          setTimeout(() => {
            setUploadProgress(prev => {
              const newProgress = { ...prev };
              delete newProgress[id];
              return newProgress;
            });
          }, 1000);
        }

        setUploadProgress(prev => ({
          ...prev,
          [id]: progress
        }));
      }, 200);

      return {
        id,
        url: URL.createObjectURL(file),
        file,
        isExisting: false
      };
    });

    // Update preview images
    const updatedPreviews = [...previewImages, ...newPreviews];
    setPreviewImages(updatedPreviews);

    // Notify parent component
    if (onImagesChange) {
      const updatedFiles = updatedPreviews.map(preview => {
        if (preview.isExisting) {
          return preview.url;
        } else {
          return preview.file;
        }
      });
      onImagesChange(updatedFiles);
    }
  };

  // Handle drag events
  const handleDragEnter = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (!disabled) setIsDragging(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (!disabled) setIsDragging(true);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    if (disabled) return;

    const files = e.dataTransfer.files;
    if (!files || files.length === 0) return;

    processFiles(Array.from(files));
  };

  // Delete an image from the API
  const deleteImageFromApi = async (imageId) => {
    if (!missionId || !imageId) return;

    try {
      // Show loading state
      setUploadProgress(prev => ({
        ...prev,
        [imageId]: -1 // Use negative value to indicate deletion in progress
      }));

      // Call the API to delete the image
      await missionApi.deleteMissionImage(missionId, imageId);

      // Show success message
      toast.success('Image deleted successfully');

      return true;
    } catch (error) {
      console.error('Error deleting image:', error);
      toast.error('Failed to delete image: ' + (error.message || 'Unknown error'));
      return false;
    } finally {
      // Clear progress
      setUploadProgress(prev => {
        const newProgress = { ...prev };
        delete newProgress[imageId];
        return newProgress;
      });
    }
  };

  // Remove an image
  const removeImage = async (id) => {
    // Find the image
    const image = previewImages.find(preview => preview.id === id);
    if (!image) return;

    // If this is an existing image and we have a mission ID, delete from API
    if (image.isExisting && missionId) {
      const success = await deleteImageFromApi(id);
      if (!success) return; // Don't remove from UI if API call failed
    }

    // Remove from UI
    const updatedPreviews = previewImages.filter(preview => preview.id !== id);
    setPreviewImages(updatedPreviews);

    // Notify parent component
    if (onImagesChange) {
      const updatedFiles = updatedPreviews.map(preview => {
        if (preview.isExisting) {
          return preview.url;
        } else {
          return preview.file;
        }
      });
      onImagesChange(updatedFiles);
    }
  };

  // Set primary image (first in the array)
  const setPrimaryImage = (id) => {
    const imageToMove = previewImages.find(preview => preview.id === id);
    if (!imageToMove) return;

    const updatedPreviews = [
      imageToMove,
      ...previewImages.filter(preview => preview.id !== id)
    ];

    setPreviewImages(updatedPreviews);

    // Notify parent component
    if (onImagesChange) {
      const updatedFiles = updatedPreviews.map(preview => {
        if (preview.isExisting) {
          return preview.url;
        } else {
          return preview.file;
        }
      });
      onImagesChange(updatedFiles);
    }
  };

  return (
    <div className="space-y-4">
      {/* Upload Area */}
      <div
        className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
          isDragging
            ? 'border-indigo-500 bg-indigo-50'
            : disabled
              ? 'border-gray-300 bg-gray-50 cursor-not-allowed'
              : 'border-gray-300 hover:border-indigo-400 hover:bg-indigo-50/30'
        }`}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        onClick={() => !disabled && fileInputRef.current?.click()}
        style={{ cursor: disabled ? 'not-allowed' : 'pointer' }}
      >
        <input
          type="file"
          ref={fileInputRef}
          className="hidden"
          accept="image/*"
          multiple
          onChange={handleFileSelect}
          disabled={disabled}
        />

        <div className="flex flex-col items-center justify-center space-y-2">
          <svg className={`w-12 h-12 ${disabled ? 'text-gray-400' : 'text-indigo-500'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
          <p className={`text-sm font-medium ${disabled ? 'text-gray-400' : 'text-gray-700'}`}>
            {isDragging ? 'Drop images here' : 'Drag and drop images here, or click to browse'}
          </p>
          <p className={`text-xs ${disabled ? 'text-gray-400' : 'text-gray-500'}`}>
            Supports JPG, PNG, GIF, WebP (max {maxSizeMB}MB)
          </p>
          <p className={`text-xs ${disabled ? 'text-gray-400' : 'text-gray-500'}`}>
            {previewImages.length}/{maxImages} images
          </p>
        </div>
      </div>

      {/* Preview Images */}
      {previewImages.length > 0 && (
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
          <AnimatePresence>
            {previewImages.map((preview, index) => (
              <motion.div
                key={preview.id}
                className={`relative rounded-lg overflow-hidden border ${index === 0 ? 'border-indigo-500 ring-2 ring-indigo-500' : 'border-gray-200'}`}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                transition={{ duration: 0.2 }}
              >
                {/* Image */}
                <div className="aspect-w-16 aspect-h-9 bg-gray-100">
                  <img
                    src={preview.url}
                    alt={`Preview ${index + 1}`}
                    className="object-cover w-full h-full"
                  />
                </div>

                {/* Progress Overlay */}
                {uploadProgress[preview.id] !== undefined && (
                  <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                    <div className="w-16 h-16 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center">
                      <svg className="w-8 h-8 text-white" viewBox="0 0 24 24">
                        <circle
                          className="text-gray-300"
                          strokeWidth="2"
                          stroke="currentColor"
                          fill="transparent"
                          r="10"
                          cx="12"
                          cy="12"
                        />
                        <circle
                          className="text-indigo-500"
                          strokeWidth="2"
                          strokeDasharray={`${uploadProgress[preview.id] * 0.628} 100`}
                          strokeLinecap="round"
                          stroke="currentColor"
                          fill="transparent"
                          r="10"
                          cx="12"
                          cy="12"
                        />
                      </svg>
                      <span className="absolute text-xs font-medium text-white">
                        {uploadProgress[preview.id]}%
                      </span>
                    </div>
                  </div>
                )}

                {/* Controls */}
                <div className="absolute bottom-0 left-0 right-0 p-2 bg-gradient-to-t from-black/70 to-transparent flex justify-between items-center">
                  {/* Primary Badge */}
                  {index === 0 && (
                    <span className="text-xs text-white bg-indigo-500 px-1.5 py-0.5 rounded">
                      Primary
                    </span>
                  )}

                  {/* Action Buttons */}
                  <div className="flex space-x-1 ml-auto">
                    {/* Set as Primary Button (not for the first image) */}
                    {index !== 0 && (
                      <button
                        type="button"
                        onClick={(e) => {
                          e.stopPropagation();
                          setPrimaryImage(preview.id);
                        }}
                        className="p-1 bg-gray-800/70 rounded hover:bg-indigo-600/70 transition-colors"
                        title="Set as primary image"
                      >
                        <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                        </svg>
                      </button>
                    )}

                    {/* Remove Button */}
                    <button
                      type="button"
                      onClick={(e) => {
                        e.stopPropagation();
                        removeImage(preview.id);
                      }}
                      disabled={uploadProgress[preview.id] !== undefined}
                      className={`p-1 ${uploadProgress[preview.id] !== undefined
                        ? 'bg-gray-500/70 cursor-not-allowed'
                        : 'bg-gray-800/70 hover:bg-red-600/70 transition-colors'}`}
                      title="Remove image"
                    >
                      {uploadProgress[preview.id] === -1 ? (
                        // Show spinner for deletion in progress
                        <svg className="w-3 h-3 text-white animate-spin" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                        </svg>
                      ) : (
                        // Show X icon for normal state
                        <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      )}
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
        </div>
      )}
    </div>
  );
};

export default MissionImageUploader;
