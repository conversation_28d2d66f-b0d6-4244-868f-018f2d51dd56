import React from 'react';
import { motion } from 'framer-motion';

const MissionStatusCard = ({ mission, isHost, onComplete }) => {
  // Calculate mission progress
  const calculateProgress = () => {
    if (!mission || !mission.checklist) return 0;
    
    const completedItems = mission.checklist.filter(item => item.completed).length;
    return Math.round((completedItems / mission.checklist.length) * 100);
  };
  
  // Calculate participant readiness
  const calculateReadiness = () => {
    if (!mission || !mission.participants) return 0;
    
    const readyParticipants = mission.participants.filter(participant => participant.status === 'ready').length;
    return Math.round((readyParticipants / mission.participants.length) * 100);
  };
  
  // Format time remaining
  const formatTimeRemaining = () => {
    if (!mission || !mission.end_time) return 'N/A';
    
    const endTime = new Date(mission.end_time);
    const now = new Date();
    
    if (now > endTime) return 'Ended';
    
    const diffMs = endTime - now;
    const diffHrs = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMins = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    
    if (diffHrs > 0) {
      return `${diffHrs}h ${diffMins}m remaining`;
    } else {
      return `${diffMins}m remaining`;
    }
  };
  
  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'not_started':
        return 'bg-yellow-100 text-yellow-800';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };
  
  // Format status text
  const formatStatus = (status) => {
    switch (status) {
      case 'not_started':
        return 'Not Started';
      case 'in_progress':
        return 'In Progress';
      case 'completed':
        return 'Completed';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
    }
  };

  return (
    <motion.div
      className="bg-white rounded-xl shadow-md overflow-hidden"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Mission Status</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          {/* Status */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex justify-between items-center mb-2">
              <p className="text-sm text-gray-500">Status</p>
              <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${getStatusColor(mission?.status || 'in_progress')}`}>
                {formatStatus(mission?.status || 'in_progress')}
              </span>
            </div>
            <p className="text-sm text-gray-500">
              {formatTimeRemaining()}
            </p>
          </div>
          
          {/* Participants */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex justify-between items-center mb-2">
              <p className="text-sm text-gray-500">Participants</p>
              <span className="text-sm font-medium text-gray-800">
                {mission?.participants?.length || 0} / {mission?.slots_total || 0}
              </span>
            </div>
            <div className="flex items-center">
              {mission?.participants?.slice(0, 3).map((participant, index) => (
                <div 
                  key={participant.id} 
                  className="w-8 h-8 rounded-full overflow-hidden border-2 border-white"
                  style={{ marginLeft: index > 0 ? '-8px' : '0' }}
                >
                  <img 
                    src={participant.avatar || '/images/default-avatar.jpg'} 
                    alt={participant.name} 
                    className="w-full h-full object-cover"
                  />
                </div>
              ))}
              {(mission?.participants?.length || 0) > 3 && (
                <div 
                  className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-xs font-medium text-gray-600 border-2 border-white"
                  style={{ marginLeft: '-8px' }}
                >
                  +{(mission?.participants?.length || 0) - 3}
                </div>
              )}
            </div>
          </div>
        </div>
        
        {/* Progress */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-1">
            <p className="text-sm font-medium text-gray-700">Checklist Progress</p>
            <span className="text-sm text-gray-500">{calculateProgress()}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2.5">
            <div 
              className="bg-indigo-600 h-2.5 rounded-full" 
              style={{ width: `${calculateProgress()}%` }}
            ></div>
          </div>
        </div>
        
        {/* Readiness */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-1">
            <p className="text-sm font-medium text-gray-700">Participant Readiness</p>
            <span className="text-sm text-gray-500">{calculateReadiness()}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2.5">
            <div 
              className="bg-green-500 h-2.5 rounded-full" 
              style={{ width: `${calculateReadiness()}%` }}
            ></div>
          </div>
        </div>
        
        {/* Actions */}
        {isHost && mission?.status === 'in_progress' && (
          <button
            onClick={onComplete}
            className="w-full px-4 py-2 bg-indigo-600 text-white rounded-lg font-medium hover:bg-indigo-700 transition-colors flex items-center justify-center"
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
            </svg>
            Complete Mission
          </button>
        )}
      </div>
    </motion.div>
  );
};

export default MissionStatusCard;
