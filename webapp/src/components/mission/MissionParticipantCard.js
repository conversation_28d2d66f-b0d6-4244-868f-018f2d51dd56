import React from 'react';
import { motion } from 'framer-motion';

const MissionParticipantCard = ({ participants, currentUser, onUpdateStatus }) => {
  return (
    <motion.div
      className="bg-white rounded-xl shadow-md overflow-hidden"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.1 }}
    >
      <div className="p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Participants</h3>
        
        <div className="space-y-3">
          {participants.map(participant => (
            <div 
              key={participant.id} 
              className="flex items-center justify-between bg-gray-50 p-3 rounded-lg"
            >
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full overflow-hidden mr-3 bg-gray-200">
                  <img 
                    src={participant.avatar || '/images/default-avatar.jpg'} 
                    alt={participant.name} 
                    className="w-full h-full object-cover"
                  />
                </div>
                <div>
                  <p className="font-medium text-gray-800">
                    {participant.name} {participant.id === currentUser.id && <span className="text-xs text-indigo-600">(You)</span>}
                  </p>
                  <div className="flex items-center text-sm">
                    <span className="text-gray-500 mr-2">LV{participant.level}</span>
                    {participant.role && (
                      <span className="bg-blue-100 text-blue-800 text-xs px-2 py-0.5 rounded-full">
                        {participant.role}
                      </span>
                    )}
                  </div>
                </div>
              </div>
              
              <div>
                {participant.id === currentUser.id ? (
                  <button
                    className={`px-3 py-1 rounded-lg text-sm font-medium ${
                      participant.status === 'ready'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-800 hover:bg-green-100 hover:text-green-800'
                    } transition-colors`}
                    onClick={() => onUpdateStatus(participant.id, participant.status === 'ready' ? 'not_ready' : 'ready')}
                  >
                    {participant.status === 'ready' ? 'Ready' : 'Mark Ready'}
                  </button>
                ) : (
                  <span className={`px-3 py-1 rounded-lg text-xs font-medium ${
                    participant.status === 'ready'
                      ? 'bg-green-100 text-green-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {participant.status === 'ready' ? 'Ready' : 'Not Ready'}
                  </span>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </motion.div>
  );
};

export default MissionParticipantCard;
