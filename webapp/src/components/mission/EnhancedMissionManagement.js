import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { useToast } from '../common/ToastProvider';
import { missionApi } from '../../services/missionApi';

const EnhancedMissionManagement = ({ mission, onUpdate, onDelete }) => {
  const navigate = useNavigate();
  const toast = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showStatusModal, setShowStatusModal] = useState(false);
  const [newStatus, setNewStatus] = useState(mission.status);
  const [applicants, setApplicants] = useState([]);
  const [showApplicants, setShowApplicants] = useState(false);
  
  // Fetch applicants for this mission
  useEffect(() => {
    const fetchApplicants = async () => {
      try {
        const response = await missionApi.getMissionApplicants(mission.id);
        setApplicants(response.data || []);
      } catch (error) {
        console.error('Error fetching applicants:', error);
      }
    };
    
    if (mission.id) {
      fetchApplicants();
    }
  }, [mission.id]);
  
  // Handle status change
  const handleStatusChange = async () => {
    if (newStatus === mission.status) {
      setShowStatusModal(false);
      return;
    }
    
    setIsLoading(true);
    try {
      await missionApi.updateMission(mission.id, { status: newStatus });
      toast.success(`Mission status updated to ${newStatus}`);
      onUpdate && onUpdate();
      setShowStatusModal(false);
    } catch (error) {
      toast.error('Failed to update mission status');
      console.error('Error updating mission status:', error);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle mission deletion
  const handleDelete = async () => {
    setIsLoading(true);
    try {
      await missionApi.deleteMission(mission.id);
      toast.success('Mission deleted successfully');
      onDelete && onDelete(mission.id);
      setShowDeleteModal(false);
    } catch (error) {
      toast.error('Failed to delete mission');
      console.error('Error deleting mission:', error);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle applicant approval/rejection
  const handleApplicantAction = async (applicantId, action) => {
    setIsLoading(true);
    try {
      if (action === 'approve') {
        await missionApi.approveApplicant(mission.id, applicantId);
        toast.success('Applicant approved');
      } else {
        await missionApi.rejectApplicant(mission.id, applicantId);
        toast.success('Applicant rejected');
      }
      
      // Refresh applicants
      const response = await missionApi.getMissionApplicants(mission.id);
      setApplicants(response.data || []);
      onUpdate && onUpdate();
    } catch (error) {
      toast.error(`Failed to ${action} applicant`);
      console.error(`Error ${action}ing applicant:`, error);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'open': return 'bg-green-100 text-green-800';
      case 'in_progress': return 'bg-blue-100 text-blue-800';
      case 'completed': return 'bg-purple-100 text-purple-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };
  
  // Calculate mission progress
  const getProgress = () => {
    const now = new Date();
    const start = new Date(mission.date);
    const end = new Date(mission.end_date);
    
    if (now < start) return 0;
    if (now > end) return 100;
    
    const total = end - start;
    const elapsed = now - start;
    return Math.round((elapsed / total) * 100);
  };
  
  return (
    <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
      {/* Mission Header */}
      <div className="p-6 border-b border-gray-100">
        <div className="flex justify-between items-start">
          <div className="flex-1">
            <div className="flex items-center space-x-3 mb-2">
              <h3 className="text-lg font-semibold text-gray-800">{mission.title}</h3>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(mission.status)}`}>
                {mission.status.replace('_', ' ').toUpperCase()}
              </span>
            </div>
            <p className="text-gray-600 text-sm mb-3">{mission.description}</p>
            
            {/* Mission Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <p className="text-xs text-gray-500">Bounty</p>
                <p className="font-semibold text-indigo-600">₵{mission.bounty}</p>
              </div>
              <div className="text-center">
                <p className="text-xs text-gray-500">Participants</p>
                <p className="font-semibold">{mission.slots_filled || 0}/{mission.slots_total}</p>
              </div>
              <div className="text-center">
                <p className="text-xs text-gray-500">Applicants</p>
                <p className="font-semibold text-blue-600">{applicants.length}</p>
              </div>
              <div className="text-center">
                <p className="text-xs text-gray-500">Progress</p>
                <p className="font-semibold text-green-600">{getProgress()}%</p>
              </div>
            </div>
          </div>
          
          {/* Action Buttons */}
          <div className="flex space-x-2 ml-4">
            <motion.button
              className="p-2 text-gray-600 hover:text-indigo-600 hover:bg-indigo-50 rounded-lg"
              onClick={() => navigate(`/missions/${mission.id}`)}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
            </motion.button>
            
            <motion.button
              className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg"
              onClick={() => navigate(`/missions/${mission.id}/edit`)}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
            </motion.button>
            
            <motion.button
              className="p-2 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded-lg"
              onClick={() => setShowStatusModal(true)}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </motion.button>
            
            <motion.button
              className="p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg"
              onClick={() => setShowDeleteModal(true)}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </motion.button>
          </div>
        </div>
      </div>
      
      {/* Mission Progress Bar */}
      {mission.status === 'in_progress' && (
        <div className="px-6 py-3 bg-blue-50">
          <div className="flex justify-between text-xs text-blue-600 mb-1">
            <span>Mission in progress</span>
            <span>{getProgress()}% complete</span>
          </div>
          <div className="w-full bg-blue-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300" 
              style={{ width: `${getProgress()}%` }}
            ></div>
          </div>
        </div>
      )}
      
      {/* Applicants Section */}
      {applicants.length > 0 && (
        <div className="p-6 border-t border-gray-100">
          <div className="flex justify-between items-center mb-4">
            <h4 className="font-medium text-gray-800">Recent Applicants</h4>
            <button
              className="text-sm text-indigo-600 hover:text-indigo-800"
              onClick={() => setShowApplicants(!showApplicants)}
            >
              {showApplicants ? 'Hide' : 'Show All'} ({applicants.length})
            </button>
          </div>
          
          <AnimatePresence>
            {showApplicants && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="space-y-3"
              >
                {applicants.slice(0, 5).map((applicant) => (
                  <div key={applicant.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 rounded-full overflow-hidden bg-gray-200">
                        <img
                          src={applicant.user?.avatar || '/images/default-avatar.jpg'}
                          alt={applicant.user?.name || 'Applicant'}
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <div>
                        <p className="font-medium text-gray-800">{applicant.user?.name || 'Anonymous'}</p>
                        <p className="text-xs text-gray-500">{applicant.role || 'Participant'}</p>
                      </div>
                    </div>
                    
                    {applicant.status === 'pending' && (
                      <div className="flex space-x-2">
                        <button
                          className="px-3 py-1 bg-green-100 text-green-800 rounded-lg text-xs font-medium hover:bg-green-200"
                          onClick={() => handleApplicantAction(applicant.id, 'approve')}
                          disabled={isLoading}
                        >
                          Approve
                        </button>
                        <button
                          className="px-3 py-1 bg-red-100 text-red-800 rounded-lg text-xs font-medium hover:bg-red-200"
                          onClick={() => handleApplicantAction(applicant.id, 'reject')}
                          disabled={isLoading}
                        >
                          Reject
                        </button>
                      </div>
                    )}
                    
                    {applicant.status !== 'pending' && (
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        applicant.status === 'approved' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {applicant.status}
                      </span>
                    )}
                  </div>
                ))}
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      )}
      
      {/* Status Change Modal */}
      <AnimatePresence>
        {showStatusModal && (
          <motion.div
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <motion.div
              className="bg-white rounded-lg p-6 max-w-md w-full mx-4"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
            >
              <h3 className="text-lg font-semibold mb-4">Change Mission Status</h3>
              <div className="space-y-2 mb-6">
                {['open', 'in_progress', 'completed', 'cancelled'].map((status) => (
                  <label key={status} className="flex items-center">
                    <input
                      type="radio"
                      name="status"
                      value={status}
                      checked={newStatus === status}
                      onChange={(e) => setNewStatus(e.target.value)}
                      className="mr-2"
                    />
                    <span className="capitalize">{status.replace('_', ' ')}</span>
                  </label>
                ))}
              </div>
              <div className="flex justify-end space-x-3">
                <button
                  className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
                  onClick={() => setShowStatusModal(false)}
                >
                  Cancel
                </button>
                <button
                  className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700"
                  onClick={handleStatusChange}
                  disabled={isLoading}
                >
                  {isLoading ? 'Updating...' : 'Update Status'}
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* Delete Confirmation Modal */}
      <AnimatePresence>
        {showDeleteModal && (
          <motion.div
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <motion.div
              className="bg-white rounded-lg p-6 max-w-md w-full mx-4"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
            >
              <h3 className="text-lg font-semibold mb-4 text-red-600">Delete Mission</h3>
              <p className="text-gray-600 mb-6">
                Are you sure you want to delete this mission? This action cannot be undone.
              </p>
              <div className="flex justify-end space-x-3">
                <button
                  className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
                  onClick={() => setShowDeleteModal(false)}
                >
                  Cancel
                </button>
                <button
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
                  onClick={handleDelete}
                  disabled={isLoading}
                >
                  {isLoading ? 'Deleting...' : 'Delete Mission'}
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default EnhancedMissionManagement;
