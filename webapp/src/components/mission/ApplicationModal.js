import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '../../contexts/AuthContext';

const ApplicationModal = ({ isOpen, onClose, onSubmit, mission }) => {
  const { user } = useAuth();
  const [message, setMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [agreedToTerms, setAgreedToTerms] = useState(false);
  const [role, setRole] = useState('');
  const [customRole, setCustomRole] = useState('');
  const [meetRequirements, setMeetRequirements] = useState(false);
  const [formErrors, setFormErrors] = useState({});

  // Suggested roles based on mission theme
  const suggestedRoles = getSuggestedRoles(mission?.theme);

  // Reset form when modal opens
  useEffect(() => {
    if (isOpen) {
      setMessage('');
      setRole('');
      setCustomRole('');
      setAgreedToTerms(false);
      setMeetRequirements(false);
      setFormErrors({});
    }
  }, [isOpen]);

  // Get suggested roles based on mission theme
  function getSuggestedRoles(theme) {
    if (!theme) return ['Participant'];

    const lowerTheme = theme.toLowerCase();

    if (lowerTheme.includes('moba') || lowerTheme.includes('league') || lowerTheme.includes('dota') || lowerTheme.includes('mobile legend')) {
      return ['Tank', 'Support', 'Mage', 'Marksman', 'Assassin', 'Fighter'];
    } else if (lowerTheme.includes('fps') || lowerTheme.includes('shooter') || lowerTheme.includes('valorant') || lowerTheme.includes('csgo')) {
      return ['Entry Fragger', 'Support', 'Sniper', 'In-game Leader', 'Lurker'];
    } else if (lowerTheme.includes('rpg') || lowerTheme.includes('mmorpg')) {
      return ['Tank', 'Healer', 'DPS', 'Support', 'Ranged DPS'];
    } else if (lowerTheme.includes('battle royale') || lowerTheme.includes('fortnite') || lowerTheme.includes('pubg')) {
      return ['Fragger', 'Scout', 'Sniper', 'Support', 'In-game Leader'];
    } else {
      return ['Participant', 'Team Member', 'Support'];
    }
  }

  // Validate form
  const validateForm = () => {
    const errors = {};

    if (!message.trim()) {
      errors.message = 'Please introduce yourself to the host';
    }

    if (!role && !customRole) {
      errors.role = 'Please select or enter your role';
    }

    if (!meetRequirements) {
      errors.meetRequirements = 'You must confirm that you meet all requirements';
    }

    if (!agreedToTerms) {
      errors.agreedToTerms = 'You must agree to the terms';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Prepare application data
      const applicationData = {
        message,
        role: role === 'custom' ? customRole : role,
        agreedToTerms,
        meetRequirements
      };

      // Pass application data to parent component
      await onSubmit(applicationData);

      // Reset form
      setMessage('');
      setRole('');
      setCustomRole('');
      setAgreedToTerms(false);
      setMeetRequirements(false);
      setFormErrors({});
    } catch (error) {
      console.error('Error submitting application:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Animation variants
  const backdropVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 }
  };

  const modalVariants = {
    hidden: { opacity: 0, y: 50, scale: 0.95 },
    visible: { opacity: 1, y: 0, scale: 1, transition: { duration: 0.3, ease: "easeOut" } },
    exit: { opacity: 0, y: 50, scale: 0.95, transition: { duration: 0.2, ease: "easeIn" } }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm"
          initial="hidden"
          animate="visible"
          exit="hidden"
          variants={backdropVariants}
        >
          <motion.div
            className="bg-white rounded-2xl shadow-xl w-full max-w-md overflow-hidden"
            variants={modalVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="bg-gradient-to-r from-indigo-500 to-indigo-600 px-6 py-4 text-white">
              <div className="flex justify-between items-center">
                <h3 className="text-xl font-bold">Apply for Mission</h3>
                <button
                  onClick={onClose}
                  className="text-white/80 hover:text-white transition-colors"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>

            {/* Content */}
            <div className="p-6">
              <div className="mb-6">
                <h4 className="font-medium text-gray-800 mb-2">Mission Details</h4>
                <div className="bg-gray-50 p-3 rounded-lg">
                  <p className="font-medium text-gray-800">{mission?.title}</p>
                  <div className="flex justify-between text-sm text-gray-600 mt-1">
                    <span>Bounty: {mission?.bounty} credits</span>
                    <span>{mission?.slots_total - mission?.slots_filled} slots remaining</span>
                  </div>
                </div>
              </div>

              <form onSubmit={handleSubmit}>
                {/* User profile summary */}
                {user && (
                  <div className="mb-4 bg-indigo-50 p-3 rounded-lg flex items-center">
                    <div className="w-10 h-10 rounded-full overflow-hidden bg-gray-200 mr-3">
                      <img
                        src={user.avatar || '/images/default-avatar.jpg'}
                        alt={user.name || 'User'}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div>
                      <p className="font-medium text-gray-800">{user.name || 'Anonymous User'}</p>
                      <p className="text-xs text-gray-500">Applying as: {role === 'custom' ? customRole : role || 'Not selected'}</p>
                    </div>
                  </div>
                )}

                {/* Message to host */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Message to Host <span className="text-red-500">*</span>
                  </label>
                  <textarea
                    className={`w-full border ${formErrors.message ? 'border-red-500' : 'border-gray-300'} rounded-lg px-3 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 min-h-[100px] resize-none`}
                    placeholder="Introduce yourself and explain why you're a good fit for this mission..."
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                  />
                  {formErrors.message && (
                    <p className="mt-1 text-sm text-red-500">{formErrors.message}</p>
                  )}
                </div>

                {/* Role selection */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Your Role <span className="text-red-500">*</span>
                  </label>
                  <div className="grid grid-cols-3 gap-2 mb-2">
                    {suggestedRoles.map((suggestedRole) => (
                      <button
                        key={suggestedRole}
                        type="button"
                        className={`px-3 py-2 text-sm rounded-lg border ${
                          role === suggestedRole
                            ? 'bg-indigo-100 border-indigo-300 text-indigo-700'
                            : 'border-gray-300 hover:bg-gray-50'
                        }`}
                        onClick={() => {
                          setRole(suggestedRole);
                          setCustomRole('');
                        }}
                      >
                        {suggestedRole}
                      </button>
                    ))}
                    <button
                      type="button"
                      className={`px-3 py-2 text-sm rounded-lg border ${
                        role === 'custom'
                          ? 'bg-indigo-100 border-indigo-300 text-indigo-700'
                          : 'border-gray-300 hover:bg-gray-50'
                      }`}
                      onClick={() => setRole('custom')}
                    >
                      Custom
                    </button>
                  </div>

                  {role === 'custom' && (
                    <input
                      type="text"
                      className={`w-full border ${formErrors.role && !customRole ? 'border-red-500' : 'border-gray-300'} rounded-lg px-3 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500`}
                      placeholder="Enter your custom role..."
                      value={customRole}
                      onChange={(e) => setCustomRole(e.target.value)}
                    />
                  )}

                  {formErrors.role && (
                    <p className="mt-1 text-sm text-red-500">{formErrors.role}</p>
                  )}
                </div>

                {/* Requirements confirmation */}
                {mission?.requirements && mission.requirements.length > 0 && (
                  <div className="mb-4">
                    <h5 className="text-sm font-medium text-gray-700 mb-2">Mission Requirements</h5>
                    <div className="bg-gray-50 p-3 rounded-lg mb-2">
                      <ul className="text-sm text-gray-600 space-y-1">
                        {mission.requirements.map((requirement, index) => (
                          <li key={index} className="flex items-start">
                            <svg className="w-4 h-4 text-indigo-500 mr-1 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            {requirement}
                          </li>
                        ))}
                      </ul>
                    </div>

                    <label className={`flex items-start cursor-pointer ${formErrors.meetRequirements ? 'text-red-500' : ''}`}>
                      <input
                        type="checkbox"
                        className={`form-checkbox h-5 w-5 ${formErrors.meetRequirements ? 'text-red-500 border-red-500' : 'text-indigo-600 border-gray-300'} rounded focus:ring-indigo-500 mt-0.5`}
                        checked={meetRequirements}
                        onChange={() => setMeetRequirements(!meetRequirements)}
                      />
                      <span className="ml-2 text-sm">
                        I confirm that I meet all the requirements for this mission.
                      </span>
                    </label>

                    {formErrors.meetRequirements && (
                      <p className="mt-1 text-sm text-red-500">{formErrors.meetRequirements}</p>
                    )}
                  </div>
                )}

                {/* Terms agreement */}
                <div className="mb-6">
                  <label className={`flex items-start cursor-pointer ${formErrors.agreedToTerms ? 'text-red-500' : ''}`}>
                    <input
                      type="checkbox"
                      className={`form-checkbox h-5 w-5 ${formErrors.agreedToTerms ? 'text-red-500 border-red-500' : 'text-indigo-600 border-gray-300'} rounded focus:ring-indigo-500 mt-0.5`}
                      checked={agreedToTerms}
                      onChange={() => setAgreedToTerms(!agreedToTerms)}
                    />
                    <span className="ml-2 text-sm text-gray-600">
                      I agree to be available during the scheduled mission time and understand that not showing up may affect my reputation score.
                    </span>
                  </label>

                  {formErrors.agreedToTerms && (
                    <p className="mt-1 text-sm text-red-500">{formErrors.agreedToTerms}</p>
                  )}
                </div>

                <div className="flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-3">
                  <button
                    type="button"
                    className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                    onClick={onClose}
                    disabled={isSubmitting}
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className={`px-4 py-2 bg-indigo-600 text-white rounded-lg font-medium hover:bg-indigo-700 transition-colors flex items-center justify-center ${
                      isSubmitting ? 'opacity-70 cursor-wait' :
                      (!message.trim() || (!role && !customRole) || !meetRequirements || !agreedToTerms) ? 'opacity-50 cursor-not-allowed' : ''
                    }`}
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Submitting...
                      </>
                    ) : (
                      <>
                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Submit Application
                      </>
                    )}
                  </button>
                </div>

                {/* Form validation summary */}
                {Object.keys(formErrors).length > 0 && (
                  <div className="mt-4 p-3 bg-red-50 border border-red-100 rounded-lg">
                    <p className="text-sm text-red-600 font-medium mb-1">Please fix the following issues:</p>
                    <ul className="text-xs text-red-500 list-disc list-inside">
                      {Object.values(formErrors).map((error, index) => (
                        <li key={index}>{error}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </form>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default ApplicationModal;
