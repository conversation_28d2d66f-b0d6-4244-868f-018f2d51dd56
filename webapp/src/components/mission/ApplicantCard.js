import React from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';

const ApplicantCard = ({
  applicant,
  isSelected,
  onToggleSelect,
  onAccept,
  onReject,
  isProcessing
}) => {
  const navigate = useNavigate();

  // Format relative time
  const formatRelativeTime = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 60) {
      return 'just now';
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes} ${minutes === 1 ? 'minute' : 'minutes'} ago`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `${hours} ${hours === 1 ? 'hour' : 'hours'} ago`;
    } else if (diffInSeconds < 604800) {
      const days = Math.floor(diffInSeconds / 86400);
      return `${days} ${days === 1 ? 'day' : 'days'} ago`;
    } else {
      const options = {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      };
      return new Date(dateString).toLocaleDateString('en-US', options);
    }
  };

  return (
    <motion.div
      className={`p-6 transition-all duration-200 ${
        isSelected
          ? 'bg-indigo-50 border-l-4 border-indigo-500'
          : 'hover:bg-gray-50'
      }`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ scale: 1.01 }}
    >
      <div className="flex items-start">
        {/* Enhanced Checkbox */}
        <div className="mr-4 pt-1">
          <motion.div
            className={`relative ${isSelected ? 'scale-110' : ''}`}
            whileTap={{ scale: 0.95 }}
          >
            <input
              type="checkbox"
              className={`form-checkbox h-5 w-5 rounded border-2 focus:ring-2 focus:ring-indigo-500 transition-all ${
                isSelected
                  ? 'text-indigo-600 border-indigo-500 bg-indigo-50'
                  : 'text-indigo-600 border-gray-300'
              }`}
              checked={isSelected}
              onChange={onToggleSelect}
              disabled={isProcessing}
            />
            {isSelected && (
              <motion.div
                className="absolute inset-0 pointer-events-none"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ type: "spring", stiffness: 500, damping: 30 }}
              >
                <div className="w-full h-full rounded border-2 border-indigo-500 bg-indigo-100 opacity-20"></div>
              </motion.div>
            )}
          </motion.div>
        </div>

        {/* Avatar */}
        <div className="mr-4">
          <div className="w-12 h-12 rounded-full overflow-hidden bg-gray-200">
            <img
              src={applicant.user?.profile_picture || '/images/default-avatar.jpg'}
              alt={applicant.user?.name || 'Applicant'}
              className="w-full h-full object-cover"
            />
          </div>
        </div>

        {/* Applicant Info */}
        <div className="flex-1">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-2">
            <div>
              <h3 className="text-lg font-bold text-gray-800">{applicant.user?.name || 'Unknown Applicant'}</h3>
              <div className="flex items-center text-sm text-gray-500">
                <span className="mr-2">LV{applicant.user?.level || '0'}</span>
                <span className="flex items-center mr-2">
                  <svg className="w-4 h-4 text-yellow-400 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                  {applicant.user?.rating || '0'}
                </span>
                <span>{applicant.user?.completed_missions_count || '0'} missions completed</span>
              </div>
            </div>
            <div className="mt-2 md:mt-0">
              <span className="text-sm text-gray-500">
                Applied {formatRelativeTime(applicant.created_at)}
              </span>
            </div>
          </div>

          {/* Application Message */}
          {applicant.notes && (
            <div className="bg-gray-50 p-3 rounded-lg mb-3">
              <p className="text-gray-700 text-sm">{applicant.notes}</p>
            </div>
          )}

          {/* Status and Actions */}
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
              {applicant.status === 'pending' ? (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                  <span className="w-1.5 h-1.5 rounded-full bg-yellow-500 mr-1.5"></span>
                  Pending
                </span>
              ) : applicant.status === 'approved' ? (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  <span className="w-1.5 h-1.5 rounded-full bg-green-500 mr-1.5"></span>
                  Accepted
                </span>
              ) : (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                  <span className="w-1.5 h-1.5 rounded-full bg-red-500 mr-1.5"></span>
                  Rejected
                </span>
              )}
            </div>

            <div className="mt-3 md:mt-0 space-x-2">
              <button
                className="px-3 py-1 bg-white border border-gray-300 text-gray-700 rounded-lg text-sm font-medium hover:bg-gray-50 transition-colors"
                onClick={() => navigate(`/talents/${applicant.user_id}`)}
              >
                View Profile
              </button>

              {applicant.status === 'pending' && (
                <>
                  <button
                    className="px-3 py-1 bg-green-100 text-green-800 rounded-lg text-sm font-medium hover:bg-green-200 transition-colors"
                    onClick={onAccept}
                    disabled={isProcessing}
                  >
                    {isProcessing ? 'Processing...' : 'Accept'}
                  </button>
                  <button
                    className="px-3 py-1 bg-red-100 text-red-800 rounded-lg text-sm font-medium hover:bg-red-200 transition-colors"
                    onClick={onReject}
                    disabled={isProcessing}
                  >
                    Reject
                  </button>
                </>
              )}

              {applicant.status === 'approved' && (
                <button
                  className="px-3 py-1 bg-indigo-100 text-indigo-800 rounded-lg text-sm font-medium hover:bg-indigo-200 transition-colors"
                  onClick={() => navigate('/chat')}
                >
                  Message
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default ApplicantCard;
