import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import profileService from '../services/profileService';

const MediaGallery = ({ onSave, initialMedia = { photos: [], video: null, selectedCover: null } }) => {
    const [photos, setPhotos] = useState(initialMedia.photos || []);
    const [video, setVideo] = useState(initialMedia.video || null);
    const [selectedCover, setSelectedCover] = useState(initialMedia.selectedCover || null);
    const [isDragging, setIsDragging] = useState(false);
    const [draggedItem, setDraggedItem] = useState(null);
    const [uploadProgress, setUploadProgress] = useState(0);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState(null);
    const videoRef = useRef(null);
    const MAX_PHOTOS = 5;
    const MAX_VIDEO_DURATION = 10; // seconds

    // Fetch media gallery on component mount if no initial media is provided
    useEffect(() => {
        if (initialMedia.photos.length === 0 && !initialMedia.video) {
            fetchMediaGallery();
        }
    }, [initialMedia.photos.length, initialMedia.video]);

    // Function to fetch media gallery
    const fetchMediaGallery = async () => {
        setIsLoading(true);
        setError(null);
        try {
            const response = await profileService.getMediaGallery();
            if (response.success) {
                const mediaItems = response.data || [];

                // Separate photos and video
                const photoItems = mediaItems.filter(item => item.type === 'image').map(item => ({
                    id: item.id,
                    preview: item.url,
                    thumbnail: item.thumbnail_url,
                    title: item.title,
                    description: item.description
                }));

                const videoItem = mediaItems.find(item => item.type === 'video');

                setPhotos(photoItems);
                if (videoItem) {
                    setVideo({
                        id: videoItem.id,
                        preview: videoItem.url,
                        thumbnail: videoItem.thumbnail_url
                    });
                }

                // Set cover if available
                if (initialMedia.selectedCover) {
                    setSelectedCover(initialMedia.selectedCover);
                } else if (videoItem) {
                    setSelectedCover({ type: 'video', id: videoItem.id });
                } else if (photoItems.length > 0) {
                    setSelectedCover({ type: 'photo', id: photoItems[0].id });
                }
            } else {
                setError(response.error || 'Failed to load media gallery');
            }
        } catch (err) {
            console.error('Error fetching media gallery:', err);
            setError('Failed to load media gallery');
        } finally {
            setIsLoading(false);
        }
    };

    const handlePhotoUpload = async (event) => {
        const files = Array.from(event.target.files);
        if (photos.length + files.length > MAX_PHOTOS) {
            alert(`You can only upload up to ${MAX_PHOTOS} photos`);
            return;
        }

        // Create temporary photo objects with local URLs for immediate display
        const tempPhotos = files.map(file => ({
            id: `temp-${Date.now()}-${Math.random()}`,
            file,
            preview: URL.createObjectURL(file),
            isUploading: true,
            order: photos.length + 1
        }));

        // Add temporary photos to state
        setPhotos(prev => [...prev, ...tempPhotos]);

        // Upload each photo and update with real data
        for (const tempPhoto of tempPhotos) {
            try {
                setUploadProgress(0);
                const response = await profileService.uploadMedia(tempPhoto.file, 'image');

                if (response.success) {
                    // Replace temp photo with real one
                    setPhotos(prev => prev.map(p =>
                        p.id === tempPhoto.id ? {
                            id: response.data.id,
                            preview: response.data.url,
                            thumbnail: response.data.thumbnail_url,
                            title: response.data.title,
                            description: response.data.description,
                            isUploading: false,
                            order: p.order
                        } : p
                    ));

                    // If no cover is selected, set the first photo as cover
                    if (!selectedCover) {
                        handleSelectCover('photo', response.data.id);
                    }
                } else {
                    // Remove failed upload
                    setPhotos(prev => prev.filter(p => p.id !== tempPhoto.id));
                    console.error('Failed to upload photo:', response.error);
                    alert(`Failed to upload photo: ${response.error || 'Unknown error'}`);
                }
            } catch (error) {
                // Remove failed upload
                setPhotos(prev => prev.filter(p => p.id !== tempPhoto.id));
                console.error('Error uploading photo:', error);
                alert('Failed to upload photo. Please try again.');
            } finally {
                // Revoke object URL to avoid memory leaks
                URL.revokeObjectURL(tempPhoto.preview);
            }
        }

        // Update parent component
        if (onSave) {
            const updatedPhotos = photos.filter(p => !p.isUploading);
            onSave({ photos: updatedPhotos, video, selectedCover });
        }
    };

    const handleVideoUpload = async (event) => {
        const file = event.target.files[0];
        if (!file) return;

        // Create video element to check duration
        const videoElement = document.createElement('video');
        videoElement.preload = 'metadata';

        videoElement.onloadedmetadata = async () => {
            window.URL.revokeObjectURL(videoElement.src);
            if (videoElement.duration > MAX_VIDEO_DURATION) {
                alert(`Video must be ${MAX_VIDEO_DURATION} seconds or less`);
                return;
            }

            // Create temporary video object with local URL for immediate display
            const tempVideo = {
                id: `temp-video-${Date.now()}`,
                file,
                preview: URL.createObjectURL(file),
                thumbnail: null,
                isUploading: true
            };

            // Add temporary video to state
            setVideo(tempVideo);

            try {
                setUploadProgress(0);
                const response = await profileService.uploadMedia(file, 'video');

                if (response.success) {
                    // Replace temp video with real one
                    const realVideo = {
                        id: response.data.id,
                        preview: response.data.url,
                        thumbnail: response.data.thumbnail_url,
                        title: response.data.title,
                        description: response.data.description,
                        isUploading: false
                    };

                    setVideo(realVideo);

                    // Automatically select video as cover when uploaded
                    handleSelectCover('video', realVideo.id);

                    if (onSave) {
                        onSave({
                            photos,
                            video: realVideo,
                            selectedCover: { type: 'video', id: realVideo.id }
                        });
                    }
                } else {
                    // Remove failed upload
                    setVideo(null);
                    console.error('Failed to upload video:', response.error);
                    alert(`Failed to upload video: ${response.error || 'Unknown error'}`);
                }
            } catch (error) {
                // Remove failed upload
                setVideo(null);
                console.error('Error uploading video:', error);
                alert('Failed to upload video. Please try again.');
            } finally {
                // Revoke object URL to avoid memory leaks
                URL.revokeObjectURL(tempVideo.preview);
            }
        };

        videoElement.src = URL.createObjectURL(file);
    };

    const handleSelectCover = async (type, id) => {
        // Skip if trying to select a temporary item that's still uploading
        if (id.toString().includes('temp-')) {
            return;
        }

        const newCover = { type, id };
        setSelectedCover(newCover);

        // Update the cover in the API
        try {
            const response = await profileService.setMediaAsCover(id, type);
            if (!response.success) {
                console.error('Failed to set media as cover:', response.error);
                // Don't revert UI state as it's not critical
            }
        } catch (error) {
            console.error('Error setting media as cover:', error);
            // Don't revert UI state as it's not critical
        }

        if (onSave) onSave({ photos, video, selectedCover: newCover });
    };

    const handleDragStart = (e, index) => {
        setDraggedItem(index);
        setIsDragging(true);
        e.dataTransfer.effectAllowed = 'move';
        e.target.style.opacity = '0.5';
    };

    const handleDragEnd = (e) => {
        setIsDragging(false);
        setDraggedItem(null);
        e.target.style.opacity = '1';
    };

    const handleDragOver = (e, index) => {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'move';
    };

    const handleDrop = async (e, dropIndex) => {
        e.preventDefault();
        if (draggedItem === null) return;

        // Create a new array with the reordered photos
        const newPhotos = [...photos];
        const [draggedPhoto] = newPhotos.splice(draggedItem, 1);
        newPhotos.splice(dropIndex, 0, draggedPhoto);

        // Optimistically update UI
        setPhotos(newPhotos);
        setDraggedItem(null);
        setIsDragging(false);

        // Skip API call if any of the photos are still uploading
        if (newPhotos.some(photo => photo.id.toString().includes('temp-'))) {
            if (onSave) onSave({ photos: newPhotos, video, selectedCover });
            return;
        }

        try {
            // Prepare the media order data for the API
            const mediaOrder = newPhotos.map((photo, index) => ({
                id: photo.id,
                order: index + 1
            }));

            // Call API to update the order
            const response = await profileService.reorderMedia(mediaOrder);

            if (!response.success) {
                console.error('Failed to reorder photos:', response.error);
                // Don't revert the UI as it's not critical
            }

            if (onSave) onSave({ photos: newPhotos, video, selectedCover });
        } catch (error) {
            console.error('Error reordering photos:', error);
            // Don't revert the UI as it's not critical
        }
    };

    const deletePhoto = async (index) => {
        const photoToDelete = photos[index];

        // Skip API call for temporary photos that are still uploading
        if (photoToDelete.id.toString().includes('temp-')) {
            const updatedPhotos = photos.filter((_, i) => i !== index);
            setPhotos(updatedPhotos);
            if (onSave) onSave({ photos: updatedPhotos, video, selectedCover });
            return;
        }

        try {
            // Optimistically update UI
            const updatedPhotos = photos.filter((_, i) => i !== index);
            setPhotos(updatedPhotos);

            // If the deleted photo was the cover, select the first available photo or video as cover
            if (selectedCover?.type === 'photo' && selectedCover.id === photoToDelete.id) {
                if (updatedPhotos.length > 0) {
                    handleSelectCover('photo', updatedPhotos[0].id);
                } else if (video) {
                    handleSelectCover('video', video.id);
                } else {
                    setSelectedCover(null);
                }
            }

            // Call API to delete the photo
            const response = await profileService.deleteMedia(photoToDelete.id);

            if (!response.success) {
                console.error('Failed to delete photo:', response.error);
                // Revert the change if the API call fails
                setPhotos(prev => [...prev, photoToDelete]);
                alert(`Failed to delete photo: ${response.error || 'Unknown error'}`);
            }

            if (onSave) onSave({ photos: updatedPhotos, video, selectedCover });
        } catch (error) {
            console.error('Error deleting photo:', error);
            // Revert the change if there's an error
            setPhotos(prev => [...prev, photoToDelete]);
            alert('Failed to delete photo. Please try again.');
        }
    };

    const deleteVideo = async () => {
        if (!video) return;

        // Skip API call for temporary videos that are still uploading
        if (video.id.toString().includes('temp-')) {
            if (video?.preview) URL.revokeObjectURL(video.preview);
            setVideo(null);

            // If video was the cover, select the first photo as cover
            if (selectedCover?.type === 'video') {
                if (photos.length > 0) {
                    handleSelectCover('photo', photos[0].id);
                } else {
                    setSelectedCover(null);
                }
            }

            if (onSave) onSave({ photos, video: null, selectedCover });
            return;
        }

        try {
            // Store video for potential revert
            const videoToDelete = { ...video };

            // Optimistically update UI
            if (video?.preview) URL.revokeObjectURL(video.preview);
            setVideo(null);

            // If video was the cover, select the first photo as cover
            if (selectedCover?.type === 'video') {
                if (photos.length > 0) {
                    handleSelectCover('photo', photos[0].id);
                } else {
                    setSelectedCover(null);
                }
            }

            // Call API to delete the video
            const response = await profileService.deleteMedia(videoToDelete.id);

            if (!response.success) {
                console.error('Failed to delete video:', response.error);
                // Revert the change if the API call fails
                setVideo(videoToDelete);
                alert(`Failed to delete video: ${response.error || 'Unknown error'}`);
            }

            if (onSave) onSave({ photos, video: null, selectedCover });
        } catch (error) {
            console.error('Error deleting video:', error);
            // Don't revert the change if there's an error as we've already revoked the object URL
            alert('Failed to delete video. Please try again.');
        }
    };

    return (
        <div className="space-y-6">
            <div className="bg-white rounded-xl shadow-sm p-6">
                <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-semibold">Cover Media</h3>
                    <span className="text-sm text-gray-500">Select a photo or video for your profile cover</span>
                </div>

                {/* Error Message */}
                {error && (
                    <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700">
                        <p className="text-sm font-medium">{error}</p>
                        <button
                            onClick={fetchMediaGallery}
                            className="mt-2 text-xs font-medium text-red-700 hover:text-red-800"
                        >
                            Try Again
                        </button>
                    </div>
                )}

                {/* Loading State */}
                {isLoading && (
                    <div className="py-12 flex flex-col items-center justify-center">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
                        <p className="mt-4 text-gray-500">Loading media gallery...</p>
                    </div>
                )}

                {/* Media Grid */}
                {!isLoading && (
                    <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
                        {/* Video Section */}
                        {video && (
                            <motion.div
                                className={`relative aspect-square col-span-2 sm:col-span-3 ${
                                    selectedCover?.type === 'video' && selectedCover.id === video.id
                                        ? 'ring-4 ring-indigo-500'
                                        : ''
                                }`}
                                whileHover={{ scale: 1.02 }}
                                onClick={() => handleSelectCover('video', video.id)}
                            >
                                {/* Video Loading Overlay */}
                                {video.isUploading && (
                                    <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center rounded-lg z-10">
                                        <div className="text-center text-white">
                                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto"></div>
                                            <p className="mt-2 text-sm">Uploading video...</p>
                                        </div>
                                    </div>
                                )}

                                <video
                                    ref={videoRef}
                                    src={video.preview}
                                    className="w-full h-full object-cover rounded-lg cursor-pointer"
                                    controls
                                />
                                <button
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        deleteVideo();
                                    }}
                                    className="absolute top-2 right-2 p-2 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors z-20"
                                    disabled={video.isUploading}
                                >
                                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </button>
                                {selectedCover?.type === 'video' && selectedCover.id === video.id && (
                                    <div className="absolute bottom-2 right-2 bg-indigo-500 text-white px-2 py-1 rounded-lg text-sm z-20">
                                        Cover
                                    </div>
                                )}
                            </motion.div>
                        )}

                        {/* Photos Grid */}
                        {photos.map((photo, index) => (
                            <motion.div
                                key={photo.id || index}
                                className={`relative aspect-square cursor-pointer ${
                                    selectedCover?.type === 'photo' && selectedCover.id === photo.id
                                        ? 'ring-4 ring-indigo-500'
                                        : ''
                                }`}
                                draggable={!photo.isUploading}
                                onDragStart={(e) => !photo.isUploading && handleDragStart(e, index)}
                                onDragEnd={handleDragEnd}
                                onDragOver={(e) => handleDragOver(e, index)}
                                onDrop={(e) => handleDrop(e, index)}
                                whileHover={{ scale: 1.02 }}
                                whileTap={{ scale: 0.98 }}
                                onClick={() => !photo.isUploading && handleSelectCover('photo', photo.id)}
                            >
                                {/* Photo Loading Overlay */}
                                {photo.isUploading && (
                                    <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center rounded-lg z-10">
                                        <div className="text-center text-white">
                                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto"></div>
                                            <p className="mt-2 text-sm">Uploading...</p>
                                        </div>
                                    </div>
                                )}

                                <img
                                    src={photo.preview}
                                    alt={`Upload ${index + 1}`}
                                    className="w-full h-full object-cover rounded-lg"
                                />
                                <button
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        deletePhoto(index);
                                    }}
                                    className="absolute top-2 right-2 p-2 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors z-20"
                                    disabled={photo.isUploading}
                                >
                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </button>
                                {selectedCover?.type === 'photo' && selectedCover.id === photo.id && (
                                    <div className="absolute bottom-2 right-2 bg-indigo-500 text-white px-2 py-1 rounded-lg text-sm z-20">
                                        Cover
                                    </div>
                                )}
                            </motion.div>
                        ))}

                        {/* Upload Buttons */}
                        {photos.length < MAX_PHOTOS && (
                            <div className="relative aspect-square">
                                <input
                                    type="file"
                                    accept="image/*"
                                    multiple
                                    onChange={handlePhotoUpload}
                                    className="hidden"
                                    id="photo-upload"
                                    disabled={isLoading}
                                />
                                <label
                                    htmlFor="photo-upload"
                                    className={`flex items-center justify-center w-full h-full border-2 border-dashed border-gray-300 rounded-lg ${isLoading ? 'cursor-not-allowed opacity-50' : 'cursor-pointer hover:border-gray-400'} transition-colors`}
                                >
                                    <div className="text-center">
                                        <svg className="w-8 h-8 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                        </svg>
                                        <span className="text-sm text-gray-500">Add Photos</span>
                                        <span className="text-xs text-gray-400 block">{photos.length}/{MAX_PHOTOS}</span>
                                    </div>
                                </label>
                            </div>
                        )}

                        {!video && (
                            <div className="relative aspect-square">
                                <input
                                    type="file"
                                    accept="video/*"
                                    onChange={handleVideoUpload}
                                    className="hidden"
                                    id="video-upload"
                                    disabled={isLoading}
                                />
                                <label
                                    htmlFor="video-upload"
                                    className={`flex items-center justify-center w-full h-full border-2 border-dashed border-gray-300 rounded-lg ${isLoading ? 'cursor-not-allowed opacity-50' : 'cursor-pointer hover:border-gray-400'} transition-colors`}
                                >
                                    <div className="text-center">
                                        <svg className="w-8 h-8 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                        </svg>
                                        <span className="text-sm text-gray-500">Add Video</span>
                                        <span className="text-xs text-gray-400 block">Max {MAX_VIDEO_DURATION} seconds</span>
                                    </div>
                                </label>
                            </div>
                        )}
                    </div>
                )}

                {isDragging && (
                    <div className="mt-4 text-sm text-gray-500 text-center">
                        Drag to reorder photos
                    </div>
                )}

                {/* Upload Progress */}
                {uploadProgress > 0 && uploadProgress < 100 && (
                    <div className="mt-4">
                        <div className="w-full bg-gray-200 rounded-full h-2.5">
                            <div
                                className="bg-indigo-600 h-2.5 rounded-full"
                                style={{ width: `${uploadProgress}%` }}
                            ></div>
                        </div>
                        <p className="text-xs text-gray-500 mt-1 text-right">{uploadProgress}% uploaded</p>
                    </div>
                )}
            </div>
        </div>
    );
};

export default MediaGallery;