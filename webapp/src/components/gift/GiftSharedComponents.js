import React from 'react';
import { motion } from 'framer-motion';

// Reusable Gift Card Component
export const GiftCard = ({ gift, onAction, actionLabel = "View", className = "" }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`bg-white/60 backdrop-blur-sm border border-white/30 rounded-xl p-4 hover:shadow-lg transition-all duration-300 ${className}`}
    >
      <div className="text-center">
        <div className="w-16 h-16 bg-gradient-to-br from-indigo-400 to-purple-500 rounded-full mx-auto mb-3 flex items-center justify-center">
          {gift.image_url ? (
            <img 
              src={gift.image_url} 
              alt={gift.name} 
              className="w-full h-full object-cover rounded-full"
              onError={(e) => {
                e.target.style.display = 'none';
                e.target.nextSibling.style.display = 'flex';
              }}
            />
          ) : null}
          <span 
            className="text-white font-bold text-lg"
            style={{ display: gift.image_url ? 'none' : 'flex' }}
          >
            {gift.name?.[0] || '🎁'}
          </span>
        </div>
        <h3 className="font-semibold text-gray-800 mb-1">{gift.name || 'Gift Item'}</h3>
        {gift.quantity && (
          <p className="text-sm text-gray-600">Quantity: {gift.quantity}</p>
        )}
        {gift.price && (
          <p className="text-sm text-indigo-600 font-semibold">{gift.price} credits</p>
        )}
        {gift.is_giftable && (
          <span className="inline-block mt-2 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
            Giftable
          </span>
        )}
        {onAction && (
          <motion.button
            onClick={() => onAction(gift)}
            className="mt-3 px-4 py-2 bg-gradient-to-r from-indigo-500 to-purple-600 text-white text-sm rounded-lg hover:from-indigo-600 hover:to-purple-700 transition-all duration-300"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {actionLabel}
          </motion.button>
        )}
      </div>
    </motion.div>
  );
};

// Loading Skeleton for Gift Cards
export const GiftCardSkeleton = () => {
  return (
    <div className="bg-white/60 backdrop-blur-sm border border-white/30 rounded-xl p-4 animate-pulse">
      <div className="text-center">
        <div className="w-16 h-16 bg-gray-300 rounded-full mx-auto mb-3"></div>
        <div className="h-4 bg-gray-300 rounded mb-2 w-3/4 mx-auto"></div>
        <div className="h-3 bg-gray-300 rounded w-1/2 mx-auto"></div>
      </div>
    </div>
  );
};

// Statistics Card Component
export const StatCard = ({ title, value, color = "blue", icon }) => {
  const colorClasses = {
    blue: "from-blue-500 to-blue-600 text-blue-100",
    purple: "from-purple-500 to-purple-600 text-purple-100",
    green: "from-green-500 to-green-600 text-green-100",
    orange: "from-orange-500 to-orange-600 text-orange-100",
    red: "from-red-500 to-red-600 text-red-100",
    indigo: "from-indigo-500 to-indigo-600 text-indigo-100"
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className={`bg-gradient-to-br ${colorClasses[color]} text-white p-4 rounded-xl`}
    >
      <div className="flex items-center justify-between">
        <div>
          <div className="text-2xl font-bold">{value || 0}</div>
          <div className={`text-sm ${colorClasses[color].split(' ')[2]}`}>{title}</div>
        </div>
        {icon && (
          <div className="text-white/70">
            {icon}
          </div>
        )}
      </div>
    </motion.div>
  );
};

// Empty State Component
export const EmptyState = ({ 
  icon, 
  title, 
  description, 
  actionLabel, 
  onAction,
  className = "" 
}) => {
  return (
    <div className={`text-center py-12 ${className}`}>
      <div className="w-24 h-24 bg-gradient-to-br from-gray-200 to-gray-300 rounded-full mx-auto mb-4 flex items-center justify-center">
        {icon || (
          <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
          </svg>
        )}
      </div>
      <h3 className="text-xl font-semibold text-gray-700 mb-2">{title}</h3>
      <p className="text-gray-500 mb-6">{description}</p>
      {onAction && actionLabel && (
        <motion.button
          onClick={onAction}
          className="px-6 py-3 bg-gradient-to-r from-indigo-500 to-purple-600 text-white rounded-xl hover:from-indigo-600 hover:to-purple-700 transition-all duration-300"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          {actionLabel}
        </motion.button>
      )}
    </div>
  );
};

// Error State Component
export const ErrorState = ({ 
  title = "Something went wrong", 
  message, 
  onRetry,
  className = "" 
}) => {
  return (
    <div className={`bg-red-50 border border-red-200 rounded-xl p-6 text-center ${className}`}>
      <div className="flex flex-col items-center space-y-4">
        <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
          <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <div>
          <h3 className="text-lg font-semibold text-red-800 mb-2">{title}</h3>
          <p className="text-red-600 mb-4">{message}</p>
          {onRetry && (
            <motion.button
              onClick={onRetry}
              className="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Try Again
            </motion.button>
          )}
        </div>
      </div>
    </div>
  );
};

// Search Bar Component
export const SearchBar = ({ 
  value, 
  onChange, 
  placeholder = "Search gifts...", 
  className = "" 
}) => {
  return (
    <div className={`relative ${className}`}>
      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
        <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
        </svg>
      </div>
      <input
        type="text"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg leading-5 bg-white/80 backdrop-blur-sm placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
        placeholder={placeholder}
      />
    </div>
  );
};

// Filter Chip Component
export const FilterChip = ({ 
  label, 
  active = false, 
  onClick, 
  onRemove,
  className = "" 
}) => {
  return (
    <motion.div
      className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium transition-all duration-200 ${
        active 
          ? 'bg-indigo-100 text-indigo-800 border border-indigo-200' 
          : 'bg-gray-100 text-gray-700 border border-gray-200 hover:bg-gray-200'
      } ${className}`}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
    >
      <span onClick={onClick} className="cursor-pointer">
        {label}
      </span>
      {active && onRemove && (
        <button
          onClick={onRemove}
          className="ml-2 text-indigo-600 hover:text-indigo-800"
        >
          <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      )}
    </motion.div>
  );
};
