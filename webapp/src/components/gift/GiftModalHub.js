import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import GiftInventoryManager from './GiftInventoryManager';
import GiftShopManager from './GiftShopManager';
import GiftTransactionManager from './GiftTransactionManager';

const GiftModalHub = () => {
  const [activeTab, setActiveTab] = useState('inventory');

  // Listen for tab switch events from child components
  useEffect(() => {
    const handleTabSwitch = (event) => {
      if (event.detail && event.detail.tab) {
        setActiveTab(event.detail.tab);
      }
    };

    window.addEventListener('switchGiftTab', handleTabSwitch);
    return () => window.removeEventListener('switchGiftTab', handleTabSwitch);
  }, []);

  const tabs = [
    {
      id: 'inventory',
      label: 'My Gifts',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
        </svg>
      ),
      description: 'Manage your gift inventory',
      component: GiftInventoryManager
    },
    {
      id: 'shop',
      label: 'Gift Shop',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z" />
        </svg>
      ),
      description: 'Browse and purchase gifts',
      component: GiftShopManager
    },
    {
      id: 'history',
      label: 'History',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      ),
      description: 'View transaction history and analytics',
      component: GiftTransactionManager
    }
  ];

  const ActiveComponent = tabs.find(tab => tab.id === activeTab)?.component || GiftInventoryManager;

  return (
    <div className="w-full h-full">
      {/* Modal-optimized header with tabs */}
      <div className="border-b border-gray-200 bg-gradient-to-r from-indigo-900 via-indigo-800 to-blue-900">
        {/* Tab navigation */}
        <div className="relative flex">
          {tabs.map((tab) => (
            <motion.button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex-1 px-4 py-3 bg-transparent text-center transition-all duration-300 ${
                activeTab === tab.id
                  ? 'text-white bg-white/20 backdrop-blur-sm'
                  : 'text-white/70 hover:bg-transparent hover:text-white hover:bg-white/10'
              } cursor-pointer`}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <div className="flex flex-col bg-transparent items-center space-y-1">
                <div className={`transition-transform duration-300 ${
                  activeTab === tab.id ? 'scale-110' : 'scale-100'
                }`}>
                  {tab.icon}
                </div>
                <div>
                  <div className="font-semibold text-sm">{tab.label}</div>
                  <div className="text-xs opacity-75 hidden sm:block">{tab.description}</div>
                </div>
              </div>

              {/* Active tab indicator */}
              {activeTab === tab.id && (
                <motion.div
                  layoutId="activeTabModal"
                  className="absolute bottom-0 left-0 right-0 h-1 bg-white rounded-t-full"
                  initial={false}
                  transition={{ type: "spring", stiffness: 500, damping: 30 }}
                />
              )}
            </motion.button>
          ))}
        </div>
      </div>

      {/* Tab Content - Modal optimized */}
      <div className="p-4 bg-gradient-to-br from-indigo-50/30 via-white to-purple-50/30 min-h-[500px]">
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            <ActiveComponent />
          </motion.div>
        </AnimatePresence>
      </div>


    </div>
  );
};

export default GiftModalHub;
