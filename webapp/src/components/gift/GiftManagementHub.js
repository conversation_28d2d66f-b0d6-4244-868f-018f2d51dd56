import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import GiftInventoryManager from './GiftInventoryManager';
import GiftShopManager from './GiftShopManager';
import GiftTransactionManager from './GiftTransactionManager';

const GiftManagementHub = () => {
  const [activeTab, setActiveTab] = useState('inventory');

  const tabs = [
    {
      id: 'inventory',
      label: 'My Gifts',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
        </svg>
      ),
      description: 'Manage your gift inventory',
      component: GiftInventoryManager
    },
    {
      id: 'shop',
      label: 'Gift Shop',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z" />
        </svg>
      ),
      description: 'Browse and purchase gifts',
      component: GiftShopManager
    },
    {
      id: 'history',
      label: 'History',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      ),
      description: 'View transaction history and analytics',
      component: GiftTransactionManager
    }
  ];

  const ActiveComponent = tabs.find(tab => tab.id === activeTab)?.component || GiftInventoryManager;

  return (
    <div className="min-h-screen bg-gradient-to-r from-indigo-600 via-indigo-600 to-blue-600">
      <div className="container mx-auto px-4 py-8">
        {/* Main Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 bg-clip-text text-transparent mb-4">
            🎁 Gift Management Center
          </h1>
          <p className="text-gray-600 text-lg max-w-2xl mx-auto">
            Manage your gift collection, discover new items, and track your gift transactions all in one place.
          </p>
        </div>

        {/* Tab Navigation */}
        <div className="bg-white backdrop-blur-md rounded-2xl shadow-xl overflow-hidden mb-8">
          {/* Background gradient */}
          <div className="relative">
            <div className="absolute inset-0 h-full w-full bg-gradient-to-r from-indigo-600 via-indigo-600 to-blue-600" />
            
            {/* Tab navigation */}
            <div className="relative flex">
              {tabs.map((tab) => (
                <motion.button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex-1 px-6 py-4 bg-transparent text-center transition-all duration-300 ${
                    activeTab === tab.id
                      ? 'text-white bg-white/20 backdrop-blur-sm'
                      : 'text-white/70 bg-transparent hover:bg-transparent hover:text-white hover:bg-white/10'
                  } cursor-pointer`}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <div className="flex flex-col items-center space-y-2">
                    <div className={`transition-transform duration-300 ${
                      activeTab === tab.id ? 'scale-110' : 'scale-100'
                    }`}>
                      {tab.icon}
                    </div>
                    <div>
                      <div className="font-semibold text-sm">{tab.label}</div>
                      <div className="text-xs opacity-75 hidden sm:block">{tab.description}</div>
                    </div>
                  </div>

                  {/* Active tab indicator */}
                  {activeTab === tab.id && (
                    <motion.div
                      layoutId="activeTab"
                      className="absolute bottom-0 left-0 right-0 h-1 bg-white rounded-t-full"
                      initial={false}
                      transition={{ type: "spring", stiffness: 500, damping: 30 }}
                    />
                  )}
                </motion.button>
              ))}
            </div>
          </div>
        </div>

        {/* Tab Content */}
        <div className="bg-white/80 backdrop-blur-md rounded-2xl shadow-xl overflow-hidden">
          <div className="p-6">
            <AnimatePresence mode="wait">
              <motion.div
                key={activeTab}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
              >
                <ActiveComponent />
              </motion.div>
            </AnimatePresence>
          </div>
        </div>

        {/* Help Section */}
        <div className="mt-8 text-center">
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-6 max-w-2xl mx-auto">
            <h3 className="text-lg font-semibold text-blue-800 mb-2">💡 How it works</h3>
            <div className="text-sm text-blue-700 space-y-2">
              <p><strong>My Gifts:</strong> View and manage your gift collection. Sell gifts back for credits or gift them to other users.</p>
              <p><strong>Gift Shop:</strong> Browse available gifts and purchase them using credits or redeem with points.</p>
              <p><strong>History:</strong> Track all your gift transactions including purchases, sales, and gifts sent/received.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GiftManagementHub;
