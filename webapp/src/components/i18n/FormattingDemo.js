import React from 'react';
import useTranslation from '../../hooks/useTranslation';
import {
  formatDate,
  formatShortDate,
  formatLongDate,
  formatTime,
  formatNumber,
  formatCurrency,
  formatPercent,
  formatRelativeTime
} from '../../utils/i18nUtils';

/**
 * Formatting Demo Component
 *
 * This component demonstrates the internationalization formatting capabilities
 * for dates, times, numbers, currencies, and percentages.
 */
const FormattingDemo = () => {
  const { t, currentLanguage } = useTranslation();

  // Sample data for formatting
  const now = new Date();
  const pastDate = new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000); // 3 days ago
  const futureDate = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000); // 7 days in future
  const number = 1234567.89;
  const price = 99.99;
  const percentage = 75;

  return (
    <div className="bg-white rounded-lg shadow-md p-6 max-w-2xl mx-auto">
      <h2 className="text-2xl font-bold text-gray-800 mb-4">
        {t('i18n.formattingDemo.title', 'Internationalization Formatting Demo')}
      </h2>

      <p className="text-gray-600 mb-6">
        {t('i18n.formattingDemo.description', 'This demo shows how dates, times, numbers, and currencies are formatted based on the current language ({{language}}).', { language: currentLanguage })}
      </p>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Date Formatting */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-gray-700 mb-3">
            {t('i18n.formattingDemo.dateFormatting', 'Date Formatting')}
          </h3>

          <div className="space-y-2">
            <div>
              <span className="text-gray-500 text-sm">
                {t('i18n.formattingDemo.shortDate', 'Short Date')}:
              </span>
              <p className="font-medium">{formatShortDate(now)}</p>
            </div>

            <div>
              <span className="text-gray-500 text-sm">
                {t('i18n.formattingDemo.longDate', 'Long Date')}:
              </span>
              <p className="font-medium">{formatLongDate(now)}</p>
            </div>

            <div>
              <span className="text-gray-500 text-sm">
                {t('i18n.formattingDemo.customDate', 'Custom Date Format')}:
              </span>
              <p className="font-medium">
                {formatDate(now, {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'short',
                  day: 'numeric'
                })}
              </p>
            </div>

            <div>
              <span className="text-gray-500 text-sm">
                {t('i18n.formattingDemo.relativeTime', 'Relative Time')}:
              </span>
              <p className="font-medium">
                {t('i18n.formattingDemo.past', 'Past')}: {formatRelativeTime(pastDate)}
              </p>
              <p className="font-medium">
                {t('i18n.formattingDemo.future', 'Future')}: {formatRelativeTime(futureDate)}
              </p>
            </div>
          </div>
        </div>

        {/* Time Formatting */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-gray-700 mb-3">
            {t('i18n.formattingDemo.timeFormatting', 'Time Formatting')}
          </h3>

          <div className="space-y-2">
            <div>
              <span className="text-gray-500 text-sm">
                {t('i18n.formattingDemo.shortTime', 'Short Time')}:
              </span>
              <p className="font-medium">{formatTime(now)}</p>
            </div>

            <div>
              <span className="text-gray-500 text-sm">
                {t('i18n.formattingDemo.longTime', 'Long Time with Seconds')}:
              </span>
              <p className="font-medium">
                {formatTime(now, {
                  hour: 'numeric',
                  minute: 'numeric',
                  second: 'numeric'
                })}
              </p>
            </div>

            <div>
              <span className="text-gray-500 text-sm">
                {t('i18n.formattingDemo.timeWithTimeZone', 'Time with Timezone')}:
              </span>
              <p className="font-medium">
                {formatTime(now, {
                  hour: 'numeric',
                  minute: 'numeric',
                  timeZoneName: 'short'
                })}
              </p>
            </div>
          </div>
        </div>

        {/* Number Formatting */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-gray-700 mb-3">
            {t('i18n.formattingDemo.numberFormatting', 'Number Formatting')}
          </h3>

          <div className="space-y-2">
            <div>
              <span className="text-gray-500 text-sm">
                {t('i18n.formattingDemo.decimal', 'Decimal Number')}:
              </span>
              <p className="font-medium">{formatNumber(number)}</p>
            </div>

            <div>
              <span className="text-gray-500 text-sm">
                {t('i18n.formattingDemo.integer', 'Integer')}:
              </span>
              <p className="font-medium">
                {formatNumber(number, { maximumFractionDigits: 0 })}
              </p>
            </div>

            <div>
              <span className="text-gray-500 text-sm">
                {t('i18n.formattingDemo.percentage', 'Percentage')}:
              </span>
              <p className="font-medium">{formatPercent(percentage)}</p>
            </div>
          </div>
        </div>

        {/* Currency Formatting */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-gray-700 mb-3">
            {t('i18n.formattingDemo.currencyFormatting', 'Currency Formatting')}
          </h3>

          <div className="space-y-2">
            <div>
              <span className="text-gray-500 text-sm">
                {t('i18n.formattingDemo.localCurrency', 'Local Currency (MYR)')}:
              </span>
              <p className="font-medium">{formatCurrency(price)}</p>
            </div>

            <div>
              <span className="text-gray-500 text-sm">
                {t('i18n.formattingDemo.usd', 'US Dollar')}:
              </span>
              <p className="font-medium">{formatCurrency(price, 'USD')}</p>
            </div>

            <div>
              <span className="text-gray-500 text-sm">
                {t('i18n.formattingDemo.eur', 'Euro')}:
              </span>
              <p className="font-medium">{formatCurrency(price, 'EUR')}</p>
            </div>

            <div>
              <span className="text-gray-500 text-sm">
                {t('i18n.formattingDemo.jpy', 'Japanese Yen')}:
              </span>
              <p className="font-medium">{formatCurrency(price, 'JPY')}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FormattingDemo;
