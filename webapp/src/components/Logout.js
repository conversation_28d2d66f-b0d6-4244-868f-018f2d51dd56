// Logout.js
import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FiLogOut, FiX } from 'react-icons/fi';

const LogoutConfirmation = ({ isOpen, onClose, onLogout }) => {
  const modalRef = useRef();

  // Focus trap for accessibility
  useEffect(() => {
    if (isOpen && modalRef.current) {
      modalRef.current.focus();
    }
  }, [isOpen]);

  const handleKeyDown = (e) => {
    if (e.key === 'Escape') onClose();
    if (e.key === 'Enter' && document.activeElement === modalRef.current) onLogout();
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 flex items-center justify-center z-[100]">
          {/* Backdrop */}
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/30 backdrop-blur-sm"
            onClick={onClose}
          />

          {/* Modal */}
          <motion.div
            ref={modalRef}
            tabIndex="0"
            onKeyDown={handleKeyDown}
            initial={{ y: -20, opacity: 0, scale: 0.95 }}
            animate={{ y: 0, opacity: 1, scale: 1 }}
            exit={{ y: 20, opacity: 0, scale: 0.95 }}
            transition={{ type: "spring", damping: 25, stiffness: 300 }}
            className="relative w-full max-w-md p-6 rounded-2xl 
              bg-gradient-to-br from-white/90 to-indigo-50/30
              shadow-xl shadow-indigo-500/10
              border border-white/30
              backdrop-blur-xl
              space-y-6
              mx-4"
          >
            {/* Close Button */}
            <button
              onClick={onClose}
              className="absolute right-4 top-4 p-2 rounded-full
                hover:bg-white/20 bg-red-200 transition-colors duration-200
                focus:outline-none focus:ring-2 focus:ring-white/40"
              aria-label="Close confirmation dialog"
            >
              <FiX className="w-5 h-5 text-gray-700" />
            </button>

            {/* Icon */}
            <div className="flex justify-center mt-2">
              <div className="p-3 rounded-full bg-gradient-to-br from-red-100 to-pink-100/50">
                <FiLogOut className="w-8 h-8 text-red-500" />
              </div>
            </div>

            {/* Content */}
            <div className="text-center space-y-2">
              <h3 className="text-2xl font-semibold text-gray-800">
                Log out of your account?
              </h3>
              <p className="text-gray-600 text-sm px-4">
                Are you sure you want to end your current session? Any unsaved changes will be lost.
              </p>
            </div>

            {/* Action Buttons */}
            <div className="grid grid-cols-2 gap-4 pt-2">
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={onClose}
                className="px-4 py-2.5 rounded-xl
                  bg-white/60 hover:bg-white/80
                  text-gray-700 font-medium
                  transition-all duration-200
                  border border-white/50 hover:border-white/70
                  backdrop-blur-sm
                  focus:outline-none focus:ring-2 focus:ring-gray-300/50"
              >
                Cancel
              </motion.button>

              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={onLogout}
                className="px-4 py-2.5 rounded-xl
                  bg-gradient-to-r from-red-500 to-pink-500
                  hover:from-red-600 hover:to-pink-600
                  text-white font-medium
                  shadow-md shadow-red-500/20
                  transition-all duration-200
                  focus:outline-none focus:ring-2 focus:ring-red-400/60"
              >
                Log Out
              </motion.button>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
};

export default LogoutConfirmation;