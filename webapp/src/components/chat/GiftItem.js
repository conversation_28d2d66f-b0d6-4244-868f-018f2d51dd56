import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '../../lib/utils';

/**
 * GiftItem component displays a virtual gift with optional pricing information
 * 
 * @param {Object} props
 * @param {Object} props.gift - Gift object with id, name, image_url, price, owned, and quantity
 * @param {boolean} props.showPrice - Whether to show the price of the gift
 * @param {boolean} props.isSelected - Whether the gift is currently selected
 * @param {Function} props.onClick - Click handler for the gift item
 * @param {string} props.size - Size of the gift ('sm', 'md', or 'lg')
 * @param {boolean} props.animate - Whether to animate the gift
 * @param {boolean} props.disabled - Whether the gift is disabled (e.g., insufficient balance)
 * @param {Function} props.onPurchase - Handler for purchasing the gift (optional)
 */
const GiftItem = ({ 
    gift, 
    showPrice = true, 
    isSelected = false, 
    onClick, 
    size = 'md',
    animate = false,
    disabled = false,
    onPurchase = null
}) => {
    const sizeClasses = {
        sm: 'w-14 h-14',
        md: 'w-20 h-20',
        lg: 'w-28 h-28',
    };

    // Price formatting helper
    const formatPrice = (price) => {
        return new Intl.NumberFormat('en-US', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
        }).format(price);
    };

    // Animation variants
    const giftAnimation = animate ? {
        initial: { scale: 0.8, opacity: 0, y: 20 },
        animate: { 
            scale: 1, 
            opacity: 1, 
            y: 0,
            transition: { 
                type: 'spring', 
                stiffness: 300, 
                damping: 15 
            }
        },
        hover: { scale: 1.05, y: -5 },
        tap: { scale: 0.95 }
    } : {};

    // For large gifts in messages, add a special animation
    const messageAnimation = size === 'lg' && animate ? {
        initial: { scale: 0.1, opacity: 0, rotate: -10 },
        animate: {
            scale: 1,
            opacity: 1,
            rotate: 0,
            transition: {
                type: 'spring',
                stiffness: 200,
                damping: 20,
                delay: 0.2
            }
        }
    } : {};

    // Handle purchase click without triggering the main click
    const handlePurchaseClick = (e) => {
        e.stopPropagation();
        if (onPurchase) {
            onPurchase(gift);
        }
    };

    return (
        <motion.div
            className={cn(
                "flex flex-col items-center justify-center p-2 cursor-pointer transition-transform rounded-lg relative",
                isSelected ? "bg-indigo-50 border-2 border-indigo-500" : "bg-white border border-gray-200",
                disabled && "opacity-60 cursor-not-allowed",
                onClick && "hover:shadow-md"
            )}
            onClick={disabled ? undefined : onClick}
            initial={animate ? "initial" : undefined}
            animate={animate ? "animate" : undefined}
            whileHover={!disabled && animate ? "hover" : undefined}
            whileTap={!disabled && animate ? "tap" : undefined}
            variants={giftAnimation}
        >
            {/* Owned badge */}
            {gift.owned && (
                <div className="absolute -top-1 -right-1 bg-green-500 text-white text-xs font-bold px-1.5 py-0.5 rounded-full z-10">
                    {gift.quantity > 1 ? `x${gift.quantity}` : 'Owned'}
                </div>
            )}
            
            <motion.div
                className={cn(
                    "relative flex items-center justify-center bg-gradient-to-br from-indigo-50 to-purple-50 rounded-md p-2",
                    sizeClasses[size]
                )}
                variants={messageAnimation}
            >
                <img 
                    src={gift.image_url} 
                    alt={gift.name}
                    className={cn(
                        "object-contain w-full h-full transition-all duration-300",
                        disabled && "grayscale"
                    )}
                />
                
                {/* Sparkle effects for large gifts */}
                {size === 'lg' && animate && (
                    <>
                        <span className="absolute top-0 left-0 w-2 h-2 bg-yellow-400 rounded-full animate-ping-slow"/>
                        <span className="absolute bottom-0 right-0 w-3 h-3 bg-indigo-400 rounded-full animate-ping-slow delay-300"/>
                        <span className="absolute top-1/4 right-1/4 w-2 h-2 bg-pink-400 rounded-full animate-ping-slow delay-700"/>
                    </>
                )}
            </motion.div>
            
            <p className={cn(
                "mt-1 text-center font-medium",
                size === 'sm' ? "text-xs" : "text-sm",
                disabled ? "text-gray-400" : "text-gray-700"
            )}>
                {gift.name}
            </p>
            
            <div className="mt-1 flex items-center space-x-2">
                {showPrice && (
                    <div className={cn(
                        "flex items-center justify-center",
                        size === 'sm' ? "text-xs" : "text-sm",
                        disabled ? "text-gray-400" : "text-indigo-600 font-semibold"
                    )}>
                        <span className="mr-1">⭐</span>
                        {formatPrice(gift.price)}
                    </div>
                )}
                
                {/* Purchase button for gifts not owned */}
                {onPurchase && !gift.owned && size !== 'sm' && (
                    <button
                        onClick={handlePurchaseClick}
                        disabled={disabled}
                        className={cn(
                            "text-xs px-2 py-0.5 rounded-full",
                            disabled 
                                ? "bg-gray-200 text-gray-500" 
                                : "bg-indigo-500 text-white hover:bg-indigo-600 transition-colors"
                        )}
                    >
                        Buy
                    </button>
                )}
            </div>
            
            {/* Limited time or special badge */}
            {gift.is_limited && (
                <div className="absolute top-0 left-0 bg-amber-500 text-white text-xs font-bold px-1.5 py-0.5 rounded-tr-lg rounded-bl-lg">
                    Limited
                </div>
            )}
            
            {/* For sale badge (special discount) */}
            {gift.on_sale && (
                <div className="absolute bottom-10 left-1/2 transform -translate-x-1/2 bg-red-500 text-white text-xs font-bold px-2 py-0.5 rounded-full">
                    SALE
                </div>
            )}
        </motion.div>
    );
};

export default GiftItem; 