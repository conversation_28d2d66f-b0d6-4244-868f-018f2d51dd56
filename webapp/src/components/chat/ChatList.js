import React, { useEffect, useState, useMemo } from 'react';
import { chatApi } from '../../services/chatApi';
import { chatWebSocket } from '../../services/chatWebSocket';
import { DateTime } from 'luxon';
import { Avatar, AvatarImage, AvatarFallback } from "../../components/ui/avatar";
import { Button } from "../../components/ui/button";
import { ScrollArea } from "../../components/ui/scroll-area";
import { HoverCard, HoverCardTrigger, HoverCardContent } from "../../components/ui/hover-card";
import {
    HeartIcon,
    MagnifyingGlassIcon,
    GearIcon,
    Pencil1Icon,
    DotsVerticalIcon,
    ExclamationTriangleIcon,
    TrashIcon,
    EnvelopeClosedIcon,
    ChatBubbleIcon
} from '@radix-ui/react-icons';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from "../../lib/utils";
import { Command, CommandInput, CommandList, CommandEmpty, CommandGroup, CommandItem } from "../../components/ui/command";
import { Badge } from "../../components/ui/badge";
import { Skeleton } from "../../components/ui/skeleton";
import ChatWindow from './ChatWindow';
import { getCdnUrl } from '../../utils/cdnUtils';

const listItemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
    hover: { scale: 1.02, backgroundColor: "var(--accent)" }
};

const ChatListHeader = ({
    searchQuery,
    setSearchQuery,
    filteredChats,
    onChatSelect,
    onToggleFavorite,
    onArchiveChat,
    onDeleteChat,
    selectedChat,
    favorites,
    filter,
    onFilterChange,
    archivedMode,
    setArchivedMode
}) => {
    const [isSearching, setIsSearching] = useState(false);
    const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
    const [chatToDelete, setChatToDelete] = useState(null);
    const [showAdvancedSearch, setShowAdvancedSearch] = useState(false);
    const [advancedFilters, setAdvancedFilters] = useState({
        hasAttachments: false,
        hasUnread: false,
        dateRange: 'all',
        sortBy: 'recent'
    });

    const handleDelete = () => {
        if (chatToDelete) {
            onDeleteChat(chatToDelete);
            setChatToDelete(null);
            setShowDeleteConfirm(false);
        }
    };

    const toggleAdvancedSearch = () => {
        setShowAdvancedSearch(prev => !prev);
    };

    const updateAdvancedFilter = (key, value) => {
        setAdvancedFilters(prev => ({
            ...prev,
            [key]: value
        }));
    };

    return (
        <div className="sticky top-0 z-20 bg-white/95 backdrop-blur-lg border-b border-gray-100 shadow-sm">
            <div className="px-4 pt-4 pb-3">
                <div className="flex items-center justify-between mb-4">
                    <h1 className="text-2xl font-bold bg-gradient-to-r from-indigo-600 to-violet-500
                        bg-clip-text text-transparent flex items-center">
                        {archivedMode ? "Archived" : "Messages"}
                        <span className="ml-2 flex h-2 w-2">
                            <span className="absolute inline-flex h-2 w-2 rounded-full bg-indigo-400 opacity-75"></span>
                            <span className="relative inline-flex rounded-full h-2 w-2 bg-indigo-500"></span>
                        </span>
                    </h1>
                    <div className="flex items-center space-x-1">
                        <Button
                            variant="ghost"
                            size="icon"
                            className="rounded-full bg-pink-50 text-pink-600 hover:text-gray-600
                                transition-colors duration-200"
                            onClick={() => onToggleFavorite(selectedChat?.id)}
                            disabled={!selectedChat}
                        >
                            <HeartIcon className={cn(
                                "w-7 h-7",
                                favorites?.includes(selectedChat?.id) && "fill-current"
                            )} />
                        </Button>
                        <Button
                            variant="ghost"
                            size="icon"
                            className="rounded-full bg-gray-50 text-gray-600 hover:text-gray-800
                                transition-colors duration-200"
                            onClick={() => {
                                if (selectedChat) {
                                    onArchiveChat(selectedChat.id);
                                }
                            }}
                            disabled={!selectedChat}
                            title={archivedMode ? "Unarchive" : "Archive"}
                        >
                            {archivedMode ? (
                                <svg className="w-7 h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
                                </svg>
                            ) : (
                                <svg className="w-7 h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
                                </svg>
                            )}
                        </Button>
                        <Button
                            variant="ghost"
                            size="icon"
                            className="rounded-full bg-red-50 text-red-600 hover:text-gray-600
                                transition-colors duration-200"
                            onClick={() => {
                                setChatToDelete(selectedChat?.id);
                                setShowDeleteConfirm(true);
                            }}
                            disabled={!selectedChat}
                        >
                            <TrashIcon className="w-7 h-7"/>
                        </Button>
                    </div>
                </div>

                {/* Delete Confirmation Dialog */}
                {showDeleteConfirm && (
                    <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
                        <div className="bg-white rounded-lg p-6 max-w-sm w-full mx-4 shadow-xl">
                            <h3 className="text-lg font-semibold text-gray-900 mb-2">Delete Conversation</h3>
                            <p className="text-gray-500 mb-4">
                                Are you sure you want to delete this conversation? This action cannot be undone.
                            </p>
                            <div className="flex justify-end space-x-2">
                                <Button
                                    variant="ghost"
                                    onClick={() => {
                                        setChatToDelete(null);
                                        setShowDeleteConfirm(false);
                                    }}
                                >
                                    Cancel
                                </Button>
                                <Button
                                    variant="destructive"
                                    onClick={handleDelete}
                                >
                                    Delete
                                </Button>
                            </div>
                        </div>
                    </div>
                )}

                {/* Unified Search with Command Menu */}
                <div className="flex items-center">
                    <Command className="rounded-xl border-0 shadow-none bg-gray-50/80 flex-1">
                        <CommandInput
                            placeholder="Search messages or people..."
                            value={searchQuery}
                            onValueChange={(value) => {
                                setSearchQuery(value);
                                setIsSearching(value.length > 0);
                            }}
                            onBlur={() => setTimeout(() => setIsSearching(false), 200)}
                            onFocus={() => setIsSearching(true)}
                            className="h-11 rounded-lg border-0 bg-transparent focus:ring-2
                                focus:ring-indigo-500/20 transition-all duration-200"
                            leadingIcon={<MagnifyingGlassIcon className="w-4 h-4 text-gray-500" />}
                        />
                        {isSearching && (
                            <CommandList className="absolute mt-1 w-full bg-white rounded-lg shadow-lg
                                border border-gray-100 animate-in fade-in-0 zoom-in-95">
                                {!searchQuery ? (
                                    <CommandGroup heading="Recent Searches">
                                        <div className="px-2 py-3 text-sm text-gray-500">
                                            Start typing to search...
                                        </div>
                                    </CommandGroup>
                                ) : (
                                    <>
                                        <CommandEmpty className="p-4 text-sm text-gray-500">
                                            No results found for "{searchQuery}"
                                        </CommandEmpty>
                                        {filteredChats.length > 0 && (
                                            <CommandGroup heading="Chats">
                                                {filteredChats.map((chat) => (
                                                    <CommandItem
                                                        key={chat.id}
                                                        className="flex items-center px-2 py-2 cursor-pointer hover:bg-gray-50"
                                                        onSelect={() => onChatSelect(chat)}
                                                    >
                                                        <Avatar
                                                            src={getCdnUrl(chat.user?.profile_image || chat.talent?.profile_image)}
                                                            fallback={chat.user?.nickname?.[0] || chat.talent?.nickname?.[0]}
                                                            className="h-8 w-8 mr-2"
                                                        />
                                                        <div className="flex-1 min-w-0">
                                                            <p className="text-sm font-medium text-gray-900 truncate">
                                                                {chat.user?.nickname || chat.talent?.nickname}
                                                            </p>
                                                            <p className="text-xs text-gray-500 truncate">
                                                                {chat.last_message}
                                                            </p>
                                                        </div>
                                                    </CommandItem>
                                                ))}
                                            </CommandGroup>
                                        )}
                                    </>
                                )}
                            </CommandList>
                        )}
                    </Command>
                    <Button
                        variant="ghost"
                        size="icon"
                        className="ml-2 rounded-lg p-3 bg-gray-50/80 text-gray-600 hover:text-indigo-600
                            transition-colors duration-200"
                        onClick={toggleAdvancedSearch}
                    >
                        <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <path d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" strokeLinecap="round" strokeLinejoin="round" />
                        </svg>
                    </Button>
                </div>

                {/* Advanced Search Options */}
                {showAdvancedSearch && (
                    <div className="bg-white rounded-lg border border-gray-200 p-3 mt-2 space-y-3 shadow-sm">
                        <div className="grid grid-cols-2 gap-2">
                            <div>
                                <label className="text-xs font-medium text-gray-700 mb-1 block">Sort by</label>
                                <select
                                    className="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 text-sm"
                                    value={advancedFilters.sortBy}
                                    onChange={(e) => updateAdvancedFilter('sortBy', e.target.value)}
                                >
                                    <option value="recent">Most Recent</option>
                                    <option value="oldest">Oldest First</option>
                                    <option value="unread">Unread First</option>
                                    <option value="alphabetical">Alphabetical</option>
                                </select>
                            </div>
                            <div>
                                <label className="text-xs font-medium text-gray-700 mb-1 block">Date Range</label>
                                <select
                                    className="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 text-sm"
                                    value={advancedFilters.dateRange}
                                    onChange={(e) => updateAdvancedFilter('dateRange', e.target.value)}
                                >
                                    <option value="all">All Time</option>
                                    <option value="today">Today</option>
                                    <option value="week">This Week</option>
                                    <option value="month">This Month</option>
                                </select>
                            </div>
                        </div>
                        <div className="flex items-center space-x-4">
                            <label className="flex items-center text-sm text-gray-700">
                                <input
                                    type="checkbox"
                                    className="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-200 focus:ring-opacity-50 mr-2"
                                    checked={advancedFilters.hasAttachments}
                                    onChange={(e) => updateAdvancedFilter('hasAttachments', e.target.checked)}
                                />
                                Has Attachments
                            </label>
                            <label className="flex items-center text-sm text-gray-700">
                                <input
                                    type="checkbox"
                                    className="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-200 focus:ring-opacity-50 mr-2"
                                    checked={advancedFilters.hasUnread}
                                    onChange={(e) => updateAdvancedFilter('hasUnread', e.target.checked)}
                                />
                                Unread Only
                            </label>
                        </div>
                        <div className="flex justify-between pt-2">
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                    setAdvancedFilters({
                                        hasAttachments: false,
                                        hasUnread: false,
                                        dateRange: 'all',
                                        sortBy: 'recent'
                                    });
                                }}
                            >
                                Reset Filters
                            </Button>
                            <Button
                                variant="default"
                                size="sm"
                                onClick={() => setShowAdvancedSearch(false)}
                            >
                                Apply Filters
                            </Button>
                        </div>
                    </div>
                )}

                {/* Chat Filters */}
                <div className="flex space-x-2 mt-3 justify-center overflow-x-auto pb-2 scrollbar-none">
                    <Button
                        variant="ghost"
                        size="sm"
                        className={cn(
                            "rounded-full px-4 py-2 text-sm font-medium whitespace-nowrap",
                            filter === 'all'
                                ? "bg-indigo-100 text-indigo-600"
                                : "bg-white text-gray-600 hover:bg-indigo-50"
                        )}
                        onClick={() => onFilterChange('all')}
                    >
                        All Messages
                    </Button>
                    <Button
                        variant="ghost"
                        size="sm"
                        className={cn(
                            "rounded-full px-4 py-2 text-sm font-medium whitespace-nowrap",
                            filter === 'unread'
                                ? "bg-indigo-100 text-indigo-600"
                                : "bg-white text-gray-600 hover:bg-indigo-50"
                        )}
                        onClick={() => onFilterChange('unread')}
                    >
                        Unread
                        {filteredChats.filter(chat => chat.unread_count > 0).length > 0 && (
                            <Badge className="ml-2 bg-red-500 text-white">
                                {filteredChats.filter(chat => chat.unread_count > 0).length}
                            </Badge>
                        )}
                    </Button>
                    <Button
                        variant="ghost"
                        size="sm"
                        className={cn(
                            "rounded-full px-4 py-2 text-sm font-medium whitespace-nowrap",
                            filter === 'favorites'
                                ? "bg-pink-100 text-pink-600"
                                : "bg-white text-gray-600 hover:bg-pink-50"
                        )}
                        onClick={() => onFilterChange('favorites')}
                    >
                        Favorites
                        {favorites?.length > 0 && (
                            <Badge className="ml-2 bg-pink-500 text-white">
                                {favorites.length}
                            </Badge>
                        )}
                    </Button>
                    <Button
                        variant="ghost"
                        size="sm"
                        className={cn(
                            "rounded-full px-4 py-2 text-sm font-medium whitespace-nowrap",
                            filter === 'missions'
                                ? "bg-indigo-100 text-indigo-600"
                                : "bg-white text-gray-600 hover:bg-indigo-50"
                        )}
                        onClick={() => onFilterChange('missions')}
                    >
                        Missions
                    </Button>

                    <Button
                        variant="ghost"
                        size="sm"
                        className={cn(
                            "rounded-full px-4 py-2 text-sm font-medium whitespace-nowrap",
                            archivedMode
                                ? "bg-amber-100 text-amber-600"
                                : "bg-white text-gray-600 hover:bg-amber-50"
                        )}
                        onClick={() => setArchivedMode(!archivedMode)}
                    >
                        {archivedMode ? "Active" : "Archived"}
                    </Button>
                </div>
            </div>
        </div>
    );
};

// Quick reply suggestions based on message content
const QUICK_REPLIES = {
    greeting: ["Hi! How are you?", "Hello! Nice to meet you", "Hey there!"],
    question: ["Let me check that for you", "I will look into it", "Good question!"],
    thanks: ["You are welcome!", "No problem at all", "Glad I could help!"],
    goodbye: ["Goodbye!", "Take care!", "Talk to you later!"],
    default: ["I will get back to you soon", "Thanks for your message", "Let me help you with that"]
};

const getQuickReplies = (lastMessage) => {
    if (!lastMessage) return QUICK_REPLIES.default;

    const text = lastMessage.toLowerCase();
    if (text.match(/\b(hi|hello|hey|good morning|good evening)\b/)) return QUICK_REPLIES.greeting;
    if (text.includes('?')) return QUICK_REPLIES.question;
    if (text.match(/\b(thanks|thank you|thx)\b/)) return QUICK_REPLIES.thanks;
    if (text.match(/\b(bye|goodbye|see you|talk to you later)\b/)) return QUICK_REPLIES.goodbye;
    return QUICK_REPLIES.default;
};

const ChatList = ({ selectedChat, onChatSelect, favorites = [], onToggleFavorite }) => {
    const [chats, setChats] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [page, setPage] = useState(1);
    const [hasMore, setHasMore] = useState(true);
    const [searchQuery, setSearchQuery] = useState('');
    const [filter, setFilter] = useState('all');
    const [archivedMode, setArchivedMode] = useState(false);

    // Add state for archived conversations
    const [archivedChats, setArchivedChats] = useState(() => {
        // Initialize from localStorage
        const saved = localStorage.getItem('archivedChats');
        return saved ? JSON.parse(saved) : [];
    });

    // Function to archive/unarchive a chat
    const handleArchiveChat = (chatId) => {
        if (archivedMode) {
            // We're in archived mode, so unarchive the chat
            setArchivedChats(prev => prev.filter(id => id !== chatId));
        } else {
            // Archive the chat
            setArchivedChats(prev => [...prev, chatId]);
        }

        // If the archived chat is selected, deselect it
        if (selectedChat?.id === chatId && !archivedMode) {
            onChatSelect(null);
        }
    };

    // Save archived chats to localStorage
    useEffect(() => {
        localStorage.setItem('archivedChats', JSON.stringify(archivedChats));
    }, [archivedChats]);

    const handleDeleteChat = async (chatId) => {
        try {
            // In a production environment, call API to delete chat
            // await chatApi.deleteConversation(chatId);

            // Remove from local state
            setChats(prev => prev.filter(chat => chat.id !== chatId));

            // If chat was archived, remove from archived list too
            if (archivedChats.includes(chatId)) {
                setArchivedChats(prev => prev.filter(id => id !== chatId));
            }

            // If deleted chat was selected, deselect it
            if (selectedChat?.id === chatId) {
                onChatSelect(null);
            }

        } catch (error) {
            console.error('Error deleting chat:', error);
        }
    };

    // Format time helper function with smart relative time
    const formatTimeAgo = (dateStr) => {
        const date = DateTime.fromISO(dateStr);
        return date.toRelative();
    };

    // Format date for grouping
    const formatMessageDate = (dateStr) => {
        try {
            const date = DateTime.fromISO(dateStr);
            const now = DateTime.now();

            if (!date.isValid) return 'Unknown date';

            if (date.hasSame(now, 'day')) return 'Today';
            if (date.hasSame(now.minus({ days: 1 }), 'day')) return 'Yesterday';
            if (date.hasSame(now, 'week')) return date.toFormat('cccc');
            if (date.hasSame(now, 'year')) return date.toFormat('LLLL d');
            return date.toFormat('LLLL d, yyyy');
        } catch (error) {
            console.error('Date parsing error:', error);
            return 'Unknown date';
        }
    };

    // Load conversations with pagination
    const loadConversations = async () => {
        try {
            setLoading(true);
            const response = await chatApi.getConversations(page);
            const { data } = response.data;

            setChats(prevChats =>
                page === 1 ? data : [...prevChats, ...data]
            );
            setHasMore(data.length === 15);
            setError(null);
        } catch (err) {
            setError('Failed to load conversations');
            console.error('Error loading conversations:', err);
        } finally {
            setLoading(false);
        }
    };

    // Update the WebSocket event handling to use .on() and .off()
    useEffect(() => {
        loadConversations();

        // WebSocket event listeners for real-time updates
        const handleNewMessage = (message) => {
            setChats(prevChats => {
                const chatIndex = prevChats.findIndex(c => c.id === message.conversation_id);
                if (chatIndex === -1) return prevChats;

                const updatedChats = [...prevChats];
                const chat = { ...updatedChats[chatIndex] };

                chat.last_message = message.content;
                chat.last_message_at = message.created_at;
                chat.unread_count = selectedChat?.id === chat.id ? 0 : (chat.unread_count || 0) + 1;

                updatedChats[chatIndex] = chat;
                return updatedChats;
            });
        };

        const handleStatusUpdate = ({ conversationId, status }) => {
            setChats(prevChats =>
                prevChats.map(chat =>
                    chat.id === conversationId
                        ? { ...chat, last_message_status: status }
                        : chat
                )
            );
        };

        // Register WebSocket event handlers using .on()
        chatWebSocket.on('message', handleNewMessage);
        chatWebSocket.on('status_update', handleStatusUpdate);

        return () => {
            // Clean up event listeners using .off()
            chatWebSocket.off('message', handleNewMessage);
            chatWebSocket.off('status_update', handleStatusUpdate);
        };
    }, [page, selectedChat]);

    // Handle scroll for infinite loading
    const handleScroll = (e) => {
        const { scrollTop, clientHeight, scrollHeight } = e.target;
        if (scrollHeight - scrollTop <= clientHeight * 1.5 && !loading && hasMore) {
            setPage(prev => prev + 1);
        }
    };

    // Filter chats based on current filter and search query
    const filteredChats = useMemo(() => {
        let result = [...chats];

        // Filter by archived status
        if (archivedMode) {
            result = result.filter(chat => archivedChats.includes(chat.id));
        } else {
            result = result.filter(chat => !archivedChats.includes(chat.id));
        }

        // Apply text search
        if (searchQuery) {
            const query = searchQuery.toLowerCase();
            result = result.filter(chat => {
                const nickname = chat.user?.nickname || chat.talent?.nickname || chat.title || '';
                const lastMessage = chat.last_message || '';
                return nickname.toLowerCase().includes(query) ||
                       lastMessage.toLowerCase().includes(query);
            });
        }

        // Apply filter
        if (filter === 'unread') {
            result = result.filter(chat => chat.unread_count > 0);
        } else if (filter === 'favorites') {
            result = result.filter(chat => favorites.includes(chat.id));
        } else if (filter === 'missions') {
            result = result.filter(chat => chat.metadata?.type === 'mission');
        }

        return result;
    }, [chats, searchQuery, filter, favorites, archivedMode, archivedChats]);

    // Group chats by recency
    const groupedChats = filteredChats.reduce((groups, chat) => {
        const messageDate = new Date(chat.last_message_at);
        const now = new Date();
        const isRecent = (now - messageDate) < 24 * 60 * 60 * 1000; // Less than 24 hours

        if (isRecent) {
            groups.recent.push(chat);
        } else {
            groups.earlier.push(chat);
        }

        return groups;
    }, { recent: [], earlier: [] });

    const handleQuickReply = (chatId, reply) => {
        // In a real app, this would send the message through your chat service
        console.log(`Sending quick reply to chat ${chatId}:`, reply);
    };

    return (
        <div className="flex h-screen bg-gray-50/50">
            {/* Chat List Section */}
            <div className={cn(
                "w-96 bg-white border-r border-gray-100",
                "transition-all duration-300 ease-in-out",
                selectedChat ? 'hidden lg:block' : ''
            )}>
                <ChatListHeader
                    searchQuery={searchQuery}
                    setSearchQuery={setSearchQuery}
                    filteredChats={filteredChats}
                    onChatSelect={onChatSelect}
                    onToggleFavorite={onToggleFavorite}
                    onArchiveChat={handleArchiveChat}
                    onDeleteChat={handleDeleteChat}
                    selectedChat={selectedChat}
                    favorites={favorites}
                    filter={filter}
                    onFilterChange={setFilter}
                    archivedMode={archivedMode}
                    setArchivedMode={setArchivedMode}
                />

                {/* Chat List using shadcn/ui scroll area */}
                <ScrollArea className="h-[calc(100vh-9rem)]">
                    <AnimatePresence mode="wait">
                        {loading ? (
                            <div className="p-4 space-y-4">
                                {[...Array(5)].map((_, i) => (
                                    <div key={i} className="flex items-center space-x-4">
                                        <Skeleton className="h-12 w-12 rounded-full" />
                                        <div className="space-y-2 flex-1">
                                            <Skeleton className="h-4 w-[60%]" />
                                            <Skeleton className="h-3 w-[80%]" />
                                        </div>
                                    </div>
                                ))}
                            </div>
                        ) : error ? (
                            <motion.div
                                initial={{ opacity: 0 }}
                                animate={{ opacity: 1 }}
                                exit={{ opacity: 0 }}
                                className="p-8 text-center"
                            >
                                <div className="inline-flex h-12 w-12 items-center justify-center rounded-full bg-red-100 mb-4">
                                    <ExclamationTriangleIcon className="h-6 w-6 text-red-600" />
                                </div>
                                <h3 className="text-sm font-medium text-gray-900 mb-1">
                                    Failed to load chats
                                </h3>
                                <p className="text-sm text-gray-500 mb-4">
                                    Please try again later or contact support if the problem persists.
                                </p>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                        setPage(1);
                                        loadConversations();
                                    }}
                                >
                                    Try Again
                                </Button>
                            </motion.div>
                        ) : (
                            <div className="py-2">
                                {filteredChats.length > 0 ? (
                                    <>
                                        {/* Recent conversations section */}
                                        {groupedChats.recent.map((chat) => (
                                            <ChatListItem
                                                key={chat.id}
                                                chat={chat}
                                                isSelected={selectedChat?.id === chat.id}
                                                isFavorite={favorites.includes(chat.id)}
                                                isArchived={archivedChats.includes(chat.id)}
                                                onSelect={onChatSelect}
                                                onQuickReply={handleQuickReply}
                                                formatTimeAgo={formatTimeAgo}
                                            />
                                        ))}

                                        {/* Earlier conversations section */}
                                        {groupedChats.earlier.map((chat) => (
                                            <ChatListItem
                                                key={chat.id}
                                                chat={chat}
                                                isSelected={selectedChat?.id === chat.id}
                                                isFavorite={favorites.includes(chat.id)}
                                                isArchived={archivedChats.includes(chat.id)}
                                                onSelect={onChatSelect}
                                                onQuickReply={handleQuickReply}
                                                formatTimeAgo={formatTimeAgo}
                                            />
                                        ))}
                                    </>
                                ) : (
                                    <div className="flex flex-col items-center justify-center py-8 px-4 text-center">
                                        <div className="w-16 h-16 bg-indigo-50 rounded-full flex items-center justify-center mb-4">
                                            {archivedMode ? (
                                                <HeartIcon className="w-8 h-8 text-pink-400" />
                                            ) : filter === 'favorites' ? (
                                                <HeartIcon className="w-8 h-8 text-pink-400" />
                                            ) : filter === 'unread' ? (
                                                <EnvelopeClosedIcon className="w-8 h-8 text-indigo-400" />
                                            ) : filter === 'missions' ? (
                                                <svg className="w-8 h-8 text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                                </svg>
                                            ) : (
                                                <ChatBubbleIcon className="w-8 h-8 text-indigo-400" />
                                            )}
                                        </div>
                                        <p className="text-gray-500 mb-2">
                                            {archivedMode
                                                ? "No archived conversations"
                                                : searchQuery
                                                    ? "No results found"
                                                    : filter === 'missions'
                                                        ? "No mission chats found"
                                                        : "No conversations yet"}
                                        </p>
                                        <Button
                                            variant="link"
                                            className="text-indigo-600 bg-indigo-50 text-sm font-medium hover:bg-indigo-100 hover:text-indigo-700 hover:underline"
                                            onClick={() => setFilter('all')}
                                        >
                                            {archivedMode ? "View active conversations" : filter !== 'all' ? 'View all messages' : 'Start a new chat'}
                                        </Button>
                                    </div>
                                )}
                            </div>
                        )}
                    </AnimatePresence>
                </ScrollArea>
            </div>
        </div>
    );
};

const ChatListItem = ({
    chat,
    isSelected,
    isFavorite,
    isArchived,
    onSelect,
    onQuickReply,
    formatTimeAgo
}) => {
    const [showQuickReplies, setShowQuickReplies] = useState(false);
    const quickReplies = getQuickReplies(chat.last_message);

    // Find the latest message for this chat
    const lastMessage = chat.last_message || 'No messages yet.';

    // Emoji indicator based on message content
    const getMessageMood = (message) => {
        if (!message) return null;

        if (message.toLowerCase().includes('hello') || message.toLowerCase().includes('hi')) return '👋';
        if (message.toLowerCase().includes('thank')) return '🙏';
        if (message.toLowerCase().includes('ok') || message.toLowerCase().includes('okay')) return '👍';
        if (message.toLowerCase().includes('?')) return '❓';
        if (message.includes('!')) return '❗';
        if (message.toLowerCase().includes('cool') || message.toLowerCase().includes('awesome')) return '🤩';
        if (message.toLowerCase().includes('sorry')) return '😔';
        if (message.toLowerCase().includes('haha') || message.toLowerCase().includes('lol')) return '😄';
        return null;
    };

    const messageEmoji = getMessageMood(lastMessage);

    return (
        <motion.div
            whileHover={{ scale: 1.01, boxShadow: '0 4px 16px rgba(99,102,241,0.08)' }}
            whileTap={{ scale: 0.99 }}
            className={cn(
                "group relative px-4 py-3 cursor-pointer transition-all duration-200 rounded-xl mx-2 mb-1",
                isSelected ?
                    "bg-gradient-to-r from-indigo-100 to-indigo-50 border-l-4 border-indigo-500 shadow-lg" :
                    "hover:bg-indigo-50 hover:shadow-md",
                chat.unread_count > 0 && "bg-indigo-50",
                isArchived && "opacity-70"
            )}
            onClick={() => {
                onSelect(chat);
                setShowQuickReplies(false);
            }}
            onContextMenu={(e) => {
                e.preventDefault();
                setShowQuickReplies(!showQuickReplies);
            }}
        >
            <div className="flex items-start space-x-3">
                <div className="relative">
                    <Avatar
                        src={getCdnUrl(chat.user?.profile_image || chat.talent?.profile_image)}
                        alt={chat.user?.nickname || chat.talent?.nickname}
                        className={cn(
                            "h-12 w-12 transition-all duration-300 border-2",
                            "border-gray-200 group-hover:shadow-lg",
                            isArchived && "grayscale"
                        )}
                    />
                    {isFavorite && (
                        <div className="absolute -top-1 -right-1 bg-pink-500 rounded-full p-1 shadow-lg">
                            <HeartIcon className="w-3 h-3 text-white fill-current" />
                        </div>
                    )}
                </div>

                <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center">
                            <span className={cn(
                                "text-sm font-semibold",
                                chat.unread_count > 0 ? "text-indigo-700" : "text-gray-700"
                            )}>
                                {chat.user?.nickname || chat.talent?.nickname || chat.title}
                            </span>

                            {/* Mission indicator */}
                            {chat.metadata?.type === 'mission' && (
                                <Badge className="ml-2 bg-indigo-100 text-indigo-800 hover:bg-indigo-200 transition-colors">
                                    Mission
                                </Badge>
                            )}
                        </div>
                        <time className="text-xs text-gray-400 tabular-nums">
                            {formatTimeAgo(chat.last_message_at)}
                        </time>
                    </div>

                    <div className="mt-0.5 flex items-center justify-between">
                        <p className={cn(
                            "text-xs truncate text-gray-500 flex items-center gap-1",
                            chat.unread_count > 0 ? "font-semibold text-indigo-900" : ""
                        )}>
                            {messageEmoji && <span className="text-sm">{messageEmoji}</span>}
                            {lastMessage}
                        </p>
                        {chat.unread_count > 0 && (
                            <Badge variant="default" className="ml-2 bg-red-500 text-white shadow-sm">
                                {chat.unread_count}
                            </Badge>
                        )}
                    </div>
                </div>
            </div>

            {/* Quick reply suggestions */}
            <AnimatePresence>
                {showQuickReplies && (
                    <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: 10 }}
                        className="absolute left-0 right-0 mt-2 p-2 bg-white rounded-lg shadow-lg border border-gray-100 z-10"
                    >
                        <div className="grid gap-1">
                            {quickReplies.map((reply, index) => (
                                <button
                                    key={index}
                                    className="text-left px-3 py-2 text-sm text-gray-700 hover:bg-indigo-50 rounded-md transition-colors"
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        onQuickReply(chat.id, reply);
                                        setShowQuickReplies(false);
                                    }}
                                >
                                    {reply}
                                </button>
                            ))}
                        </div>
                    </motion.div>
                )}
            </AnimatePresence>
        </motion.div>
    );
};

export default ChatList;