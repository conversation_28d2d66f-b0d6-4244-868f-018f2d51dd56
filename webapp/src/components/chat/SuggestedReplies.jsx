import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '../../lib/utils';

/**
 * SuggestedReplies component displays contextual reply suggestions
 * based on the conversation
 */
const SuggestedReplies = ({ 
  suggestions = [],
  onSelectReply,
  className,
  isLoading = false,
  conversationContext = {}
}) => {
  if (suggestions.length === 0 && !isLoading) return null;

  return (
    <div className={cn("py-2 border-t border-gray-200", className)}>
      <AnimatePresence>
        {isLoading ? (
          <div className="flex justify-center items-center py-2">
            <div className="animate-pulse flex space-x-2">
              <div className="rounded-full bg-gray-200 h-2 w-2"></div>
              <div className="rounded-full bg-gray-200 h-2 w-2"></div>
              <div className="rounded-full bg-gray-200 h-2 w-2"></div>
            </div>
          </div>
        ) : (
          <div className="flex flex-wrap gap-2 px-4">
            {suggestions.map((suggestion, index) => (
              <motion.button
                key={index}
                className="px-3 py-1.5 rounded-full bg-indigo-50 text-indigo-700 
                  text-sm font-medium hover:bg-indigo-100 transition-colors
                  border border-indigo-100 whitespace-nowrap"
                onClick={() => onSelectReply(suggestion.text)}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ 
                  duration: 0.2,
                  delay: index * 0.05 // Stagger animation
                }}
              >
                {suggestion.text}
              </motion.button>
            ))}
          </div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default SuggestedReplies; 