import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Cross1Icon, PlusIcon } from '@radix-ui/react-icons';
import { giftApi } from '../../services/giftApi';
import giftService from '../../services/giftService';
import walletAPI from '../../services/walletService';
import { transactionService } from '../../services/transactionService';
import GiftItem from './GiftItem';
import GiftReceipt from './GiftReceipt';
import { Button } from '../ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../components/ui/tabs';
import { ScrollArea } from '../ui/scroll-area';
import { Progress } from '../ui/progress';
import { Textarea } from '../ui/textarea';
import { Spinner } from '../ui/spinner';
import { useToast } from '../ui/toast';

const GiftModal = ({ 
    isOpen, 
    onClose, 
    onSendGift, 
    conversationId,
    conversationPartnerName 
}) => {
    const [gifts, setGifts] = useState([]);
    const [popularGifts, setPopularGifts] = useState([]);
    const [categories, setCategories] = useState(['All', 'Popular', 'Animals', 'Food', 'Emotions', 'Special']);
    const [selectedCategory, setSelectedCategory] = useState('All');
    const [selectedGift, setSelectedGift] = useState(null);
    const [message, setMessage] = useState('');
    const [walletBalance, setWalletBalance] = useState(0);
    const [isLoading, setIsLoading] = useState(false);
    const [isSending, setIsSending] = useState(false);
    const [isPurchasing, setIsPurchasing] = useState(false);
    const [showTopUp, setShowTopUp] = useState(false);
    const [topUpAmount, setTopUpAmount] = useState(100);
    const [error, setError] = useState(null);
    const [purchaseSuccessMessage, setPurchaseSuccessMessage] = useState('');
    const [filterMode, setFilterMode] = useState('all'); // 'all', 'owned', 'shop'
    const [showReceipt, setShowReceipt] = useState(false);
    const [receiptData, setReceiptData] = useState(null);
    const toast = useToast();

    useEffect(() => {
        if (isOpen) {
            loadGifts();
            loadWalletBalance();
            loadCategories();
        }
    }, [isOpen]);

    useEffect(() => {
        if (isOpen && selectedCategory) {
            loadGiftsByCategory(selectedCategory);
        }
    }, [isOpen, selectedCategory]);

    // Load gift categories from the backoffice API
    const loadCategories = async () => {
        try {
            const response = await giftService.getGiftCategories();
            if (response.data && response.data.categories) {
                const categoryNames = ['All', ...response.data.categories.map(cat => cat.name)];
                setCategories(categoryNames);
            }
        } catch (error) {
            console.error('Error loading gift categories:', error);
        }
    };

    // Load gifts with inventory information
    const loadGifts = async () => {
        try {
            setIsLoading(true);
            const response = await giftService.getEnhancedGiftCatalog();
            setGifts(response.data.gifts || []);

            // Set popular gifts from the result
            const popular = response.data.gifts?.filter(gift => gift.is_popular) || [];
            setPopularGifts(popular);
        } catch (error) {
            console.error('Error loading gifts:', error);
            setError('Failed to load gifts. Please try again.');
        } finally {
            setIsLoading(false);
        }
    };

    // Load gifts by category
    const loadGiftsByCategory = async (category) => {
        try {
            setIsLoading(true);
            setError(null);
            
            // Convert category name to slug for API call
            const categorySlug = category === 'All' 
                ? 'all' 
                : category.toLowerCase().replace(/\s+/g, '-');
            
            const response = await giftService.getEnhancedGiftCatalog(categorySlug);
            setGifts(response.data.gifts || []);
        } catch (error) {
            console.error(`Error loading gifts for category ${category}:`, error);
            setError('Failed to load gifts for this category.');
        } finally {
            setIsLoading(false);
        }
    };

    const loadWalletBalance = async () => {
        try {
            const response = await walletAPI.getBalance();
            setWalletBalance(response.data.credits_balance || 0);
        } catch (error) {
            console.error('Error loading wallet balance:', error);
        }
    };

    const handleSelectGift = (gift) => {
        setSelectedGift(gift);
        // Auto-populate a message based on the gift
        if (!message) {
            setMessage(`Here's a ${gift.name} for you!`);
        }

        // Clear any previous success messages
        setPurchaseSuccessMessage('');
    };

    const handlePurchaseGift = async (gift) => {
        setIsPurchasing(true);
        setError(null);
        setPurchaseSuccessMessage('');

        try {
            // Execute transaction to purchase the gift
            const result = await transactionService.executeTransaction({
                amount: gift.price,
                transactionFn: async () => {
                    return await giftService.purchaseGift(gift.id, 1);
                },
                onValidationFailed: (validationResult) => {
                    setError(`Insufficient balance to purchase this gift. Required: ${gift.price}, Available: ${validationResult.currentBalance}`);
                    setShowTopUp(true);
                },
                onSuccess: async (result, balanceBefore) => {
                    // Update wallet balance
                    const newBalance = balanceBefore - gift.price;
                    setWalletBalance(newBalance);
                    
                    // Show success message
                    setPurchaseSuccessMessage(`Successfully purchased ${gift.name}!`);
                    toast.success(`You've purchased ${gift.name}`);
                    
                    // Refresh gifts to update inventory
                    await loadGifts();
                },
                onError: (error) => {
                    console.error('Error purchasing gift:', error);
                    setError('Failed to purchase gift. Please try again.');
                    toast.error('Failed to purchase gift. Please try again.');
                }
            });
        } finally {
            setIsPurchasing(false);
        }
    };

    const handleSendFromInventory = async () => {
        if (!selectedGift || !selectedGift.owned) return;
        
        setIsSending(true);
        setError(null);
        
        try {
            // Store initial inventory quantity for receipt
            const inventoryBefore = selectedGift.quantity;
            
            // Send gift using user_gift_id (from inventory)
            const response = await giftApi.sendGift(
                conversationId, 
                selectedGift.user_gift_id, 
                message
            );
            
            // Show receipt
            setReceiptData({
                transaction: response,
                gift: selectedGift,
                recipient: conversationPartnerName,
                wasFromInventory: true,
                inventoryBefore,
                inventoryAfter: inventoryBefore - 1,
                walletBefore: walletBalance,
                walletAfter: walletBalance
            });
            
            // Show receipt
            setShowReceipt(true);
            
            // Notify success
            toast.success(`You sent ${selectedGift.name} to ${conversationPartnerName}`);
            
            // Notify parent component
            onSendGift(response);
            
        } catch (error) {
            console.error('Error sending gift from inventory:', error);
            setError('Failed to send gift from inventory. Please try again.');
            toast.error('Failed to send gift. Please try again.');
            setIsSending(false);
        }
    };

    const handlePurchaseAndSend = async () => {
        if (!selectedGift || selectedGift.owned) return;
        
        setIsSending(true);
        setError(null);
        
        // Use the transaction service to handle the gift transaction
        const result = await transactionService.executeGiftTransaction({
            amount: selectedGift.price,
            
            // Define the main transaction function (sending the gift)
            sendGiftFn: async () => {
                // When sending a gift we don't own, we purchase it directly
                return await giftApi.sendGift(conversationId, selectedGift.id, message);
            },
            
            // Handle insufficient balance
            onInsufficientBalance: (validationResult) => {
                setError(`Insufficient balance to send this gift. Required: ${selectedGift.price}, Available: ${validationResult.currentBalance}`);
                setShowTopUp(true);
                setIsSending(false);
            },
            
            // Handle successful transaction
            onSuccess: (result, balanceBefore) => {
                const newBalance = balanceBefore - selectedGift.price;
                
                // Update wallet balance
                setWalletBalance(newBalance);
                
                // Show receipt
                setReceiptData({
                    transaction: result,
                    gift: selectedGift,
                    recipient: conversationPartnerName,
                    wasFromInventory: false,
                    inventoryBefore: 0,
                    inventoryAfter: 0,
                    walletBefore: balanceBefore,
                    walletAfter: newBalance
                });
                
                setShowReceipt(true);
                
                // Notify success
                toast.success(`You sent ${selectedGift.name} to ${conversationPartnerName}`);
                
                // Notify parent component
                onSendGift(result);
            },
            
            // Handle transaction errors
            onError: (error) => {
                console.error('Error in gift transaction:', error);
                
                if (error.response && error.response.status === 422) {
                    // Handle validation errors
                    setError(error.response.data.message || 'Failed to send gift due to validation error.');
                } else {
                    setError('Failed to send gift. Please try again.');
                }
                
                // Notify error
                toast.error('Failed to send gift. Please try again.');
                
                // Refresh the wallet balance to ensure accuracy
                loadWalletBalance();
                
                setIsSending(false);
            }
        });
        
        if (!result.success) {
            setIsSending(false);
        }
    };

    const handleSendGift = () => {
        if (!selectedGift) return;
        
        // Determine which flow to use based on gift ownership
        if (selectedGift.owned) {
            handleSendFromInventory();
        } else {
            handlePurchaseAndSend();
        }
    };

    const handleCloseReceipt = () => {
        setShowReceipt(false);
        // After closing receipt, also close the modal
        onClose();
    };

    const handleTopUp = async () => {
        try {
            setIsLoading(true);
            
            // Create payment for credit package
            const response = await walletAPI.createPayment(topUpAmount, window.location.href);
            
            // In a real app, you'd redirect to the payment gateway
            // For demo purposes, we'll simulate a successful payment
            setTimeout(async () => {
                try {
                    // Check payment status
                    const statusResponse = await walletAPI.checkPaymentStatus(response.data.payment.transaction_id);
                    
                    // If payment was successful, update balance
                    if (statusResponse.data.status === 'success') {
                        // Refresh balance
                        const balanceResponse = await walletAPI.getBalance();
                        setWalletBalance(balanceResponse.data.credits_balance);
                        setShowTopUp(false);
                        toast.success(`Added ${topUpAmount} credits to your wallet`);
                    }
                } catch (statusError) {
                    console.error('Error checking payment status:', statusError);
                    toast.error('Payment processing failed. Please try again.');
                } finally {
                    setIsLoading(false);
                }
            }, 2000);
        } catch (error) {
            console.error('Error topping up wallet:', error);
            setError('Failed to top up wallet. Please try again.');
            toast.error('Failed to top up wallet. Please try again.');
            setIsLoading(false);
        }
    };

    // Filter gifts based on selected mode (all, owned, shop)
    const filteredGiftsByMode = () => {
        let filteredList = selectedCategory === 'All' 
            ? gifts 
            : selectedCategory === 'Popular'
                ? popularGifts
                : gifts.filter(gift => gift.category === selectedCategory.toLowerCase());
                
        // Apply ownership filter
        if (filterMode === 'owned') {
            return filteredList.filter(gift => gift.owned);
        } else if (filterMode === 'shop') {
            return filteredList.filter(gift => !gift.owned);
        }
        
        return filteredList;
    };

    // Format currency for display
    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2
        }).format(amount / 100); // Assuming price is in cents
    };

    return (
        <AnimatePresence>
            {isOpen && (
                <motion.div
                    className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    onClick={onClose}
                >
                    <motion.div
                        className="bg-white rounded-xl shadow-2xl w-full max-w-lg overflow-hidden"
                        initial={{ scale: 0.9, y: 20, opacity: 0 }}
                        animate={{ scale: 1, y: 0, opacity: 1 }}
                        exit={{ scale: 0.9, y: 20, opacity: 0 }}
                        transition={{ type: 'spring', damping: 25, stiffness: 300 }}
                        onClick={(e) => e.stopPropagation()}
                    >
                        {/* Modal Header */}
                        <div className="p-4 border-b border-gray-200 flex items-center justify-between bg-gradient-to-r from-indigo-500 to-purple-600 text-white">
                            <h3 className="text-lg font-semibold">Send a Virtual Gift</h3>
                            <button 
                                onClick={onClose}
                                className="p-1 rounded-full hover:bg-white/20 transition-colors"
                            >
                                <Cross1Icon className="w-5 h-5" />
                            </button>
                        </div>

                        {/* Wallet Balance */}
                        <div className="p-4 bg-indigo-50 flex items-center justify-between">
                            <div>
                                <p className="text-sm text-indigo-700">Your Balance</p>
                                <p className="text-xl font-bold text-indigo-900 flex items-center">
                                    <span className="mr-1">⭐</span>
                                    {walletBalance.toLocaleString()}
                                </p>
                            </div>
                            <Button
                                variant="outline"
                                className="bg-white border-indigo-200 text-indigo-600 hover:bg-indigo-100"
                                onClick={() => setShowTopUp(true)}
                            >
                                <PlusIcon className="w-4 h-4 mr-1" />
                                Top Up
                            </Button>
                        </div>

                        {/* Modal Content */}
                        {showTopUp ? (
                            // Top Up UI
                            <div className="p-6">
                                <h4 className="text-lg font-medium text-gray-900 mb-4">Top Up Your Balance</h4>
                                
                                <div className="space-y-4">
                                    <div className="flex justify-between gap-2">
                                        {[100, 500, 1000, 5000].map(amount => (
                                            <button
                                                key={amount}
                                                className={`flex-1 py-2 px-3 rounded-lg border ${
                                                    topUpAmount === amount 
                                                        ? 'bg-indigo-100 border-indigo-500 text-indigo-700' 
                                                        : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                                                }`}
                                                onClick={() => setTopUpAmount(amount)}
                                            >
                                                <div className="text-sm font-medium">⭐ {amount}</div>
                                                <div className="text-xs text-gray-500">{formatCurrency(amount)}</div>
                                            </button>
                                        ))}
                                    </div>
                                    
                                    <div className="bg-gray-50 p-4 rounded-lg">
                                        <p className="text-sm text-gray-500 mb-2">Payment amount</p>
                                        <p className="text-lg font-semibold text-gray-900">{formatCurrency(topUpAmount)}</p>
                                    </div>
                                    
                                    <div className="flex gap-2">
                                        <Button
                                            variant="outline"
                                            className="flex-1"
                                            onClick={() => setShowTopUp(false)}
                                        >
                                            Cancel
                                        </Button>
                                        <Button
                                            className="flex-1 bg-gradient-to-r from-indigo-500 to-purple-600"
                                            onClick={handleTopUp}
                                            disabled={isLoading}
                                        >
                                            {isLoading ? <Spinner size="sm" /> : 'Proceed to Payment'}
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        ) : (
                            // Gift Selection UI
                            <div className="max-h-[600px] flex flex-col">
                                {isLoading ? (
                                    <div className="flex-1 flex items-center justify-center p-8">
                                        <Spinner size="lg" />
                                    </div>
                                ) : error ? (
                                    <div className="flex-1 flex items-center justify-center p-8">
                                        <div className="text-center">
                                            <p className="text-red-500 mb-2">{error}</p>
                                            <Button
                                                variant="outline"
                                                onClick={loadGifts}
                                            >
                                                Try Again
                                            </Button>
                                        </div>
                                    </div>
                                ) : (
                                    <Tabs defaultValue="All" className="w-full">
                                        <div className="px-4 py-2 border-b border-gray-200 overflow-x-auto">
                                            <TabsList className="inline-flex w-auto h-auto bg-transparent p-0 space-x-2">
                                                {categories.map(category => (
                                                    <TabsTrigger
                                                        key={category}
                                                        value={category}
                                                        className="px-4 py-2 rounded-full data-[state=active]:bg-indigo-100 data-[state=active]:text-indigo-700 data-[state=active]:shadow-none"
                                                        onClick={() => setSelectedCategory(category)}
                                                    >
                                                        {category}
                                                    </TabsTrigger>
                                                ))}
                                            </TabsList>
                                        </div>

                                        {/* Success message for purchases */}
                                        {purchaseSuccessMessage && (
                                            <div className="px-4 py-2 bg-green-50 border-b border-green-200 text-green-700 text-sm flex items-center justify-between">
                                                <p>{purchaseSuccessMessage}</p>
                                                <button 
                                                    className="text-green-500 hover:text-green-700"
                                                    onClick={() => setPurchaseSuccessMessage('')}
                                                >
                                                    ✕
                                                </button>
                                            </div>
                                        )}

                                        <div className="p-4">
                                            {/* Filter tabs to show owned/all/purchasable */}
                                            <div className="flex mb-4 border rounded-lg overflow-hidden">
                                                <button 
                                                    className={`flex-1 py-1.5 px-3 text-sm font-medium ${
                                                        filterMode === 'all' ? 'bg-indigo-50 text-indigo-700' : 'bg-white text-gray-600'
                                                    }`}
                                                    onClick={() => setFilterMode('all')}
                                                >
                                                    All Gifts
                                                </button>
                                                <button 
                                                    className={`flex-1 py-1.5 px-3 text-sm font-medium ${
                                                        filterMode === 'owned' ? 'bg-indigo-50 text-indigo-700' : 'bg-white text-gray-600'
                                                    }`}
                                                    onClick={() => setFilterMode('owned')}
                                                >
                                                    Owned
                                                </button>
                                                <button 
                                                    className={`flex-1 py-1.5 px-3 text-sm font-medium ${
                                                        filterMode === 'shop' ? 'bg-indigo-50 text-indigo-700' : 'bg-white text-gray-600'
                                                    }`}
                                                    onClick={() => setFilterMode('shop')}
                                                >
                                                    Shop
                                                </button>
                                            </div>

                                            <ScrollArea className="max-h-[250px]">
                                                <div className="grid grid-cols-3 gap-2">
                                                    {filteredGiftsByMode().map(gift => (
                                                        <GiftItem
                                                            key={gift.id}
                                                            gift={gift}
                                                            onClick={() => handleSelectGift(gift)}
                                                            isSelected={selectedGift?.id === gift.id}
                                                            animate={true}
                                                            disabled={!gift.owned && gift.price > walletBalance}
                                                            onPurchase={isPurchasing ? null : handlePurchaseGift}
                                                        />
                                                    ))}
                                                </div>
                                                
                                                {filteredGiftsByMode().length === 0 && (
                                                    <div className="p-8 text-center text-gray-500">
                                                        {filterMode === 'owned' 
                                                            ? "You don't own any gifts in this category yet" 
                                                            : "No gifts available in this category"}
                                                    </div>
                                                )}
                                            </ScrollArea>
                                        </div>
                                        
                                        {selectedGift && (
                                            <div className="p-4 border-t border-gray-200">
                                                <p className="text-sm text-gray-500 mb-2">
                                                    Add a message (optional)
                                                </p>
                                                <Textarea
                                                    placeholder={`Send a message to ${conversationPartnerName} with your gift...`}
                                                    value={message}
                                                    onChange={(e) => setMessage(e.target.value)}
                                                    className="mb-4 resize-none"
                                                    rows={2}
                                                />
                                                
                                                <div className="flex justify-between items-center">
                                                    <div className="flex items-center space-x-2">
                                                        <div className="w-10 h-10 flex items-center justify-center bg-indigo-100 rounded-full">
                                                            <img 
                                                                src={selectedGift.image_url} 
                                                                alt={selectedGift.name}
                                                                className="w-6 h-6"
                                                            />
                                                        </div>
                                                        <div>
                                                            <p className="text-sm font-medium text-gray-700 flex items-center">
                                                                {selectedGift.name}
                                                                {selectedGift.owned && (
                                                                    <span className="ml-2 text-xs bg-green-100 text-green-700 px-1.5 py-0.5 rounded-full">
                                                                        Owned {selectedGift.quantity > 1 && `(${selectedGift.quantity})`}
                                                                    </span>
                                                                )}
                                                            </p>
                                                            {!selectedGift.owned && (
                                                                <p className="text-xs text-indigo-600">
                                                                    <span className="mr-1">⭐</span>
                                                                    {selectedGift.price.toLocaleString()}
                                                                </p>
                                                            )}
                                                        </div>
                                                    </div>
                                                    
                                                    <Button
                                                        className="bg-gradient-to-r from-indigo-500 to-purple-600"
                                                        onClick={handleSendGift}
                                                        disabled={isSending || (!selectedGift.owned && selectedGift.price > walletBalance)}
                                                    >
                                                        {isSending ? (
                                                            <Spinner size="sm" className="mr-2" />
                                                        ) : (
                                                            <>
                                                                {selectedGift.owned ? 'Send from Inventory' : 'Purchase & Send'}
                                                                {!selectedGift.owned && selectedGift.price > walletBalance && (
                                                                    <span className="ml-2 text-xs bg-white/20 px-2 py-1 rounded-full">
                                                                        Insufficient balance
                                                                    </span>
                                                                )}
                                                            </>
                                                        )}
                                                    </Button>
                                                </div>
                                            </div>
                                        )}
                                    </Tabs>
                                )}
                            </div>
                        )}
                    </motion.div>
                </motion.div>
            )}
            
            {/* Transaction Receipt */}
            <GiftReceipt
                isVisible={showReceipt}
                transaction={receiptData?.transaction}
                gift={receiptData?.gift}
                recipient={receiptData?.recipient}
                wasFromInventory={receiptData?.wasFromInventory}
                inventoryBefore={receiptData?.inventoryBefore}
                inventoryAfter={receiptData?.inventoryAfter}
                walletBefore={receiptData?.walletBefore}
                walletAfter={receiptData?.walletAfter}
                onClose={handleCloseReceipt}
                status="success"
            />
            
            {/* Toast Display */}
            {toast.ToastDisplay && <toast.ToastDisplay />}
        </AnimatePresence>
    );
};

export default GiftModal; 