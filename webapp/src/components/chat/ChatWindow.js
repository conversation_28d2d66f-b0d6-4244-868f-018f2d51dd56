import React, { useState, useEffect, useRef, useCallback } from 'react';
import { chatApi } from '../../services/chatApi';
import { chatWebSocket } from '../../services/chatWebSocket';
import { getCdnUrl } from '../../utils/cdnUtils';
import { DateTime } from 'luxon';
import { Textarea } from "../ui/textarea";
import { Button } from "../ui/button";
import { Avatar } from "../ui/avatar";
import { Badge } from "../ui/badge";
import { ScrollArea } from "../ui/scroll-area";
import { Tooltip} from "../ui/tooltip";
import { Progress } from "../ui/progress";
import { cn } from "../../lib/utils";
import { motion, AnimatePresence } from "framer-motion";
import { MagnifyingGlassIcon, ArrowLeftIcon } from '@radix-ui/react-icons';
import { useVirtualizer } from '@tanstack/react-virtual';
import { debounce } from 'lodash';
import { LazyImage } from '../../utils/imageUtils';
import { giftApi } from '../../services/giftApi';
import GiftModal from './GiftModal';
import GiftNotification from './GiftNotification';
import GiftItem from './GiftItem';
import SuggestedReplies from './SuggestedReplies';
import { suggestedRepliesApi } from '../../services/suggestedRepliesApi';
import VirtualizedMessageList from './VirtualizedMessageList';

// Utility functions
const formatReadReceiptTime = (timestamp) => {
    return DateTime.fromISO(timestamp).toFormat('HH:mm');
};

const getUserAvatar = (userId) => {
    // In a real app, this would fetch from your user service
    // For now, return a placeholder
    return `https://api.dicebear.com/7.x/avatars/svg?seed=${userId}`;
};

// Quick reactions
const QUICK_REACTIONS = ['👍', '❤️', '😂', '😮', '😢', '🙏'];

// Link preview regex
const URL_REGEX = /(https?:\/\/[^\s]+)/g;

// Add ModeratedContentNotice component
const ModeratedContentNotice = ({ reason, severity = 'warning' }) => {
    const severityStyles = {
        warning: "bg-amber-50 text-amber-500 border-amber-200",
        error: "bg-red-50 text-red-500 border-red-200",
        info: "bg-blue-50 text-blue-500 border-blue-200"
    };

    return (
        <div className={`flex items-center text-xs mb-1 px-2 py-1 rounded-lg border ${severityStyles[severity]}`}>
            <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            <span>
                {reason || 'This content has been flagged and is under review'}
            </span>
        </div>
    );
};

const ChatWindow = ({ chat, onBack, onMessageSent }) => {
    const [message, setMessage] = useState('');
    const [messages, setMessages] = useState([]);
    const [page, setPage] = useState(1);
    const [hasMore, setHasMore] = useState(true);
    const [loading, setLoading] = useState(false);
    const [sending, setSending] = useState(false);
    const [isTyping, setIsTyping] = useState(false);
    const [replyTo, setReplyTo] = useState(null);
    const [unreadCount, setUnreadCount] = useState(0);
    const [showScrollButton, setShowScrollButton] = useState(false);
    const fileInputRef = useRef(null);
    const messagesEndRef = useRef(null);
    const scrollAreaRef = useRef(null);
    const typingTimeoutRef = useRef(null);
    const [attachments, setAttachments] = useState([]);
    const [isRecording, setIsRecording] = useState(false);
    const [dragOver, setDragOver] = useState(false);
    const [uploadProgress, setUploadProgress] = useState({});
    const [reactions, setReactions] = useState({});
    const dropZoneRef = useRef(null);
    const [readReceipts, setReadReceipts] = useState({});
    const [searchResults, setSearchResults] = useState([]);
    const [windowWidth, setWindowWidth] = useState(window.innerWidth);
    const [isGiftModalOpen, setIsGiftModalOpen] = useState(false);
    const [receivedGift, setReceivedGift] = useState(null);
    const [showGiftNotification, setShowGiftNotification] = useState(false);
    const [walletBalance, setWalletBalance] = useState(0);

    // Add state for suggested replies
    const [suggestedReplies, setSuggestedReplies] = useState([]);
    const [loadingSuggestions, setLoadingSuggestions] = useState(false);

    // Add state for virtualized list
    const [isAtBottom, setIsAtBottom] = useState(true);

    // Add a function to fetch suggested replies
    const fetchSuggestedReplies = useCallback(async (lastMessage) => {
        if (!lastMessage || lastMessage.is_self) return;

        try {
            setLoadingSuggestions(true);

            // Get the last 5 messages for context
            const recentMessages = messages
                .slice(-10)
                .filter(m => m.content && m.content.trim() !== '')
                .slice(-5)
                .map(m => ({
                    id: m.id,
                    content: m.content,
                    is_self: m.sender_id === localStorage.getItem('user_id')
                }));

            const response = await suggestedRepliesApi.getSuggestedReplies(
                lastMessage.content,
                recentMessages,
                chat.id
            );

            if (response.success && response.data.suggestions) {
                setSuggestedReplies(response.data.suggestions);
            } else {
                setSuggestedReplies([]);
            }
        } catch (error) {
            console.error('Error fetching suggested replies:', error);
            setSuggestedReplies([]);
        } finally {
            setLoadingSuggestions(false);
        }
    }, [chat.id, messages]);

    const loadMessages = useCallback(async () => {
        try {
            setLoading(true);
            const response = await chatApi.getConversationMessages(chat.id, page);
            const { data } = response.data;

            setMessages(prev => {
                // Deduplicate messages
                const messageMap = new Map(prev.map(msg => [msg.id, msg]));
                data.forEach(msg => messageMap.set(msg.id, msg));
                return Array.from(messageMap.values())
                    .sort((a, b) => new Date(a.created_at) - new Date(b.created_at));
            });
            setHasMore(data.length === 20);
        } catch (error) {
            console.error('Error loading messages:', error);
        } finally {
            setLoading(false);
        }
    }, [chat.id, page]);

    useEffect(() => {
        loadMessages();

        const handleNewMessage = (message) => {
            if (message.conversation_id === chat.id) {
                setMessages(prev => {
                    // Deduplicate messages by checking against existing IDs
                    const messageExists = prev.some(m => m.id === message.id);
                    if (messageExists) return prev;

                    // Get suggestions for the new message if it's not from the current user
                    if (message.sender_id !== localStorage.getItem('user_id')) {
                        fetchSuggestedReplies(message);
                    }

                    return [...prev, message];
                });

                // Scroll to bottom when receiving new messages
                setTimeout(() => {
                messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
                }, 100);

                // Mark message as delivered immediately
                chatApi.updateMessageStatus(message.id, 'delivered').catch(error => {
                    console.error('Error updating message status to delivered:', error);
                });

                // If chat window is visible to user, mark as read after a short delay
                if (document.visibilityState === 'visible') {
                    setTimeout(() => {
                        chatApi.updateMessageStatus(message.id, 'read').catch(error => {
                            console.error('Error updating message status to read:', error);
                        });

                        // Also send read receipt via WebSocket for real-time updates
                        chatWebSocket.sendReadReceipt(message.id);
                    }, 1000);
                }
            }
        };

        const handleStatusUpdate = ({ messageId, status, userId }) => {
            setMessages(prev => {
                const messageIndex = prev.findIndex(msg => msg.id === messageId);
                if (messageIndex === -1) return prev;

                const newMessages = [...prev];
                const message = { ...newMessages[messageIndex] };

                // Initialize statuses array if it doesn't exist
                if (!message.statuses) {
                    message.statuses = [];
                }

                // Add new status with timestamp
                message.statuses.push({
                    status,
                    status_timestamp: new Date().toISOString(),
                    user_id: userId
                });

                newMessages[messageIndex] = message;
                return newMessages;
            });
        };

        const handleTyping = ({ conversationId, isTyping }) => {
            if (conversationId === chat.id) {
                setIsTyping(isTyping);
            }
        };

        const handleReadReceipt = ({ message_id, timestamp, user_id }) => {
            setReadReceipts(prev => ({
                ...prev,
                [message_id]: {
                    read_at: timestamp,
                    user_id
                }
            }));
        };

        // Register WebSocket event handlers
        chatWebSocket.on('message', handleNewMessage);
        chatWebSocket.on('status_update', handleStatusUpdate);
        chatWebSocket.on('typing', handleTyping);
        chatWebSocket.on('read_receipt', handleReadReceipt);

        // Handling visibility change to mark messages as read when user returns to tab
        const handleVisibilityChange = () => {
            if (document.visibilityState === 'visible') {
                // Mark all unread messages as read
                setMessages(prev => {
                    const unreadMessages = prev.filter(m =>
                        m.sender_id !== localStorage.getItem('user_id') && // Not sent by current user
                        (!m.statuses || !m.statuses.some(s => s.status === 'read')) // Not already read
                    );

                    // Update status for all unread messages
                    unreadMessages.forEach(msg => {
                        chatApi.updateMessageStatus(msg.id, 'read').catch(error => {
                            console.error('Error updating message status to read:', error);
                        });

                        // Also send read receipt via WebSocket for real-time updates
                        chatWebSocket.sendReadReceipt(msg.id);
                    });

                    return prev;
                });
            }
        };

        // Add visibility change listener
        document.addEventListener('visibilitychange', handleVisibilityChange);

        // Cleanup event listeners on unmount
        return () => {
            chatWebSocket.off('message', handleNewMessage);
            chatWebSocket.off('status_update', handleStatusUpdate);
            chatWebSocket.off('typing', handleTyping);
            chatWebSocket.off('read_receipt', handleReadReceipt);
            document.removeEventListener('visibilitychange', handleVisibilityChange);
        };
    }, [chat.id, loadMessages, fetchSuggestedReplies]);

    useEffect(() => {
        // Fetch wallet balance when component mounts
        const fetchWalletBalance = async () => {
            try {
                const response = await giftApi.getWalletBalance();
                setWalletBalance(response.data.balance || 0);
            } catch (error) {
                console.error('Error fetching wallet balance:', error);
            }
        };

        fetchWalletBalance();

        // Handler for receiving gift events via WebSocket
        const handleGiftReceived = (giftData) => {
            // Only show notifications for gifts in the current conversation
            if (giftData.conversation_id === chat.id) {
                setReceivedGift(giftData);
                setShowGiftNotification(true);

                // Also update messages to include the gift
                setMessages(prev => {
                    // Check if we already have this message
                    const exists = prev.some(m => m.id === giftData.message_id);
                    if (exists) return prev;

                    // Create a message object from the gift data
                    const newMessage = {
                        id: giftData.message_id,
                        conversation_id: giftData.conversation_id,
                        sender_id: giftData.sender_id,
                        content: giftData.message || `Sent a ${giftData.gift.name}`,
                        created_at: giftData.created_at,
                        updated_at: giftData.created_at,
                        gift: giftData.gift
                    };

                    return [...prev, newMessage];
                });
            }
        };

        // Register WebSocket handler for gift events
        chatWebSocket.on('gift_received', handleGiftReceived);

        return () => {
            chatWebSocket.off('gift_received', handleGiftReceived);
        };
    }, [chat.id]);

    const handleSend = async () => {
        if ((!message.trim() && attachments.length === 0) || sending) return;

        try {
            setSending(true);

            // Create temporary message for optimistic UI update
            const tempMessage = {
                id: `temp-${Date.now()}`,
                conversation_id: chat.id,
                sender_id: localStorage.getItem('user_id'),
                is_self: true,
                content: message.trim(),
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
                attachments: attachments.map((att, idx) => ({
                    id: `temp-att-${idx}`,
                    message_id: `temp-${Date.now()}`,
                    optimized_path: att.preview,
                    filename: att.file.name,
                    size: att.file.size,
                    dimensions: { width: 0, height: 0 }
                })),
                statuses: [
                    {
                        status: 'sent',
                        status_timestamp: new Date().toISOString()
                    }
                ]
            };

            // Add temporary message to UI immediately
            setMessages(prev => [...prev, tempMessage]);

            // Handle attachments if present
            if (attachments.length > 0) {
                for (const attachment of attachments) {
                    try {
                        // Update progress in state
                        const updateProgress = (progress) => {
                            setUploadProgress(prev => ({
                                ...prev,
                                [attachment.file.name]: progress
                            }));
                        };

                        const response = await chatApi.sendAttachmentMessage(
                            chat.id,
                            message.trim(),
                            attachment.file,
                            updateProgress
                        );

                        // Replace temp message with real message
                        setMessages(prev => prev.map(msg =>
                            msg.id === tempMessage.id ? response.data : msg
                        ));

                    // Notify parent component about sent message
                    onMessageSent?.(response.data);
                    } catch (error) {
                        console.error('Error sending attachment:', error);
                        // Mark the message as failed
                        setMessages(prev => prev.map(msg =>
                            msg.id === tempMessage.id
                                ? { ...msg, sendError: true, errorMessage: error.message || 'Failed to send attachment' }
                                : msg
                        ));
                    }
                }
                setAttachments([]);
            }

            // Send text message if present
            if (message.trim()) {
                try {
                const response = await chatApi.sendTextMessage(chat.id, message.trim());

                    // Replace temp message with real message from server if no attachments were sent
                    if (attachments.length === 0) {
                        setMessages(prev => prev.map(msg =>
                            msg.id === tempMessage.id ? response.data : msg
                        ));
                    }

                // Notify parent component about sent message
                onMessageSent?.(response.data);
                } catch (error) {
                    console.error('Error sending message:', error);
                    // Mark the message as failed
                    setMessages(prev => prev.map(msg =>
                        msg.id === tempMessage.id && attachments.length === 0
                            ? { ...msg, sendError: true, errorMessage: error.message || 'Failed to send message' }
                            : msg
                    ));
                }
            }

            setMessage('');
            scrollToBottom();
        } catch (error) {
            console.error('Error in handleSend:', error);
        } finally {
            setSending(false);
            setUploadProgress({});
        }
    };

    const handleAttachment = async (files) => {
        try {
            // Convert FileList to Array
            const fileArray = Array.from(files);

            // Check file types and sizes
            const validFileTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/heic', 'image/heif'];
            const maxFileSize = 10 * 1024 * 1024; // 10MB

            // Validate each file
            for (const file of fileArray) {
                if (!validFileTypes.includes(file.type)) {
                    console.error(`Invalid file type: ${file.type}`);
                    // You could show a toast notification here
                    continue;
                }

                if (file.size > maxFileSize) {
                    console.error(`File too large: ${file.size / 1024 / 1024}MB`);
                    // You could show a toast notification here
                    continue;
                }
            }

            // Process each valid file
            const processedFiles = await Promise.all(
                fileArray.map(async (file) => {
                if (!file.type.startsWith('image/')) return file;

                try {
                        // Create a preview immediately for better UX
                        const preview = URL.createObjectURL(file);

                        // For image files, compress them
                        let compressedFile = file;

                        // Skip GIFs from compression to maintain animation
                        if (file.type !== 'image/gif') {
                            // Create a promise for image loading
                            const loadImagePromise = new Promise((resolve) => {
                                const img = new Image();
                                img.onload = () => resolve(img);
                                img.src = preview;
                            });

                            // Wait for image to load
                            const img = await loadImagePromise;

                            // Calculate new dimensions (max 1200px width/height while maintaining aspect ratio)
                    const maxSize = 1200;
                    let width = img.width;
                    let height = img.height;

                    if (width > height && width > maxSize) {
                        height = (height * maxSize) / width;
                        width = maxSize;
                    } else if (height > maxSize) {
                        width = (width * maxSize) / height;
                        height = maxSize;
                    }

                            // Create canvas for resizing
                            const canvas = document.createElement('canvas');
                            const ctx = canvas.getContext('2d');

                            // Set canvas dimensions
                    canvas.width = width;
                    canvas.height = height;

                            // Draw image on canvas with new dimensions
                    ctx.drawImage(img, 0, 0, width, height);

                            // Convert to blob with reduced quality for JPG/JPEG
                            const format = file.type === 'image/jpeg' ? 'image/jpeg' : 'image/webp';
                            const quality = file.type === 'image/jpeg' ? 0.8 : 0.85;

                            // Convert canvas to blob
                    const blob = await new Promise(resolve =>
                                canvas.toBlob(resolve, format, quality)
                    );

                            // Create new File object from blob
                            compressedFile = new File([blob], file.name, {
                                type: format,
                        lastModified: Date.now()
                    });
                        }

                        return {
                            file: compressedFile,
                            preview,
                            progress: 0,
                            originalSize: file.size,
                            compressedSize: compressedFile.size,
                            compressionRatio: Math.round((1 - (compressedFile.size / file.size)) * 100)
                        };
                } catch (error) {
                    console.error('Error compressing image:', error);
                        // If compression fails, return original file
                        return {
                            file,
                            preview: URL.createObjectURL(file),
                            progress: 0,
                            originalSize: file.size,
                            compressedSize: file.size,
                            compressionRatio: 0
                        };
                }
            })
        );

            // Add to attachments state
            setAttachments(prev => [...prev, ...processedFiles]);

            // Log compression statistics
            processedFiles.forEach(file => {
                if (file.compressionRatio > 0) {
                    console.log(`Compressed ${file.file.name}: ${file.compressionRatio}% reduction (${(file.originalSize / 1024).toFixed(1)}KB → ${(file.compressedSize / 1024).toFixed(1)}KB)`);
                }
            });
        } catch (error) {
            console.error('Error processing attachments:', error);
        }
    };

    const toggleRecording = () => {
        setIsRecording(prev => !prev);
        // Implement actual recording logic here
    };

    const handleTyping = () => {
        chatWebSocket.simulateTypingIndicator(chat.id, true);

        if (typingTimeoutRef.current) {
            clearTimeout(typingTimeoutRef.current);
        }

        typingTimeoutRef.current = setTimeout(() => {
            chatWebSocket.simulateTypingIndicator(chat.id, false);
        }, 1000);
    };

    // Update scroll handling logic
    const handleScrollChange = useCallback((scrollInfo) => {
        setShowScrollButton(!scrollInfo.isNearBottom);
        setIsAtBottom(scrollInfo.isNearBottom);

            // Load more messages if needed
        if (scrollInfo.scrollTop < 100 && hasMore && !loading) {
                setPage(prev => prev + 1);
            }

        // Update unread count if not at bottom
        if (!scrollInfo.isNearBottom) {
            // Check for unread messages that are not visible
            const unreadMessages = messages.filter(m =>
                m.sender_id !== localStorage.getItem('user_id') &&
                !readReceipts[m.id]
            );
            setUnreadCount(unreadMessages.length);
        } else {
            // Reset unread count when at bottom
            setUnreadCount(0);
        }
    }, [messages, hasMore, loading, readReceipts]);

    const scrollToBottom = () => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
        setUnreadCount(0);
    };

    const handleReply = (msg) => {
        setReplyTo(msg);
        // Focus the input
        document.querySelector('textarea')?.focus();
    };

    const cancelReply = () => {
        setReplyTo(null);
    };

    // Format date for message groups
    const formatMessageDate = (dateStr) => {
        try {
            const date = DateTime.fromISO(dateStr);
            const now = DateTime.now();
            const yesterday = now.minus({ days: 1 });

            if (date.hasSame(now, 'day')) return 'Today';
            if (date.hasSame(yesterday, 'day')) return 'Yesterday';
            if (date.hasSame(now, 'week')) return date.toFormat('cccc'); // Day name
            if (date.hasSame(now, 'year')) return date.toFormat('LLL d'); // Month day
            return date.toFormat('LLL d, yyyy'); // Month day, year
        } catch (error) {
            console.error('Date parsing error:', error);
            return 'Unknown date';
        }
    };

    // Handle file drop
    const handleDrop = useCallback((e) => {
        e.preventDefault();
        setDragOver(false);

        const files = Array.from(e.dataTransfer.files);
        const imageFiles = files.filter(file => file.type.startsWith('image/'));

        if (imageFiles.length > 0) {
            handleAttachment(imageFiles);
        }
    }, []);

    // Handle message reactions
    const handleReaction = (messageId, emoji) => {
        setReactions(prev => ({
            ...prev,
            [messageId]: {
                ...prev[messageId],
                [emoji]: (prev[messageId]?.[emoji] || 0) + 1
            }
        }));
    };

    // Handle message visibility for read receipts
    const handleMessageVisible = useCallback((messageId) => {
        if (!readReceipts[messageId]?.[chat.user_id]) {
            chatWebSocket.sendReadReceipt(messageId);

            // Update local read receipts state
            setReadReceipts(prev => ({
                ...prev,
                [messageId]: {
                    ...prev[messageId],
                    [chat.user_id]: new Date().toISOString()
                }
            }));
        }
    }, [chat.user_id, readReceipts]);

    // Render link preview
    const LinkPreview = ({ url }) => {
        const [preview, setPreview] = useState(null);
        const [loading, setLoading] = useState(true);

        useEffect(() => {
            const fetchPreview = async () => {
                try {
                    // In a real app, you would call your backend to fetch the preview
                    const response = await fetch(`/api/link-preview?url=${encodeURIComponent(url)}`);
                    const data = await response.json();
                    setPreview(data);
                } catch (error) {
                    console.error('Error fetching link preview:', error);
                } finally {
                    setLoading(false);
                }
            };

            fetchPreview();
        }, [url]);

        if (loading) return null;
        if (!preview) return <a href={url} target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">{url}</a>;

        return (
            <a
                href={url}
                target="_blank"
                rel="noopener noreferrer"
                className="block mt-2 rounded-lg border border-gray-200 overflow-hidden hover:shadow-md transition-shadow"
            >
                {preview.image && (
                    <img
                        src={preview.image}
                        alt={preview.title}
                        className="w-full h-32 object-cover"
                    />
                )}
                <div className="p-3">
                    <h4 className="font-medium text-sm">{preview.title}</h4>
                    {preview.description && (
                        <p className="text-xs text-gray-500 mt-1 line-clamp-2">
                            {preview.description}
                        </p>
                    )}
                    <p className="text-xs text-gray-400 mt-2">{new URL(url).hostname}</p>
                </div>
            </a>
        );
    };

    // Update the MessageItem component to add message reporting controls
    const MessageItem = ({ msg, isLast, onVisible, readReceipts, isHighlighted, windowWidth }) => {
        const isCurrentUser = msg.sender_id === localStorage.getItem('user_id') || msg.is_self;
        const [linkPreview, setLinkPreview] = useState(null);
        const [isImageModalOpen, setIsImageModalOpen] = useState(false);
        const [selectedImage, setSelectedImage] = useState(null);
        const [showContextMenu, setShowContextMenu] = useState(false);
        const [contextMenuPosition, setContextMenuPosition] = useState({ x: 0, y: 0 });
        const contextMenuRef = useRef(null);

        // Get the last status from the message statuses array
        const getMessageStatus = () => {
            if (!msg.statuses || msg.statuses.length === 0) {
                return 'sent';
            }

            // Sort by timestamp to get the latest status
            const sortedStatuses = [...msg.statuses].sort((a, b) =>
                new Date(b.status_timestamp) - new Date(a.status_timestamp)
            );

            // Return the status of the last update
            return sortedStatuses[0].status;
        };

        // Report message handling
        const handleReportMessage = () => {
            setShowContextMenu(false);

            // Show report confirmation dialog
            const confirmed = window.confirm('Are you sure you want to report this message as inappropriate?');

            if (confirmed) {
                // Call API to report the message
                reportMessage(msg.id);
            }
        };

        // Function to report a message to the backend
        const reportMessage = async (messageId) => {
            try {
                // Call the actual API endpoint
                await chatApi.reportMessage(messageId, 'User reported inappropriate content');

                // Optimistic UI update - mark the message as flagged
                setMessages(prev =>
                    prev.map(message =>
                        message.id === messageId
                            ? { ...message, is_flagged: true, flagged_reason: 'User reported content' }
                            : message
                    )
                );

                // Show success message to user
                alert('Message has been reported and will be reviewed by moderators.');
            } catch (error) {
                console.error('Error reporting message:', error);
                alert('Failed to report message. Please try again.');
            }
        };

        // Handle right-click on message to show context menu
        const handleContextMenu = (e) => {
            e.preventDefault();

            // Position the context menu at the mouse pointer
            setContextMenuPosition({ x: e.clientX, y: e.clientY });
            setShowContextMenu(true);
        };

        // Function to handle clicking outside the context menu to close it
        useEffect(() => {
            const handleClickOutside = (event) => {
                if (contextMenuRef.current && !contextMenuRef.current.contains(event.target)) {
                    setShowContextMenu(false);
                }
            };

            if (showContextMenu) {
                document.addEventListener('mousedown', handleClickOutside);
            }

            return () => {
                document.removeEventListener('mousedown', handleClickOutside);
            };
        }, [showContextMenu]);

        // Handle copy message text
        const handleCopyMessage = () => {
            setShowContextMenu(false);
            navigator.clipboard.writeText(msg.content)
                .then(() => console.log('Message copied to clipboard'))
                .catch(err => console.error('Failed to copy message:', err));
        };

        // Render the appropriate status icon based on message status
        const renderStatusIcon = () => {
            // Only show status for messages sent by current user
            if (!isCurrentUser) return null;

            const status = getMessageStatus();

            switch (status) {
                case 'sent':
                    return (
                        <svg className="h-3 w-3 text-gray-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <path d="M5 13l4 4L19 7" strokeLinecap="round" strokeLinejoin="round" />
                        </svg>
                    );
                case 'delivered':
                    return (
                        <svg className="h-3 w-3 text-blue-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <path d="M5 13l4 4L19 7" strokeLinecap="round" strokeLinejoin="round" />
                        </svg>
                    );
                case 'read':
                    return (
                        <div className="flex">
                            <svg className="h-3 w-3 text-blue-500" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                <path d="M5 13l4 4L19 7" strokeLinecap="round" strokeLinejoin="round" />
                            </svg>
                            <svg className="h-3 w-3 -ml-1 text-blue-500" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                <path d="M5 13l4 4L19 7" strokeLinecap="round" strokeLinejoin="round" />
                            </svg>
                        </div>
                    );
                default:
                    return null;
            }
        };

        // Check if the message has been flagged for moderation
        const isFlagged = msg.is_flagged;

        // Find URL in message text
        useEffect(() => {
            if (msg.content) {
                const urlMatch = msg.content.match(URL_REGEX);
                if (urlMatch && urlMatch[0]) {
                    // Set preview data
                    setLinkPreview({ url: urlMatch[0] });
                }
            }
        }, [msg.content]);

        // Open the image in a modal when clicked
        const handleImageClick = (attachment) => {
            setSelectedImage(attachment);
            setIsImageModalOpen(true);
        };

        // Close the image modal
        const handleCloseImageModal = () => {
            setIsImageModalOpen(false);
            setSelectedImage(null);
        };

        return (
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.3 }}
                className={cn(
                    "py-1 px-2 group",
                    isHighlighted && "bg-indigo-50/80"
                )}
                onContextMenu={handleContextMenu}
                id={`message-${msg.id}`}
            >
                <div className={cn(
                    "flex items-end gap-2",
                    isCurrentUser ? "justify-end" : "justify-start"
                )}>
                    {!isCurrentUser && (
                <Avatar
                            src={getCdnUrl(msg.sender?.profile_image)}
                            fallback={msg.sender?.nickname?.[0] || "?"}
                            className="h-8 w-8 flex-shrink-0"
                />
                    )}

                <div className={cn(
                        "max-w-[75%] flex flex-col",
                        isCurrentUser && "items-end"
                )}>
                        {/* Display flagged message warning if applicable */}
                        {isFlagged && (
                            <ModeratedContentNotice
                                reason={msg.flagged_reason || 'This message has been flagged for review'}
                                severity={msg.flagged_severity || 'warning'}
                            />
                        )}

                        {/* Display gift if present */}
                        {msg.gift && (
                        <div className={cn(
                                "flex flex-col items-center my-2 px-2",
                                isCurrentUser ? "items-end" : "items-start"
                        )}>
                                <div className={cn(
                                    "rounded-xl px-4 py-3 mb-2 text-sm font-medium",
                                    isCurrentUser
                                        ? "bg-indigo-50 text-indigo-700"
                                        : "bg-pink-50 text-pink-700"
                                )}>
                                    {isCurrentUser ? "You sent a gift" : "You received a gift"}
                                </div>
                                <GiftItem
                                    gift={msg.gift}
                                    size="lg"
                                    showPrice={false}
                                    animate={true}
                                />
                        </div>
                    )}

                        {/* Only show message bubble if there's content or attachments */}
                        {(msg.content || (msg.attachments && msg.attachments.length > 0)) && (
                            <motion.div
                                className={cn(
                                    "rounded-2xl px-4 py-3 text-sm relative overflow-hidden",
                                    "backdrop-blur-sm transition-all duration-200",
                                    isCurrentUser
                                        ? "bg-gradient-to-br from-indigo-500 to-indigo-600 text-white rounded-tr-md shadow-lg shadow-indigo-500/25"
                                        : "bg-white/90 border border-gray-200/50 rounded-tl-md shadow-lg shadow-gray-500/10",
                                    msg.attachments && msg.attachments.length > 0 && "mb-1",
                                    isFlagged && "opacity-70"
                                )}
                                whileHover={{
                                    scale: 1.02,
                                    transition: { type: "spring", stiffness: 400, damping: 25 }
                                }}
                                initial={{ scale: 0.95, opacity: 0 }}
                                animate={{ scale: 1, opacity: 1 }}
                                transition={{
                                    type: "spring",
                                    stiffness: 500,
                                    damping: 30,
                                    delay: 0.1
                                }}
                            >
                                {/* Subtle gradient overlay for current user messages */}
                                {isCurrentUser && (
                                    <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent pointer-events-none" />
                                )}

                                {/* Message content */}
                                <div className="relative z-10">
                                    {msg.content}
                                </div>

                                {/* Link preview */}
                                {linkPreview && <LinkPreview url={linkPreview.url} />}

                                {/* Subtle shine effect on hover */}
                                <motion.div
                                    className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full"
                                    whileHover={{
                                        translateX: "200%",
                                        transition: { duration: 0.6, ease: "easeInOut" }
                                    }}
                                />
                            </motion.div>
                        )}

                        {/* Attachments */}
                        {msg.attachments && msg.attachments.length > 0 && (
                            <div className="mt-1 grid grid-cols-2 gap-1">
                                {msg.attachments.map((attachment, index) => (
                                    <div
                                        key={attachment.id || index}
                                        className="relative group/img cursor-pointer overflow-hidden rounded-lg shadow-sm"
                                        onClick={() => handleImageClick(attachment)}
                                    >
                                        <LazyImage
                                            src={attachment.optimized_path}
                                            alt={attachment.filename || 'Image attachment'}
                                            className={cn(
                                                "w-full h-auto object-cover rounded-lg max-h-40 transition-transform duration-200 group-hover/img:scale-105",
                                                isFlagged && "opacity-70"
                                            )}
                                        />

                                        {/* Image info overlay on hover */}
                                        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover/img:bg-opacity-30 transition-opacity duration-200 flex items-end">
                                            <div className="p-2 w-full text-white opacity-0 group-hover/img:opacity-100 transition-opacity duration-200 text-xs">
                                                <div className="truncate">{attachment.filename}</div>
                                                <div>{Math.round(attachment.size / 1024)} KB</div>
                                        </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        )}

                        {/* Time and status */}
                        <div className={cn(
                            "flex items-center text-xs text-gray-500 mt-1",
                            isCurrentUser ? "justify-end" : "justify-start"
                        )}>
                            <span>{formatMessageDate(msg.created_at)}</span>

                            {/* Only show status for own messages */}
                            {isCurrentUser && (
                                <span className="ml-1">
                                    {renderStatusIcon()}
                                </span>
                            )}
                                </div>
                        </div>
                    </div>

                {/* Context Menu */}
                {showContextMenu && (
                    <div
                        ref={contextMenuRef}
                        className="fixed bg-white shadow-lg rounded-lg border border-gray-200 py-1 z-50"
                        style={{
                            top: `${contextMenuPosition.y}px`,
                            left: `${contextMenuPosition.x}px`,
                        }}
                    >
                        <button
                            className="w-full text-left px-4 py-2 text-sm hover:bg-gray-100 flex items-center"
                            onClick={handleCopyMessage}
                        >
                            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                            </svg>
                            Copy message
                        </button>
                        <button
                            className="w-full text-left px-4 py-2 text-sm hover:bg-gray-100 flex items-center text-red-500"
                            onClick={handleReportMessage}
                        >
                            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                            </svg>
                            Report message
                        </button>
                        {!isCurrentUser && (
                            <button
                                className="w-full text-left px-4 py-2 text-sm hover:bg-gray-100 flex items-center"
                                onClick={() => {
                                    setShowContextMenu(false);
                                    handleReply(msg);
                                }}
                            >
                                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
                                </svg>
                                Reply
                            </button>
                        )}
                    </div>
                    )}

                {/* Full-screen image modal */}
                {isImageModalOpen && selectedImage && (
                    <div
                        className="fixed inset-0 bg-black bg-opacity-80 z-50 flex items-center justify-center p-4"
                        onClick={handleCloseImageModal}
                    >
                        <div
                            className="relative max-w-4xl max-h-full"
                            onClick={e => e.stopPropagation()}
                        >
                            <button
                                className="absolute top-2 right-2 bg-black bg-opacity-50 rounded-full p-2 text-white"
                                onClick={handleCloseImageModal}
                            >
                                <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>

                            <img
                                src={selectedImage.optimized_path || selectedImage.original_path}
                                alt={selectedImage.filename || 'Full-size image'}
                                className="max-w-full max-h-[90vh] object-contain"
                            />

                            <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white p-4">
                                <p className="text-sm">{selectedImage.filename}</p>
                                {selectedImage.dimensions && (
                                    <p className="text-xs text-gray-300 mt-1">
                                        {selectedImage.dimensions.width} × {selectedImage.dimensions.height} •
                                        {Math.round(selectedImage.size / 1024)} KB
                                    </p>
                    )}
                </div>
                        </div>
                    </div>
                )}
            </motion.div>
        );
    };

    // Add a function to handle sending gifts
    const handleSendGift = async (giftData) => {
        console.group('🎁 ChatWindow: Sending Gift');
        console.log('Gift data:', giftData);
        console.log('Chat ID:', chat.id);

        try {
            console.log('Calling giftApi.sendGift...');
            const response = await giftApi.sendGift(chat.id, giftData);
            console.log('Gift API response:', response);

            if (response.success) {
                console.log('Gift sent successfully, adding to messages');
                // Add the gift to the messages
                setMessages(prev => [...prev, {
                    id: response.data.id,
                    conversation_id: chat.id,
                    sender_id: localStorage.getItem('user_id'),
                    content: `Sent a ${giftData.name}`,
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString(),
                    gift: giftData
                }]);

                // Update balance
                console.log('Updating wallet balance');
                setWalletBalance(prev => {
                    const newBalance = prev - giftData.cost;
                    console.log(`Balance updated: ${prev} -> ${newBalance}`);
                    return newBalance;
                });
            } else {
                console.error('Gift sending failed:', response);
            }
        } catch (error) {
            console.error('Error sending gift:', error);
        }
        console.log('Closing gift modal');
        setIsGiftModalOpen(false);
        console.groupEnd();
    };

    // Handle selecting a suggested reply
    const handleSelectSuggestedReply = useCallback((replyText) => {
        setMessage(replyText);
        setSuggestedReplies([]); // Clear suggestions after selection

        // Focus the input
        document.querySelector('textarea')?.focus();

        // Optional: Auto-send if enabled in user preferences
        // handleSend();
    }, []);

    return (
        <div
            className="flex flex-col h-full w-full bg-gray-50"
            onDragOver={(e) => {
                e.preventDefault();
                setDragOver(true);
            }}
            onDragLeave={() => setDragOver(false)}
            onDrop={handleDrop}
            ref={dropZoneRef}
        >
            {/* Chat Header */}
            <div className="flex items-center p-4 border-b border-gray-200 bg-white">
                <button
                    onClick={onBack}
                    className="lg:hidden p-2 hover:bg-gray-100 rounded-full mr-2"
                >
                    <svg className="w-5 h-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                </button>
                <div className="flex items-center flex-1">
                    <div className="relative">
                        <img
                            src={getCdnUrl(chat.user?.profile_image || chat.talent?.profile_image)}
                            alt={chat.user?.nickname || chat.talent?.nickname || chat.title}
                            className="w-10 h-10 rounded-full object-cover"
                            onError={(e) => {
                                e.target.src = '/default-avatar.png';
                            }}
                        />
                        {chat.is_active && (
                            <span className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full
                                border-2 border-white"></span>
                        )}
                    </div>
                    <div className="ml-3">
                        <div className="flex items-center">
                            <h2 className="text-sm font-semibold text-gray-900">
                                {chat.user?.nickname || chat.talent?.nickname || chat.title}
                            </h2>

                            {/* Mission indicator */}
                            {chat.metadata?.type === 'mission' && (
                                <Badge className="ml-2 bg-indigo-100 text-indigo-800 hover:bg-indigo-200 transition-colors">
                                    Mission
                                </Badge>
                            )}
                        </div>

                        {isTyping ? (
                            <motion.div
                                className="flex items-center text-xs text-indigo-600"
                                initial={{ opacity: 0, y: 5 }}
                                animate={{ opacity: 1, y: 0 }}
                                exit={{ opacity: 0, y: -5 }}
                            >
                                <div className="flex space-x-1 mr-2">
                                    <motion.div
                                        className="w-1 h-1 bg-indigo-600 rounded-full"
                                        animate={{ scale: [1, 1.5, 1] }}
                                        transition={{ duration: 1, repeat: Infinity, delay: 0 }}
                                    />
                                    <motion.div
                                        className="w-1 h-1 bg-indigo-600 rounded-full"
                                        animate={{ scale: [1, 1.5, 1] }}
                                        transition={{ duration: 1, repeat: Infinity, delay: 0.2 }}
                                    />
                                    <motion.div
                                        className="w-1 h-1 bg-indigo-600 rounded-full"
                                        animate={{ scale: [1, 1.5, 1] }}
                                        transition={{ duration: 1, repeat: Infinity, delay: 0.4 }}
                                    />
                                </div>
                                Typing...
                            </motion.div>
                        ) : chat.is_active ? (
                            <div className="flex items-center text-xs text-gray-500">
                                <div className="w-2 h-2 bg-green-400 rounded-full mr-1 animate-pulse"></div>
                                Active now
                            </div>
                        ) : chat.metadata?.type === 'mission' ? (
                            <div className="flex items-center mt-1">
                                <button
                                    onClick={() => window.open(`/missions/${chat.metadata.missionId}`, '_blank')}
                                    className="text-xs text-indigo-600 hover:text-indigo-800 flex items-center"
                                >
                                    <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                    </svg>
                                    View Mission Details
                                </button>
                            </div>
                        ) : null}
                    </div>
                </div>
            </div>

            {/* Drag & drop overlay */}
            <AnimatePresence>
                {dragOver && (
                    <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        className="absolute inset-0 bg-indigo-500/20 backdrop-blur-sm flex items-center justify-center"
                    >
                        <div className="bg-white p-6 rounded-xl shadow-lg text-center">
                            <div className="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg className="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3 3m0 0l-3-3m3 3V8" />
                                </svg>
                            </div>
                            <h3 className="text-lg font-medium text-gray-900">Drop files to send</h3>
                            <p className="text-sm text-gray-500 mt-1">Drop your images here to send them</p>
                    </div>
                    </motion.div>
                )}
            </AnimatePresence>

            {/* Messages area */}
            <VirtualizedMessageList
                messages={messages}
                renderMessage={(msg, isLast) => (
                            <MessageItem
                                key={msg.id}
                                msg={msg}
                        isLast={isLast}
                                onVisible={() => handleMessageVisible(msg.id)}
                                readReceipts={readReceipts[msg.id]}
                                isHighlighted={searchResults.some(r => r.id === msg.id)}
                                windowWidth={windowWidth}
                            />
                )}
                loading={loading}
                onLoadMore={() => {
                    if (hasMore && !loading) {
                        setPage(p => p + 1);
                    }
                }}
                hasMore={hasMore}
                onScrollChange={handleScrollChange}
                className="flex-1"
            />

            {/* File upload progress */}
            <AnimatePresence>
                {Object.keys(uploadProgress).length > 0 && (
                    <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: 'auto', opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        className="bg-white border-t border-gray-200 p-4"
                    >
                        {Object.entries(uploadProgress).map(([id, progress]) => (
                            <div key={id} className="flex items-center gap-4 mb-2 last:mb-0">
                                <div className="flex-1">
                                    <div className="flex justify-between mb-1">
                                        <span className="text-sm font-medium">Uploading...</span>
                                        <span className="text-sm text-gray-500">{progress}%</span>
                                        </div>
                                    <Progress value={progress} className="h-2" />
                                            </div>
                                        </div>
                                    ))}
                    </motion.div>
                )}
            </AnimatePresence>

            {/* Scroll to bottom button */}
            <AnimatePresence>
                {showScrollButton && (
                    <motion.button
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: 10 }}
                        className="absolute bottom-24 right-4 p-2 bg-indigo-600 text-white rounded-full shadow-lg"
                        onClick={scrollToBottom}
                    >
                        <div className="relative">
                            <svg className="w-6 h-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                <path d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                            </svg>
                            {unreadCount > 0 && (
                                <Badge
                                    variant="destructive"
                                    className="absolute -top-2 -right-2 min-w-[20px] h-5"
                                >
                                    {unreadCount}
                                </Badge>
                            )}
                        </div>
                    </motion.button>
                )}
            </AnimatePresence>

            {/* Message input */}
            <div className="p-4 bg-white border-t border-gray-200">
                {/* Add suggested replies above the input */}
                <SuggestedReplies
                    suggestions={suggestedReplies}
                    onSelectReply={handleSelectSuggestedReply}
                    isLoading={loadingSuggestions}
                    conversationContext={{
                        lastMessage: messages[messages.length - 1],
                        partnerName: chat.user?.nickname || chat.talent?.nickname
                    }}
                />

            {/* Reply preview */}
            <AnimatePresence>
                {replyTo && (
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: 20 }}
                            className="bg-gray-100 p-3 rounded-t-xl border-t border-x border-gray-200 mb-2"
                    >
                        <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                                <div className="w-1 h-8 bg-indigo-500 rounded-full" />
                                <div>
                                    <div className="text-sm font-medium">
                                        Replying to {replyTo.sender_id === chat.user_id ? 'yourself' : chat.talent?.nickname}
                                    </div>
                                    <div className="text-sm text-gray-500 truncate">
                                        {replyTo.content}
                                    </div>
                                </div>
                            </div>
                            <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8 rounded-full hover:bg-gray-200"
                                onClick={cancelReply}
                            >
                                <svg className="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                    <path d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </Button>
                    </div>
                    </motion.div>
                )}
            </AnimatePresence>

                <div className="flex items-end space-x-3">
                    <div className="flex-1 bg-gradient-to-r from-gray-50 to-white rounded-2xl shadow-lg border border-gray-200/50 p-3 backdrop-blur-sm">
                        <div className="relative">
                        <Textarea
                            value={message}
                            onChange={(e) => {
                                setMessage(e.target.value);
                                handleTyping();
                            }}
                            onKeyDown={(e) => {
                                if (e.key === 'Enter' && !e.shiftKey) {
                                    e.preventDefault();
                                    handleSend();
                                }
                            }}
                            placeholder="Type a message..."
                            className="w-full bg-transparent border-0 focus:ring-0 resize-none
                                    p-3 max-h-32 text-sm placeholder-gray-400 pr-10 min-h-[48px]
                                    focus:placeholder-gray-300 transition-colors duration-200"
                            rows={1}
                            autoFocus
                        />

                            {/* Message character counter - shows when typing */}
                            {message.length > 0 && (
                                <div className="absolute right-2 bottom-2 text-xs text-gray-400">
                                    {message.length > 1000 ? (
                                        <span className={message.length > 1000 ? "text-red-500" : "text-amber-500"}>
                                            {message.length}/1000
                                        </span>
                                    ) : null}
                                </div>
                            )}
                        </div>

                        <div className="flex items-center justify-between px-2 border-t border-gray-200 mt-2 pt-2">
                            <div className="flex items-center space-x-3">
                                {/* File attachment button */}
                                <div className="relative group">
                                <input
                                    type="file"
                                    ref={fileInputRef}
                                    onChange={(e) => handleAttachment(e.target.files)}
                                    accept="image/*"
                                    className="hidden"
                                        multiple
                                />
                                <button
                                    onClick={() => fileInputRef.current?.click()}
                                        className="p-2 text-indigo-500 bg-indigo-50 hover:text-gray-600
                                            rounded-full hover:bg-indigo-50 transition-colors flex items-center justify-center"
                                    disabled={sending}
                                        title="Attach images"
                                    >
                                        <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                            <path strokeLinecap="round" strokeLinejoin="round" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                        </svg>
                                    </button>
                                    <span className="absolute -top-10 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                                        Attach images
                                    </span>
                                </div>

                                {/* Gift button */}
                                <div className="relative group">
                                    <button
                                        onClick={() => {
                                            console.group('🎁 Opening Gift Modal');
                                            console.log('Conversation ID:', chat.id);
                                            console.log('Partner name:', chat.user?.nickname || chat.talent?.nickname);
                                            console.log('Current wallet balance:', walletBalance);
                                            setIsGiftModalOpen(true);
                                            console.groupEnd();
                                        }}
                                        className="p-2 text-indigo-500 bg-indigo-50 hover:text-pink-600
                                            rounded-full hover:bg-pink-50 transition-colors flex items-center justify-center"
                                        title="Send a gift"
                                        disabled={sending}
                                    >
                                        <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                            <path strokeLinecap="round" strokeLinejoin="round" d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7" />
                                        </svg>
                                    </button>
                                    <span className="absolute -top-10 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                                        Send a gift
                                    </span>
                                </div>

                                {/* Emoji picker */}
                                <div className="relative group">
                                    <button
                                        className="p-2 text-indigo-500 bg-indigo-50 hover:text-gray-600
                                            rounded-full hover:bg-indigo-50 transition-colors flex items-center justify-center"
                                        title="Add emoji"
                                    >
                                        <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                            <path strokeLinecap="round" strokeLinejoin="round" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </button>
                                    <span className="absolute -top-10 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                                        Add emoji
                                    </span>
                                </div>
                            </div>

                            {/* Send button */}
                            <motion.button
                                onClick={handleSend}
                                disabled={(!message.trim() && attachments.length === 0) || sending}
                                className={cn(
                                    "px-6 py-3 rounded-full text-sm font-semibold transition-all duration-300 flex items-center justify-center relative overflow-hidden",
                                    "min-w-[80px] h-12",
                                    message.trim() || attachments.length > 0
                                        ? "bg-gradient-to-r from-indigo-500 via-purple-500 to-indigo-600 text-white shadow-lg shadow-indigo-500/30"
                                        : "bg-gray-200 text-gray-400 cursor-not-allowed"
                                )}
                                whileHover={
                                    (message.trim() || attachments.length > 0) && !sending
                                        ? {
                                            scale: 1.05,
                                            boxShadow: "0 20px 25px -5px rgba(99, 102, 241, 0.4), 0 10px 10px -5px rgba(99, 102, 241, 0.04)"
                                        }
                                        : {}
                                }
                                whileTap={
                                    (message.trim() || attachments.length > 0) && !sending
                                        ? { scale: 0.95 }
                                        : {}
                                }
                                transition={{ type: "spring", stiffness: 400, damping: 25 }}
                            >
                                {/* Animated background gradient */}
                                {(message.trim() || attachments.length > 0) && !sending && (
                                    <motion.div
                                        className="absolute inset-0 bg-gradient-to-r from-purple-500 via-indigo-500 to-purple-500"
                                        animate={{
                                            x: ["-100%", "100%"],
                                        }}
                                        transition={{
                                            duration: 3,
                                            repeat: Infinity,
                                            ease: "linear"
                                        }}
                                    />
                                )}

                                <div className="relative z-10 flex items-center">
                                    {sending ? (
                                        <motion.span
                                            className="flex items-center"
                                            initial={{ opacity: 0 }}
                                            animate={{ opacity: 1 }}
                                        >
                                            <motion.svg
                                                className="w-4 h-4 text-white mr-2"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                animate={{ rotate: 360 }}
                                                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                                            >
                                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                            </motion.svg>
                                            Sending
                                        </motion.span>
                                    ) : (
                                        <motion.span
                                            className="flex items-center"
                                            whileHover={{ x: 2 }}
                                            transition={{ type: "spring", stiffness: 400, damping: 25 }}
                                        >
                                            Send
                                            <motion.svg
                                                className="ml-2 w-4 h-4"
                                                viewBox="0 0 20 20"
                                                fill="currentColor"
                                                whileHover={{ x: 3, rotate: -15 }}
                                                transition={{ type: "spring", stiffness: 400, damping: 25 }}
                                            >
                                                <path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z" />
                                            </motion.svg>
                                        </motion.span>
                                    )}
                                </div>
                            </motion.button>
                        </div>

                        {/* Display attachments preview */}
                        {attachments.length > 0 && (
                            <div className="flex flex-wrap gap-2 mt-2 pt-2 border-t border-gray-200">
                                {attachments.map((attachment, index) => (
                                    <div key={index} className="relative group">
                                        <img
                                            src={attachment.preview}
                                            alt="Attachment preview"
                                            className="h-16 w-16 rounded-md object-cover border border-gray-200"
                                        />
                                        <button
                                            onClick={() => setAttachments(prev => prev.filter((_, i) => i !== index))}
                                            className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center shadow-md opacity-0 group-hover:opacity-100 transition-opacity"
                                        >
                                            ×
                                        </button>
                                    </div>
                                ))}
                            </div>
                        )}

                        {isTyping && (
                            <div className="flex items-center mt-2 animate-pulse">
                                <span className="w-2 h-2 bg-indigo-400 rounded-full mr-2 animate-bounce"></span>
                                <span className="text-xs text-indigo-500 font-medium animate-pulse">Typing...</span>
                            </div>
                        )}
                    </div>
                </div>
            </div>

            {/* Gift Modal */}
            <GiftModal
                isOpen={isGiftModalOpen}
                onClose={() => {
                    console.log('🎁 Closing Gift Modal without sending');
                    setIsGiftModalOpen(false);
                }}
                onSendGift={handleSendGift}
                conversationId={chat.id}
                conversationPartnerName={chat.user?.nickname || chat.talent?.nickname}
            />

            {/* Gift Notification */}
            <GiftNotification
                gift={receivedGift?.gift}
                senderName={receivedGift?.sender_name || "Someone"}
                isVisible={showGiftNotification}
                onClose={() => setShowGiftNotification(false)}
                onView={() => {
                    setShowGiftNotification(false);
                    // Scroll to the gift message if possible
                    const giftMessageEl = document.getElementById(`message-${receivedGift?.message_id}`);
                    if (giftMessageEl) {
                        giftMessageEl.scrollIntoView({ behavior: 'smooth' });
                    }
                }}
                position="bottom-right"
            />
        </div>
    );
};

export default ChatWindow;