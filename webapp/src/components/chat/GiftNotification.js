import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '../../lib/utils';
import GiftItem from './GiftItem';

/**
 * GiftNotification component displays a popup when a gift is received
 * 
 * @param {Object} props
 * @param {Object} props.gift - Gift data including id, name, image_url, etc.
 * @param {string} props.senderName - Name of the gift sender
 * @param {Function} props.onClose - Function to call when notification is closed
 * @param {Function} props.onView - Function to call when 'View' button is clicked
 * @param {boolean} props.isVisible - Whether the notification is visible
 * @param {string} props.position - Position of the notification ('top-right', 'bottom-right', etc.)
 */
const GiftNotification = ({ 
    gift, 
    senderName, 
    onClose, 
    onView,
    isVisible = true,
    position = 'bottom-right'
}) => {
    const [autoCloseTimer, setAutoCloseTimer] = useState(null);

    // Position classes based on position prop
    const positionClasses = {
        'top-right': 'top-4 right-4',
        'top-left': 'top-4 left-4',
        'bottom-right': 'bottom-4 right-4',
        'bottom-left': 'bottom-4 left-4',
        'top-center': 'top-4 left-1/2 -translate-x-1/2',
        'bottom-center': 'bottom-4 left-1/2 -translate-x-1/2',
    };

    // Set up auto-close timer
    useEffect(() => {
        if (isVisible) {
            // Auto-close after 6 seconds
            const timer = setTimeout(() => {
                onClose();
            }, 6000);
            
            setAutoCloseTimer(timer);
            
            return () => {
                clearTimeout(timer);
            };
        }
    }, [isVisible, onClose]);

    // Cancel auto-close when hovering
    const handleMouseEnter = () => {
        if (autoCloseTimer) {
            clearTimeout(autoCloseTimer);
            setAutoCloseTimer(null);
        }
    };

    // Restart auto-close timer when mouse leaves
    const handleMouseLeave = () => {
        const timer = setTimeout(() => {
            onClose();
        }, 3000);
        
        setAutoCloseTimer(timer);
    };

    return (
        <AnimatePresence>
            {isVisible && gift && (
                <motion.div
                    className={cn(
                        "fixed z-50 max-w-sm w-full",
                        positionClasses[position]
                    )}
                    initial={{ opacity: 0, y: 50, scale: 0.9 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    exit={{ opacity: 0, y: 20, scale: 0.9 }}
                    transition={{ 
                        type: 'spring', 
                        stiffness: 300, 
                        damping: 20 
                    }}
                    onMouseEnter={handleMouseEnter}
                    onMouseLeave={handleMouseLeave}
                >
                    <div className="bg-white border border-indigo-100 rounded-xl shadow-2xl overflow-hidden">
                        {/* Gift celebration header */}
                        <div className="bg-gradient-to-r from-indigo-500 to-purple-600 text-white p-4">
                            <h3 className="text-lg font-bold flex items-center">
                                <span className="mr-2">🎉</span>
                                Gift Received!
                            </h3>
                        </div>
                        
                        {/* Gift content */}
                        <div className="p-4 bg-gradient-to-b from-indigo-50 to-white">
                            <div className="flex">
                                <div className="flex-shrink-0 mr-4">
                                    <GiftItem
                                        gift={gift}
                                        showPrice={false}
                                        size="lg"
                                        animate={true}
                                    />
                                </div>
                                
                                <div className="flex-1">
                                    <h4 className="text-gray-900 font-semibold text-lg mb-1">
                                        {senderName} sent you a {gift.name}!
                                    </h4>
                                    
                                    {gift.message && (
                                        <p className="text-gray-600 text-sm mb-3 italic">
                                            "{gift.message}"
                                        </p>
                                    )}
                                    
                                    <div className="flex space-x-2 mt-4">
                                        <button
                                            className="flex-1 py-2 px-4 bg-indigo-600 hover:bg-indigo-700 text-white rounded-md text-sm font-medium transition-colors"
                                            onClick={onView}
                                        >
                                            View
                                        </button>
                                        <button
                                            className="py-2 px-4 bg-white border border-gray-200 hover:bg-gray-50 text-gray-600 rounded-md text-sm font-medium transition-colors"
                                            onClick={onClose}
                                        >
                                            Dismiss
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </motion.div>
            )}
        </AnimatePresence>
    );
};

export default GiftNotification; 