import React, { useState } from 'react';
import { 
  filterTalents, 
  getServiceTypes, 
  getServiceStyles 
} from '../services/talentService';
import ErrorMessage from './common/ErrorMessage';
import { API_ERROR_TYPES } from '../utils/errorTypes';

/**
 * A component to test error handling
 */
const ErrorHandlingTest = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [result, setResult] = useState(null);
  const [testType, setTestType] = useState('filterTalents');
  const [errorType, setErrorType] = useState('none');

  // Function to simulate different types of errors
  const simulateError = async (type) => {
    // Create a mock axios error
    const mockError = {
      response: {
        status: 500,
        data: {
          message: 'Server error'
        }
      }
    };

    // Customize the error based on the selected type
    switch (type) {
      case 'network':
        delete mockError.response;
        mockError.message = 'Network Error';
        break;
      case 'auth':
        mockError.response.status = 401;
        mockError.response.data.message = 'Unauthorized';
        break;
      case 'validation':
        mockError.response.status = 422;
        mockError.response.data.message = 'Validation failed';
        mockError.response.data.errors = {
          'service_type_id': ['The service type id field is required.'],
          'min_level': ['The min level must be a number.']
        };
        break;
      case 'notFound':
        mockError.response.status = 404;
        mockError.response.data.message = 'Resource not found';
        break;
      case 'rateLimit':
        mockError.response.status = 429;
        mockError.response.data.message = 'Too Many Requests';
        mockError.response.headers = {
          'retry-after': '60'
        };
        break;
      default:
        // Use the default server error
    }

    throw mockError;
  };

  // Function to run the selected test
  const runTest = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      let response;

      // If we're simulating an error, throw it
      if (errorType !== 'none') {
        await simulateError(errorType);
      }

      // Otherwise, call the actual API function
      switch (testType) {
        case 'filterTalents':
          response = await filterTalents();
          break;
        case 'getServiceTypes':
          response = await getServiceTypes();
          break;
        case 'getServiceStyles':
          response = await getServiceStyles();
          break;
        default:
          throw new Error('Invalid test type');
      }

      setResult(response);
    } catch (err) {
      setError(err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-4 max-w-4xl mx-auto">
      <h2 className="text-2xl font-bold mb-4">Error Handling Test</h2>
      
      <div className="bg-white p-4 rounded-lg shadow mb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              API Function
            </label>
            <select
              value={testType}
              onChange={(e) => setTestType(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md"
            >
              <option value="filterTalents">Filter Talents</option>
              <option value="getServiceTypes">Get Service Types</option>
              <option value="getServiceStyles">Get Service Styles</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Simulate Error
            </label>
            <select
              value={errorType}
              onChange={(e) => setErrorType(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md"
            >
              <option value="none">No Error</option>
              <option value="network">Network Error</option>
              <option value="auth">Authentication Error</option>
              <option value="validation">Validation Error</option>
              <option value="notFound">Not Found Error</option>
              <option value="rateLimit">Rate Limit Error</option>
              <option value="server">Server Error</option>
            </select>
          </div>
        </div>
        
        <button
          onClick={runTest}
          disabled={loading}
          className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:bg-blue-300"
        >
          {loading ? 'Running...' : 'Run Test'}
        </button>
      </div>
      
      {error && (
        <div className="mb-6">
          <h3 className="text-lg font-medium mb-2">Error Response</h3>
          <ErrorMessage error={error} onRetry={runTest} />
          
          <div className="mt-4 bg-gray-50 p-4 rounded-md">
            <h4 className="text-sm font-medium mb-2">Error Details</h4>
            <pre className="text-xs overflow-auto p-2 bg-gray-100 rounded">
              {JSON.stringify(error, null, 2)}
            </pre>
          </div>
        </div>
      )}
      
      {result && (
        <div>
          <h3 className="text-lg font-medium mb-2">Success Response</h3>
          <div className="bg-green-50 p-4 rounded-md">
            <pre className="text-xs overflow-auto p-2 bg-green-100 rounded">
              {JSON.stringify(result, null, 2)}
            </pre>
          </div>
        </div>
      )}
    </div>
  );
};

export default ErrorHandlingTest;
