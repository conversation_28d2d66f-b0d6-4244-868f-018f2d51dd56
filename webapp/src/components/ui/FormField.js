/**
 * Form Field Component
 * Enhanced form inputs with validation states and error handling
 */

import React, { useState, forwardRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '../../lib/utils';
import { EyeIcon, EyeSlashIcon, ExclamationCircleIcon, CheckCircleIcon } from '@heroicons/react/24/outline';

/**
 * FormField Component
 * @param {Object} props
 * @param {string} props.label - Field label
 * @param {string} props.type - Input type
 * @param {string} props.value - Input value
 * @param {Function} props.onChange - Change handler
 * @param {string} props.error - Error message
 * @param {boolean} props.success - Success state
 * @param {string} props.placeholder - Placeholder text
 * @param {boolean} props.required - Required field
 * @param {boolean} props.disabled - Disabled state
 * @param {string} props.className - Additional CSS classes
 * @param {React.ReactNode} props.icon - Leading icon
 * @param {React.ReactNode} props.suffix - Trailing content
 * @param {string} props.helperText - Helper text
 * @param {Object} props.validation - Validation state
 */
const FormField = forwardRef(({
  label,
  type = 'text',
  value = '',
  onChange,
  error,
  success,
  placeholder,
  required = false,
  disabled = false,
  className = '',
  icon,
  suffix,
  helperText,
  validation,
  ...props
}, ref) => {
  const [showPassword, setShowPassword] = useState(false);
  const [isFocused, setIsFocused] = useState(false);

  const isPassword = type === 'password';
  const inputType = isPassword && showPassword ? 'text' : type;
  const hasError = error || (validation && !validation.valid && validation.message);
  const hasSuccess = success || (validation && validation.valid);
  const errorMessage = error || (validation && validation.message);

  const handleTogglePassword = () => {
    setShowPassword(!showPassword);
  };

  return (
    <motion.div
      className={cn('space-y-2', className)}
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* Label */}
      {label && (
        <label className="block text-sm font-medium text-gray-700">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}

      {/* Input Container */}
      <div className="relative">
        {/* Leading Icon */}
        {icon && (
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-black w-5 h-5 z-10">
            {React.cloneElement(icon, { className: 'w-5 h-5' })}
          </div>
        )}

        {/* Input Field */}
        <input
          ref={ref}
          type={inputType}
          value={value}
          onChange={onChange}
          placeholder={placeholder}
          disabled={disabled}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          className={cn(
            // Base styles
            'w-full px-4 py-4 rounded-xl border transition-all duration-300',
            'focus:outline-none focus:ring-4 focus:ring-opacity-50',
            'placeholder-gray-500 text-gray-900',
            'bg-white',
            // Icon padding
            icon && 'pl-12',
            (isPassword || suffix) && 'pr-12',
            // State styles
            isFocused && 'transform scale-[1.02]',
            hasError && 'border-red-300 focus:border-red-500 focus:ring-red-500',
            hasSuccess && 'border-green-300 focus:border-green-500 focus:ring-green-500',
            !hasError && !hasSuccess && 'border-gray-300 focus:border-indigo-500 focus:ring-indigo-500',
            disabled && 'bg-gray-100 cursor-not-allowed opacity-60'
          )}
          {...props}
        />

        {/* Password Toggle / Suffix */}
        {(isPassword || suffix) && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            {isPassword ? (
              <button
                type="button"
                onClick={handleTogglePassword}
                className="text-black hover:text-gray-900 transition-colors duration-200 w-5 h-5 z-10"
                disabled={disabled}
              >
                {showPassword ? 
                  <EyeSlashIcon className="w-5 h-5" /> : 
                  <EyeIcon className="w-5 h-5" />
                }
              </button>
            ) : (
              suffix
            )}
          </div>
        )}

        {/* Validation Icon */}
        {(hasError || hasSuccess) && (
          <motion.div
            className="absolute right-10 top-1/2 transform -translate-y-1/2"
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ type: "spring", stiffness: 500, damping: 30 }}
          >
            {hasError ? (
              <ExclamationCircleIcon className="w-5 h-5 text-red-600" />
            ) : (
              <CheckCircleIcon className="w-5 h-5 text-green-600" />
            )}
          </motion.div>
        )}
      </div>

      {/* Error/Success/Helper Text */}
      <AnimatePresence mode="wait">
        {(hasError || hasSuccess || helperText) && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.2 }}
            className="overflow-hidden"
          >
            <p className={cn(
              'text-sm',
              hasError && 'text-red-600',
              hasSuccess && 'text-green-600',
              !hasError && !hasSuccess && 'text-gray-500'
            )}>
              {errorMessage || (hasSuccess && 'Looks good!') || helperText}
            </p>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
});

FormField.displayName = 'FormField';

/**
 * Specialized form field variants
 */

// Phone Number Field with Malaysian formatting
export const PhoneField = ({ value, onChange, ...props }) => {
  const handlePhoneChange = (e) => {
    const formattedValue = e.target.value; // Add phone formatting logic here
    onChange({ ...e, target: { ...e.target, value: formattedValue } });
  };

  return (
    <FormField
      type="tel"
      value={value}
      onChange={handlePhoneChange}
      placeholder="+60 12-345 6789"
      {...props}
    />
  );
};

// OTP Input Field
export const OTPField = ({ value, onChange, length = 6, ...props }) => {
  const handleOTPChange = (e) => {
    const newValue = e.target.value.replace(/\D/g, '').slice(0, length);
    onChange({ ...e, target: { ...e.target, value: newValue } });
  };

  return (
    <FormField
      type="text"
      value={value}
      onChange={handleOTPChange}
      placeholder="000000"
      className="text-center text-2xl font-mono tracking-widest"
      maxLength={length}
      {...props}
    />
  );
};

// Date Field
export const DateField = ({ ...props }) => (
  <FormField
    type="date"
    {...props}
  />
);

// Select Field Base Component
const SelectBase = ({ 
  options = [], 
  value, 
  onChange, 
  placeholder = 'Select an option',
  error,
  success,
  validation,
  ...props 
}) => {
  const hasError = error || (validation && !validation.valid && validation.message);
  const hasSuccess = success || (validation && validation.valid);
  
  return (
    <div className="relative">
      <select
        value={value}
        onChange={onChange}
        className={cn(
          'w-full px-4 py-4 rounded-xl border transition-all duration-300',
          'focus:outline-none focus:ring-4 focus:ring-opacity-50',
          'bg-white text-gray-900',
          'appearance-none cursor-pointer',
          hasError && 'border-red-300 focus:border-red-500 focus:ring-red-500',
          hasSuccess && 'border-green-300 focus:border-green-500 focus:ring-green-500',
          !hasError && !hasSuccess && 'border-gray-300 focus:border-indigo-500 focus:ring-indigo-500'
        )}
        {...props}
      >
        <option value="">{placeholder}</option>
        {options.map((option, index) => (
          <option key={index} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
      <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none z-10">
        <svg className="w-5 h-5 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </div>
      
      {/* Validation Icon */}
      {(hasError || hasSuccess) && (
        <div className="absolute right-10 top-1/2 transform -translate-y-1/2">
          {hasError ? (
            <ExclamationCircleIcon className="w-5 h-5 text-red-600" />
          ) : (
            <CheckCircleIcon className="w-5 h-5 text-green-600" />
          )}
        </div>
      )}
    </div>
  );
};

// Select Field with FormField-like wrapper
export const SelectField = ({ 
  label,
  options = [], 
  value, 
  onChange, 
  placeholder = 'Select an option',
  error,
  success,
  validation,
  required = false,
  helperText,
  className = '',
  ...props 
}) => {
  const hasError = error || (validation && !validation.valid && validation.message);
  const hasSuccess = success || (validation && validation.valid);
  const errorMessage = error || (validation && validation.message);
  
  return (
    <motion.div
      className={cn('space-y-2', className)}
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* Label */}
      {label && (
        <label className="block text-sm font-medium text-gray-700">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      {/* Select Input */}
      <SelectBase
        options={options}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        error={error}
        success={success}
        validation={validation}
        required={required}
        {...props}
      />
      
      {/* Error/Success/Helper Text */}
      <AnimatePresence mode="wait">
        {(hasError || hasSuccess || helperText) && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.2 }}
            className="overflow-hidden"
          >
            <p className={cn(
              'text-sm',
              hasError && 'text-red-600',
              hasSuccess && 'text-green-600',
              !hasError && !hasSuccess && 'text-gray-500'
            )}>
              {errorMessage || (hasSuccess && 'Looks good!') || helperText}
            </p>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default FormField;