/**
 * UI Skeleton Components
 * 
 * This file contains skeleton loading components for various UI elements.
 */

import React from 'react';

/**
 * Game Card Skeleton
 * @returns {JSX.Element} - Game card skeleton component
 */
export const GameCardSkeleton = () => {
  return (
    <div className="bg-white rounded-2xl overflow-hidden shadow-sm border border-gray-100 animate-pulse">
      <div className="h-32 sm:h-36 bg-gray-200"></div>
      <div className="p-3 flex-grow flex flex-col justify-between">
        <div className="h-5 bg-gray-200 rounded w-3/4 mx-auto"></div>
      </div>
    </div>
  );
};

/**
 * Talent Card Skeleton
 * @returns {JSX.Element} - Talent card skeleton component
 */
export const TalentCardSkeleton = () => {
  return (
    <div className="bg-white rounded-2xl overflow-hidden shadow-md border border-gray-100 animate-pulse">
      <div className="h-48 bg-gray-200"></div>
      <div className="p-4">
        <div className="flex justify-between items-center mb-2">
          <div className="h-5 bg-gray-200 rounded w-1/3"></div>
          <div className="h-5 bg-gray-200 rounded w-1/4"></div>
        </div>
        <div className="h-4 bg-gray-200 rounded w-full mb-1"></div>
        <div className="h-4 bg-gray-200 rounded w-3/4 mb-3"></div>
        <div className="mt-3 flex justify-between items-center">
          <div className="h-6 bg-gray-200 rounded w-1/4"></div>
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
        </div>
      </div>
    </div>
  );
};

/**
 * New Talent Card Skeleton
 * @returns {JSX.Element} - New talent card skeleton component
 */
export const NewTalentCardSkeleton = () => {
  return (
    <div className="bg-white rounded-2xl overflow-hidden shadow-md border border-gray-100 animate-pulse">
      <div className="h-40 bg-gray-200"></div>
      <div className="p-3">
        <div className="h-5 bg-gray-200 rounded w-1/2 mb-2"></div>
        <div className="h-8 bg-gray-200 rounded w-full"></div>
      </div>
    </div>
  );
};

/**
 * Mission Banner Skeleton
 * @returns {JSX.Element} - Mission banner skeleton component
 */
export const MissionBannerSkeleton = () => {
  return (
    <div className="mt-8 bg-gradient-to-r from-gray-200 to-gray-300 rounded-2xl p-6 shadow-lg animate-pulse">
      <div className="flex flex-col md:flex-row items-center justify-between">
        <div className="mb-6 md:mb-0 md:mr-8 w-full md:w-2/3">
          <div className="h-8 bg-gray-300 rounded w-3/4 mb-3"></div>
          <div className="h-4 bg-gray-300 rounded w-full mb-2"></div>
          <div className="h-4 bg-gray-300 rounded w-5/6 mb-4"></div>
          <div className="flex space-x-4">
            <div className="h-4 bg-gray-300 rounded w-24"></div>
            <div className="h-4 bg-gray-300 rounded w-24"></div>
          </div>
        </div>
        <div className="flex flex-col space-y-2 w-full md:w-1/3">
          <div className="h-12 bg-gray-300 rounded w-full"></div>
          <div className="h-10 bg-gray-300 rounded w-full"></div>
        </div>
      </div>
    </div>
  );
};

/**
 * Game Cards Skeleton Grid
 * @param {Object} props - Component props
 * @param {number} props.count - Number of skeleton cards to display
 * @returns {JSX.Element} - Grid of game card skeletons
 */
export const GameCardSkeletonGrid = ({ count = 4 }) => {
  return (
    <div className="grid grid-cols-2 sm:grid-cols-4 gap-5 md:gap-6">
      {Array(count).fill(0).map((_, idx) => (
        <GameCardSkeleton key={idx} />
      ))}
    </div>
  );
};

/**
 * Talent Cards Skeleton Grid
 * @param {Object} props - Component props
 * @param {number} props.count - Number of skeleton cards to display
 * @param {number} props.columns - Number of columns in the grid
 * @returns {JSX.Element} - Grid of talent card skeletons
 */
export const TalentCardSkeletonGrid = ({ count = 3, columns = 3 }) => {
  return (
    <div className={`grid grid-cols-1 md:grid-cols-${columns} gap-5`}>
      {Array(count).fill(0).map((_, idx) => (
        <TalentCardSkeleton key={idx} />
      ))}
    </div>
  );
};

/**
 * New Talent Cards Skeleton Grid
 * @param {Object} props - Component props
 * @param {number} props.count - Number of skeleton cards to display
 * @returns {JSX.Element} - Grid of new talent card skeletons
 */
export const NewTalentCardSkeletonGrid = ({ count = 4 }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-5">
      {Array(count).fill(0).map((_, idx) => (
        <NewTalentCardSkeleton key={idx} />
      ))}
    </div>
  );
};
