/**
 * Spinner Component
 * Loading spinner with different variants and sizes
 * 
 * This implementation uses an SVG-based spinner with more styling options
 * and visual customization compared to the simplified border-based version.
 * This provides better visual consistency across different sizes and variants.
 */

import React from 'react';
import { cn } from '../../lib/utils';

/**
 * Spinner Component
 * @param {Object} props
 * @param {string} props.size - Spinner size (xs, sm, default, lg, xl)
 * @param {string} props.variant - Spinner variant (default, light, dark)
 * @param {string} props.className - Additional CSS classes
 */
export const Spinner = ({ 
  size = 'default', 
  variant = 'default', 
  className = '' 
}) => {
  const sizes = {
    xs: 'w-3 h-3',
    sm: 'w-4 h-4',
    default: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12'
  };

  const variants = {
    default: 'text-indigo-600',
    light: 'text-white',
    dark: 'text-gray-800'
  };

  return (
    <svg
      className={cn(
        'animate-spin',
        sizes[size],
        variants[variant],
        className
      )}
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
    >
      <circle
        className="opacity-25"
        cx="12"
        cy="12"
        r="10"
        stroke="currentColor"
        strokeWidth="4"
      />
      <path
        className="opacity-75"
        fill="currentColor"
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      />
    </svg>
  );
};

export default Spinner;
