/**
 * Gradient <PERSON><PERSON> Component
 * Animated gradient buttons with Framer Motion animations
 */

import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '../../lib/utils';
import { Spinner } from './spinner';

/**
 * GradientButton Component
 * @param {Object} props
 * @param {React.ReactNode} props.children - Button content
 * @param {string} props.variant - Button variant
 * @param {string} props.size - Button size
 * @param {boolean} props.loading - Loading state
 * @param {boolean} props.disabled - Disabled state
 * @param {string} props.className - Additional CSS classes
 * @param {Function} props.onClick - Click handler
 * @param {string} props.type - Button type
 * @param {Object} props.motionProps - Additional Framer Motion props
 */
const GradientButton = ({
  children,
  variant = 'primary',
  size = 'default',
  loading = false,
  disabled = false,
  className = '',
  onClick,
  type = 'button',
  ...motionProps
}) => {
  // Variant styles
  const variants = {
    primary: {
      base: 'bg-gradient-to-r from-indigo-600 via-purple-600 to-blue-600',
      hover: 'from-indigo-700 via-purple-700 to-blue-700',
      text: 'text-white',
      shadow: 'shadow-lg shadow-indigo-500/25'
    },
    secondary: {
      base: 'bg-gradient-to-r from-gray-600 via-gray-700 to-gray-800',
      hover: 'from-gray-700 via-gray-800 to-gray-900',
      text: 'text-white',
      shadow: 'shadow-lg shadow-gray-500/25'
    },
    success: {
      base: 'bg-gradient-to-r from-green-500 via-emerald-500 to-teal-500',
      hover: 'from-green-600 via-emerald-600 to-teal-600',
      text: 'text-white',
      shadow: 'shadow-lg shadow-green-500/25'
    },
    danger: {
      base: 'bg-gradient-to-r from-red-500 via-rose-500 to-pink-500',
      hover: 'from-red-600 via-rose-600 to-pink-600',
      text: 'text-white',
      shadow: 'shadow-lg shadow-red-500/25'
    },
    outline: {
      base: 'bg-transparent border-2 border-indigo-500',
      hover: 'border-indigo-600 bg-indigo-50',
      text: 'text-indigo-600',
      shadow: 'shadow-lg shadow-indigo-500/10'
    },
    ghost: {
      base: 'bg-white/10 backdrop-blur-md border border-white/20',
      hover: 'bg-white/20 border-white/30',
      text: 'text-white',
      shadow: 'shadow-lg shadow-black/10'
    }
  };

  // Size styles
  const sizes = {
    sm: 'px-4 py-2 text-sm rounded-lg',
    default: 'px-6 py-3 text-base rounded-xl',
    lg: 'px-8 py-4 text-lg rounded-xl',
    xl: 'px-10 py-5 text-xl rounded-2xl'
  };

  const currentVariant = variants[variant];
  const isDisabled = disabled || loading;

  return (
    <motion.button
      type={type}
      onClick={onClick}
      disabled={isDisabled}
      className={cn(
        // Base styles
        'relative overflow-hidden font-semibold transition-all duration-300 transform-gpu',
        'focus:outline-none focus:ring-4 focus:ring-indigo-500/50',
        'disabled:opacity-50 disabled:cursor-not-allowed',
        // Variant styles
        currentVariant.base,
        currentVariant.text,
        currentVariant.shadow,
        // Size styles
        sizes[size],
        // Custom classes
        className
      )}
      whileHover={!isDisabled ? {
        scale: 1.02,
        boxShadow: '0 10px 25px rgba(0, 0, 0, 0.15)'
      } : {}}
      whileTap={!isDisabled ? {
        scale: 0.98
      } : {}}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
      {...motionProps}
    >
      {/* Hover overlay */}
      <motion.div
        className={cn(
          'absolute inset-0 opacity-0 transition-opacity duration-300',
          `bg-gradient-to-r ${currentVariant.hover}`
        )}
        whileHover={{ opacity: 1 }}
      />

      {/* Shimmer effect */}
      <div className="absolute inset-0 -top-2 -bottom-2 bg-gradient-to-r from-transparent via-white/20 to-transparent transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000" />

      {/* Content */}
      <div className="relative z-10 flex items-center justify-center space-x-2">
        {loading && (
          <Spinner 
            size="sm" 
            variant={variant === 'outline' ? 'default' : 'light'} 
          />
        )}
        <span className={loading ? 'opacity-75' : ''}>
          {children}
        </span>
      </div>
    </motion.button>
  );
};

/**
 * Specialized button variants
 */

// OTP Request Button with countdown
export const OTPButton = ({ 
  children, 
  countdown = 0, 
  onResend, 
  ...props 
}) => {
  const isCountingDown = countdown > 0;
  
  return (
    <GradientButton
      variant={isCountingDown ? 'secondary' : 'primary'}
      disabled={isCountingDown}
      onClick={onResend}
      {...props}
    >
      {isCountingDown ? `Resend in ${countdown}s` : children}
    </GradientButton>
  );
};

// Submit Button with loading state
export const SubmitButton = ({ 
  children, 
  loading = false, 
  ...props 
}) => (
  <GradientButton
    type="submit"
    variant="primary"
    size="lg"
    loading={loading}
    className="w-full"
    {...props}
  >
    {loading ? 'Processing...' : children}
  </GradientButton>
);

// Social Login Button
export const SocialButton = ({ 
  provider, 
  icon, 
  children, 
  ...props 
}) => {
  const socialVariants = {
    google: 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50',
    apple: 'bg-black text-white hover:bg-gray-900',
    facebook: 'bg-blue-600 text-white hover:bg-blue-700'
  };

  return (
    <motion.button
      className={cn(
        'flex items-center justify-center space-x-3 px-6 py-3 rounded-xl font-medium transition-all duration-300',
        'focus:outline-none focus:ring-4 focus:ring-opacity-50',
        socialVariants[provider] || socialVariants.google
      )}
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      {...props}
    >
      {icon && <span className="w-5 h-5">{icon}</span>}
      <span>{children}</span>
    </motion.button>
  );
};

// Icon Button
export const IconButton = ({ 
  icon, 
  children, 
  ...props 
}) => (
  <GradientButton
    variant="ghost"
    size="sm"
    className="flex items-center space-x-2"
    {...props}
  >
    {icon && <span className="w-4 h-4">{icon}</span>}
    {children && <span>{children}</span>}
  </GradientButton>
);

export default GradientButton;
