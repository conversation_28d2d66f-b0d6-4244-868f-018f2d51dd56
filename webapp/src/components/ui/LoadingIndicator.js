import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';

// Main Loading Indicator Component
const LoadingIndicator = ({
  type = 'page',
  size = 'medium',
  message = 'Loading...',
  showMessage = true,
  className = '',
  overlay = false,
  color = 'indigo'
}) => {
  const sizeClasses = {
    small: 'w-6 h-6',
    medium: 'w-12 h-12',
    large: 'w-16 h-16',
    xlarge: 'w-20 h-20'
  };

  const colorClasses = {
    indigo: {
      primary: 'from-indigo-600 to-blue-600',
      secondary: 'from-indigo-400 to-blue-400',
      accent: 'bg-indigo-600',
      text: 'text-indigo-600'
    },
    purple: {
      primary: 'from-purple-600 to-indigo-600',
      secondary: 'from-purple-400 to-indigo-400',
      accent: 'bg-purple-600',
      text: 'text-purple-600'
    },
    blue: {
      primary: 'from-blue-600 to-cyan-600',
      secondary: 'from-blue-400 to-cyan-400',
      accent: 'bg-blue-600',
      text: 'text-blue-600'
    }
  };

  const colors = colorClasses[color] || colorClasses.indigo;

  // Spinning Gradient Ring Loader
  const SpinningRing = () => (
    <div className={`relative ${sizeClasses[size]}`}>
      {/* Outer spinning ring */}
      <div className={`absolute inset-0 rounded-full bg-gradient-to-r ${colors.primary} animate-spin`}>
        <div className="absolute inset-1 rounded-full bg-white"></div>
      </div>

      {/* Inner pulsing dot */}
      <div className="absolute inset-0 flex items-center justify-center">
        <div className={`w-2 h-2 rounded-full ${colors.accent} animate-pulse`}></div>
      </div>

      {/* Glow effect */}
      <div className={`absolute inset-0 rounded-full bg-gradient-to-r ${colors.secondary} opacity-30 blur-sm animate-pulse`}></div>
    </div>
  );

  // Pulsing Dots Loader
  const PulsingDots = () => (
    <div className="flex space-x-2">
      {[0, 1, 2].map((index) => (
        <motion.div
          key={index}
          className={`w-3 h-3 rounded-full bg-gradient-to-r ${colors.primary}`}
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.7, 1, 0.7]
          }}
          transition={{
            duration: 1.5,
            repeat: Infinity,
            delay: index * 0.2
          }}
        />
      ))}
    </div>
  );

  // Wave Loader
  const WaveLoader = () => (
    <div className="flex space-x-1 items-end">
      {[0, 1, 2, 3, 4].map((index) => (
        <motion.div
          key={index}
          className={`w-2 bg-gradient-to-t ${colors.primary} rounded-full`}
          animate={{
            height: ['8px', '24px', '8px']
          }}
          transition={{
            duration: 1.2,
            repeat: Infinity,
            delay: index * 0.1
          }}
        />
      ))}
    </div>
  );

  // Morphing Shapes Loader
  const MorphingShapes = () => (
    <div className={`relative ${sizeClasses[size]}`}>
      <motion.div
        className={`absolute inset-0 bg-gradient-to-r ${colors.primary} rounded-full`}
        animate={{
          borderRadius: ['50%', '20%', '50%'],
          rotate: [0, 180, 360]
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
      <motion.div
        className={`absolute inset-2 bg-white rounded-full`}
        animate={{
          borderRadius: ['50%', '30%', '50%']
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
    </div>
  );

  // Floating Particles Loader
  const FloatingParticles = () => (
    <div className={`relative ${sizeClasses[size]}`}>
      {[...Array(6)].map((_, index) => (
        <motion.div
          key={index}
          className={`absolute w-2 h-2 rounded-full bg-gradient-to-r ${colors.primary}`}
          style={{
            left: '50%',
            top: '50%',
            transformOrigin: '0 0'
          }}
          animate={{
            rotate: [0, 360],
            scale: [1, 0.5, 1],
            opacity: [1, 0.3, 1]
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            delay: index * 0.3
          }}
          initial={{
            x: Math.cos((index * 60) * Math.PI / 180) * 20,
            y: Math.sin((index * 60) * Math.PI / 180) * 20
          }}
        />
      ))}
      <div className={`absolute inset-0 flex items-center justify-center`}>
        <div className={`w-3 h-3 rounded-full ${colors.accent} animate-pulse`}></div>
      </div>
    </div>
  );

  const renderLoader = () => {
    switch (type) {
      case 'ring':
        return <SpinningRing />;
      case 'dots':
        return <PulsingDots />;
      case 'wave':
        return <WaveLoader />;
      case 'morph':
        return <MorphingShapes />;
      case 'particles':
        return <FloatingParticles />;
      case 'page':
      default:
        return <SpinningRing />;
    }
  };

  const content = (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.8 }}
      transition={{ duration: 0.3 }}
      className={`flex flex-col items-center justify-center space-y-4 ${className}`}
    >
      {renderLoader()}

      {showMessage && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="text-center"
        >
          <p className={`font-medium ${colors.text} text-lg`}>{message}</p>
          <div className="flex space-x-1 mt-2 justify-center">
            {[0, 1, 2].map((index) => (
              <motion.div
                key={index}
                className={`w-1 h-1 rounded-full ${colors.accent}`}
                animate={{
                  opacity: [0.3, 1, 0.3]
                }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  delay: index * 0.2
                }}
              />
            ))}
          </div>
        </motion.div>
      )}
    </motion.div>
  );

  if (overlay) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-white/80 backdrop-blur-sm z-50 flex items-center justify-center"
      >
        {content}
      </motion.div>
    );
  }

  return content;
};

// Page Loading Component
export const PageLoader = ({ message = 'Loading your experience...', color = 'indigo' }) => (
  <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50">
    <LoadingIndicator
      type="particles"
      size="large"
      message={message}
      color={color}
      className="p-8"
    />
  </div>
);

// Section Loading Component
export const SectionLoader = ({ message = 'Loading...', type = 'ring', size = 'medium', color = 'indigo' }) => (
  <div className="flex items-center justify-center py-12">
    <LoadingIndicator
      type={type}
      size={size}
      message={message}
      color={color}
    />
  </div>
);

// Inline Loading Component
export const InlineLoader = ({ size = 'small', color = 'indigo', className = '' }) => (
  <LoadingIndicator
    type="ring"
    size={size}
    showMessage={false}
    color={color}
    className={className}
  />
);

// Overlay Loading Component
export const OverlayLoader = ({ message = 'Processing...', color = 'indigo' }) => (
  <LoadingIndicator
    type="morph"
    size="large"
    message={message}
    overlay={true}
    color={color}
  />
);

// Navigation Transition Loader
export const NavigationLoader = () => (
  <motion.div
    initial={{ scaleX: 0 }}
    animate={{ scaleX: 1 }}
    exit={{ scaleX: 0 }}
    className="fixed top-0 left-0 right-0 h-1 bg-gradient-to-r from-indigo-600 via-purple-600 to-blue-600 z-50 origin-left"
    style={{ transformOrigin: 'left' }}
  />
);

export default LoadingIndicator;
