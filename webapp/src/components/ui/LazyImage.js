/**
 * LazyImage Component
 *
 * A component that lazily loads images when they enter the viewport.
 * Features:
 * - Uses Intersection Observer API for efficient lazy loading
 * - Shows a placeholder while the image is loading
 * - Handles loading errors with a fallback image
 * - Supports blur-up effect for a smoother loading experience
 * - Optimized for performance with proper cleanup
 */

import React, { useState, useEffect, useRef } from 'react';

const LazyImage = ({
  src,
  alt,
  className = '',
  placeholderSrc = '/images/profile-placeholder-low.svg',
  fallbackSrc = '/images/profile-placeholder.svg',
  blurUp = true,
  onLoad = () => {},
  onError = () => {},
  ...props
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isError, setIsError] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const imgRef = useRef(null);
  const observerRef = useRef(null);

  // Set up the Intersection Observer to detect when the image enters the viewport
  useEffect(() => {
    observerRef.current = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          setIsInView(true);
          // Disconnect the observer once the image is in view
          observerRef.current.disconnect();
        }
      },
      { threshold: 0.1 } // Trigger when at least 10% of the image is visible
    );

    if (imgRef.current) {
      observerRef.current.observe(imgRef.current);
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, []);

  // Handle image loading and errors
  const handleLoad = () => {
    setIsLoaded(true);
    onLoad();
  };

  const handleError = () => {
    setIsError(true);
    onError();
  };

  // Determine which image source to use
  const imageSrc = isError ? fallbackSrc : (isInView ? src : placeholderSrc);

  return (
    <div
      ref={imgRef}
      className={`relative overflow-hidden ${className}`}
      {...props}
    >
      {/* Main image */}
      <img
        src={imageSrc}
        alt={alt}
        className={`
          w-full h-full object-cover
          ${blurUp && !isLoaded && isInView ? 'opacity-0' : 'opacity-100'}
          transition-opacity duration-300
        `}
        loading="lazy"
        onLoad={isInView ? handleLoad : undefined}
        onError={isInView ? handleError : undefined}
      />

      {/* Blur-up placeholder effect */}
      {blurUp && isInView && !isLoaded && (
        <div className="absolute inset-0 bg-gray-200 animate-pulse"></div>
      )}
    </div>
  );
};

export default LazyImage;
