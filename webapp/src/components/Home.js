import React, { useEffect, useState, useRef, useCallback, useMemo } from 'react';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import GamesModal from './GamesModal';
import GameTalents from './GameTalents';
import Settings from './Settings';
import { profileAPI } from '../services/api';
import walletAPI from '../services/walletService';
import { useHomepage, useHomepageSection } from '../contexts/HomepageContext';
import {
    GameCardSkeletonGrid,
    TalentCardSkeletonGrid,
    NewTalentCardSkeletonGrid,
    MissionBannerSkeleton
} from './ui/Skeletons';
import {
    EmptyGamesState,
    EmptyTalentsState,
    EmptyCarouselState,
    EmptyMissionsState
} from './ui/EmptyStates';
import LazyImage from './ui/LazyImage';
import { PageLoader, SectionLoader } from './ui/LoadingIndicator';

// Hero Banner Carousel Component
const HeroBannerCarousel = () => {
    const [currentSlideIndex, setCurrentSlideIndex] = useState(0);
    const [isTransitioning, setIsTransitioning] = useState(false);
    const [direction, setDirection] = useState('right');
    const [isPaused, setIsPaused] = useState(false);
    const navigate = useNavigate();
    const slideRef = useRef(null);
    const timerRef = useRef(null);

    // Use the homepage context for carousel slides
    const {
        carouselSlides: slides,
        loading,
        error,
        fetchCarouselSlides
    } = useHomepage();

    // Fetch carousel slides on component mount - only if needed
    useEffect(() => {
        // Only fetch if we don't already have slides
        if (!slides || slides.length === 0) {
            console.log('HeroBannerCarousel: Fetching slides');
            fetchCarouselSlides();
        } else {
            console.log('HeroBannerCarousel: Using existing slides');
        }
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    // Wrap changeSlide in useCallback to prevent unnecessary re-renders
    const changeSlide = useCallback((newIndex, slideDirection = 'right') => {
        if (isTransitioning || slides.length <= 1) return;

        setDirection(slideDirection);
        setIsTransitioning(true);

        // Apply slide animation
        if (slideRef.current) {
            slideRef.current.classList.add(slideDirection === 'right' ? 'slide-out-left' : 'slide-out-right');
        }

        // After animation starts, change the slide
        setTimeout(() => {
            setCurrentSlideIndex(newIndex);

            // Reset the animation classes after changing slide
            if (slideRef.current) {
                slideRef.current.classList.remove('slide-out-left', 'slide-out-right');
                slideRef.current.classList.add(slideDirection === 'right' ? 'slide-in-right' : 'slide-in-left');

                // Clean up animation classes after transition completes
                setTimeout(() => {
                    if (slideRef.current) {
                        slideRef.current.classList.remove('slide-in-right', 'slide-in-left');
                    }
                    setIsTransitioning(false);
                }, 500);
            } else {
                setIsTransitioning(false);
            }
        }, 300);
    }, [isTransitioning, slides.length]);

    // Function to start the automatic slideshow
    const startAutoSlide = useCallback(() => {
        if (slides.length <= 1) return;

        // Clear any existing timer
        if (timerRef.current) {
            clearInterval(timerRef.current);
        }

        // Set new timer
        timerRef.current = setInterval(() => {
            if (!isPaused) {
                changeSlide((currentSlideIndex + 1) % slides.length, 'right');
            }
        }, 5000);
    }, [slides.length, currentSlideIndex, isPaused, changeSlide]);

    // Start/restart the slideshow when dependencies change
    useEffect(() => {
        startAutoSlide();

        // Cleanup on unmount
        return () => {
            if (timerRef.current) {
                clearInterval(timerRef.current);
            }
        };
    }, [startAutoSlide, slides, currentSlideIndex]);

    // Pause slideshow on hover/focus
    const pauseSlideshow = () => setIsPaused(true);
    const resumeSlideshow = () => setIsPaused(false);

    const handleSlideClick = (slide) => {
        if (slide.isClickable && slide.buttonUrl) {
            if (slide.buttonUrl.startsWith('http')) {
                window.open(slide.buttonUrl, '_blank');
            } else {
                navigate(slide.buttonUrl);
            }
        }
    };

    const nextSlide = () => {
        changeSlide((currentSlideIndex + 1) % slides.length, 'right');
    };

    const prevSlide = () => {
        changeSlide((currentSlideIndex - 1 + slides.length) % slides.length, 'left');
    };

    if (loading) {
        return (
            <div className="bg-gradient-to-r from-purple-900 to-purple-800 rounded-2xl overflow-hidden mb-6 relative group h-[500px] w-full">
                <div className="absolute inset-0 opacity-10 bg-noise mix-blend-overlay"></div>
                <div className="p-6 md:p-8 flex items-center justify-center h-full">
                    <SectionLoader
                        type="particles"
                        size="large"
                        message="Loading carousel..."
                        color="purple"
                    />
                </div>
            </div>
        );
    }

    if (error || slides.length === 0) {
        // Use the EmptyCarouselState component for better error handling
        return (
            <EmptyCarouselState
                onAction={() => navigate('/missions')}
            />
        );
    }

    const currentSlide = slides[currentSlideIndex];

    return (
        <div
            className="bg-gradient-to-r from-blue-600 via-indigo-600 to-blue-800 rounded-2xl overflow-hidden mb-6 relative group w-full h-[500px] animate-gradient-shift shadow-lg"
            onMouseEnter={pauseSlideshow}
            onMouseLeave={resumeSlideshow}
            style={{ backgroundSize: '200% 200%' }}
        >
            {/* Enhanced background patterns and effects */}
            <div className="absolute inset-0 opacity-10 bg-noise mix-blend-overlay"></div>
            <div className="absolute inset-0 bg-blue-900/[0.05] bg-[length:20px_20px]"></div>

            {/* Subtle glow effects */}
            <div className="absolute -top-20 -right-20 w-64 h-64 bg-blue-400/20 rounded-full filter blur-3xl"></div>
            <div className="absolute -bottom-20 -left-20 w-64 h-64 bg-indigo-400/20 rounded-full filter blur-3xl"></div>

            {/* Enhanced floating particles for depth */}
            <div className="absolute inset-0 overflow-hidden pointer-events-none">
                {/* Larger particles */}
                <div className="absolute h-8 w-8 rounded-full bg-white/10 top-[20%] left-[10%] animate-float-particle"></div>
                <div className="absolute h-6 w-6 rounded-full bg-white/10 top-[30%] left-[80%] animate-float-particle" style={{ animationDelay: '1s' }}></div>
                <div className="absolute h-10 w-10 rounded-full bg-white/10 top-[60%] left-[20%] animate-float-particle" style={{ animationDelay: '2s' }}></div>
                <div className="absolute h-7 w-7 rounded-full bg-white/10 top-[70%] left-[70%] animate-float-particle" style={{ animationDelay: '3s' }}></div>
                <div className="absolute h-12 w-12 rounded-full bg-white/5 top-[40%] left-[40%] animate-float-particle" style={{ animationDelay: '1.5s' }}></div>

                {/* Smaller particles */}
                <div className="absolute h-4 w-4 rounded-full bg-white/15 top-[15%] left-[25%] animate-float-particle" style={{ animationDelay: '0.5s' }}></div>
                <div className="absolute h-3 w-3 rounded-full bg-white/15 top-[55%] left-[85%] animate-float-particle" style={{ animationDelay: '2.5s' }}></div>
                <div className="absolute h-5 w-5 rounded-full bg-white/15 top-[80%] left-[35%] animate-float-particle" style={{ animationDelay: '3.5s' }}></div>

                {/* Glowing particles */}
                <div className="absolute h-6 w-6 rounded-full bg-blue-300/20 top-[25%] left-[60%] animate-pulse-slow"></div>
                <div className="absolute h-8 w-8 rounded-full bg-indigo-300/20 top-[65%] left-[50%] animate-pulse-slow" style={{ animationDelay: '2s' }}></div>
            </div>

            {/* Enhanced Slide Container with better transitions */}
            <div
                ref={slideRef}
                className="absolute inset-0 transition-all duration-500 ease-out"
                onClick={() => handleSlideClick(currentSlide)}
                style={{ cursor: currentSlide.isClickable ? 'pointer' : 'default' }}
            >
                {/* Background image with enhanced effects */}
                {currentSlide.mediaFiles && currentSlide.mediaFiles[0] && (
                    <div className="absolute inset-0 z-0 overflow-hidden">
                        <img
                            src={`${process.env.REACT_APP_CDN_URL}/${currentSlide.mediaFiles[0].optimized}`}
                            alt={currentSlide.title || 'Carousel slide'}
                            className="w-full h-full object-cover opacity-90 transition-transform duration-[10s] transform scale-[1.03] group-hover:scale-[1.08]"
                        />
                        {/* Overlay gradient for better text readability */}
                        <div className="absolute inset-0 bg-gradient-to-r from-blue-900/50 via-transparent to-blue-900/30"></div>
                        {/* Subtle vignette effect */}
                        <div className="absolute inset-0 bg-radial-gradient-vignette opacity-40"></div>
                    </div>
                )}

                <div className="p-8 md:p-12 flex items-center justify-between relative z-10 h-full">
                    <div className="max-w-md transform transition-all duration-500 group-hover:translate-y-[-5px]">
                        {/* Animated badge if slide is new */}
                        {currentSlide.isNew && (
                            <div className="inline-flex items-center bg-white/20 backdrop-blur-sm px-3 py-1 rounded-full mb-4 animate-pulse-slow">
                                <span className="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                                <span className="text-white text-sm font-medium">New</span>
                            </div>
                        )}

                        {/* Enhanced title with better text shadow and animation */}
                        <h2 className="text-3xl md:text-5xl font-bold text-white text-left leading-2 [text-shadow:0_2px_10px_rgba(0,0,0,0.3)] animate-fade-in" style={{ animationDuration: '0.8s' }}>
                            {currentSlide.title || 'Meet Friend & Complete Mission'}
                        </h2>

                        {/* Enhanced description with better styling */}
                        <p className="text-blue-50 text-left mt-4 text-lg md:text-xl [text-shadow:0_1px_5px_rgba(0,0,0,0.3)] animate-fade-in" style={{ animationDuration: '1s', animationDelay: '0.2s' }}>
                            {currentSlide.content || 'Join forces with top gamers and earn while you play'}
                        </p>

                        {/* Enhanced CTA button with shimmer effect */}
                        <button
                            className="mt-8 bg-white text-blue-700 px-8 py-4 rounded-lg font-medium hover:bg-blue-50 transition-all hover:scale-105 duration-300 inline-flex items-center text-lg shadow-xl hover:shadow-2xl group relative overflow-hidden animate-fade-in"
                            style={{ animationDuration: '1s', animationDelay: '0.4s' }}
                            onClick={(e) => {
                                e.stopPropagation();
                                navigate(currentSlide.buttonUrl || '/missions');
                            }}
                        >
                            {/* Button shimmer effect */}
                            <div className="absolute inset-0 w-full h-full overflow-hidden">
                                <div className="absolute -inset-[100%] bg-gradient-to-r from-transparent via-blue-100/30 to-transparent animate-shimmer transform -translate-x-full"></div>
                            </div>

                            <span className="relative z-10">{currentSlide.buttonText || 'Start Now'}</span>
                            <svg className="w-5 h-5 ml-2 transform transition-transform group-hover:translate-x-2 relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                            </svg>
                        </button>
                    </div>

                    {/* Enhanced desktop decorative elements */}
                    <div className="hidden lg:flex space-x-[-15px] transform transition-all duration-700 group-hover:translate-x-[-10px]">
                        <div className="w-32 h-32 rounded-full flex items-center justify-center transform transition-transform group-hover:translate-y-[-15px] duration-700 animate-bounce-slower">
                            <div className="w-28 h-28 rounded-full bg-gradient-to-br from-blue-200 to-blue-300 opacity-90 shadow-lg"></div>
                        </div>
                        <div className="w-44 h-44 rounded-full bg-gradient-to-br from-blue-300 to-indigo-300 flex items-center justify-center transform transition-transform group-hover:translate-y-[-20px] duration-700 animate-float opacity-90 shadow-lg">
                            <div className="flex items-center justify-center">
                                <span className="text-6xl filter drop-shadow-lg">😊</span>
                            </div>
                        </div>
                        <div className="w-40 h-40 rounded-full bg-gradient-to-br from-indigo-200 to-indigo-300 flex items-center justify-center transform transition-transform group-hover:translate-y-[-18px] duration-700 animate-float-delayed shadow-lg">
                            <div className="flex items-center justify-center">
                                <span className="text-5xl filter drop-shadow-lg">☺️</span>
                            </div>
                        </div>
                    </div>

                    {/* Enhanced mobile decorative elements */}
                    <div className="lg:hidden relative w-32 h-32">
                        <div className="absolute top-0 right-0 w-20 h-20 rounded-full bg-gradient-to-br from-indigo-300 to-indigo-400 animate-pulse-slow opacity-80 shadow-md"></div>
                        <div className="absolute bottom-0 left-0 w-24 h-24 rounded-full bg-gradient-to-br from-blue-300 to-blue-400 flex items-center justify-center animate-float opacity-90 shadow-md">
                            <span className="text-3xl filter drop-shadow-md">😊</span>
                        </div>
                        <div className="absolute bottom-0 right-0 w-24 h-24 rounded-full bg-gradient-to-br from-indigo-200 to-indigo-300 flex items-center justify-center animate-float-delayed shadow-md">
                            <span className="text-3xl filter drop-shadow-md">☺️</span>
                        </div>
                    </div>
                </div>
            </div>

            {/* Enhanced Carousel indicators with animations */}
            <div className="absolute bottom-6 right-8 flex space-x-3 z-20">
                {slides.map((_, index) => (
                    <button
                        key={index}
                        className={`rounded-full transition-all duration-300 transform hover:scale-110 ${index === currentSlideIndex
                            ? 'w-10 h-3 bg-white shadow-[0_0_10px_rgba(255,255,255,0.7)] scale-100'
                            : 'w-3 h-3 bg-white/40 hover:bg-white/70 scale-90'
                            }`}
                        onClick={(e) => {
                            e.stopPropagation();
                            if (index > currentSlideIndex) {
                                changeSlide(index, 'right');
                            } else if (index < currentSlideIndex) {
                                changeSlide(index, 'left');
                            }
                        }}
                        aria-label={`Go to slide ${index + 1}`}
                    />
                ))}
            </div>

            {/* Enhanced Slide counter with animation */}
            {slides.length > 1 && (
                <div className="absolute top-6 left-6 bg-black/30 backdrop-blur-md text-white text-sm px-4 py-2 rounded-full z-20 shadow-lg border border-white/10 flex items-center">
                    <span className="font-medium">{currentSlideIndex + 1}</span>
                    <span className="mx-1 text-white/70">/</span>
                    <span className="text-white/70">{slides.length}</span>
                </div>
            )}

            {/* Enhanced Previous/Next buttons with animations */}
            {slides.length > 1 && (
                <>
                    <button
                        className="absolute left-6 top-1/2 transform -translate-y-1/2 bg-black/30 hover:bg-black/50 w-12 h-12 rounded-full flex items-center justify-center text-white transition-all duration-300 backdrop-blur-md opacity-0 group-hover:opacity-100 z-20 shadow-lg border border-white/10 hover:scale-110"
                        onClick={(e) => {
                            e.stopPropagation();
                            prevSlide();
                        }}
                        aria-label="Previous slide"
                    >
                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                        </svg>
                        <div className="absolute inset-0 rounded-full border border-white/20 animate-pulse-slow opacity-0 group-hover:opacity-100"></div>
                    </button>
                    <button
                        className="absolute right-6 top-1/2 transform -translate-y-1/2 bg-black/30 hover:bg-black/50 w-12 h-12 rounded-full flex items-center justify-center text-white transition-all duration-300 backdrop-blur-md opacity-0 group-hover:opacity-100 z-20 shadow-lg border border-white/10 hover:scale-110"
                        onClick={(e) => {
                            e.stopPropagation();
                            nextSlide();
                        }}
                        aria-label="Next slide"
                    >
                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                        </svg>
                        <div className="absolute inset-0 rounded-full border border-white/20 animate-pulse-slow opacity-0 group-hover:opacity-100"></div>
                    </button>
                </>
            )}

            {/* Enhanced Auto-play indicator with animation */}
            <div className="absolute bottom-6 left-6 z-20 flex items-center space-x-2">
                <div className="w-16 h-1.5 bg-white/20 rounded-full overflow-hidden shadow-inner backdrop-blur-sm">
                    <div className="h-full w-full bg-gradient-to-r from-blue-400 to-indigo-400 origin-left animate-progress-bar"></div>
                </div>
                <button
                    onClick={(e) => {
                        e.stopPropagation();
                        if (!isPaused) {
                            pauseSlideshow();
                        } else {
                            resumeSlideshow();
                        }
                    }}
                    className="w-6 h-6 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center hover:bg-white/30 transition-colors"
                >
                    {!isPaused ? (
                        <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <rect x="6" y="4" width="4" height="16" rx="1" />
                            <rect x="14" y="4" width="4" height="16" rx="1" />
                        </svg>
                    ) : (
                        <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M8 5v14l11-7z" />
                        </svg>
                    )}
                </button>
            </div>
        </div>
    );
};

function Home() {
    // User and authentication state
    const [user, setUser] = useState(null);
    const [walletBalance, setWalletBalance] = useState(0);

    // UI state
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState('');
    const [activeTab, setActiveTab] = useState('recommended');
    const [isGamesModalOpen, setIsGamesModalOpen] = useState(false);
    const [isGameTalentsModalOpen, setIsGameTalentsModalOpen] = useState(false);
    const [selectedGame, setSelectedGame] = useState(null);
    const [isQuickActionsOpen, setIsQuickActionsOpen] = useState(false);
    const [hasSeenQuickActions, setHasSeenQuickActions] = useState(false);
    const [showSettings, setShowSettings] = useState(false);

    // Get homepage data from context with section-specific hooks
    const {
        availableMissionsCount: apiMissionsCount,
        refresh,
        silentRefresh,
        dataStatus,
        refreshMissionsCount
    } = useHomepage();

    // Use section-specific hooks for better data management
    const { data: apiNewTalents, loading: newTalentsLoading } = useHomepageSection('newTalents');
    const { data: apiRecommendedTalents, loading: recommendedTalentsLoading } = useHomepageSection('recommendedTalents');
    const { data: apiOnlineTalents, loading: onlineTalentsLoading } = useHomepageSection('onlineTalents');
    const { games: apiPopularGames, loading: gamesLoading } = useHomepageSection('popularGames');

    // Combine loading states
    const homepageLoading = newTalentsLoading || recommendedTalentsLoading ||
                           onlineTalentsLoading || gamesLoading;

    // Check if data is stale
    const isStale = dataStatus === 'stale';

    // Get error state from the context
    const homepageError = error;

    // Use the data directly from the context without additional transformation
    // since it's already transformed by the data transformers
    const transformedPopularGames = useMemo(() => {
        return apiPopularGames || [];
    }, [apiPopularGames]);

    const transformedRecommendedTalents = useMemo(() => {
        return apiRecommendedTalents?.talents || [];
    }, [apiRecommendedTalents]);

    const transformedNewTalents = useMemo(() => {
        // Limit to 4 talents for the UI
        return (apiNewTalents?.talents || []).slice(0, 4);
    }, [apiNewTalents]);

    const transformedOnlineTalents = useMemo(() => {
        // Limit to 3 talents for the UI
        return (apiOnlineTalents?.talents || []).slice(0, 3);
    }, [apiOnlineTalents]);

    const navigate = useNavigate();

    // Effect for fetching user data (requires authentication)
    useEffect(() => {
        const fetchUserData = async () => {
            setIsLoading(true);
            const token = localStorage.getItem('token');
            if (!token) {
                setError('Not logged in.');
                setIsLoading(false);
                return;
            }

            try {
                // Fetch both profile and wallet data in parallel
                const [profileResponse, walletResponse] = await Promise.all([
                    profileAPI.getProfile(),
                    walletAPI.getBalance()
                ]);

                setUser(profileResponse.data);
                // Set wallet balance from the wallet API response
                setWalletBalance(walletResponse.data.credits_balance);
            } catch (err) {
                console.error('Error fetching user data:', err);
                setError('Failed to load data. Please log in again.');
            } finally {
                setIsLoading(false);
            }
        };

        fetchUserData();

        // Check if user has seen quick actions before
        const hasSeenQuickActionsFlag = localStorage.getItem('hasSeenQuickActions');
        if (hasSeenQuickActionsFlag === 'true') {
            setHasSeenQuickActions(true);
        }

        // Add scroll event listener for header animation
        const handleScroll = () => {
            const nav = document.getElementById('main-nav');
            if (nav) {
                if (window.scrollY > 20) {
                    nav.classList.add('py-2', 'shadow-xl', 'bg-white/95');
                    nav.classList.remove('py-4', 'shadow-lg', 'bg-white/90');
                } else {
                    nav.classList.add('py-4', 'shadow-lg', 'bg-white/90');
                    nav.classList.remove('py-2', 'shadow-xl', 'bg-white/95');
                }
            }
        };

        window.addEventListener('scroll', handleScroll);

        // Clean up event listener
        return () => {
            window.removeEventListener('scroll', handleScroll);
        };
    }, []);

    // Effect for fetching homepage data (does not require authentication)
    useEffect(() => {
        // Check if we already have data before triggering a refresh
        const hasData = apiNewTalents?.talents?.length > 0 ||
                       apiRecommendedTalents?.talents?.length > 0 ||
                       apiPopularGames?.length > 0;

        if (!hasData) {
            console.log('Home component: Initial data fetch');
            // Only fetch if we don't have data yet
            refresh();
        } else {
            console.log('Home component: Using existing data');
        }

        // Set up auto-refresh every 5 minutes, but only if the tab is visible
        const refreshInterval = setInterval(() => {
            // Only refresh if the document is visible to save resources
            if (document.visibilityState === 'visible') {
                console.log('Home component: Scheduled silent refresh');
                silentRefresh(); // Silent refresh to avoid UI flicker
            }
        }, 5 * 60 * 1000); // 5 minutes

        // Add visibility change listener to refresh data when tab becomes visible again
        const handleVisibilityChange = () => {
            if (document.visibilityState === 'visible') {
                // Check if data is stale (more than 5 minutes old)
                const lastRefreshTime = localStorage.getItem('homepageLastRefresh');
                const now = Date.now();
                if (!lastRefreshTime || now - parseInt(lastRefreshTime) > 5 * 60 * 1000) {
                    console.log('Home component: Visibility change silent refresh');
                    silentRefresh();
                    // Update the refresh timestamp
                    localStorage.setItem('homepageLastRefresh', now.toString());
                }
            }
        };

        document.addEventListener('visibilitychange', handleVisibilityChange);

        // Store initial refresh timestamp
        localStorage.setItem('homepageLastRefresh', Date.now().toString());

        return () => {
            clearInterval(refreshInterval);
            document.removeEventListener('visibilitychange', handleVisibilityChange);
        };
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    // Effect to handle quick actions menu state
    useEffect(() => {
        if (isQuickActionsOpen && !hasSeenQuickActions) {
            // Mark that the user has seen the quick actions menu
            setHasSeenQuickActions(true);
            localStorage.setItem('hasSeenQuickActions', 'true');
        }
    }, [isQuickActionsOpen, hasSeenQuickActions]);



    // Handle authentication error
    if (error) {
        return (
            <div className="flex items-center justify-center h-screen bg-gray-50">
                <div className="text-center p-8 bg-white rounded-2xl shadow-lg">
                    <p className="text-red-500 text-xl mb-4">{error}</p>
                    <button
                        onClick={() => navigate('/')}
                        className="px-6 py-2 rounded-lg font-medium text-white bg-blue-600 hover:bg-blue-700"
                    >
                        Go to Login
                    </button>
                </div>
            </div>
        );
    }

    // Show loading state when both user data and homepage data are loading
    if (isLoading && homepageLoading) {
        return <PageLoader message="Loading your experience..." color="indigo" />;
    }

    return (
        <div className="min-h-screen bg-gradient-to-b from-indigo-50 to-white">
            {/* Skip to Main Content Link - Hidden visually but accessible to screen readers and keyboard users */}
            <a
                href="#main-content"
                className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-[100] focus:px-4 focus:py-2 focus:bg-indigo-600 focus:text-white focus:rounded-md focus:shadow-lg focus:outline-none"
            >
                Skip to main content
            </a>

            {/* Games Modal */}
            <GamesModal isOpen={isGamesModalOpen} onClose={() => setIsGamesModalOpen(false)} />

            {/* Enhanced Floating Quick Action Button with Accessibility Improvements */}
            <div className="fixed right-6 bottom-24 md:bottom-6 z-40">
                <div className="relative">
                    {/* Main Action Button with Enhanced Accessibility */}
                    <button
                        onClick={() => setIsQuickActionsOpen(!isQuickActionsOpen)}
                        className="w-14 h-14 rounded-full bg-gradient-to-r from-indigo-600 to-blue-600 text-white shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center relative z-10 hover:scale-105 active:scale-95 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                        aria-label="Quick actions menu"
                        aria-expanded={isQuickActionsOpen}
                        aria-haspopup="true"
                        aria-controls="quick-actions-menu"
                    >
                        <span className="sr-only">{isQuickActionsOpen ? 'Close quick actions menu' : 'Open quick actions menu'}</span>
                        {isQuickActionsOpen ? (
                            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        ) : (
                            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                            </svg>
                        )}

                        {/* Reduced animation for better performance - only visible to new users */}
                        {!hasSeenQuickActions && (
                            <span className="absolute inset-0 rounded-full bg-indigo-600 animate-ping opacity-20"></span>
                        )}
                    </button>

                    {/* Quick Action Menu with Accessibility Improvements */}
                    {isQuickActionsOpen && (
                        <div
                            id="quick-actions-menu"
                            className="absolute bottom-16 right-0 bg-white rounded-2xl shadow-xl border border-indigo-100/50 w-72 overflow-hidden animate-modal-slide-in"
                            role="menu"
                            aria-orientation="vertical"
                            aria-labelledby="quick-actions-button"
                        >
                            <div className="p-3 bg-gradient-to-r from-indigo-600 to-blue-600 text-white">
                                <h3 className="font-bold" id="quick-actions-heading">Quick Actions</h3>
                                <p className="text-xs text-indigo-100">Access common features faster</p>
                            </div>

                            <div className="p-2">
                                {/* Mission Action with Enhanced Accessibility */}
                                <button
                                    onClick={() => {
                                        setIsQuickActionsOpen(false);
                                        navigate('/missions');
                                    }}
                                    className="flex items-center w-full p-3 bg-indigo-50 hover:bg-indigo-50 rounded-xl transition-colors text-left focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-inset"
                                    role="menuitem"
                                >
                                    <div className="w-10 h-10 rounded-lg bg-blue-100 flex items-center justify-center text-blue-600 mr-3" aria-hidden="true">
                                        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clipRule="evenodd" />
                                            <path d="M2 13.692V16a2 2 0 002 2h12a2 2 0 002-2v-2.308A24.974 24.974 0 0110 15c-2.796 0-5.487-.46-8-1.308z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <div className="font-medium text-gray-800">Browse Missions</div>
                                        <div className="text-xs text-gray-500">Find gaming tasks to complete</div>
                                    </div>
                                    <div className="ml-auto bg-blue-500 px-2 py-0.5 rounded-full text-white text-xs font-medium">
                                        20+
                                    </div>
                                </button>

                                {/* Create Mission Action */}
                                <button
                                    onClick={() => {
                                        setIsQuickActionsOpen(false);
                                        navigate('/missions/create');
                                    }}
                                    className="flex items-center w-full p-3 bg-indigo-50 hover:bg-indigo-50 rounded-xl transition-colors text-left focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-inset"
                                    role="menuitem"
                                >
                                    <div className="w-10 h-10 rounded-lg bg-indigo-100 flex items-center justify-center text-indigo-600 mr-3" aria-hidden="true">
                                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                                        </svg>
                                    </div>
                                    <div>
                                        <div className="font-medium text-gray-800">Create Mission</div>
                                        <div className="text-xs text-gray-500">Post a new gaming task</div>
                                    </div>
                                </button>

                                {/* Add Credits Action */}
                                <button
                                    onClick={() => {
                                        setIsQuickActionsOpen(false);
                                        navigate('/wallet/deposit');
                                    }}
                                    className="flex items-center w-full p-3 bg-indigo-50 hover:bg-indigo-50 rounded-xl transition-colors text-left focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-inset"
                                    role="menuitem"
                                >
                                    <div className="w-10 h-10 rounded-lg bg-green-100 flex items-center justify-center text-green-600 mr-3" aria-hidden="true">
                                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                        </svg>
                                    </div>
                                    <div>
                                        <div className="font-medium text-gray-800">Add Credits</div>
                                        <div className="text-xs text-gray-500">Top up your wallet</div>
                                    </div>
                                </button>

                                {/* Messages Action */}
                                <button
                                    onClick={() => {
                                        setIsQuickActionsOpen(false);
                                        navigate('/chat');
                                    }}
                                    className="flex items-center w-full p-3 bg-indigo-50 hover:bg-indigo-50 rounded-xl transition-colors text-left focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-inset"
                                    role="menuitem"
                                >
                                    <div className="w-10 h-10 rounded-lg bg-orange-100 flex items-center justify-center text-orange-600 mr-3" aria-hidden="true">
                                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <div className="font-medium text-gray-800">Messages</div>
                                        <div className="text-xs text-gray-500">Chat with players</div>
                                    </div>
                                    <div className="ml-auto bg-orange-500 px-2 py-0.5 rounded-full text-white text-xs font-medium">
                                        3
                                    </div>
                                </button>
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/* Enhanced Navigation Bar with scroll animation */}
            <nav className="bg-white/90 shadow-lg sticky top-0 z-50 backdrop-filter backdrop-blur-md transition-all duration-300" id="main-nav">
                <div className="container mx-auto px-4">
                    <div className="flex justify-between items-center py-4">
                        <div className="flex items-center">
                            <h1 className="text-2xl font-bold text-gray-800 transform transition-all hover:scale-105 duration-300 relative group">
                                Mission<span className="text-indigo-600 relative">X
                                    <span className="absolute -top-1 -right-1 w-2 h-2 bg-indigo-500 rounded-full animate-pulse-light"></span>
                                </span>
                                {/* Logo glow effect on hover */}
                                <span className="absolute -inset-2 bg-indigo-100 rounded-full opacity-0 group-hover:opacity-30 blur-xl transition-opacity duration-500"></span>
                            </h1>
                        </div>

                        {/* Enhanced desktop navigation with accessibility improvements */}
                        <nav aria-label="Main Navigation">
                            <ul className="hidden md:flex space-x-8 list-none">
                                <li>
                                    <a
                                        href="/home"
                                        className="text-indigo-600 font-medium relative group flex items-center focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 rounded-lg px-3 py-2 -mx-3 -my-2"
                                        aria-current="page"
                                    >
                                        <span className="relative z-10">Home</span>
                                        {/* Active indicator */}
                                        <span className="absolute bottom-0 left-0 w-full h-0.5 bg-indigo-600 rounded-full"></span>
                                        {/* Hover effect */}
                                        <span className="absolute inset-0 -z-10 scale-75 rounded-lg bg-indigo-50 opacity-0 group-hover:scale-100 group-hover:opacity-100 transition-all duration-300"></span>
                                    </a>
                                </li>
                                <li>
                                    <a
                                        href="/talent"
                                        className="text-gray-600 hover:text-indigo-600 transition-colors relative group flex items-center focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 rounded-lg px-3 py-2 -mx-3 -my-2"
                                    >
                                        <span className="relative z-10">Talent</span>
                                        {/* Hover indicator */}
                                        <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-indigo-600 rounded-full group-hover:w-full focus:w-full transition-all duration-300"></span>
                                        {/* Hover effect */}
                                        <span className="absolute inset-0 -z-10 scale-75 rounded-lg bg-indigo-50 opacity-0 group-hover:scale-100 group-focus:scale-100 group-hover:opacity-100 group-focus:opacity-100 transition-all duration-300"></span>
                                    </a>
                                </li>
                                <li>
                                    <a
                                        href="/explore"
                                        className="text-gray-600 hover:text-indigo-600 transition-colors relative group flex items-center focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 rounded-lg px-3 py-2 -mx-3 -my-2"
                                    >
                                        <span className="relative z-10">Explore</span>
                                        {/* Hover indicator */}
                                        <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-indigo-600 rounded-full group-hover:w-full focus:w-full transition-all duration-300"></span>
                                        {/* Hover effect */}
                                        <span className="absolute inset-0 -z-10 scale-75 rounded-lg bg-indigo-50 opacity-0 group-hover:scale-100 group-focus:scale-100 group-hover:opacity-100 group-focus:opacity-100 transition-all duration-300"></span>
                                    </a>
                                </li>
                                <li>
                                    <a
                                        href="/chat"
                                        className="text-gray-600 hover:text-indigo-600 transition-colors relative group flex items-center focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 rounded-lg px-3 py-2 -mx-3 -my-2"
                                    >
                                        <span className="relative z-10">Chat</span>
                                        {/* Hover indicator */}
                                        <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-indigo-600 rounded-full group-hover:w-full focus:w-full transition-all duration-300"></span>
                                        {/* Hover effect */}
                                        <span className="absolute inset-0 -z-10 scale-75 rounded-lg bg-indigo-50 opacity-0 group-hover:scale-100 group-focus:scale-100 group-hover:opacity-100 group-focus:opacity-100 transition-all duration-300"></span>
                                    </a>
                                </li>
                                <li>
                                    <a
                                        href="/profile"
                                        className="text-gray-600 hover:text-indigo-600 transition-colors relative group flex items-center focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 rounded-lg px-3 py-2 -mx-3 -my-2"
                                    >
                                        <span className="relative z-10">Profile</span>
                                        {/* Hover indicator */}
                                        <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-indigo-600 rounded-full group-hover:w-full focus:w-full transition-all duration-300"></span>
                                        {/* Hover effect */}
                                        <span className="absolute inset-0 -z-10 scale-75 rounded-lg bg-indigo-50 opacity-0 group-hover:scale-100 group-focus:scale-100 group-hover:opacity-100 group-focus:opacity-100 transition-all duration-300"></span>
                                    </a>
                                </li>
                            </ul>
                        </nav>

                        <div className="flex items-center space-x-4">
                            {/* Enhanced Wallet with better accessibility */}
                            <a
                                href="/wallet"
                                className="flex items-center bg-gradient-to-r from-indigo-50 to-blue-50 px-4 py-2 rounded-full shadow-sm hover:shadow-md transition-all duration-300 hover:bg-gradient-to-r hover:from-indigo-100 hover:to-blue-100 group border border-indigo-100/50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 relative"
                                aria-label={`View wallet with balance of ${walletBalance} credits`}
                            >
                                <div className="w-8 h-8 rounded-full bg-gradient-to-br from-indigo-600 to-blue-500 flex items-center justify-center mr-3 group-hover:scale-110 transition-transform duration-300 shadow-sm" aria-hidden="true">
                                    <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                                    </svg>
                                </div>
                                <div className="flex flex-col">
                                    <span className="text-xs text-indigo-500 leading-none font-medium">Credits Balance</span>
                                    <span className="font-semibold text-indigo-800 text-lg" id="wallet-balance">{walletBalance}</span>
                                </div>
                                {/* Reduced animation for better performance */}
                                <div className="absolute inset-0 overflow-hidden rounded-full opacity-0 group-hover:opacity-100 pointer-events-none">
                                    <div className="absolute -inset-[100%] bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer transform -translate-x-full"></div>
                                </div>
                            </a>

                            {/* Notifications indicator with better accessibility */}
                            <button
                                className="relative w-10 h-10 rounded-full bg-indigo-50 flex items-center justify-center hover:bg-indigo-100 transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                                aria-label="View 3 notifications"
                                onClick={() => navigate('/notifications')}
                            >
                                <svg className="w-5 h-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                                </svg>
                                <span
                                    className="absolute -top-1 -right-1 flex h-4 w-4"
                                    aria-hidden="true"
                                >
                                    <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-indigo-400 opacity-75"></span>
                                    <span className="relative inline-flex rounded-full h-4 w-4 bg-indigo-500 text-[10px] text-white flex items-center justify-center font-bold">3</span>
                                </span>
                            </button>

                            {/* Enhanced Settings Button with Modern Aesthetics */}
                            <motion.button
                                onClick={() => setShowSettings(true)}
                                className="relative w-10 h-10 rounded-full bg-gradient-to-br from-indigo-50 to-purple-50 flex items-center justify-center hover:from-indigo-100 hover:to-purple-100 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 shadow-sm hover:shadow-md group"
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                                aria-label="Open settings"
                            >
                                {/* Subtle glow effect */}
                                <div className="absolute inset-0 rounded-full bg-gradient-to-br from-indigo-400/20 to-purple-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"></div>

                                <svg className="w-5 h-5 text-indigo-600 relative z-10 transition-transform duration-300 group-hover:rotate-90" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>

                                {/* Shimmer effect on hover */}
                                <div className="absolute inset-0 rounded-full overflow-hidden opacity-0 group-hover:opacity-100 pointer-events-none">
                                    <div className="absolute -inset-[100%] bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer transform -translate-x-full"></div>
                                </div>
                            </motion.button>
                        </div>
                    </div>
                </div>
            </nav>

            {/* Enhanced Mobile Navigation with haptic feedback and better visual indicators */}
            <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white/95 shadow-[0_-4px_20px_rgba(0,0,0,0.08)] z-50 backdrop-filter backdrop-blur-lg border-t border-indigo-100/50">
                <div className="flex justify-around items-center py-3 px-2">
                    {/* Home - Active */}
                    <a href="/home" className="flex flex-col items-center relative group" onClick={() => navigator.vibrate && navigator.vibrate(5)}>
                        {/* Active indicator pill */}
                        <div className="absolute -top-3 w-10 h-1 bg-indigo-600 rounded-full shadow-[0_0_8px_rgba(79,70,229,0.5)]"></div>
                        {/* Active background glow */}
                        <div className="absolute inset-0 bg-indigo-50 rounded-xl -z-10"></div>

                        <div className="w-12 h-12 flex items-center justify-center">
                            <svg className="w-6 h-6 text-indigo-600 transition-transform group-active:scale-90 duration-150" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                            </svg>
                        </div>
                        <span className="text-xs font-medium text-indigo-600 mt-0.5">Home</span>
                    </a>

                    {/* Talent */}
                    <a href="/talent" className="flex flex-col items-center relative group" onClick={() => navigator.vibrate && navigator.vibrate(5)}>
                        <div className="w-12 h-12 flex items-center justify-center">
                            <svg className="w-6 h-6 text-gray-500 transition-transform group-active:scale-90 duration-150" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                            </svg>
                        </div>
                        <span className="text-xs font-medium text-gray-500 mt-0.5">Talent</span>

                        {/* Hover effect */}
                        <div className="absolute inset-0 bg-indigo-50/0 rounded-xl -z-10 transition-colors duration-200 group-active:bg-indigo-50/50"></div>
                    </a>

                    {/* Explore */}
                    <a href="/explore" className="flex flex-col items-center relative group" onClick={() => navigator.vibrate && navigator.vibrate(5)}>
                        <div className="w-12 h-12 flex items-center justify-center">
                            <svg className="w-6 h-6 text-gray-500 transition-transform group-active:scale-90 duration-150" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <span className="text-xs font-medium text-gray-500 mt-0.5">Explore</span>

                        {/* Hover effect */}
                        <div className="absolute inset-0 bg-indigo-50/0 rounded-xl -z-10 transition-colors duration-200 group-active:bg-indigo-50/50"></div>
                    </a>

                    {/* Chat navigation item */}
                    <a href="/chat" className="flex flex-col items-center relative group" onClick={() => navigator.vibrate && navigator.vibrate(5)}>
                        <div className="w-12 h-12 flex items-center justify-center">
                            <svg className="w-6 h-6 text-gray-500 transition-transform group-active:scale-90 duration-150" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                            </svg>
                        </div>
                        <span className="text-xs font-medium text-gray-500 mt-0.5">Chat</span>

                        {/* Hover effect */}
                        <div className="absolute inset-0 bg-indigo-50/0 rounded-xl -z-10 transition-colors duration-200 group-active:bg-indigo-50/50"></div>
                    </a>

                    {/* Profile */}
                    <a href="/profile" className="flex flex-col items-center relative group" onClick={() => navigator.vibrate && navigator.vibrate(5)}>
                        <div className="w-12 h-12 flex items-center justify-center">
                            <svg className="w-6 h-6 text-gray-500 transition-transform group-active:scale-90 duration-150" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                        </div>
                        <span className="text-xs font-medium text-gray-500 mt-0.5">Profile</span>

                        {/* Hover effect */}
                        <div className="absolute inset-0 bg-indigo-50/0 rounded-xl -z-10 transition-colors duration-200 group-active:bg-indigo-50/50"></div>
                    </a>
                </div>

                {/* Bottom safe area for newer iPhones */}
                <div className="h-safe-bottom bg-white/95 backdrop-filter backdrop-blur-lg"></div>
            </div>

            <main id="main-content" className="container mx-auto px-5 py-8 mb-20 md:mb-0">
                {/* Stale data indicator */}
                {isStale && (
                    <div className="mb-4 bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded-md flex items-center justify-between">
                        <div className="flex items-center">
                            <svg className="h-5 w-5 text-yellow-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <p className="text-sm text-yellow-700">
                                You're viewing cached data. We're refreshing in the background.
                            </p>
                        </div>
                        <button
                            onClick={() => refresh()}
                            className="text-sm text-yellow-700 hover:text-yellow-900 font-medium flex items-center"
                        >
                            <svg className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                            Refresh Now
                        </button>
                    </div>
                )}

                {/* Hero Banner Carousel (Replacing static hero banner) */}
                <HeroBannerCarousel />

                {/* Enhanced Popular Game Section */}
                <div className="mb-12">
                    {/* Section Title with Icon */}
                    <div className="flex items-center space-x-3 mb-6">
                        <div className="flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-purple-500 to-indigo-600 shadow-md text-white">
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a2 2 0 110-4h1a1 1 0 001-1V7a1 1 0 011-1h3a1 1 0 001-1V4z" />
                            </svg>
                        </div>
                        <div className="flex items-center">
                            <h2 className="text-2xl font-bold text-gray-800">Popular Games</h2>
                            <div className="ml-2 flex items-center justify-center">
                                <span className="relative flex h-3 w-3">
                                    <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-purple-400 opacity-75"></span>
                                    <span className="relative inline-flex rounded-full h-3 w-3 bg-purple-500"></span>
                                </span>
                            </div>
                        </div>
                        <div className="ml-2 px-2.5 py-1 bg-indigo-100 rounded-full text-xs font-medium text-indigo-800">
                            {apiPopularGames?.length || 0}
                        </div>
                        <button
                            className="ml-auto text-indigo-600 text-sm font-medium hover:text-indigo-800 transition-colors inline-flex items-center group bg-indigo-50 px-3 py-1 rounded-lg"
                            onClick={() => setIsGamesModalOpen(true)}
                        >
                            See All
                            <svg className="w-4 h-4 ml-1 transform transition-transform group-hover:translate-x-1 duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                            </svg>
                        </button>
                    </div>

                    {/* Enhanced Game Cards */}
                    {homepageLoading ? (
                        <GameCardSkeletonGrid count={4} />
                    ) : homepageError || !transformedPopularGames?.length ? (
                        // Show empty state when there's an error or no games
                        <EmptyGamesState onExplore={() => setIsGamesModalOpen(true)} />
                    ) : (
                        <div className="grid grid-cols-2 sm:grid-cols-4 gap-5 md:gap-6">
                            {/* Use transformed data for better performance */}
                            {transformedPopularGames.map((gameData, idx) => {

                                return (
                                    <div
                                        key={gameData.id}
                                        className="cursor-pointer transition-all hover:shadow-lg group animate-fade-in rounded-2xl"
                                        style={{ animationDelay: `${idx * 100}ms` }}
                                        onClick={() => {
                                            setSelectedGame(gameData);
                                            setIsGameTalentsModalOpen(true);
                                        }}
                                    >
                                        <div className="bg-gradient-to-br from-indigo-50 to-white rounded-2xl overflow-hidden relative border border-indigo-100/50 shadow-sm group-hover:shadow-md transition-all h-full flex flex-col">
                                            {/* Game Image with Overlay */}
                                            <div className="relative overflow-hidden">
                                                <LazyImage
                                                    src={gameData.image}
                                                    alt={gameData.name}
                                                    className="w-full h-32 sm:h-36 transform transition-transform group-hover:scale-110 duration-500"
                                                    fallbackSrc="/images/game-placeholder.jpg"
                                                    placeholderSrc="/images/game-placeholder-low.jpg"
                                                    blurUp={true}
                                                />
                                                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                                                {/* Usage Count Badge */}
                                                {gameData.usageCount > 0 && (
                                                    <div className="absolute top-2 right-2 bg-indigo-600/80 text-white text-xs font-bold px-2 py-1 rounded-full backdrop-blur-sm">
                                                        {gameData.usageCount}+ players
                                                    </div>
                                                )}

                                                {/* Play Now Button */}
                                                <div className="absolute bottom-0 left-0 w-full p-3 opacity-0 group-hover:opacity-100 transform group-hover:translate-y-0 translate-y-4 transition-all duration-300">
                                                    <button
                                                        className="text-sm bg-white/90 backdrop-blur-sm text-indigo-800 rounded-lg px-3 py-1.5 w-full font-medium hover:bg-white transition-colors shadow-md flex items-center justify-center space-x-1"
                                                        aria-label={`View talents for ${gameData.name}`}
                                                    >
                                                        <span>View Talents</span>
                                                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                                        </svg>
                                                    </button>
                                                </div>
                                            </div>

                                            {/* Game Info */}
                                            <div className="p-3 flex-grow flex flex-col justify-between">
                                                <h3 className="font-bold text-gray-800 group-hover:text-indigo-600 transition-colors text-center">
                                                    {gameData.shortName}
                                                </h3>
                                                {gameData.description && (
                                                    <p className="text-xs text-gray-500 mt-1 text-center line-clamp-2">
                                                        {gameData.description}
                                                    </p>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                );
                            })}
                        </div>
                    )}
                </div>

                {/* Game Talents Modal */}
                {selectedGame && (
                    <GameTalents
                        isOpen={isGameTalentsModalOpen}
                        onClose={() => setIsGameTalentsModalOpen(false)}
                        gameId={selectedGame.id}
                        gameName={selectedGame.name}
                    />
                )}

                {/* Enhanced CTA Banner */}
                <div className="bg-gradient-to-r from-indigo-600 via-indigo-700 to-blue-700 rounded-2xl overflow-hidden mb-12 relative group shadow-xl">
                    {/* Enhanced Background Effects */}
                    <div className="absolute inset-0 opacity-15 bg-noise mix-blend-overlay"></div>
                    <div className="absolute right-0 bottom-0 w-64 h-64 bg-white/10 rounded-full transform translate-x-20 translate-y-20 blur-xl"></div>
                    <div className="absolute left-0 top-0 w-40 h-40 bg-white/10 rounded-full transform -translate-x-20 -translate-y-20 blur-xl"></div>

                    {/* Enhanced Animated Particles */}
                    <div className="absolute inset-0 overflow-hidden pointer-events-none">
                        <div className="absolute h-3 w-3 rounded-full bg-white/30 top-[15%] left-[10%] animate-float-particle" style={{ animationDuration: '8s' }}></div>
                        <div className="absolute h-4 w-4 rounded-full bg-white/20 top-[35%] left-[15%] animate-float-particle" style={{ animationDuration: '12s', animationDelay: '1s' }}></div>
                        <div className="absolute h-3 w-3 rounded-full bg-white/25 top-[65%] left-[5%] animate-float-particle" style={{ animationDuration: '10s', animationDelay: '2s' }}></div>
                        <div className="absolute h-3 w-3 rounded-full bg-white/20 top-[75%] left-[50%] animate-float-particle" style={{ animationDuration: '9s', animationDelay: '0.5s' }}></div>
                        <div className="absolute h-4 w-4 rounded-full bg-white/25 top-[25%] right-[30%] animate-float-particle" style={{ animationDuration: '11s', animationDelay: '1.5s' }}></div>
                        <div className="absolute h-2 w-2 rounded-full bg-white/30 top-[45%] right-[10%] animate-float-particle" style={{ animationDuration: '13s', animationDelay: '0.7s' }}></div>
                        <div className="absolute h-2 w-2 rounded-full bg-white/30 top-[85%] right-[20%] animate-float-particle" style={{ animationDuration: '10s', animationDelay: '1.2s' }}></div>
                    </div>

                    {/* Enhanced Content Layout */}
                    <div className="p-8 md:p-10 flex flex-col md:flex-row items-center justify-between relative z-10 gap-8">
                        <div className="max-w-xl">
                            {/* Badge */}
                            <div className="inline-flex items-center justify-center bg-white/20 backdrop-blur-sm px-3 py-1 rounded-full mb-4">
                                <span className="w-2 h-2 bg-yellow-300 rounded-full mr-2 animate-pulse"></span>
                                <span className="text-white text-sm font-medium">New Opportunities</span>
                            </div>

                            {/* Enhanced Heading */}
                            <h3 className="text-2xl md:text-3xl font-bold text-white mb-3 [text-shadow:0_2px_5px_rgba(0,0,0,0.2)]">
                                {apiMissionsCount > 0
                                    ? `More than ${apiMissionsCount} missions waiting for you!`
                                    : 'Exciting missions waiting for you!'}
                            </h3>

                            {/* Enhanced Description */}
                            <p className="text-indigo-100 mb-6 text-left max-w-lg">
                                Join exciting gaming missions, complete tasks with other players, and earn rewards. New missions are added daily!
                            </p>

                            {/* Enhanced CTA Buttons */}
                            <div className="flex flex-wrap gap-4">
                                <button
                                    className="group bg-white text-indigo-700 px-6 py-3 rounded-xl font-medium hover:bg-indigo-50 transition-all hover:shadow-xl duration-300 inline-flex items-center relative overflow-hidden"
                                    onClick={() => navigate('/missions')}
                                >
                                    {/* Button shimmer effect */}
                                    <div className="absolute inset-0 w-full h-full overflow-hidden">
                                        <div className="absolute -inset-[100%] bg-gradient-to-r from-transparent via-indigo-100/30 to-transparent animate-shimmer transform -translate-x-full"></div>
                                    </div>

                                    <span className="relative z-10 font-semibold">Apply Now</span>
                                    <svg className="w-5 h-5 ml-2 relative z-10 transform transition-transform group-hover:translate-x-1 duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                                    </svg>
                                </button>

                                <button
                                    className="group bg-indigo-800/50 backdrop-blur-sm text-white border border-indigo-400/30 px-6 py-3 rounded-xl font-medium hover:bg-indigo-800/70 transition-all hover:shadow-lg duration-300 inline-flex items-center"
                                    onClick={() => navigate('/missions')}
                                >
                                    <span className="font-semibold">Browse Missions</span>
                                    <svg className="w-5 h-5 ml-2 transform transition-transform group-hover:translate-x-1 duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                                    </svg>
                                </button>
                            </div>

                            {/* Stats */}
                            <div className="flex gap-6 mt-6">
                                <div className="flex items-center">
                                    <svg className="w-5 h-5 text-indigo-300 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                                    </svg>
                                    <div>
                                        <div className="text-white font-bold">500+</div>
                                        <div className="text-indigo-200 text-xs">Active Players</div>
                                    </div>
                                </div>
                                <div className="flex items-center">
                                    <svg className="w-5 h-5 text-indigo-300 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <div>
                                        <div className="text-white font-bold">98%</div>
                                        <div className="text-indigo-200 text-xs">Completion Rate</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Enhanced Image Section */}
                        <div className="hidden md:block relative">
                            <div className="absolute -top-10 -left-10 w-32 h-32 bg-yellow-300/30 rounded-full opacity-50 animate-spin-slow blur-md"></div>
                            <div className="relative bg-indigo-800/30 backdrop-blur-md rounded-2xl p-6 border border-indigo-400/20 shadow-2xl transform group-hover:scale-105 transition-transform duration-500">
                                <LazyImage
                                    src="/gifts/medal.png"
                                    alt="Mission target"
                                    className="h-40 object-contain relative z-10 transform transition-transform group-hover:scale-110 duration-500 drop-shadow-xl"
                                    fallbackSrc="/images/game-placeholder.svg"
                                    placeholderSrc="/images/game-placeholder-low.svg"
                                    blurUp={true}
                                />

                                {/* Decorative elements */}
                                <div className="absolute top-4 right-4 w-8 h-8 rounded-full bg-yellow-400/50 animate-pulse-slow"></div>
                                <div className="absolute bottom-4 left-4 w-6 h-6 rounded-full bg-indigo-400/50 animate-float"></div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Enhanced Recommended Section */}
                <div className="mb-12 relative">
                    {/* Section Title with Icon */}
                    <div className="flex items-center space-x-3 mb-6">
                        <div className="flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-orange-500 to-red-500 shadow-md text-white">
                            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z" clipRule="evenodd" />
                            </svg>
                        </div>
                        <div className="flex items-center">
                            <h2 className="text-2xl font-bold text-gray-800">Recommended For You</h2>
                            <div className="ml-2 flex items-center justify-center">
                                <span className="relative flex h-3 w-3">
                                    <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-orange-400 opacity-75"></span>
                                    <span className="relative inline-flex rounded-full h-3 w-3 bg-orange-500"></span>
                                </span>
                            </div>
                        </div>
                        <button
                            className="ml-auto text-orange-600 text-sm font-medium hover:text-orange-800 transition-colors inline-flex items-center group bg-orange-50 px-3 py-1 rounded-lg"
                            onClick={() => navigate('/recommended')}
                        >
                            See All
                            <svg className="w-4 h-4 ml-1 transform transition-transform group-hover:translate-x-1 duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                            </svg>
                        </button>
                    </div>

                    {/* Enhanced Talent Cards */}
                    {homepageLoading ? (
                        <TalentCardSkeletonGrid count={3} columns={3} />
                    ) : homepageError || !transformedRecommendedTalents?.length ? (
                        // Show empty state when there's an error or no recommended talents
                        <EmptyTalentsState type="recommended" onExplore={() => navigate('/recommended')} />
                    ) : (
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-5">
                            {/* Use transformed data for better performance */}
                            {transformedRecommendedTalents.map((talentData, idx) => {

                                return (
                                    <div
                                        key={talentData.id}
                                        className="bg-gradient-to-b from-white to-orange-50/30 rounded-2xl overflow-hidden shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-orange-100/50 group cursor-pointer animate-fade-in"
                                        style={{ animationDelay: `${idx * 150}ms` }}
                                        onClick={() => navigate(`/talents/${talentData.id}`)}
                                    >
                                        {/* Enhanced Profile Image Section */}
                                        <div className="relative rounded-t-2xl overflow-hidden h-48">
                                            {/* Hot Badge with Enhanced Animation */}
                                            {talentData.isHot && (
                                                <div className="absolute top-3 left-3 bg-gradient-to-r from-orange-500 to-red-500 text-white px-3 py-1 rounded-full z-10 flex items-center shadow-lg">
                                                    <svg className="w-3.5 h-3.5 mr-1.5 animate-pulse" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fillRule="evenodd" d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z" clipRule="evenodd" />
                                                    </svg>
                                                    <span className="font-bold text-xs">HOT</span>
                                                </div>
                                            )}

                                            {/* Enhanced Image with Better Hover Effects */}
                                            <LazyImage
                                                src={talentData.image}
                                                alt={talentData.name}
                                                className="w-full h-full transition-transform duration-700 group-hover:scale-110 filter group-hover:brightness-110"
                                                fallbackSrc="/images/profile-placeholder.jpg"
                                                placeholderSrc="/images/profile-placeholder-low.jpg"
                                                blurUp={true}
                                            />

                                            {/* Enhanced Gradient Overlay */}
                                            <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/30 to-transparent opacity-70 group-hover:opacity-60 transition-opacity"></div>

                                            {/* Quick Action Buttons */}
                                            <div className="absolute bottom-3 right-3 flex space-x-2 opacity-0 group-hover:opacity-100 transform translate-y-2 group-hover:translate-y-0 transition-all duration-300">
                                                <button
                                                    className="w-8 h-8 rounded-full bg-white/90 backdrop-blur-sm flex items-center justify-center text-orange-600 shadow-md hover:bg-white transition-colors"
                                                    aria-label={`Chat with ${talentData.name}`}
                                                >
                                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                                    </svg>
                                                </button>
                                                <button
                                                    className="w-8 h-8 rounded-full bg-white/90 backdrop-blur-sm flex items-center justify-center text-orange-600 shadow-md hover:bg-white transition-colors"
                                                    aria-label={`Add ${talentData.name} to favorites`}
                                                >
                                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                                                    </svg>
                                                </button>
                                            </div>

                                            {/* Level Badge */}
                                            <div className="absolute bottom-3 left-3 bg-white/90 backdrop-blur-sm px-2.5 py-1 rounded-lg shadow-md flex items-center">
                                                <span className="font-bold text-xs text-orange-800">LV{talentData.level}</span>
                                            </div>
                                        </div>

                                        {/* Enhanced Profile Info Section */}
                                        <div className="p-4 relative">
                                            {/* Name and Gender with Better Styling */}
                                            <div className="flex justify-between items-center mb-2">
                                                <div className="flex items-center">
                                                    <h3 className="font-semibold text-gray-800 group-hover:text-orange-600 transition-colors">{talentData.name}</h3>
                                                    <span className={`ml-1.5 ${talentData.gender === 'female' ? 'text-pink-500' : 'text-blue-500'}`}>
                                                        {talentData.gender === 'male' ? '♂' : '♀'}
                                                    </span>
                                                </div>

                                                {/* Rating with Enhanced Styling */}
                                                <div className="flex items-center bg-yellow-50 px-2 py-1 rounded-lg border border-yellow-100 group-hover:bg-yellow-100 transition-colors shadow-sm">
                                                    <svg className="w-3.5 h-3.5 mr-1 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                                    </svg>
                                                    <span className="text-xs font-medium text-gray-700">4.5</span>
                                                </div>
                                            </div>

                                            {/* Bio with Better Text Styling */}
                                            <p className="text-sm text-gray-600 line-clamp-2 mb-3">{talentData.bio}</p>

                                            {/* Enhanced CTA Button */}
                                            <div className="mt-3 flex justify-between items-center">
                                                {/* Availability Status */}
                                                <div className="flex items-center bg-green-50 px-2 py-1 rounded-lg border border-green-100 shadow-sm">
                                                    <span className="w-2 h-2 bg-green-500 rounded-full mr-1.5 animate-pulse"></span>
                                                    <span className="text-xs font-medium text-green-800">Available</span>
                                                </div>

                                                {/* Connect Button */}
                                                <button
                                                    className="px-3 py-1.5 bg-gradient-to-r from-orange-500 to-red-500 text-white text-xs font-medium rounded-lg shadow-sm hover:shadow-md transition-all transform hover:scale-105 flex items-center"
                                                    aria-label={`View ${talentData.name}'s profile`}
                                                >
                                                    <span>View Profile</span>
                                                    <svg className="w-3.5 h-3.5 ml-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 5l7 7-7 7M5 5l7 7-7 7" />
                                                    </svg>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                );
                            })}
                        </div>
                    )}
                </div>

                {/* Enhanced Mission CTA Banner */}
                {homepageLoading ? (
                    <MissionBannerSkeleton />
                ) : homepageError ? (
                    // Show empty state when there's an error
                    <EmptyMissionsState onExplore={() => navigate('/create-mission')} />
                ) : (
                        <div className="mt-8 bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl p-6 text-white shadow-lg relative overflow-hidden">
                            {/* Background Pattern */}
                            <div className="absolute inset-0 bg-grid-white/[0.05] bg-[length:20px_20px]"></div>

                            {/* Decorative Elements */}
                            <div className="absolute -top-10 -right-10 w-40 h-40 bg-white/10 rounded-full blur-xl"></div>
                            <div className="absolute -bottom-10 -left-10 w-32 h-32 bg-white/10 rounded-full blur-lg"></div>

                            <div className="flex flex-col md:flex-row items-center justify-between relative z-10">
                                <div className="mb-6 md:mb-0 md:mr-8 text-center md:text-left">
                                    {/* Enhanced Heading */}
                                    <div className="flex items-center justify-center md:justify-start mb-3">
                                        <h3 className="text-2xl md:text-3xl font-bold text-white [text-shadow:0_2px_5px_rgba(0,0,0,0.2)] flex items-center">
                                            {apiMissionsCount > 0
                                                ? `${apiMissionsCount} Missions Available`
                                                : 'Exciting Missions Waiting'}
                                            <button
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    refreshMissionsCount();
                                                }}
                                                className="ml-3 text-white/70 hover:text-white transition-colors"
                                                aria-label="Refresh mission count"
                                            >
                                                <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                                </svg>
                                            </button>
                                        </h3>
                                        {apiMissionsCount > 0 && (
                                            <div className="ml-3 px-3 py-1 bg-white/20 backdrop-blur-sm rounded-full text-sm font-semibold animate-pulse">
                                                Live Now
                                            </div>
                                        )}
                                    </div>
                                    {/* Enhanced Description */}
                                    <p className="text-white/90 text-sm md:text-base max-w-xl">
                                        Join exciting gaming missions, team up with other players, and earn rewards.
                                        {apiMissionsCount > 5
                                            ? ` High demand right now - ${apiMissionsCount - 5}+ new missions added today!`
                                            : ' Create your own missions or join existing ones!'}
                                    </p>

                                    {/* Mission Stats */}
                                    <div className="flex items-center justify-center md:justify-start space-x-4 mt-4">
                                        <div className="flex items-center">
                                            <svg className="w-4 h-4 mr-1 text-white/80" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            <span className="text-sm text-white/80">Avg. 2hr duration</span>
                                        </div>
                                        <div className="flex items-center">
                                            <svg className="w-4 h-4 mr-1 text-white/80" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            <span className="text-sm text-white/80">Earn 100-200 coins</span>
                                        </div>
                                    </div>
                                </div>

                                {/* Enhanced CTA Button Group */}
                                <div className="flex flex-col space-y-2">
                                    <button
                                        onClick={() => navigate('/missions')}
                                        className="px-6 py-3 bg-white text-red-600 rounded-xl font-bold shadow-lg hover:shadow-xl transition-all transform hover:scale-105 flex items-center justify-center"
                                        aria-label="Explore available missions"
                                    >
                                        <span>Explore Missions</span>
                                        <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 8l4 4m0 0l-4 4m4-4H3" />
                                        </svg>
                                    </button>
                                    <button
                                        onClick={() => navigate('/create-mission')}
                                        className="px-6 py-2 bg-white/20 backdrop-blur-sm text-white rounded-xl font-medium hover:bg-white/30 transition-all flex items-center justify-center"
                                        aria-label="Create your own mission"
                                    >
                                        <span>Create Your Own</span>
                                        <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                )}

                {/* Section Title with Icon */}
                <div className="flex items-center space-x-3 mt-12 mb-6">
                    <div className="flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-indigo-500 to-indigo-600 shadow-md text-white">
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                    </div>
                    <h2 className="text-2xl font-bold text-gray-800">Wallet</h2>
                </div>

                {/* Enhanced Wallet Card - Single Card with Better Accessibility */}
                <div className="mb-12">
                    <div
                        className="bg-gradient-to-br from-indigo-50 via-white to-blue-50 rounded-2xl p-8 cursor-pointer shadow-lg hover:shadow-xl transition-all transform hover:scale-[1.01] duration-300 border border-indigo-100/50 relative overflow-hidden group"
                        onClick={() => navigate('/wallet')}
                        tabIndex="0"
                        role="button"
                        aria-label="View wallet details and manage credits"
                        onKeyDown={(e) => {
                            if (e.key === 'Enter' || e.key === ' ') {
                                navigate('/wallet');
                            }
                        }}
                    >
                        {/* Background patterns with reduced animation for better performance */}
                        <div className="absolute inset-0 bg-grid-gray/[0.03] bg-[length:20px_20px]"></div>
                        <div className="absolute -left-10 -bottom-10 w-40 h-40 bg-indigo-200/20 rounded-full blur-xl"></div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                            {/* Left Column - Wallet Info */}
                            <div className="relative z-10">
                                {/* Content Header */}
                                <div className="flex items-center space-x-4 mb-6">
                                    <div className="w-14 h-14 bg-gradient-to-br from-indigo-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg text-white group-hover:rotate-3 transition-transform duration-300">
                                        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 className="text-2xl font-bold text-left text-gray-800 group-hover:translate-x-1 transition-transform duration-300">My Wallet</h3>
                                        <p className="text-gray-500 mt-1">Manage your credits and transactions</p>
                                    </div>
                                </div>

                                {/* Balance with Enhanced Accessibility */}
                                <div className="mb-6">
                                    <p className="text-sm font-medium text-gray-500 mb-2">Current balance</p>
                                    <div className="flex items-center bg-gradient-to-r from-indigo-500 to-blue-600 px-6 py-3 rounded-xl shadow-md group-hover:shadow-lg transition-all duration-300 w-fit">
                                        <span className="font-bold text-white text-2xl flex items-center gap-1">
                                            <img src="/In-AppAssets/xcoin.png" alt="currency" className="w-6 h-6" />
                                            {walletBalance}
                                        </span>
                                    </div>
                                </div>

                                {/* Transaction summary with currency icon */}
                                <div className="grid grid-cols-2 gap-4">
                                    <div className="bg-indigo-50 p-4 rounded-xl border border-indigo-100 shadow-sm">
                                        <p className="text-xs text-indigo-500 mb-1">Income (30 days)</p>
                                        <p className="text-xl font-bold items-center justify-center text-indigo-700 flex gap-1">
                                            <img src="/In-AppAssets/xcoin.png" alt="Currency Icon" className="w-5 h-5 inline-block" />
                                            1,250
                                        </p>
                                    </div>
                                    <div className="bg-indigo-50 p-4 rounded-xl border border-indigo-100 shadow-sm">
                                        <p className="text-xs text-indigo-500 mb-1">Spent (30 days)</p>
                                        <p className="text-xl font-bold items-center justify-center text-indigo-700 flex gap-1">
                                            <img src="/In-AppAssets/xcoin.png" alt="Currency Icon" className="w-5 h-5 inline-block" />
                                            450
                                        </p>
                                    </div>
                                </div>


                                {/* Quick Actions */}
                                <div className="mt-6 flex space-x-3">
                                    <button
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            navigate('/wallet/deposit');
                                        }}
                                        className="px-4 py-2 bg-indigo-600 text-white rounded-lg text-sm font-medium hover:bg-indigo-700 transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                                    >
                                        Add Credits
                                    </button>
                                    <button
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            navigate('/wallet/withdraw');
                                        }}
                                        className="px-4 py-2 bg-white text-indigo-600 border border-indigo-200 rounded-lg text-sm font-medium hover:bg-indigo-50 transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                                    >
                                        Withdraw
                                    </button>
                                </div>
                            </div>

                            {/* Right Column - Transaction History Preview */}
                            <div className="relative z-10 hidden md:block">
                                <h4 className="font-medium text-gray-700 mb-4">Recent Transactions</h4>
                                <div className="space-y-3">
                                    {/* Transaction Item 1 */}
                                    <div className="bg-white p-3 rounded-xl border border-gray-100 shadow-sm flex items-center justify-between">
                                        <div className="flex items-center">
                                            <div className="w-10 h-10 rounded-lg bg-green-100 flex items-center justify-center text-green-600 mr-3">
                                                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                                </svg>
                                            </div>
                                            <div>
                                                <p className="font-medium text-gray-800">Deposit</p>
                                                <p className="text-xs text-gray-500">2 days ago</p>
                                            </div>
                                        </div>
                                        <span className="font-bold text-green-600">+₩ 500</span>
                                    </div>

                                    {/* Transaction Item 2 */}
                                    <div className="bg-white p-3 rounded-xl border border-gray-100 shadow-sm flex items-center justify-between">
                                        <div className="flex items-center">
                                            <div className="w-10 h-10 rounded-lg bg-red-100 flex items-center justify-center text-red-600 mr-3">
                                                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                                                </svg>
                                            </div>
                                            <div>
                                                <p className="font-medium text-gray-800">Mission Purchase</p>
                                                <p className="text-xs text-left text-gray-500">5 days ago</p>
                                            </div>
                                        </div>
                                        <span className="font-bold text-red-600">-₩ 250</span>
                                    </div>

                                    {/* Transaction Item 3 */}
                                    <div className="bg-white p-3 rounded-xl border border-gray-100 shadow-sm flex items-center justify-between">
                                        <div className="flex items-center">
                                            <div className="w-10 h-10 rounded-lg bg-blue-100 flex items-center justify-center text-blue-600 mr-3">
                                                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                </svg>
                                            </div>
                                            <div>
                                                <p className="font-medium text-gray-800">Mission Reward</p>
                                                <p className="text-xs text-left text-gray-500">1 week ago</p>
                                            </div>
                                        </div>
                                        <span className="font-bold text-green-600">+₩ 750</span>
                                    </div>
                                </div>

                                <button
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        navigate('/wallet/transactions');
                                    }}
                                    className="mt-4 text-sm bg-transparent hover:bg-transparent text-indigo-600 font-medium hover:text-indigo-800 transition-colors flex items-center"
                                >
                                    View All Transactions
                                    <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                                    </svg>
                                </button>
                            </div>
                        </div>

                        {/* Animated coins with reduced animation for better performance */}
                        <div className="absolute bottom-4 right-4 flex space-x-[-8px]">
                            <div className="w-8 h-8 rounded-full bg-yellow-400 shadow-md opacity-80 rotate-0 group-hover:rotate-[25deg] transition-transform z-20">
                                <div className="absolute inset-1 bg-yellow-300 rounded-full flex items-center justify-center text-yellow-700 text-[10px] font-bold">₩</div>
                            </div>
                            <div className="w-8 h-8 rounded-full bg-indigo-400 shadow-md opacity-80 rotate-0 group-hover:rotate-[-15deg] transition-transform z-10">
                                <div className="absolute inset-1 bg-indigo-300 rounded-full flex items-center justify-center text-indigo-700 text-[10px] font-bold">₩</div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* New Talents Section */}
                <div className="mb-12 relative">
                    {/* Section Title with Icon */}
                    <div className="flex items-center space-x-3 mb-6">
                        <div className="flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-green-500 to-teal-500 shadow-md text-white">
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                            </svg>
                        </div>
                        <div className="flex items-center">
                            <h2 className="text-2xl font-bold text-gray-800">New Talents</h2>
                            <div className="ml-2 flex items-center justify-center">
                                <span className="relative flex h-3 w-3">
                                    <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-green-400 opacity-75"></span>
                                    <span className="relative inline-flex rounded-full h-3 w-3 bg-green-500"></span>
                                </span>
                            </div>
                        </div>
                        <button
                            className="ml-auto text-green-600 text-sm font-medium hover:text-green-800 transition-colors inline-flex items-center group bg-green-50 px-3 py-1 rounded-lg"
                            onClick={() => navigate('/new-talents')}
                        >
                            See All
                            <svg className="w-4 h-4 ml-1 transform transition-transform group-hover:translate-x-1 duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                            </svg>
                        </button>
                    </div>

                    {/* New Talents Cards - Horizontal Layout */}
                    {homepageLoading ? (
                        <NewTalentCardSkeletonGrid count={4} />
                    ) : homepageError || !transformedNewTalents?.length ? (
                        // Show empty state when there's an error or no new talents
                        <EmptyTalentsState type="new" onExplore={() => navigate('/new-talents')} />
                    ) : (
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-5">
                            {/* Use transformed data for better performance */}
                            {transformedNewTalents.map((talentData, idx) => {

                                return (
                                    <div
                                        key={talentData.id}
                                        className="bg-gradient-to-b from-white to-green-50/30 rounded-2xl overflow-hidden shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-green-100/50 group cursor-pointer animate-fade-in"
                                        style={{ animationDelay: `${idx * 100}ms` }}
                                        onClick={() => navigate(`/talents/${talentData.id}`)}
                                    >
                                        {/* Profile Image */}
                                        <div className="relative rounded-t-2xl overflow-hidden h-40">
                                            {/* New Badge */}
                                            <div className="absolute top-3 left-3 bg-gradient-to-r from-green-500 to-teal-500 text-white px-3 py-1 rounded-full z-10 flex items-center shadow-lg">
                                                <svg className="w-3.5 h-3.5 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                                </svg>
                                                <span className="font-bold text-xs">NEW</span>
                                            </div>

                                            {/* Image with Hover Effects */}
                                            <img
                                                src={talentData.image}
                                                alt={talentData.name}
                                                className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                                                onError={(e) => {
                                                    e.target.onerror = null;
                                                    e.target.src = '/images/profile-placeholder.jpg';
                                                }}
                                            />

                                            {/* Gradient Overlay */}
                                            <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/30 to-transparent opacity-70 group-hover:opacity-60 transition-opacity"></div>

                                            {/* Level Badge */}
                                            <div className="absolute bottom-3 left-3 bg-white/90 backdrop-blur-sm px-2.5 py-1 rounded-lg shadow-md flex items-center">
                                                <span className="font-bold text-xs text-green-800">LV{talentData.level}</span>
                                            </div>
                                        </div>

                                        {/* Profile Info */}
                                        <div className="p-3 relative">
                                            {/* Name */}
                                            <div className="flex justify-between items-center mb-2">
                                                <h3 className="font-semibold text-gray-800 group-hover:text-green-600 transition-colors">{talentData.name}</h3>
                                            </div>

                                            {/* View Profile Button */}
                                            <button className="w-full px-3 py-1.5 bg-gradient-to-r from-green-500 to-teal-500 text-white text-xs font-medium rounded-lg shadow-sm hover:shadow-md transition-all transform hover:scale-105 flex items-center justify-center">
                                                <span>View Profile</span>
                                                <svg className="w-3.5 h-3.5 ml-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 5l7 7-7 7M5 5l7 7-7 7" />
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                );
                            })}
                        </div>
                    )}
                </div>

                {/* Enhanced Hot in Game Section (Renamed to Online Talents) */}
                <div className="mb-12 relative">
                    {/* Section Title with Icon */}
                    <div className="flex items-center space-x-3 mb-6">
                        <div className="flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-red-500 to-orange-500 shadow-md text-white">
                            <svg className="w-5 h-5 animate-pulse-slow" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z" clipRule="evenodd" />
                            </svg>
                        </div>
                        <div className="flex items-center">
                            <h2 className="text-2xl font-bold text-gray-800">Online Talents</h2>
                            <div className="ml-2 flex items-center justify-center">
                                <span className="relative flex h-3 w-3">
                                    <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-red-400 opacity-75"></span>
                                    <span className="relative inline-flex rounded-full h-3 w-3 bg-red-500"></span>
                                </span>
                            </div>
                        </div>
                        <button
                            className="ml-auto text-red-600 text-sm font-medium hover:text-red-800 transition-colors inline-flex items-center group bg-red-50 px-3 py-1 rounded-lg"
                            onClick={() => navigate('/online-talents')}
                        >
                            See All
                            <svg className="w-4 h-4 ml-1 transform transition-transform group-hover:translate-x-1 duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                            </svg>
                        </button>
                    </div>

                    {/* Enhanced Talent Cards */}
                    {homepageLoading ? (
                        <TalentCardSkeletonGrid count={3} columns={3} />
                    ) : homepageError || !transformedOnlineTalents?.length ? (
                        // Show empty state when there's an error or no online talents
                        <EmptyTalentsState type="online" onExplore={() => navigate('/online-talents')} />
                    ) : (
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-5">
                            {/* Use transformed data for better performance */}
                            {transformedOnlineTalents.map((talentData, idx) => {

                                return (
                                    <div
                                        key={talentData.id}
                                        className="bg-gradient-to-b from-white to-red-50/30 rounded-2xl overflow-hidden shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-red-100/50 group cursor-pointer animate-fade-in"
                                        style={{ animationDelay: `${idx * 150}ms` }}
                                        onClick={() => navigate(`/talents/${talentData.id}`)}
                                    >
                                    {/* Enhanced Profile Image Section */}
                                    <div className="relative rounded-t-2xl overflow-hidden h-48">
                                        {/* Online Badge with Enhanced Animation */}
                                        <div className="absolute top-3 left-3 bg-gradient-to-r from-red-500 to-orange-500 text-white px-3 py-1 rounded-full z-10 flex items-center shadow-lg">
                                            <svg className="w-3.5 h-3.5 mr-1.5 animate-pulse" fill="currentColor" viewBox="0 0 20 20">
                                                <path fillRule="evenodd" d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z" clipRule="evenodd" />
                                            </svg>
                                            <span className="font-bold text-xs">ONLINE</span>
                                        </div>

                                        {/* Enhanced Image with Better Hover Effects */}
                                        <LazyImage
                                            src={talentData.image}
                                            alt={talentData.name}
                                            className="w-full h-full transition-transform duration-700 group-hover:scale-110 filter group-hover:brightness-110"
                                            fallbackSrc="/images/profile-placeholder.jpg"
                                            placeholderSrc="/images/profile-placeholder-low.jpg"
                                            blurUp={true}
                                        />

                                        {/* Enhanced Gradient Overlay */}
                                        <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/30 to-transparent opacity-70 group-hover:opacity-60 transition-opacity"></div>

                                        {/* Quick Action Buttons */}
                                        <div className="absolute bottom-3 right-3 flex space-x-2 opacity-0 group-hover:opacity-100 transform translate-y-2 group-hover:translate-y-0 transition-all duration-300">
                                            <button className="w-8 h-8 rounded-full bg-white/90 backdrop-blur-sm flex items-center justify-center text-red-600 shadow-md hover:bg-white transition-colors">
                                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                                </svg>
                                            </button>
                                            <button className="w-8 h-8 rounded-full bg-white/90 backdrop-blur-sm flex items-center justify-center text-red-600 shadow-md hover:bg-white transition-colors">
                                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                                                </svg>
                                            </button>
                                        </div>

                                        {/* Level Badge */}
                                        <div className="absolute bottom-3 left-3 bg-white/90 backdrop-blur-sm px-2.5 py-1 rounded-lg shadow-md flex items-center">
                                            <span className="font-bold text-xs text-red-800">LV{talentData.level}</span>
                                        </div>
                                    </div>

                                    {/* Enhanced Profile Info Section */}
                                    <div className="p-4 relative">
                                        {/* Name and Gender with Better Styling */}
                                        <div className="flex justify-between items-center mb-2">
                                            <div className="flex items-center">
                                                <h3 className="font-semibold text-gray-800 group-hover:text-red-600 transition-colors">{talentData.name}</h3>
                                                <span className={`ml-1.5 ${talentData.gender === 'female' ? 'text-pink-500' : 'text-blue-500'}`}>
                                                    {talentData.gender === 'male' ? '♂' : '♀'}
                                                </span>
                                            </div>

                                            {/* Rating with Enhanced Styling */}
                                            <div className="flex items-center bg-yellow-50 px-2 py-1 rounded-lg border border-yellow-100 group-hover:bg-yellow-100 transition-colors shadow-sm">
                                                <svg className="w-3.5 h-3.5 mr-1 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                                </svg>
                                                <span className="text-xs font-medium text-gray-700">4.9</span>
                                            </div>
                                        </div>

                                        {/* Bio with Better Text Styling */}
                                        <p className="text-sm text-gray-600 line-clamp-2 mb-3">{talentData.bio}</p>

                                        {/* Enhanced CTA Button */}
                                        <button className="w-full px-3 py-2 bg-gradient-to-r from-red-500 to-orange-500 text-white text-sm font-medium rounded-lg shadow-sm hover:shadow-md transition-all transform hover:scale-105 flex items-center justify-center">
                                            <span>View Profile</span>
                                            <svg className="w-4 h-4 ml-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 5l7 7-7 7M5 5l7 7-7 7" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                                );
                            })}
                        </div>
                    )}
                </div>
            </main>

            {/* Settings Modal */}
            {showSettings && (
                <Settings
                    isOpen={showSettings}
                    onClose={() => setShowSettings(false)}
                />
            )}
        </div>
    );
}

export default Home;

<style jsx>{`
    @keyframes spin-slow {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    .animate-spin-slow {
        animation: spin-slow 15s linear infinite;
    }
    @keyframes pulse-slow {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.7; }
    }
    .animate-pulse-slow {
        animation: pulse-slow 3s ease-in-out infinite;
    }
    .bg-grid-white {
        background-image: linear-gradient(to right, rgba(255,255,255,0.1) 1px, transparent 1px),
                          linear-gradient(to bottom, rgba(255,255,255,0.1) 1px, transparent 1px);
    }
    .bg-noise {
        background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E");
    }
    @keyframes float-particle {
        0% {
            transform: translate(0, 0) scale(1);
            opacity: 0;
        }
        50% {
            opacity: 0.5;
        }
        100% {
            transform: translate(0, -100px) scale(0.5);
            opacity: 0;
        }
    }

    @keyframes float {
        0%, 100% { transform: translateY(0); }
        50% { transform: translateY(-10px); }
    }

    @keyframes float-delayed {
        0%, 100% { transform: translateY(0); }
        50% { transform: translateY(-10px); }
    }

    @keyframes bounce-slower {
        0%, 100% { transform: translateY(0); }
        50% { transform: translateY(-5px); }
    }

    .animate-float-particle {
        animation: float-particle 10s infinite linear;
    }

    .animate-float {
        animation: float 4s infinite ease-in-out;
    }

    .animate-float-delayed {
        animation: float-delayed 5s infinite ease-in-out;
        animation-delay: 1s;
    }

    .animate-bounce-slower {
        animation: bounce-slower 6s infinite ease-in-out;
    }

    .shadow-glow {
        box-shadow: 0 0 10px 2px rgba(255, 255, 255, 0.3);
    }

    @keyframes progress-bar {
        0% { transform: scaleX(0); }
        100% { transform: scaleX(1); }
    }

    .animate-progress-bar {
        animation: progress-bar 5s linear infinite;
    }

    /* Slide animations */
    .slide-out-left {
        animation: slide-out-left 0.3s forwards ease-in-out;
    }

    .slide-out-right {
        animation: slide-out-right 0.3s forwards ease-in-out;
    }

    .slide-in-right {
        animation: slide-in-right 0.5s forwards ease-in-out;
    }

    .slide-in-left {
        animation: slide-in-left 0.5s forwards ease-in-out;
    }

    @keyframes slide-out-left {
        0% { transform: translateX(0); opacity: 1; }
        100% { transform: translateX(-5%); opacity: 0; }
    }

    @keyframes slide-out-right {
        0% { transform: translateX(0); opacity: 1; }
        100% { transform: translateX(5%); opacity: 0; }
    }

    @keyframes slide-in-right {
        0% { transform: translateX(5%); opacity: 0; }
        100% { transform: translateX(0); opacity: 1; }
    }

    @keyframes slide-in-left {
        0% { transform: translateX(-5%); opacity: 0; }
        100% { transform: translateX(0); opacity: 1; }
    }
`}</style>
