import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import '../styles/animations.css';

// Step components
import PersonalInfoStep from './ProfileSetupSteps/PersonalInfoStep';
import BioStep from './ProfileSetupSteps/BioStep';
import PersonalityStep from './ProfileSetupSteps/PersonalityStep';
import ServiceSetupStep from './ProfileSetupSteps/ServiceSetupStep';
import CompletionStep from './ProfileSetupSteps/CompletionStep';

// Step titles and descriptions
const STEPS = [
    {
        title: 'Tell Us About Yourself',
        description: 'Let\'s start with your basic information',
        icon: '👤'
    },
    {
        title: 'Make Your Profile Stand Out',
        description: 'Tell others about yourself and your gaming style',
        icon: '✏️'
    },
    {
        title: 'Select Your Personality',
        description: 'Choose traits that best describe you',
        icon: '🌟'
    },
    {
        title: 'Set Up Your Services',
        description: 'Configure the services you want to offer',
        icon: '🎮'
    },
    {
        title: 'You\'re All Set!',
        description: 'Your profile is ready to go',
        icon: '🎉'
    }
];

function ProfileSetup() {
    const navigate = useNavigate();
    const [currentStep, setCurrentStep] = useState(1);
    const [loading, setLoading] = useState(true);
    const [userData, setUserData] = useState(null);

    // Form data state
    const [profileData, setProfileData] = useState({
        // Personal info
        profileImage: null,
        nickname: '',
        gender: '',
        height: '',
        weight: '',
        birthday: '',
        race: '',
        email: '',

        // Bio
        description: '',

        // Personality traits
        personalityTraits: [],

        // Services
        services: []
    });

    // Enhanced user data loading with welcome page integration
    useEffect(() => {
        const loadUserData = async () => {
            try {
                // Check if coming from welcome page
                const fromWelcomePage = localStorage.getItem('fromWelcomePage') === 'true';

                // Get user data from localStorage
                const storedUser = localStorage.getItem('user');
                if (storedUser) {
                    try {
                        const parsedUser = JSON.parse(storedUser);
                        setUserData(parsedUser);

                        // Pre-fill data from registration if available
                        setProfileData(prev => ({
                            ...prev,
                            nickname: parsedUser.nickname || '',
                            email: parsedUser.email || '',
                            profileImage: parsedUser.profileImage || null,
                            // If gender was provided during registration, use it
                            gender: parsedUser.gender || ''
                        }));

                        // Track that we've loaded the profile setup
                        if (fromWelcomePage) {
                            // Clear the flag after using it
                            localStorage.removeItem('fromWelcomePage');

                            // Track profile setup start (for analytics)
                            try {
                                if (window.gtag) {
                                    window.gtag('event', 'profile_setup_start', {
                                        'source': 'welcome_page'
                                    });
                                }
                            } catch (analyticsError) {
                                console.error('Analytics error:', analyticsError);
                            }
                        }
                    } catch (error) {
                        console.error('Error parsing user data:', error);
                    }
                }
            } catch (error) {
                console.error('Error loading user data:', error);
            } finally {
                setLoading(false);
            }
        };

        loadUserData();
    }, []);

    // Enhanced next step handler with analytics
    const handleNext = () => {
        if (currentStep < 5) {
            // Track step completion (for analytics)
            try {
                if (window.gtag) {
                    window.gtag('event', 'profile_setup_step_complete', {
                        'step': currentStep,
                        'step_name': STEPS[currentStep - 1].title
                    });
                }
            } catch (analyticsError) {
                console.error('Analytics error:', analyticsError);
            }

            // Move to next step
            setCurrentStep(prev => prev + 1);
            window.scrollTo(0, 0);
        }
    };

    // Enhanced previous step handler with confirmation
    const handleBack = () => {
        if (currentStep > 1) {
            setCurrentStep(prev => prev - 1);
            window.scrollTo(0, 0);
        } else {
            // If on first step, confirm before going back to welcome page
            if (window.confirm('Are you sure you want to go back? Your progress will not be saved.')) {
                navigate('/welcome');
            }
        }
    };

    // Handle skip
    const handleSkip = () => {
        if (currentStep < 5) {
            setCurrentStep(prev => prev + 1);
            window.scrollTo(0, 0);
        }
    };

    // Handle form data changes
    const updateProfileData = (field, value) => {
        setProfileData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    // Enhanced completion handler with API integration and analytics
    const handleComplete = async () => {
        try {
            setLoading(true);

            // Save profile data to localStorage
            localStorage.setItem('profileData', JSON.stringify(profileData));

            // In a real app, we would send this data to the API
            // This is a mock implementation for now
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Update user data in localStorage with profile information
            const storedUser = localStorage.getItem('user');
            if (storedUser) {
                try {
                    const parsedUser = JSON.parse(storedUser);
                    const updatedUser = {
                        ...parsedUser,
                        profileCompleted: true,
                        profileImage: profileData.profileImage || parsedUser.profileImage,
                        nickname: profileData.nickname || parsedUser.nickname,
                        gender: profileData.gender || parsedUser.gender,
                        // Add other relevant profile data
                        profileCompletionDate: new Date().toISOString()
                    };
                    localStorage.setItem('user', JSON.stringify(updatedUser));
                } catch (error) {
                    console.error('Error updating user data:', error);
                }
            }

            // Track profile completion (for analytics)
            try {
                if (window.gtag) {
                    window.gtag('event', 'profile_setup_complete', {
                        'steps_completed': currentStep,
                        'has_profile_image': !!profileData.profileImage,
                        'has_bio': !!profileData.description,
                        'has_personality_traits': profileData.personalityTraits.length > 0,
                        'has_services': profileData.services.length > 0
                    });
                }
            } catch (analyticsError) {
                console.error('Analytics error:', analyticsError);
            }

            // Navigate to home after a short delay to show completion animation
            setTimeout(() => {
                navigate('/home');
            }, 2000);
        } catch (error) {
            console.error('Error completing profile setup:', error);
        } finally {
            setLoading(false);
        }
    };

    // Handle edit profile after completion
    const handleEditProfile = () => {
        // Navigate to edit profile page
        navigate('/profile/edit');
    };

    // Enhanced loading state with animation
    if (loading) {
        return (
            <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 relative overflow-hidden">
                {/* Decorative background elements */}
                <div className="absolute inset-0 overflow-hidden pointer-events-none">
                    <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(#4f46e5_1px,transparent_1px)] [background-size:20px_20px] opacity-[0.15]"></div>
                    <div className="absolute -top-24 -right-24 w-96 h-96 bg-blue-200 rounded-full mix-blend-multiply filter blur-2xl opacity-20 animate-blob"></div>
                    <div className="absolute -bottom-24 -left-24 w-96 h-96 bg-purple-200 rounded-full mix-blend-multiply filter blur-2xl opacity-20 animate-blob animation-delay-2000"></div>
                </div>

                <div className="relative z-10 flex flex-col items-center">
                    {/* Enhanced loading spinner */}
                    <div className="relative">
                        <div className="absolute inset-0 rounded-full bg-blue-400/20 blur-xl transform scale-125"></div>
                        <div className="animate-spin rounded-full h-20 w-20 border-4 border-blue-200 border-t-4 border-t-blue-600 relative"></div>
                        <div className="absolute top-0 left-0 right-0 bottom-0 flex items-center justify-center">
                            <span className="text-2xl">🎮</span>
                        </div>
                    </div>
                    <p className="mt-6 text-blue-700 font-medium text-lg">Preparing your profile setup...</p>
                    <p className="mt-2 text-blue-500 text-sm">This will only take a moment</p>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 py-12 relative overflow-hidden">
            {/* Decorative background elements */}
            <div className="absolute inset-0 overflow-hidden pointer-events-none">
                <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(#4f46e5_1px,transparent_1px)] [background-size:20px_20px] opacity-[0.15]"></div>
                <div className="absolute -top-24 -right-24 w-96 h-96 bg-blue-200 rounded-full mix-blend-multiply filter blur-2xl opacity-20 animate-blob"></div>
                <div className="absolute -bottom-24 -left-24 w-96 h-96 bg-purple-200 rounded-full mix-blend-multiply filter blur-2xl opacity-20 animate-blob animation-delay-2000"></div>
            </div>

            {/* Header with progress indicator */}
            <header className="bg-white/90 shadow-md sticky top-0 z-10 backdrop-filter backdrop-blur-lg">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
                    <div className="flex items-center justify-between">
                        <button
                            onClick={handleBack}
                            className="p-2 rounded-full bg-transparent hover:bg-transparent hover:underline hover:bg-gray-100 transition-colors"
                            aria-label="Go back"
                        >
                            <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                            </svg>
                        </button>

                        <div className="text-center">
                            <h1 className="text-xl font-bold text-gray-800">
                                {currentStep <= 5 ? STEPS[currentStep-1].title : 'Set Up Your Profile'}
                            </h1>
                            {currentStep < 5 && (
                                <div className="flex items-center justify-center mt-1">
                                    <span className="text-sm font-medium text-blue-600">Step {currentStep}</span>
                                    <span className="mx-2 text-gray-400">/</span>
                                    <span className="text-sm text-gray-500">4</span>
                                </div>
                            )}
                        </div>

                        {currentStep < 5 && (
                            <button
                                onClick={handleSkip}
                                className="text-sm font-medium text-blue-600 hover:text-blue-800 bg-transparent hover:bg-transparent hover:underline transition-colors"
                            >
                                Skip
                            </button>
                        )}
                        {currentStep === 5 && <div className="w-10"></div>}
                    </div>

                    {/* Progress bar */}
                    {currentStep < 5 && (
                        <div className="mt-4 h-2 w-full bg-gray-200 rounded-full overflow-hidden">
                            <div
                                className="h-full bg-gradient-to-r from-blue-500 to-indigo-600 transition-all duration-500 ease-out"
                                style={{ width: `${(currentStep / 4) * 100}%` }}
                            ></div>
                        </div>
                    )}
                </div>
            </header>

            {/* Step indicators */}
            <div className="max-w-5xl mx-auto px-4 pt-8 pb-4">
                <div className="flex justify-between">
                    {STEPS.slice(0, 4).map((step, index) => (
                        <div
                            key={index}
                            className={`flex flex-col items-center ${
                                index + 1 === currentStep
                                    ? 'opacity-100'
                                    : index + 1 < currentStep
                                        ? 'opacity-70'
                                        : 'opacity-40'
                            }`}
                            style={{ width: '20%' }}
                        >
                            <div
                                className={`w-12 h-12 rounded-full flex items-center justify-center mb-2 ${
                                    index + 1 === currentStep
                                        ? 'bg-blue-600 text-white shadow-lg'
                                        : index + 1 < currentStep
                                            ? 'bg-green-500 text-white'
                                            : 'bg-gray-200 text-gray-500'
                                }`}
                            >
                                {index + 1 < currentStep ? (
                                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                ) : (
                                    <span className="text-lg">{STEPS[index].icon}</span>
                                )}
                            </div>
                            <span className={`text-xs font-medium text-center ${
                                index + 1 === currentStep
                                    ? 'text-blue-600'
                                    : index + 1 < currentStep
                                        ? 'text-green-600'
                                        : 'text-gray-500'
                            }`}>
                                {index + 1}. {STEPS[index].title.split(' ')[0]}
                            </span>
                        </div>
                    ))}
                </div>
            </div>

            {/* Main content */}
            <main className="max-w-4xl mx-auto px-4 py-6">
                <div className="bg-white rounded-2xl shadow-lg p-6 sm:p-8 relative overflow-hidden">
                    {/* Background decorative elements */}
                    <div className="absolute -top-20 -right-20 w-40 h-40 bg-blue-50 rounded-full opacity-70"></div>
                    <div className="absolute -bottom-20 -left-20 w-40 h-40 bg-indigo-50 rounded-full opacity-70"></div>

                    {/* Step content with animation */}
                    <div className="relative z-10 transition-all duration-300 transform">
                        {/* Step 1: Personal Information */}
                        {currentStep === 1 && (
                            <div className="animate-fade-in">
                                <PersonalInfoStep
                                    profileData={profileData}
                                    updateProfileData={updateProfileData}
                                    onNext={handleNext}
                                />
                            </div>
                        )}

                        {/* Step 2: Bio */}
                        {currentStep === 2 && (
                            <div className="animate-fade-in">
                                <BioStep
                                    profileData={profileData}
                                    updateProfileData={updateProfileData}
                                    onNext={handleNext}
                                />
                            </div>
                        )}

                        {/* Step 3: Personality Traits */}
                        {currentStep === 3 && (
                            <div className="animate-fade-in">
                                <PersonalityStep
                                    profileData={profileData}
                                    updateProfileData={updateProfileData}
                                    onNext={handleNext}
                                />
                            </div>
                        )}

                        {/* Step 4: Service Setup */}
                        {currentStep === 4 && (
                            <div className="animate-fade-in">
                                <ServiceSetupStep
                                    profileData={profileData}
                                    updateProfileData={updateProfileData}
                                    onNext={handleNext}
                                />
                            </div>
                        )}

                        {/* Step 5: Completion */}
                        {currentStep === 5 && (
                            <div className="animate-fade-in">
                                <CompletionStep
                                    profileData={profileData}
                                    onComplete={handleComplete}
                                    onEditProfile={handleEditProfile}
                                />
                            </div>
                        )}
                    </div>
                </div>

                {/* Help text */}
                {currentStep < 5 && (
                    <div className="mt-6 text-center text-sm text-gray-500">
                        <p>Need help? Contact support at <span className="text-blue-600"><EMAIL></span></p>
                    </div>
                )}
            </main>
        </div>
    );
}

export default ProfileSetup;
