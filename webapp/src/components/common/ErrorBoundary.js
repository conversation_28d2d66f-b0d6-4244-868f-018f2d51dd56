import React from 'react';
import { motion } from 'framer-motion';
import errorHandlingService from '../../services/errorHandlingService';
import { API_ERROR_TYPES } from '../../utils/errorTypes';

/**
 * Enhanced Error Boundary
 * 
 * This component catches JavaScript errors anywhere in the child component tree,
 * logs those errors, and displays a beautiful fallback UI with recovery options.
 */
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
      retryCount: 0
    };
    
    this.handleRetry = this.handleRetry.bind(this);
    this.handleReload = this.handleReload.bind(this);
    this.handleGoHome = this.handleGoHome.bind(this);
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // Process error through error handling service
    const processedError = errorHandlingService.processError(
      error, 
      this.props.context || 'error_boundary',
      {
        componentStack: errorInfo.componentStack,
        errorBoundary: true,
        retryCount: this.state.retryCount
      }
    );

    this.setState({
      error: processedError,
      errorInfo,
      errorId: processedError.id
    });

    // Call onError callback if provided
    if (this.props.onError) {
      this.props.onError(processedError, errorInfo);
    }

    // Log to external service if configured
    if (this.props.logToService) {
      this.logToExternalService(processedError, errorInfo);
    }
  }

  logToExternalService(error, errorInfo) {
    // This would integrate with services like Sentry, LogRocket, etc.
    console.error('Error logged to external service:', {
      errorId: error.id,
      message: error.message,
      stack: error.originalMessage,
      componentStack: errorInfo.componentStack,
      context: this.props.context
    });
  }

  handleRetry() {
    this.setState(prevState => ({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
      retryCount: prevState.retryCount + 1
    }));

    // Call onRetry callback if provided
    if (this.props.onRetry) {
      this.props.onRetry();
    }
  }

  handleReload() {
    window.location.reload();
  }

  handleGoHome() {
    window.location.href = '/';
  }

  render() {
    if (this.state.hasError) {
      // Custom fallback UI if provided
      if (this.props.fallback) {
        return this.props.fallback(this.state.error, this.handleRetry);
      }

      // Default beautiful error UI
      return (
        <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-blue-50 flex items-center justify-center p-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="max-w-md w-full"
          >
            {/* Error Card */}
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-8 text-center">
              {/* Error Icon */}
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-red-500 to-pink-500 rounded-full flex items-center justify-center"
              >
                <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </motion.div>

              {/* Error Title */}
              <motion.h2
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.3 }}
                className="text-2xl font-bold text-gray-900 mb-3"
              >
                Oops! Something went wrong
              </motion.h2>

              {/* Error Message */}
              <motion.p
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.4 }}
                className="text-gray-600 mb-6 leading-relaxed"
              >
                {this.state.error?.message || 'An unexpected error occurred. Please try again.'}
              </motion.p>

              {/* Error ID (for support) */}
              {this.state.errorId && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.5 }}
                  className="bg-gray-100 rounded-lg p-3 mb-6"
                >
                  <p className="text-xs text-gray-500 mb-1">Error ID (for support):</p>
                  <p className="text-sm font-mono text-gray-700">{this.state.errorId}</p>
                </motion.div>
              )}

              {/* Action Buttons */}
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6 }}
                className="space-y-3"
              >
                {/* Retry Button */}
                <button
                  onClick={this.handleRetry}
                  className="w-full bg-gradient-to-r from-indigo-600 to-blue-600 text-white py-3 px-6 rounded-xl font-semibold hover:from-indigo-700 hover:to-blue-700 transition-all duration-300 transform hover:scale-105 shadow-lg"
                >
                  Try Again
                </button>

                {/* Secondary Actions */}
                <div className="flex space-x-3">
                  <button
                    onClick={this.handleReload}
                    className="flex-1 bg-gray-100 text-gray-700 py-2 px-4 rounded-lg font-medium hover:bg-gray-200 transition-colors duration-200"
                  >
                    Reload Page
                  </button>
                  <button
                    onClick={this.handleGoHome}
                    className="flex-1 bg-gray-100 text-gray-700 py-2 px-4 rounded-lg font-medium hover:bg-gray-200 transition-colors duration-200"
                  >
                    Go Home
                  </button>
                </div>
              </motion.div>

              {/* Development Info */}
              {process.env.NODE_ENV === 'development' && this.state.error && (
                <motion.details
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.7 }}
                  className="mt-6 text-left"
                >
                  <summary className="cursor-pointer text-sm text-gray-500 hover:text-gray-700">
                    Developer Details
                  </summary>
                  <div className="mt-2 p-3 bg-gray-50 rounded-lg text-xs">
                    <p className="font-semibold text-gray-700 mb-2">Original Error:</p>
                    <pre className="text-gray-600 whitespace-pre-wrap break-all">
                      {this.state.error.originalMessage}
                    </pre>
                    {this.state.errorInfo && (
                      <>
                        <p className="font-semibold text-gray-700 mt-3 mb-2">Component Stack:</p>
                        <pre className="text-gray-600 whitespace-pre-wrap text-xs">
                          {this.state.errorInfo.componentStack}
                        </pre>
                      </>
                    )}
                  </div>
                </motion.details>
              )}
            </div>

            {/* Help Text */}
            <motion.p
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.8 }}
              className="text-center text-gray-500 text-sm mt-6"
            >
              If this problem persists, please contact our support team with the error ID above.
            </motion.p>
          </motion.div>
        </div>
      );
    }

    return this.props.children;
  }
}

ErrorBoundary.defaultProps = {
  context: 'unknown',
  logToService: true
};

export default ErrorBoundary;
