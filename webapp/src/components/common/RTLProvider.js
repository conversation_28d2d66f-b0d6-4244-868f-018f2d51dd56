import React, { useEffect } from 'react';
import { useLanguage } from '../../contexts/LanguageContext';
import '../../styles/rtl.css';

/**
 * RTL Provider Component
 * 
 * This component handles RTL (Right-to-Left) layout switching based on the current language.
 * It updates the document direction attribute and applies RTL-specific styles.
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 */
const RTLProvider = ({ children }) => {
  const { dir } = useLanguage();
  
  // Update document direction when language changes
  useEffect(() => {
    // Set direction attribute on html element
    document.documentElement.dir = dir;
    
    // Add or remove RTL class on body
    if (dir === 'rtl') {
      document.body.classList.add('rtl');
    } else {
      document.body.classList.remove('rtl');
    }
    
    // Log direction change in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`Document direction set to: ${dir}`);
    }
  }, [dir]);
  
  return (
    <div dir={dir} className={dir === 'rtl' ? 'rtl' : 'ltr'}>
      {children}
    </div>
  );
};

export default RTLProvider;
