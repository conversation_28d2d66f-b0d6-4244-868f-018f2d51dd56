import React from 'react';
import { API_ERROR_TYPES } from '../../utils/errorTypes';

/**
 * A component to display error messages
 * 
 * @param {Object} props - Component props
 * @param {Object} props.error - The error object from handleApiError
 * @param {Function} props.onRetry - Optional callback to retry the operation
 * @param {string} props.className - Optional additional CSS classes
 * @returns {React.ReactElement} - Rendered component
 */
const ErrorMessage = ({ error, onRetry, className = '' }) => {
  if (!error) return null;

  // Determine the appropriate icon and color based on error type
  let icon = '⚠️';
  let bgColor = 'bg-red-50';
  let textColor = 'text-red-700';
  let borderColor = 'border-red-200';

  switch (error.type) {
    case API_ERROR_TYPES.NETWORK_ERROR:
      icon = '📶';
      break;
    case API_ERROR_TYPES.AUTHENTICATION_ERROR:
      icon = '🔒';
      break;
    case API_ERROR_TYPES.VALIDATION_ERROR:
      icon = '📝';
      bgColor = 'bg-yellow-50';
      textColor = 'text-yellow-700';
      borderColor = 'border-yellow-200';
      break;
    case API_ERROR_TYPES.SERVER_ERROR:
      icon = '🖥️';
      break;
    case API_ERROR_TYPES.NOT_FOUND_ERROR:
      icon = '🔍';
      bgColor = 'bg-blue-50';
      textColor = 'text-blue-700';
      borderColor = 'border-blue-200';
      break;
    case API_ERROR_TYPES.RATE_LIMIT_ERROR:
      icon = '⏱️';
      bgColor = 'bg-purple-50';
      textColor = 'text-purple-700';
      borderColor = 'border-purple-200';
      break;
    default:
      icon = '❓';
  }

  return (
    <div className={`${bgColor} ${textColor} p-3 rounded-md border ${borderColor} mb-4 ${className}`}>
      <div className="flex items-start">
        <div className="mr-3 text-xl">{icon}</div>
        <div className="flex-1">
          <p className="font-medium">{error.message}</p>
          
          {/* Show validation errors if present */}
          {error.type === API_ERROR_TYPES.VALIDATION_ERROR && error.validationErrors && (
            <ul className="mt-2 text-sm list-disc list-inside">
              {Object.entries(error.validationErrors).map(([field, errors]) => (
                <li key={field}>
                  <span className="font-medium">{field}:</span>{' '}
                  {Array.isArray(errors) ? errors[0] : errors}
                </li>
              ))}
            </ul>
          )}
          
          {/* Show retry button if onRetry is provided */}
          {onRetry && (
            <button
              onClick={onRetry}
              className={`mt-2 px-3 py-1 text-sm font-medium rounded-md ${textColor} bg-white border ${borderColor} hover:bg-gray-50`}
            >
              Try Again
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default ErrorMessage;
