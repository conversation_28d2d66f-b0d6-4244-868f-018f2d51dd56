import React, { useState, useEffect, useRef } from 'react';

/**
 * LazyImage component that loads images only when they are in the viewport
 * This improves performance by reducing initial load time and saving bandwidth
 * 
 * @param {Object} props - Component props
 * @param {string} props.src - Image source URL
 * @param {string} props.alt - Alternative text for the image
 * @param {string} props.className - CSS class for the image
 * @param {string} props.placeholderSrc - Optional placeholder image to show while loading
 * @param {Object} props.style - Additional inline styles
 * @param {Function} props.onLoad - Callback function when image is loaded
 * @param {Function} props.onError - Callback function when image fails to load
 * @returns {React.ReactElement} - Rendered component
 */
const LazyImage = ({
  src,
  alt,
  className = '',
  placeholderSrc = '',
  style = {},
  onLoad = () => {},
  onError = () => {},
  ...rest
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const imgRef = useRef(null);

  // Set up intersection observer to detect when image is in viewport
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        // When image is in viewport, set isInView to true
        if (entries[0].isIntersecting) {
          setIsInView(true);
          // Disconnect observer once image is in view
          observer.disconnect();
        }
      },
      {
        // Start loading image when it's 200px before it enters the viewport
        rootMargin: '200px',
        threshold: 0.01
      }
    );

    // Start observing the image element
    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    // Clean up observer when component unmounts
    return () => {
      if (imgRef.current) {
        observer.disconnect();
      }
    };
  }, []);

  // Handle image load event
  const handleImageLoaded = () => {
    setIsLoaded(true);
    onLoad();
  };

  // Handle image error event
  const handleImageError = (error) => {
    console.error('Error loading image:', error);
    onError(error);
  };

  // Determine what to display based on loading state
  const renderImage = () => {
    // If image is not yet in view, show placeholder or nothing
    if (!isInView) {
      return placeholderSrc ? (
        <img
          src={placeholderSrc}
          alt={alt}
          className={`${className} transition-opacity duration-300`}
          style={{ ...style, opacity: 0.5 }}
          ref={imgRef}
          {...rest}
        />
      ) : (
        <div
          className={`${className} bg-gray-200 animate-pulse`}
          style={style}
          ref={imgRef}
          aria-hidden="true"
          {...rest}
        />
      );
    }

    // If image is in view but not loaded, show loading state
    return (
      <>
        {!isLoaded && placeholderSrc && (
          <img
            src={placeholderSrc}
            alt={alt}
            className={`${className} absolute inset-0 transition-opacity duration-300`}
            style={{ ...style, opacity: 0.5 }}
            aria-hidden="true"
          />
        )}
        <img
          src={src}
          alt={alt}
          className={`${className} transition-opacity duration-300`}
          style={{ ...style, opacity: isLoaded ? 1 : 0 }}
          onLoad={handleImageLoaded}
          onError={handleImageError}
          {...rest}
        />
      </>
    );
  };

  return (
    <div className="relative">
      {renderImage()}
    </div>
  );
};

export default LazyImage;
