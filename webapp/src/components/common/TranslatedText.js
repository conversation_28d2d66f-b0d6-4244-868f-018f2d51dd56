import React from 'react';
import useTranslation from '../../hooks/useTranslation';

/**
 * A component for rendering translated text with optional interpolation
 * 
 * @param {Object} props - Component props
 * @param {string} props.i18nKey - Translation key
 * @param {string} [props.ns] - Optional namespace
 * @param {Object} [props.values] - Values for interpolation
 * @param {React.ReactNode} [props.children] - Fallback content if translation is missing
 * @param {string} [props.className] - Optional CSS class
 * @returns {React.ReactElement} - Rendered component
 */
const TranslatedText = ({ i18nKey, ns, values, children, className, ...rest }) => {
  const { t } = useTranslation(ns);
  
  // If the key doesn't exist and children are provided, render children as fallback
  const translatedText = t(i18nKey, values);
  const shouldUseChildren = translatedText === i18nKey && children;
  
  return (
    <span className={className} {...rest}>
      {shouldUseChildren ? children : translatedText}
    </span>
  );
};

export default TranslatedText;
