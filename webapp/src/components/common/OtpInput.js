import React, { useState, useEffect, useRef, useCallback } from 'react';
import useTranslation from '../../hooks/useTranslation';

/**
 * OTP Input Component
 * A specialized input for one-time passwords with enhanced UX features:
 * - Auto-focus on mount
 * - Paste support for full OTP codes
 * - Visual feedback for each digit
 * - Keyboard navigation
 * - Resend functionality with cooldown
 * - Accessibility improvements
 *
 * @param {Object} props - Component props
 * @param {string} props.value - OTP value
 * @param {function} props.onChange - Change handler function
 * @param {number} props.length - Length of OTP (default: 6)
 * @param {string} props.error - Error message to display
 * @param {function} props.onResendClick - Function to call when resend is clicked
 * @param {number} props.resendCooldown - Cooldown time for resend in seconds
 * @param {string} props.label - Input label
 * @param {boolean} props.required - Whether the field is required
 * @param {boolean} props.autoFocus - Whether to auto-focus the input on mount
 * @returns {React.ReactElement} OtpInput component
 */
const OtpInput = ({
  value,
  onChange,
  length = 6,
  error,
  onResendClick,
  resendCooldown = 0,
  label = 'Verification Code',
  required = true,
  autoFocus = true
}) => {
  const [focused, setFocused] = useState(false);
  const [animateDigit, setAnimateDigit] = useState(-1);
  const inputRef = useRef(null);
  const { t } = useTranslation('auth');

  // Auto-focus on mount if autoFocus is true
  useEffect(() => {
    if (autoFocus && inputRef.current) {
      inputRef.current.focus();
    }
  }, [autoFocus]);

  // Animate the last entered digit
  useEffect(() => {
    if (value.length > 0) {
      setAnimateDigit(value.length - 1);
      const timer = setTimeout(() => {
        setAnimateDigit(-1);
      }, 300);
      return () => clearTimeout(timer);
    }
  }, [value]);

  // Handle input change with enhanced validation
  const handleChange = useCallback((e) => {
    const inputValue = e.target.value;

    // Handle paste events (detect if pasting a full code)
    if (inputValue.length > value.length + 1) {
      // User likely pasted a code - extract digits only
      const sanitizedValue = inputValue.replace(/\D/g, '').slice(0, length);
      onChange(sanitizedValue);
      return;
    }

    // Normal typing - only allow digits and limit to specified length
    const sanitizedValue = inputValue.replace(/\D/g, '').slice(0, length);
    onChange(sanitizedValue);
  }, [value, onChange, length]);

  // Handle paste event specifically
  const handlePaste = useCallback((e) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text');
    const sanitizedValue = pastedData.replace(/\D/g, '').slice(0, length);
    onChange(sanitizedValue);
  }, [onChange, length]);

  // Handle input focus
  const handleFocus = useCallback(() => {
    setFocused(true);
  }, []);

  // Handle input blur
  const handleBlur = useCallback(() => {
    setFocused(false);
  }, []);

  // Handle container click to focus input
  const handleContainerClick = useCallback(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, []);

  // Handle keydown for better navigation
  const handleKeyDown = useCallback((e) => {
    // Handle backspace to clear the last digit
    if (e.key === 'Backspace' && value.length > 0) {
      onChange(value.slice(0, -1));
    }

    // Handle arrow keys to move cursor
    if (e.key === 'ArrowLeft') {
      // Move cursor left (for screen readers)
      if (inputRef.current) {
        const pos = Math.max(0, inputRef.current.selectionStart - 1);
        inputRef.current.setSelectionRange(pos, pos);
      }
    }

    if (e.key === 'ArrowRight') {
      // Move cursor right (for screen readers)
      if (inputRef.current) {
        const pos = Math.min(value.length, inputRef.current.selectionStart + 1);
        inputRef.current.setSelectionRange(pos, pos);
      }
    }
  }, [value, onChange]);

  // Create placeholder based on length
  const placeholder = Array(length).fill('•').join(' ');

  // Format remaining time for cooldown
  const formatRemainingTime = useCallback((seconds) => {
    if (seconds < 60) {
      return `${seconds}s`;
    }
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
  }, []);

  return (
    <div className="space-y-2 group">
      {label && (
        <label className="block text-sm font-medium text-white group-hover:text-blue-600 transition-colors duration-200">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      <div className="relative" onClick={handleContainerClick}>
        <div className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 pointer-events-none">
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
        </div>
        <input
          ref={inputRef}
          type="text"
          inputMode="numeric"
          pattern="[0-9]*"
          value={value}
          onChange={handleChange}
          onPaste={handlePaste}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onKeyDown={handleKeyDown}
          className={`w-full pl-10 pr-4 py-3 border ${
            error
              ? 'border-red-300 focus:ring-red-200'
              : focused
                ? 'border-blue-500 focus:ring-blue-100'
                : value
                  ? 'border-blue-300 focus:ring-blue-100'
                  : 'border-gray-300 focus:ring-blue-100'
          } rounded-lg bg-white focus:outline-none focus:ring-4 transition-all duration-200 shadow-sm text-center tracking-widest`}
          placeholder={placeholder}
          maxLength={length}
          autoComplete="one-time-code"
          aria-label={label || "Verification code input"}
          aria-invalid={!!error}
          aria-describedby={error ? "otp-error" : undefined}
        />

        {/* Enhanced visual representation of OTP digits */}
        <div className="absolute top-0 left-0 right-0 bottom-0 flex justify-center items-center pointer-events-none">
          <div className="flex space-x-4 pl-10">
            {Array.from({ length }).map((_, index) => (
              <div
                key={index}
                className={`w-5 h-5 flex items-center justify-center transition-all duration-200 ${
                  index === animateDigit
                    ? 'scale-125 text-blue-600'
                    : index < value.length
                      ? 'text-gray-700'
                      : 'text-transparent'
                }`}
              >
                {index < value.length ? (
                  <span className={index === animateDigit ? 'animate-bounce' : ''}>
                    {value[index]}
                  </span>
                ) : (
                  <span className="opacity-40">•</span>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Error message with ID for accessibility */}
      {error && (
        <p id="otp-error" className="text-red-500 text-xs mt-1 flex items-center gap-1.5">
          <svg className="w-3.5 h-3.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>{error}</span>
        </p>
      )}

      {/* Resend button or cooldown timer with enhanced styling */}
      <div className="flex justify-between items-center mt-2">
        {resendCooldown > 0 ? (
          <p className="text-xs text-gray-400 mt-1 ml-1 flex items-center">
            <svg className="w-3.5 h-3.5 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            {t('otp.resendIn', 'Resend code in')} {formatRemainingTime(resendCooldown)}
          </p>
        ) : (
          <button
            type="button"
            onClick={onResendClick}
            className="text-xs text-blue-600 hover:text-blue-800 mt-1 ml-1 bg-transparent border-none p-0 cursor-pointer flex items-center"
          >
            <svg className="w-3.5 h-3.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            {t('otp.resend', 'Resend verification code')}
          </button>
        )}

        {/* Show completion status if all digits are entered */}
        {value.length === length && (
          <span className="text-xs text-green-600 font-medium flex items-center">
            <svg className="w-3.5 h-3.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
            </svg>
            {t('otp.complete', 'Complete')}
          </span>
        )}
      </div>
    </div>
  );
};

export default OtpInput;
