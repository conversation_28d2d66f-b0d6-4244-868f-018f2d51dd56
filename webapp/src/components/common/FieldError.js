import React from 'react';

/**
 * A component to display field-level error messages
 * 
 * @param {Object} props - Component props
 * @param {string} props.error - The error message
 * @param {string} props.className - Optional additional CSS classes
 * @returns {React.ReactElement} - Rendered component
 */
const FieldError = ({ error, className = '' }) => {
  if (!error) return null;

  return (
    <p className={`text-red-600 text-xs mt-1 ${className}`}>
      {error}
    </p>
  );
};

export default FieldError;
