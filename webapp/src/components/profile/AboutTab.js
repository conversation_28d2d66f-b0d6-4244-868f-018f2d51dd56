import React, { useState } from 'react';

/**
 * AboutTab component displays the talent's biography and strengths
 */
const AboutTab = ({ talent }) => {
  const [bioExpanded, setBioExpanded] = useState(false);
  
  // Default values in case talent data is incomplete
  const {
    bio = '',
    strengths = []
  } = talent || {};
  
  // Determine if bio is long enough to need truncation
  const isBioLong = bio.length > 150;
  
  // Truncate bio if it's long and not expanded
  const displayBio = isBioLong && !bioExpanded 
    ? `${bio.substring(0, 150)}...` 
    : bio;
  
  // Toggle bio expansion
  const toggleBioExpansion = () => {
    setBioExpanded(!bioExpanded);
  };

  return (
    <div className="animate-fade-in">
      {/* Biography Section */}
      <div className="mb-6">
        <h2 className="text-lg font-bold mb-2">Biography</h2>
        <p className="text-gray-700 text-sm leading-relaxed">
          {displayBio}
        </p>
        
        {/* View More/Less Button */}
        {isBioLong && (
          <button 
            onClick={toggleBioExpansion}
            className="text-blue-600 text-sm mt-1 flex items-center hover:underline"
          >
            {bioExpanded ? 'View Less' : 'View More'}
            <svg 
              className={`w-4 h-4 ml-1 transition-transform ${bioExpanded ? 'rotate-180' : ''}`} 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
            </svg>
          </button>
        )}
      </div>
      
      {/* Strengths Section */}
      <div className="mb-6">
        <h2 className="text-lg font-bold mb-2">Strengths</h2>
        {strengths.length > 0 ? (
          <div className="flex flex-wrap gap-2">
            {strengths.map((strength, index) => (
              <span
                key={index}
                className="bg-green-100 text-green-800 text-xs px-3 py-1 rounded-full"
              >
                {strength}
              </span>
            ))}
          </div>
        ) : (
          <p className="text-gray-500 text-sm">No strengths listed</p>
        )}
      </div>
    </div>
  );
};

export default AboutTab;
