import React from 'react';
import { getCdnUrl } from '../../utils/cdnUtils';

/**
 * PostTab component displays the talent's social media posts
 */
const PostTab = ({ talent }) => {
  // Default values in case talent data is incomplete
  const {
    posts = []
  } = talent || {};

  // Render a single post
  const renderPost = (post) => {
    return (
      <div className="bg-white rounded-lg shadow-sm p-4 mb-4">
        {/* Post content */}
        <div className="mb-4">
          <p className="text-gray-800">{post.content}</p>
        </div>

        {/* Post image */}
        {post.image && (
          <div className="rounded-lg overflow-hidden -mx-4 mb-3">
            <img
              key={`post-${post.id}-image`}
              src={getCdnUrl(post.image)}
              alt="Post"
              className="w-full h-auto"
              onError={(e) => {
                // Prevent infinite loop by checking if we're already using the fallback
                if (!e.target.src.includes('via.placeholder.com')) {
                  console.log(`Post image for post ${post.id} failed to load, using fallback`);
                  e.target.src = 'https://via.placeholder.com/600x400?text=Post+Image';
                }
                // Remove the error handler to prevent further attempts
                e.target.onerror = null;
              }}
            />
          </div>
        )}

        {/* Post actions */}
        <div className="flex items-center justify-between text-sm text-gray-500">
          <div className="flex items-center space-x-4">
            <button className="flex items-center space-x-1 hover:text-indigo-600">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
              <span>{post.likes_count || 0}</span>
            </button>
            <button className="flex items-center space-x-1 hover:text-indigo-600">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
              <span>{post.comments_count || 0}</span>
            </button>
          </div>
          <span className="text-gray-400">{new Date(post.created_at).toLocaleDateString()}</span>
        </div>
      </div>
    );
  };

  return (
    <div className="animate-fade-in">
      {/* Header */}
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-bold">Posts ({posts.length})</h2>
        {posts.length > 0 && (
          <button className="text-blue-600 text-sm font-medium hover:underline">
            See All
          </button>
        )}
      </div>

      {/* Posts list */}
      {posts.length > 0 ? (
        <div className="space-y-4">
          {posts.map((post) => (
            <div key={post.id}>{renderPost(post)}</div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12 bg-gray-50 rounded-lg">
          <svg
            className="w-12 h-12 text-gray-300 mx-auto mb-3"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
          <p className="text-gray-500 mb-2">No posts yet</p>
          <p className="text-gray-400 text-sm">Check back later for updates from this talent</p>
        </div>
      )}
    </div>
  );
};

export default PostTab;
