import React from 'react';

/**
 * StatisticsBar component displays key metrics about a talent
 * including followers, missions completed, reviews, and zodiac sign
 */
const StatisticsBar = ({ talent }) => {
  // Default values in case talent data is incomplete
  const {
    followers = 0,
    missionsCompleted = 0,
    rating = 0,
    reviewCount = 0,
    zodiac = '',
    age = null
  } = talent || {};

  // Statistics configuration
  const statistics = [
    {
      id: 'followers',
      value: followers,
      label: 'Followers',
      bgColor: 'bg-blue-50',
      textColor: 'text-blue-500',
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" />
        </svg>
      )
    },
    {
      id: 'missions',
      value: missionsCompleted,
      label: 'Mission Done',
      bgColor: 'bg-orange-50',
      textColor: 'text-orange-500',
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clipRule="evenodd" />
          <path d="M2 13.692V16a2 2 0 002 2h12a2 2 0 002-2v-2.308A24.974 24.974 0 0110 15c-2.796 0-5.487-.46-8-1.308z" />
        </svg>
      )
    },
    {
      id: 'reviews',
      value: rating.toFixed(1),
      label: `Reviews (${reviewCount})`,
      bgColor: 'bg-yellow-50',
      textColor: 'text-yellow-500',
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
        </svg>
      )
    },
    {
      id: 'zodiac',
      value: zodiac || (age ? `${age} Yrs` : ''),
      label: zodiac ? 'Zodiac' : 'Age',
      bgColor: 'bg-purple-50',
      textColor: 'text-purple-500',
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z" />
        </svg>
      )
    }
  ];

  return (
    <div className="grid grid-cols-4 gap-2 p-4 bg-white">
      {statistics.map((stat) => (
        <div key={stat.id} className={`${stat.bgColor} rounded-lg p-3 text-center`}>
          <div className={`flex justify-center mb-1 ${stat.textColor}`}>
            {stat.icon}
          </div>
          <div className="text-lg font-bold">{stat.value}</div>
          <div className="text-xs text-gray-600">{stat.label}</div>
        </div>
      ))}
    </div>
  );
};

export default StatisticsBar;
