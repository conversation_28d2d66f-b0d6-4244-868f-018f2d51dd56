/**
 * Experience History Component
 *
 * This component displays the user's experience history and level progress.
 */

import React, { useState, useEffect } from 'react';
import { useProfile } from '../../contexts/ProfileContext';
import useTranslation from '../../hooks/useTranslation';
import profileService from '../../services/profileService';

/**
 * ExperienceHistory component
 *
 * @param {Object} props
 * @param {boolean} props.isCurrentUser - Whether the profile belongs to the current user
 */
const ExperienceHistory = ({ isCurrentUser = true }) => {
  // State for pagination
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // Get profile context
  const {
    profile,
    experienceHistory,
    levelInfo,
    loading,
    error,
    fetchExperienceHistory,
    fetchLevelInfo
  } = useProfile();

  // Use the translation hook
  const { t } = useTranslation('profile');

  // Fetch experience history on component mount or page change
  useEffect(() => {
    const loadExperienceHistory = async () => {
      try {
        await fetchExperienceHistory(page, 5);

        // Set total pages from the response
        if (experienceHistory && experienceHistory.total_pages) {
          setTotalPages(experienceHistory.total_pages);
        }
      } catch (err) {
        console.error('Error loading experience history:', err);
      }
    };

    loadExperienceHistory();
    fetchLevelInfo();
  }, [page, fetchExperienceHistory, fetchLevelInfo]);

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Get icon for action type
  const getActionIcon = (actionType) => {
    switch (actionType) {
      case 'mission_completed':
        return (
          <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
            </svg>
          </div>
        );
      case 'mission_hosted':
        return (
          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          </div>
        );
      case 'achievement_earned':
        return (
          <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
            </svg>
          </div>
        );
      case 'service_provided':
        return (
          <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          </div>
        );
      default:
        return (
          <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
          </div>
        );
    }
  };

  // If loading, show skeleton
  if (loading && (!experienceHistory || !experienceHistory.items || experienceHistory.items.length === 0)) {
    return (
      <div className="w-full bg-white rounded-xl overflow-hidden shadow-sm border border-gray-100 p-4 sm:p-6 animate-pulse">
        <div className="h-6 bg-gray-300 rounded w-1/4 mb-4"></div>
        <div className="h-4 bg-gray-200 rounded w-full mb-6"></div>

        {[...Array(3)].map((_, index) => (
          <div key={index} className="flex items-start mb-4">
            <div className="w-8 h-8 bg-gray-200 rounded-full mr-3"></div>
            <div className="flex-1">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-gray-100 rounded w-1/2"></div>
            </div>
            <div className="h-6 w-12 bg-gray-200 rounded"></div>
          </div>
        ))}
      </div>
    );
  }

  // If error, show error message
  if (error) {
    return (
      <div className="w-full bg-red-50 border border-red-200 rounded-xl p-4 text-red-700">
        <p className="font-medium">{t('profile.experience.error.title', 'Error loading experience history')}</p>
        <p className="text-sm mt-1">{error}</p>
      </div>
    );
  }

  // Get the items from the experience history
  const historyItems = experienceHistory && experienceHistory.items ? experienceHistory.items : [];

  return (
    <div className="w-full bg-white rounded-xl overflow-hidden shadow-sm border border-gray-100 p-4 sm:p-6">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold text-gray-900">
          {t('profile.experience.title', 'Experience & Level')}
        </h2>

        <div className="flex items-center">
          <span className="bg-blue-100 text-blue-700 text-xs font-bold px-2 py-1 rounded">
            LV{profile?.level || 1}
          </span>
        </div>
      </div>

      {/* Level Progress */}
      {levelInfo && (
        <div className="mb-6">
          <div className="flex justify-between items-center mb-1 text-sm">
            <span className="text-gray-600">
              {t('profile.experience.xp', 'XP')}: {levelInfo.experience_points} / {levelInfo.level_requirements.next}
            </span>
            <span className="text-blue-600 font-medium">
              {levelInfo.progress_percentage}%
            </span>
          </div>

          <div className="w-full bg-gray-200 rounded-full h-2.5">
            <div
              className="bg-blue-600 h-2.5 rounded-full"
              style={{ width: `${levelInfo.progress_percentage}%` }}
            ></div>
          </div>

          <div className="flex justify-between items-center mt-1 text-xs text-gray-500">
            <span>Level {profile?.level || 1}</span>
            <span>Level {(profile?.level || 1) + 1}</span>
          </div>
        </div>
      )}

      {/* Experience History */}
      <div>
        <h3 className="text-md font-medium text-gray-800 mb-3">
          {t('profile.experience.history', 'Experience History')}
        </h3>

        {historyItems.length === 0 ? (
          <div className="bg-gray-50 border border-gray-200 rounded-xl p-4 text-gray-700 text-center">
            <p className="font-medium">{t('profile.experience.empty.title', 'No experience history yet')}</p>
            <p className="text-sm mt-1">
              {t('profile.experience.empty.message', 'Experience history will appear here as you complete activities.')}
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {historyItems.map((item) => (
              <div key={item.id} className="flex items-start">
                {getActionIcon(item.action_type)}

                <div className="ml-3 flex-1">
                  <p className="text-sm font-medium text-gray-900">
                    {item.description}
                  </p>
                  <p className="text-xs text-gray-500">
                    {formatDate(item.created_at)}
                    {item.level_before !== item.level_after && (
                      <span className="ml-2 text-blue-600 font-medium">
                        Level Up! {item.level_before} → {item.level_after}
                      </span>
                    )}
                  </p>
                </div>

                <div className="text-sm font-medium text-green-600">
                  +{item.experience_points} XP
                </div>
              </div>
            ))}

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex justify-center mt-6">
                <nav className="flex items-center space-x-2">
                  <button
                    onClick={() => setPage(p => Math.max(p - 1, 1))}
                    disabled={page === 1}
                    className={`px-3 py-1 rounded-md ${
                      page === 1
                        ? 'text-gray-400 cursor-not-allowed'
                        : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    {t('profile.experience.previous', 'Previous')}
                  </button>

                  {[...Array(totalPages)].map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setPage(index + 1)}
                      className={`w-8 h-8 rounded-full ${
                        page === index + 1
                          ? 'bg-blue-600 text-white'
                          : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      {index + 1}
                    </button>
                  ))}

                  <button
                    onClick={() => setPage(p => Math.min(p + 1, totalPages))}
                    disabled={page === totalPages}
                    className={`px-3 py-1 rounded-md ${
                      page === totalPages
                        ? 'text-gray-400 cursor-not-allowed'
                        : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    {t('profile.experience.next', 'Next')}
                  </button>
                </nav>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default ExperienceHistory;
