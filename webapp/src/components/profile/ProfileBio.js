/**
 * Profile Bio Component
 *
 * This component displays the user's biography, personalities, languages,
 * and games they play.
 */

import React from 'react';
import { useProfile } from '../../contexts/ProfileContext';
import useTranslation from '../../hooks/useTranslation';

/**
 * ProfileBio component
 *
 * @param {Object} props
 * @param {Function} props.onEdit - Callback when Edit button is clicked
 * @param {boolean} props.isCurrentUser - Whether the profile belongs to the current user
 */
const ProfileBio = ({ onEdit, isCurrentUser = true }) => {
  // Get profile context
  const { profile, biography, loading, error } = useProfile();

  // Use the translation hook
  const { t } = useTranslation('profile');

  // If loading, show skeleton
  if (loading) {
    return (
      <div className="w-full bg-gray-100 rounded-xl overflow-hidden p-4 animate-pulse">
        <div className="h-6 bg-gray-300 rounded w-1/4 mb-4"></div>
        <div className="h-20 bg-gray-200 rounded mb-4"></div>
        <div className="h-6 bg-gray-300 rounded w-1/4 mb-4"></div>
        <div className="flex flex-wrap gap-2 mb-4">
          {[...Array(3)].map((_, index) => (
            <div key={index} className="h-8 bg-gray-200 rounded w-20"></div>
          ))}
        </div>
        <div className="h-6 bg-gray-300 rounded w-1/4 mb-4"></div>
        <div className="flex flex-wrap gap-2 mb-4">
          {[...Array(2)].map((_, index) => (
            <div key={index} className="h-8 bg-gray-200 rounded w-20"></div>
          ))}
        </div>
        <div className="h-6 bg-gray-300 rounded w-1/4 mb-4"></div>
        <div className="flex flex-wrap gap-2">
          {[...Array(3)].map((_, index) => (
            <div key={index} className="h-12 bg-gray-200 rounded w-full sm:w-48"></div>
          ))}
        </div>
      </div>
    );
  }

  // If error, show error message
  if (error) {
    return (
      <div className="w-full bg-red-50 border border-red-200 rounded-xl p-4 text-red-700">
        <p className="font-medium">{t('profile.error.title', 'Error loading biography')}</p>
        <p className="text-sm mt-1">{error}</p>
      </div>
    );
  }

  // If no biography, show empty state
  if (!biography) {
    return (
      <div className="w-full bg-white rounded-xl overflow-hidden shadow-sm border border-gray-100 p-4 sm:p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold text-gray-900">
            {t('profile.bio.title', 'About')}
          </h2>

          {isCurrentUser && (
            <button
              onClick={onEdit}
              className="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center"
            >
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
              </svg>
              {t('profile.actions.add', 'Add')}
            </button>
          )}
        </div>

        <div className="bg-gray-50 border border-gray-200 rounded-xl p-4 text-gray-700 text-center">
          <p className="font-medium">{t('profile.bio.empty.title', 'No biography available')}</p>
          <p className="text-sm mt-1">
            {isCurrentUser
              ? t('profile.bio.empty.currentUser', 'Add information about yourself to help others get to know you better.')
              : t('profile.bio.empty.otherUser', 'This user has not added any information yet.')}
          </p>

          {isCurrentUser && (
            <button
              onClick={onEdit}
              className="mt-3 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200"
            >
              {t('profile.actions.addBio', 'Add Biography')}
            </button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="w-full bg-white rounded-xl overflow-hidden shadow-sm border border-gray-100 p-4 sm:p-6">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold text-gray-900">
          {t('profile.bio.title', 'About')}
        </h2>

        {isCurrentUser && (
          <button
            onClick={onEdit}
            className="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center"
          >
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
            </svg>
            {t('profile.actions.edit', 'Edit')}
          </button>
        )}
      </div>

      {/* Bio Text */}
      <div className="mb-6">
        <p className="text-gray-700 whitespace-pre-line">
          {biography.bio || t('profile.bio.noBio', 'No biography provided.')}
        </p>

        {/* Race Information */}
        {profile?.race_name && (
          <div className="mt-3 flex items-center">
            <span className="text-sm font-medium text-gray-700 mr-2">
              {t('profile.bio.race', 'Race:')}
            </span>
            <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-indigo-100 text-indigo-800">
              {profile.race_name}
            </span>
          </div>
        )}
      </div>

      {/* Personalities */}
      {biography.personalities && biography.personalities.length > 0 && (
        <div className="mb-6">
          <h3 className="text-sm font-medium text-gray-900 mb-2">
            {t('profile.bio.personalities', 'Personalities')}
          </h3>

          <div className="flex flex-wrap gap-2">
            {biography.personalities.map((personality, index) => (
              <span
                key={index}
                className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800"
              >
                {personality}
              </span>
            ))}
          </div>
        </div>
      )}

      {/* Languages */}
      {biography.languages && biography.languages.length > 0 && (
        <div className="mb-6">
          <h3 className="text-sm font-medium text-gray-900 mb-2">
            {t('profile.bio.languages', 'Languages')}
          </h3>

          <div className="flex flex-wrap gap-2">
            {biography.languages.map((language, index) => (
              <span
                key={index}
                className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800"
              >
                {language}
              </span>
            ))}
          </div>
        </div>
      )}

      {/* Games */}
      {biography.games && biography.games.length > 0 && (
        <div>
          <h3 className="text-sm font-medium text-gray-900 mb-2">
            {t('profile.bio.games', 'Games')}
          </h3>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
            {biography.games.map((game) => (
              <div
                key={game.id}
                className="flex items-center p-3 bg-gray-50 border border-gray-200 rounded-lg"
              >
                <div className="flex-shrink-0 w-10 h-10 bg-gray-200 rounded-md overflow-hidden">
                  {game.image_url ? (
                    <img
                      src={game.image_url}
                      alt={game.name}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center bg-gray-300 text-gray-600 text-xs font-bold">
                      {game.name?.substring(0, 2).toUpperCase() || 'GA'}
                    </div>
                  )}
                </div>

                <div className="ml-3">
                  <h4 className="text-sm font-medium text-gray-900">
                    {game.name}
                  </h4>
                  <div className="flex items-center mt-1">
                    <span className="text-xs text-gray-600 mr-2">
                      {t('profile.bio.skillLevel', 'Skill: {{level}}', { level: game.skill_level })}
                    </span>
                    <span className="text-xs text-gray-600">
                      {t('profile.bio.experience', '{{years}}y exp', { years: game.years_experience })}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ProfileBio;
