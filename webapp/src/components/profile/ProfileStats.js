/**
 * Profile Statistics Component
 *
 * This component displays the user's profile statistics including
 * missions, earnings, ratings, and achievements.
 */

import React from 'react';
import { useProfile } from '../../contexts/ProfileContext';
import useTranslation from '../../hooks/useTranslation';

/**
 * ProfileStats component
 */
const ProfileStats = () => {
  // Get profile context
  const { statistics, loading, error } = useProfile();

  // Use the translation hook
  const { t } = useTranslation('profile');

  // If loading, show skeleton
  if (loading) {
    return (
      <div className="w-full bg-gray-100 rounded-xl overflow-hidden p-4 animate-pulse">
        <div className="h-6 bg-gray-300 rounded w-1/4 mb-4"></div>
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
          {[...Array(4)].map((_, index) => (
            <div key={index} className="h-20 bg-gray-200 rounded-lg"></div>
          ))}
        </div>
        <div className="h-6 bg-gray-300 rounded w-1/4 mt-6 mb-4"></div>
        <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
          {[...Array(3)].map((_, index) => (
            <div key={index} className="h-16 bg-gray-200 rounded-lg"></div>
          ))}
        </div>
      </div>
    );
  }

  // If error, show error message
  if (error) {
    return (
      <div className="w-full bg-red-50 border border-red-200 rounded-xl p-4 text-red-700">
        <p className="font-medium">{t('profile.error.title', 'Error loading statistics')}</p>
        <p className="text-sm mt-1">{error}</p>
      </div>
    );
  }

  // If no statistics, show empty state
  if (!statistics) {
    return (
      <div className="w-full bg-gray-50 border border-gray-200 rounded-xl p-4 text-gray-700 text-center">
        <p className="font-medium">{t('profile.stats.empty.title', 'No statistics available')}</p>
        <p className="text-sm mt-1">{t('profile.stats.empty.message', 'Statistics will appear here once you start using the platform.')}</p>
      </div>
    );
  }

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-MY', {
      style: 'currency',
      currency: 'MYR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  return (
    <div className="w-full bg-white rounded-xl overflow-hidden shadow-sm border border-gray-100 p-4 sm:p-6">
      {/* Experience Points */}
      <div className="mb-6">
        <div className="bg-gradient-to-r from-yellow-50 to-amber-50 rounded-xl p-4">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-lg font-semibold text-amber-800">
              {t('profile.stats.experiencePoints', 'Experience Points')}
            </h3>
            <div className="bg-gradient-to-r from-indigo-600 to-blue-500 text-white text-xs font-bold px-2.5 py-1 rounded-full flex items-center">
              <span className="mr-0.5">LV</span>
              <span className="text-sm">{statistics.level || 1}</span>
            </div>
          </div>

          <div className="flex items-baseline mb-2">
            <span className="text-2xl font-bold text-amber-600">{statistics.experience_points || 0}</span>
            <span className="ml-1 text-amber-600 text-sm">XP</span>
          </div>

          <div className="mt-2">
            <div className="flex justify-between text-xs text-gray-600 mb-1">
              <span>Level {statistics.level || 1}</span>
              <span>Level {(statistics.level || 1) + 1}</span>
            </div>
            <div className="bg-gray-200 rounded-full h-2.5 overflow-hidden">
              <div
                className="h-full bg-gradient-to-r from-yellow-500 to-amber-500 transition-all duration-500"
                style={{ width: `${((statistics.experience_points || 0) % 1000) / 10}%` }}
              />
            </div>
            <p className="text-xs text-gray-600 mt-1 text-right">
              {((statistics.experience_points || 0) % 1000)} / 1000 XP needed
            </p>
          </div>
        </div>
      </div>

      {/* Stats Section */}
      <div>
        <h2 className="text-lg font-semibold text-gray-900 mb-4">
          {t('profile.stats.title', 'Statistics')}
        </h2>

        <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
          {/* Missions Completed */}
          <div className="bg-blue-50 rounded-xl p-4 flex flex-col">
            <span className="text-blue-600 text-sm font-medium">
              {t('profile.stats.missionsCompleted', 'Missions Completed')}
            </span>
            <span className="text-2xl font-bold text-gray-900 mt-1">
              {statistics.total_missions_completed || 0}
            </span>
          </div>

          {/* Missions Hosted */}
          <div className="bg-indigo-50 rounded-xl p-4 flex flex-col">
            <span className="text-indigo-600 text-sm font-medium">
              {t('profile.stats.missionsHosted', 'Missions Hosted')}
            </span>
            <span className="text-2xl font-bold text-gray-900 mt-1">
              {statistics.total_missions_hosted || 0}
            </span>
          </div>

          {/* Hours Played */}
          <div className="bg-purple-50 rounded-xl p-4 flex flex-col">
            <span className="text-purple-600 text-sm font-medium">
              {t('profile.stats.hoursPlayed', 'Hours Played')}
            </span>
            <span className="text-2xl font-bold text-gray-900 mt-1">
              {statistics.total_hours_played || 0}
            </span>
          </div>

          {/* Total Earnings */}
          <div className="bg-green-50 rounded-xl p-4 flex flex-col">
            <span className="text-green-600 text-sm font-medium">
              {t('profile.stats.totalEarnings', 'Total Earnings')}
            </span>
            <span className="text-2xl font-bold text-gray-900 mt-1">
              {formatCurrency(statistics.total_earnings || 0)}
            </span>
          </div>
        </div>
      </div>

      {/* Social Stats */}
      <div className="mt-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">
          {t('profile.stats.socialTitle', 'Social')}
        </h2>

        <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
          {/* Rating */}
          <div className="bg-yellow-50 rounded-xl p-4 flex flex-col">
            <span className="text-yellow-600 text-sm font-medium">
              {t('profile.stats.rating', 'Rating')}
            </span>
            <div className="flex items-center mt-1">
              <span className="text-2xl font-bold text-gray-900">
                {statistics.average_rating?.toFixed(1) || '0.0'}
              </span>
              <div className="flex ml-2">
                {[...Array(5)].map((_, index) => (
                  <svg
                    key={index}
                    className={`w-4 h-4 ${index < Math.round(statistics.average_rating || 0) ? 'text-yellow-500' : 'text-gray-300'}`}
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118l-2.8-2.034c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ))}
              </div>
              <span className="text-xs text-gray-500 ml-1">
                ({statistics.total_reviews || 0})
              </span>
            </div>
          </div>

          {/* Followers */}
          <div className="bg-pink-50 rounded-xl p-4 flex flex-col">
            <span className="text-pink-600 text-sm font-medium">
              {t('profile.stats.followers', 'Followers')}
            </span>
            <span className="text-2xl font-bold text-gray-900 mt-1">
              {statistics.followers_count || 0}
            </span>
          </div>

          {/* Following */}
          <div className="bg-blue-50 rounded-xl p-4 flex flex-col">
            <span className="text-blue-600 text-sm font-medium">
              {t('profile.stats.following', 'Following')}
            </span>
            <span className="text-2xl font-bold text-gray-900 mt-1">
              {statistics.following_count || 0}
            </span>
          </div>
        </div>
      </div>

      {/* Achievements */}
      {statistics.achievements && statistics.achievements.length > 0 && (
        <div className="mt-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            {t('profile.stats.achievementsTitle', 'Achievements')}
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
            {statistics.achievements.map((achievement) => (
              <div
                key={achievement.id}
                className="bg-gradient-to-br from-amber-50 to-yellow-100 rounded-xl p-4 flex items-center"
              >
                <div className="relative">
                  {/* Glow effect */}
                  <div className="absolute inset-0 rounded-full bg-yellow-400 blur-md opacity-40"></div>

                  {/* Icon container */}
                  <div className="w-10 h-10 bg-gradient-to-br from-yellow-400 to-amber-500 rounded-full flex items-center justify-center relative shadow-md">
                    <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                    </svg>
                  </div>
                </div>

                <div className="ml-3">
                  <h3 className="font-semibold text-amber-800 text-sm">
                    {achievement.name}
                  </h3>
                  {achievement.description && (
                    <p className="text-xs text-amber-700 mt-0.5">
                      {achievement.description}
                    </p>
                  )}
                  {achievement.earned_at && (
                    <p className="text-xs text-amber-600 mt-1">
                      {new Date(achievement.earned_at).toLocaleDateString()}
                    </p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ProfileStats;
