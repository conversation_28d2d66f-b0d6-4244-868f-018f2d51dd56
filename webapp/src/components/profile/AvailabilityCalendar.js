/**
 * Availability Calendar Component
 * 
 * This component displays and manages the user's availability calendar.
 */

import React, { useState, useEffect } from 'react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { useProfile } from '../../contexts/ProfileContext';
import useTranslation from '../../hooks/useTranslation';

/**
 * AvailabilityCalendar component
 */
const AvailabilityCalendar = () => {
  // Get profile context
  const { availability, loading, error } = useProfile();
  
  // Use the translation hook
  const { t } = useTranslation('profile');
  
  // State for selected date
  const [selectedDate, setSelectedDate] = useState(new Date());
  
  // State for weekly schedule
  const [weeklySchedule, setWeeklySchedule] = useState([
    { day: 'monday', start_time: '09:00', end_time: '17:00', is_available: true },
    { day: 'tuesday', start_time: '09:00', end_time: '17:00', is_available: true },
    { day: 'wednesday', start_time: '09:00', end_time: '17:00', is_available: true },
    { day: 'thursday', start_time: '09:00', end_time: '17:00', is_available: true },
    { day: 'friday', start_time: '09:00', end_time: '17:00', is_available: true },
    { day: 'saturday', start_time: '10:00', end_time: '15:00', is_available: false },
    { day: 'sunday', start_time: '10:00', end_time: '15:00', is_available: false }
  ]);
  
  // State for date overrides
  const [dateOverrides, setDateOverrides] = useState([]);
  
  // State for editing override
  const [editingOverride, setEditingOverride] = useState({
    date: new Date(),
    is_available: true,
    start_time: '09:00',
    end_time: '17:00',
    reason: ''
  });
  
  // State for showing override form
  const [showOverrideForm, setShowOverrideForm] = useState(false);
  
  // Initialize data when availability is loaded
  useEffect(() => {
    if (availability) {
      if (availability.weekly_schedule) {
        setWeeklySchedule(availability.weekly_schedule);
      }
      
      if (availability.overrides) {
        // Convert date strings to Date objects
        const formattedOverrides = availability.overrides.map(override => ({
          ...override,
          date: override.date ? new Date(override.date) : null
        }));
        
        setDateOverrides(formattedOverrides);
      }
    }
  }, [availability]);
  
  // Handle day toggle
  const handleDayToggle = (dayIndex) => {
    setWeeklySchedule(prev => {
      const newSchedule = [...prev];
      newSchedule[dayIndex] = {
        ...newSchedule[dayIndex],
        is_available: !newSchedule[dayIndex].is_available
      };
      return newSchedule;
    });
  };
  
  // Handle time change
  const handleTimeChange = (dayIndex, field, value) => {
    setWeeklySchedule(prev => {
      const newSchedule = [...prev];
      newSchedule[dayIndex] = {
        ...newSchedule[dayIndex],
        [field]: value
      };
      return newSchedule;
    });
  };
  
  // Handle date selection
  const handleDateSelect = (date) => {
    setSelectedDate(date);
    
    // Check if there's an override for this date
    const existingOverride = dateOverrides.find(
      override => override.date && override.date.toDateString() === date.toDateString()
    );
    
    if (existingOverride) {
      setEditingOverride(existingOverride);
    } else {
      // Get the day of week
      const dayOfWeek = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'][date.getDay()];
      
      // Find the weekly schedule for this day
      const daySchedule = weeklySchedule.find(schedule => schedule.day === dayOfWeek);
      
      // Set default values based on weekly schedule
      setEditingOverride({
        date,
        is_available: daySchedule ? daySchedule.is_available : true,
        start_time: daySchedule ? daySchedule.start_time : '09:00',
        end_time: daySchedule ? daySchedule.end_time : '17:00',
        reason: ''
      });
    }
    
    setShowOverrideForm(true);
  };
  
  // Handle override toggle
  const handleOverrideToggle = () => {
    setEditingOverride(prev => ({
      ...prev,
      is_available: !prev.is_available
    }));
  };
  
  // Handle override time change
  const handleOverrideTimeChange = (field, value) => {
    setEditingOverride(prev => ({
      ...prev,
      [field]: value
    }));
  };
  
  // Handle override reason change
  const handleOverrideReasonChange = (e) => {
    setEditingOverride(prev => ({
      ...prev,
      reason: e.target.value
    }));
  };
  
  // Handle save override
  const handleSaveOverride = () => {
    // Check if there's an existing override for this date
    const existingIndex = dateOverrides.findIndex(
      override => override.date && override.date.toDateString() === editingOverride.date.toDateString()
    );
    
    if (existingIndex >= 0) {
      // Update existing override
      setDateOverrides(prev => {
        const newOverrides = [...prev];
        newOverrides[existingIndex] = editingOverride;
        return newOverrides;
      });
    } else {
      // Add new override
      setDateOverrides(prev => [...prev, editingOverride]);
    }
    
    setShowOverrideForm(false);
  };
  
  // Handle delete override
  const handleDeleteOverride = () => {
    setDateOverrides(prev => 
      prev.filter(override => 
        !override.date || override.date.toDateString() !== editingOverride.date.toDateString()
      )
    );
    
    setShowOverrideForm(false);
  };
  
  // Handle cancel override
  const handleCancelOverride = () => {
    setShowOverrideForm(false);
  };
  
  // Check if a date has an override
  const hasOverride = (date) => {
    return dateOverrides.some(
      override => override.date && override.date.toDateString() === date.toDateString()
    );
  };
  
  // Check if a date is available
  const isDateAvailable = (date) => {
    // Check if there's an override for this date
    const override = dateOverrides.find(
      override => override.date && override.date.toDateString() === date.toDateString()
    );
    
    if (override) {
      return override.is_available;
    }
    
    // Otherwise, check the weekly schedule
    const dayOfWeek = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'][date.getDay()];
    const daySchedule = weeklySchedule.find(schedule => schedule.day === dayOfWeek);
    
    return daySchedule ? daySchedule.is_available : false;
  };
  
  // Custom day class for the date picker
  const getDayClassName = (date) => {
    if (!date) return '';
    
    const classes = [];
    
    if (hasOverride(date)) {
      classes.push('has-override');
    }
    
    if (isDateAvailable(date)) {
      classes.push('is-available');
    } else {
      classes.push('is-unavailable');
    }
    
    return classes.join(' ');
  };
  
  // If loading, show skeleton
  if (loading) {
    return (
      <div className="w-full bg-gray-100 rounded-xl overflow-hidden p-6 animate-pulse">
        <div className="h-8 bg-gray-300 rounded w-1/3 mb-6"></div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="h-64 bg-gray-200 rounded"></div>
          <div className="space-y-4">
            <div className="h-8 bg-gray-300 rounded w-1/2 mb-2"></div>
            <div className="space-y-2">
              {[...Array(7)].map((_, index) => (
                <div key={index} className="h-10 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }
  
  // If error, show error message
  if (error) {
    return (
      <div className="w-full bg-red-50 border border-red-200 rounded-xl p-4 text-red-700">
        <p className="font-medium">{t('profile.error.title', 'Error loading availability')}</p>
        <p className="text-sm mt-1">{error}</p>
      </div>
    );
  }
  
  // Day names for display
  const dayNames = [
    t('profile.availability.monday', 'Monday'),
    t('profile.availability.tuesday', 'Tuesday'),
    t('profile.availability.wednesday', 'Wednesday'),
    t('profile.availability.thursday', 'Thursday'),
    t('profile.availability.friday', 'Friday'),
    t('profile.availability.saturday', 'Saturday'),
    t('profile.availability.sunday', 'Sunday')
  ];
  
  return (
    <div className="w-full bg-white rounded-xl overflow-hidden shadow-sm border border-gray-100 p-4 sm:p-6">
      <h2 className="text-xl font-bold text-gray-900 mb-6">
        {t('profile.availability.title', 'Availability Management')}
      </h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Calendar */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            {t('profile.availability.calendar', 'Calendar')}
          </h3>
          
          <div className="border border-gray-200 rounded-lg p-4">
            <DatePicker
              selected={selectedDate}
              onChange={handleDateSelect}
              inline
              dayClassName={getDayClassName}
            />
            
            <div className="mt-4 flex items-center justify-between text-sm">
              <div className="flex items-center">
                <div className="w-4 h-4 bg-green-100 border border-green-500 rounded-full mr-2"></div>
                <span>{t('profile.availability.available', 'Available')}</span>
              </div>
              <div className="flex items-center">
                <div className="w-4 h-4 bg-red-100 border border-red-500 rounded-full mr-2"></div>
                <span>{t('profile.availability.unavailable', 'Unavailable')}</span>
              </div>
              <div className="flex items-center">
                <div className="w-4 h-4 bg-blue-100 border border-blue-500 rounded-full mr-2"></div>
                <span>{t('profile.availability.override', 'Override')}</span>
              </div>
            </div>
          </div>
          
          {/* Date Override Form */}
          {showOverrideForm && (
            <div className="mt-4 border border-gray-200 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-3">
                {t('profile.availability.dateOverride', 'Date Override')} - {editingOverride.date.toLocaleDateString()}
              </h4>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">
                    {t('profile.availability.isAvailable', 'Is Available')}
                  </span>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input 
                      type="checkbox" 
                      className="sr-only peer" 
                      checked={editingOverride.is_available}
                      onChange={handleOverrideToggle}
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>
                
                {editingOverride.is_available && (
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        {t('profile.availability.startTime', 'Start Time')}
                      </label>
                      <select
                        value={editingOverride.start_time}
                        onChange={(e) => handleOverrideTimeChange('start_time', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      >
                        {[...Array(24)].map((_, hour) => (
                          <option key={hour} value={`${hour.toString().padStart(2, '0')}:00`}>
                            {`${hour.toString().padStart(2, '0')}:00`}
                          </option>
                        ))}
                      </select>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        {t('profile.availability.endTime', 'End Time')}
                      </label>
                      <select
                        value={editingOverride.end_time}
                        onChange={(e) => handleOverrideTimeChange('end_time', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      >
                        {[...Array(24)].map((_, hour) => (
                          <option key={hour} value={`${hour.toString().padStart(2, '0')}:00`}>
                            {`${hour.toString().padStart(2, '0')}:00`}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>
                )}
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('profile.availability.reason', 'Reason (Optional)')}
                  </label>
                  <input
                    type="text"
                    value={editingOverride.reason || ''}
                    onChange={handleOverrideReasonChange}
                    placeholder={t('profile.availability.reasonPlaceholder', 'e.g., Holiday, Personal Day')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                
                <div className="flex justify-end space-x-2">
                  <button
                    onClick={handleDeleteOverride}
                    className="px-3 py-1.5 border border-red-300 text-red-600 hover:bg-red-50 rounded-lg text-sm font-medium transition-colors duration-200"
                  >
                    {t('profile.availability.delete', 'Delete')}
                  </button>
                  <button
                    onClick={handleCancelOverride}
                    className="px-3 py-1.5 border border-gray-300 text-gray-700 hover:bg-gray-50 rounded-lg text-sm font-medium transition-colors duration-200"
                  >
                    {t('profile.availability.cancel', 'Cancel')}
                  </button>
                  <button
                    onClick={handleSaveOverride}
                    className="px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm font-medium transition-colors duration-200"
                  >
                    {t('profile.availability.save', 'Save')}
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
        
        {/* Weekly Schedule */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            {t('profile.availability.weeklySchedule', 'Weekly Schedule')}
          </h3>
          
          <div className="space-y-3">
            {weeklySchedule.map((schedule, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-3">
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium">{dayNames[index]}</span>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input 
                      type="checkbox" 
                      className="sr-only peer" 
                      checked={schedule.is_available}
                      onChange={() => handleDayToggle(index)}
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>
                
                {schedule.is_available && (
                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <label className="block text-xs text-gray-500 mb-1">
                        {t('profile.availability.startTime', 'Start Time')}
                      </label>
                      <select
                        value={schedule.start_time}
                        onChange={(e) => handleTimeChange(index, 'start_time', e.target.value)}
                        className="w-full px-2 py-1 text-sm border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      >
                        {[...Array(24)].map((_, hour) => (
                          <option key={hour} value={`${hour.toString().padStart(2, '0')}:00`}>
                            {`${hour.toString().padStart(2, '0')}:00`}
                          </option>
                        ))}
                      </select>
                    </div>
                    
                    <div>
                      <label className="block text-xs text-gray-500 mb-1">
                        {t('profile.availability.endTime', 'End Time')}
                      </label>
                      <select
                        value={schedule.end_time}
                        onChange={(e) => handleTimeChange(index, 'end_time', e.target.value)}
                        className="w-full px-2 py-1 text-sm border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      >
                        {[...Array(24)].map((_, hour) => (
                          <option key={hour} value={`${hour.toString().padStart(2, '0')}:00`}>
                            {`${hour.toString().padStart(2, '0')}:00`}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
      
      {/* Custom styles for the date picker */}
      <style jsx>{`
        /* Available days */
        :global(.is-available) {
          background-color: #d1fae5 !important;
          color: #065f46 !important;
        }
        
        /* Unavailable days */
        :global(.is-unavailable) {
          background-color: #fee2e2 !important;
          color: #991b1b !important;
        }
        
        /* Days with overrides */
        :global(.has-override) {
          border: 2px solid #3b82f6 !important;
        }
      `}</style>
    </div>
  );
};

export default AvailabilityCalendar;
