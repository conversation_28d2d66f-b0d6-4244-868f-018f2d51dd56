import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import ServiceForm from './ServiceForm';
import useTranslation from '../../hooks/useTranslation';

/**
 * ServiceFormModal component
 * 
 * A modal for creating and editing user services.
 * 
 * @param {Object} props
 * @param {boolean} props.isOpen - Whether the modal is open
 * @param {Function} props.onClose - Function called when modal is closed
 * @param {Object} props.initialValues - Initial form values
 * @param {Function} props.onSubmit - Function called when form is submitted
 * @param {Array} props.serviceCategories - Available service categories
 * @param {Array} props.serviceTypes - Available service types
 * @param {Array} props.serviceStyles - Available service styles
 * @param {Array} props.pricingOptionTypes - Available pricing option types
 * @param {boolean} props.isLoading - Whether the form is in a loading state
 * @param {string} props.error - Error message to display
 */
const ServiceFormModal = ({
  isOpen,
  onClose,
  initialValues = {},
  onSubmit,
  serviceCategories = [],
  serviceTypes = [],
  serviceStyles = [],
  pricingOptionTypes = [],
  isLoading = false,
  error = null
}) => {
  const { t } = useTranslation('profile');
  const [isClosing, setIsClosing] = useState(false);
  
  // Handle escape key press
  useEffect(() => {
    const handleEscapeKey = (e) => {
      if (e.key === 'Escape' && isOpen && !isLoading) {
        handleClose();
      }
    };
    
    document.addEventListener('keydown', handleEscapeKey);
    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [isOpen, isLoading]);
  
  // Handle close with animation
  const handleClose = () => {
    if (isLoading) return;
    
    setIsClosing(true);
    setTimeout(() => {
      setIsClosing(false);
      onClose();
    }, 300);
  };
  
  // Modal animation variants
  const backdropVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 },
    exit: { opacity: 0 }
  };
  
  const modalVariants = {
    hidden: { opacity: 0, y: 50, scale: 0.95 },
    visible: { opacity: 1, y: 0, scale: 1, transition: { duration: 0.3 } },
    exit: { opacity: 0, y: 50, scale: 0.95, transition: { duration: 0.2 } }
  };
  
  if (!isOpen && !isClosing) return null;
  
  return (
    <AnimatePresence>
      <motion.div
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 overflow-y-auto"
        initial="hidden"
        animate="visible"
        exit="exit"
        variants={backdropVariants}
        onClick={handleClose}
      >
        <motion.div
          className="bg-white rounded-xl w-full max-w-3xl max-h-[90vh] overflow-hidden flex flex-col"
          variants={modalVariants}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h2 className="text-xl font-bold text-gray-900">
              {initialValues.id 
                ? t('services.modal.editTitle', 'Edit Service') 
                : t('services.modal.createTitle', 'Create New Service')}
            </h2>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50"
              disabled={isLoading}
              aria-label={t('services.modal.close', 'Close')}
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          {/* Content */}
          <div className="flex-1 overflow-y-auto p-6">
            <ServiceForm
              initialValues={initialValues}
              onSubmit={onSubmit}
              onCancel={handleClose}
              serviceCategories={serviceCategories}
              serviceTypes={serviceTypes}
              serviceStyles={serviceStyles}
              pricingOptionTypes={pricingOptionTypes}
              isLoading={isLoading}
              error={error}
            />
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default ServiceFormModal;
