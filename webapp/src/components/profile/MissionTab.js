import React, { useState } from 'react';

/**
 * MissionTab component displays the talent's missions
 */
const MissionTab = ({ talent }) => {
  const [activeFilter, setActiveFilter] = useState('all');
  
  // Default values in case talent data is incomplete
  const {
    missions = [],
    hostedMissions = []
  } = talent || {};
  
  // Combine all missions
  const allMissions = [...(missions || []), ...(hostedMissions || [])];
  
  // Filter missions based on active filter
  const filteredMissions = activeFilter === 'all' 
    ? allMissions
    : activeFilter === 'hosted' 
      ? hostedMissions
      : missions;
  
  // Handle filter change
  const handleFilterChange = (filter) => {
    setActiveFilter(filter);
  };
  
  // Render a single mission
  const renderMission = (mission) => {
    return (
      <div 
        key={mission.id} 
        className="bg-white rounded-lg shadow-sm border border-gray-100 p-4 mb-4"
      >
        {/* Mission header */}
        <div className="flex justify-between items-start mb-3">
          <div>
            <div className="flex items-center mb-1">
              <span className={`text-xs px-2 py-0.5 rounded ${
                mission.isHosted 
                  ? 'bg-purple-100 text-purple-800' 
                  : 'bg-blue-100 text-blue-800'
              }`}>
                {mission.isHosted ? 'Hosted' : 'Participated'}
              </span>
              <span className={`ml-2 text-xs px-2 py-0.5 rounded ${
                mission.status === 'completed' 
                  ? 'bg-green-100 text-green-800' 
                  : mission.status === 'in_progress'
                    ? 'bg-yellow-100 text-yellow-800'
                    : 'bg-gray-100 text-gray-800'
              }`}>
                {mission.status === 'completed' 
                  ? 'Completed' 
                  : mission.status === 'in_progress'
                    ? 'In Progress'
                    : 'Pending'}
              </span>
            </div>
            <h3 className="font-medium">{mission.title}</h3>
            <p className="text-xs text-gray-500">{mission.date}</p>
          </div>
          <div className="text-right">
            <div className="font-medium text-green-600">{mission.reward} Credits</div>
            <div className="text-xs text-gray-500">{mission.duration}</div>
          </div>
        </div>
        
        {/* Mission description */}
        <p className="text-sm text-gray-700 mb-3 line-clamp-2">{mission.description}</p>
        
        {/* Mission footer */}
        <div className="flex justify-between items-center">
          <div className="flex items-center">
            <div className="flex -space-x-2">
              {mission.participants && mission.participants.slice(0, 3).map((participant, index) => (
                <div 
                  key={index} 
                  className="w-6 h-6 rounded-full border border-white overflow-hidden"
                >
                  <img 
                    src={participant.image} 
                    alt={participant.name} 
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      e.target.onerror = null;
                      e.target.src = 'https://via.placeholder.com/24?text=User';
                    }}
                  />
                </div>
              ))}
              {mission.participants && mission.participants.length > 3 && (
                <div className="w-6 h-6 rounded-full bg-gray-200 border border-white flex items-center justify-center text-xs text-gray-600">
                  +{mission.participants.length - 3}
                </div>
              )}
            </div>
            <span className="text-xs text-gray-500 ml-2">
              {mission.participants ? mission.participants.length : 0} Participants
            </span>
          </div>
          <button className="text-blue-600 text-sm hover:underline">
            View Details
          </button>
        </div>
      </div>
    );
  };

  return (
    <div className="animate-fade-in">
      {/* Header with filter tabs */}
      <div className="mb-4">
        <div className="flex justify-between items-center mb-2">
          <h2 className="text-lg font-bold">Missions</h2>
          <div className="text-sm text-gray-500">
            {filteredMissions.length} {filteredMissions.length === 1 ? 'Mission' : 'Missions'}
          </div>
        </div>
        
        <div className="flex bg-gray-100 p-1 rounded-lg">
          <button 
            className={`flex-1 py-1.5 text-sm font-medium rounded ${
              activeFilter === 'all' 
                ? 'bg-white shadow-sm' 
                : 'text-gray-600 hover:text-gray-800'
            }`}
            onClick={() => handleFilterChange('all')}
          >
            All
          </button>
          <button 
            className={`flex-1 py-1.5 text-sm font-medium rounded ${
              activeFilter === 'hosted' 
                ? 'bg-white shadow-sm' 
                : 'text-gray-600 hover:text-gray-800'
            }`}
            onClick={() => handleFilterChange('hosted')}
          >
            Hosted
          </button>
          <button 
            className={`flex-1 py-1.5 text-sm font-medium rounded ${
              activeFilter === 'participated' 
                ? 'bg-white shadow-sm' 
                : 'text-gray-600 hover:text-gray-800'
            }`}
            onClick={() => handleFilterChange('participated')}
          >
            Participated
          </button>
        </div>
      </div>
      
      {/* Missions list */}
      {filteredMissions.length > 0 ? (
        <div>
          {filteredMissions.map(renderMission)}
        </div>
      ) : (
        <div className="text-center py-12 bg-gray-50 rounded-lg">
          <svg 
            className="w-12 h-12 text-gray-300 mx-auto mb-3" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
          </svg>
          <p className="text-gray-500 mb-2">No missions found</p>
          <p className="text-gray-400 text-sm">
            {activeFilter === 'all' 
              ? 'This talent has not participated in or hosted any missions yet' 
              : activeFilter === 'hosted'
                ? 'This talent has not hosted any missions yet'
                : 'This talent has not participated in any missions yet'}
          </p>
        </div>
      )}
    </div>
  );
};

export default MissionTab;
