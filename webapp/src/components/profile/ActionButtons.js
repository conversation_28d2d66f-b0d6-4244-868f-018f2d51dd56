import React, { useState } from 'react';

/**
 * ActionButtons component displays the main action buttons for interacting with a talent
 * including more actions, follow, send gift, chat, and order
 */
const ActionButtons = ({ talent, onFollow, onSendGift, onChat, onOrder }) => {
  const [showMoreActions, setShowMoreActions] = useState(false);
  const [isFollowing, setIsFollowing] = useState(talent?.isFollowing || false);

  // Handle follow/unfollow
  const handleFollowClick = () => {
    setIsFollowing(!isFollowing);
    if (onFollow) {
      onFollow(!isFollowing);
    }
  };

  // Handle send gift
  const handleSendGift = () => {
    if (onSendGift) {
      onSendGift();
    }
  };

  // Handle chat
  const handleChat = () => {
    if (onChat) {
      onChat();
    }
  };

  // Handle order
  const handleOrder = () => {
    if (onOrder) {
      // If talent has skills, pass the first skill to the onOrder callback
      if (talent && talent.skills && talent.skills.length > 0) {
        console.log('Ordering first skill:', talent.skills[0]);
        onOrder(talent.skills[0]);
      } else {
        // If no skills, just call onOrder without parameters
        console.log('No skills available for ordering');
        onOrder();
      }
    }
  };

  // Toggle more actions modal
  const toggleMoreActions = () => {
    setShowMoreActions(!showMoreActions);
  };

  return (
    <>
      {/* Desktop Action Buttons */}
      <div className="flex gap-2 p-4 bg-white border-t border-gray-100">
        {/* More Actions Button */}
        <button
          className="w-10 h-10 flex items-center justify-center rounded-full border bg-transparent text-blue-700 border-gray-300 hover:bg-gray-200"
          onClick={toggleMoreActions}
          aria-label="More actions"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0z" />
          </svg>
        </button>

        {/* Follow Button */}
        <button
          className={`flex-1 flex items-center justify-center gap-2 rounded-full py-2 px-4 font-medium text-sm transition-colors ${
            isFollowing
              ? 'bg-blue-100 text-blue-700'
              : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
          }`}
          onClick={handleFollowClick}
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d={
              isFollowing
                ? "M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" // Checkmark for following
                : "M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" // User icon for not following
            } />
          </svg>
          {isFollowing ? 'Following' : 'Follow'}
        </button>

        {/* Send Gift Button */}
        <button
          className="flex-1 flex items-center justify-center gap-2 bg-blue-500 text-white rounded-full py-2 px-4 font-medium text-sm hover:bg-blue-600 transition-colors"
          onClick={handleSendGift}
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v13m0-13V6a4 4 0 00-4-4H5.52a3 3 0 00-2.294 1.064l-1.157 1.282a3 3 0 000 4.1l1.157 1.282A3 3 0 005.52 11H8m4-3h4a4 4 0 014 4v2a4 4 0 01-4 4H8a4 4 0 01-4-4v-2a4 4 0 014-4h4z" />
          </svg>
          Send Gift
        </button>

        {/* Chat Button - Desktop Only */}
        <button
          className="hidden md:flex flex-1 items-center justify-center gap-2 bg-gray-100 text-gray-800 rounded-full py-2 px-4 font-medium text-sm hover:bg-gray-200 transition-colors"
          onClick={handleChat}
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
          Chat
        </button>

        {/* Order Button - Desktop Only */}
        <button
          className="hidden md:flex flex-1 items-center justify-center gap-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-full py-2 px-4 font-medium text-sm hover:shadow-md transition-all"
          onClick={handleOrder}
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
          </svg>
          Order
        </button>
      </div>

      {/* Mobile Action Buttons - Fixed at bottom */}
      <div className="fixed bottom-0 left-0 right-0 flex bg-white border-t border-gray-200 p-3 gap-2 md:hidden z-10">
        <button
          className="flex-1 bg-gray-100 rounded-full py-3 font-medium hover:bg-gray-200 transition-colors"
          onClick={handleChat}
        >
          Chat
        </button>
        <button
          className="flex-1 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-full py-3 font-medium hover:shadow-md transition-all"
          onClick={handleOrder}
        >
          Order
        </button>
      </div>

      {/* More Actions Modal */}
      {showMoreActions && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-end justify-center md:items-center" onClick={toggleMoreActions}>
          <div className="bg-white rounded-t-xl md:rounded-xl w-full max-w-md p-4 animate-slide-up" onClick={(e) => e.stopPropagation()}>
            <div className="w-12 h-1 bg-gray-300 rounded-full mx-auto mb-4 md:hidden"></div>
            <h3 className="text-lg font-bold mb-4">More Actions</h3>

            <div className="space-y-4">
              <button className="flex items-center w-full p-3 hover:bg-gray-300 bg-transparent text-indigo-500 rounded-lg">
                <svg className="w-6 h-6 mr-3 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z" />
                </svg>
                <span>Share Profile</span>
              </button>

              <button className="flex items-center w-full p-3 hover:bg-gray-300 bg-transparent text-indigo-500 rounded-lg">
                <svg className="w-6 h-6 mr-3 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728A9 9 0 015.636 5.636m12.728 12.728L5.636 5.636" />
                </svg>
                <span>Block User</span>
              </button>

              <button className="flex items-center w-full p-3 hover:bg-gray-300 bg-transparent text-indigo-500 rounded-lg">
                <svg className="w-6 h-6 mr-3 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 21v-4m0 0V5a2 2 0 012-2h6.5l1 1H21l-3 6 3 6h-8.5l-1-1H5a2 2 0 00-2 2zm9-13.5V9" />
                </svg>
                <span>Report User</span>
              </button>
            </div>

            <button
              className="w-full mt-6 p-3 text-red-500 bg-red-100 hover:bg-red-300 rounded-lg font-medium"
              onClick={toggleMoreActions}
            >
              Cancel
            </button>
          </div>
        </div>
      )}
    </>
  );
};

export default ActionButtons;
