import React, { useState, useEffect } from 'react';
import ServiceSelectionModal from './ServiceSelectionModal';
import DateTimeSelectionModal from './DateTimeSelectionModal';
import PlaceOrderPage from './PlaceOrderPage';

/**
 * SkillsTab component displays the talent's skills and services
 */
const SkillsTab = ({ talent, onOrder }) => {
  const [showAllSkills, setShowAllSkills] = useState(false);
  const [selectedSkill, setSelectedSkill] = useState(null);
  const [selectedTier, setSelectedTier] = useState(null);
  const [selectedDateTime, setSelectedDateTime] = useState(null);
  const [showServiceModal, setShowServiceModal] = useState(false);
  const [showDateTimeModal, setShowDateTimeModal] = useState(false);
  const [showPlaceOrderPage, setShowPlaceOrderPage] = useState(false);

  // Handle the case when a skill is passed from the parent component (via onOrder)
  useEffect(() => {
    // This effect will run when the component mounts or when talent changes
    // If the parent component has called onOrder with a skill, we need to handle it

    // We'll check if there's a skill in the URL query params
    const urlParams = new URLSearchParams(window.location.search);
    const autoOpenOrder = urlParams.get('order');

    if (autoOpenOrder === 'true' && talent && talent.skills && talent.skills.length > 0) {
      console.log('SkillsTab - Auto-opening order modal for first skill');

      // Create a skill object with tiers
      const firstSkill = talent.skills[0];
      const skillWithTiers = {
        ...firstSkill,
        tiers: firstSkill.rates ? firstSkill.rates.map((rate, index) => ({
          id: index + 1,
          name: rate.type,
          price: rate.price,
          unit: rate.unit,
          description: rate.description
        })) : []
      };

      // Set the selected skill and show the service modal
      console.log('SkillsTab - Setting selectedSkill:', skillWithTiers);
      setSelectedSkill(skillWithTiers);
      setShowServiceModal(true);

      // Remove the URL parameter to prevent reopening on refresh
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete('order');
      window.history.replaceState({}, '', newUrl);
    }
  }, [talent]);

  // Default values in case talent data is incomplete
  const {
    skills = []
  } = talent || {};

  // Determine if we need to show "See All" button
  const hasMoreSkills = skills.length > 3;

  // Get skills to display (all or just first 3)
  const displayedSkills = showAllSkills ? skills : skills.slice(0, 3);

  // Toggle showing all skills
  const toggleShowAllSkills = () => {
    setShowAllSkills(!showAllSkills);
  };

  // Handle order button click for a specific skill and rate
  const handleOrderClick = (skill, rate) => {
    console.log('SkillsTab - handleOrderClick called with:', { skill, rate });

    // If the onOrder prop is provided, use it (for backward compatibility)
    if (onOrder) {
      console.log('SkillsTab - Using onOrder prop');
      onOrder(skill, rate);
      return;
    }

    // Otherwise, use our new flow
    console.log('SkillsTab - Using new flow');
    const skillWithTiers = {
      ...skill,
      tiers: skill.rates ? skill.rates.map((rate, index) => ({
        id: index + 1,
        name: rate.type,
        price: rate.price,
        unit: rate.unit,
        description: rate.description
      })) : []
    };

    console.log('SkillsTab - Setting selectedSkill:', skillWithTiers);
    setSelectedSkill(skillWithTiers);
    setShowServiceModal(true);
  };

  // Handle service selection
  const handleServiceSelect = (skill, tier) => {
    setSelectedTier(tier);
    setShowServiceModal(false);
    setShowDateTimeModal(true);
  };

  // Handle "Order For Now" click
  const handleOrderNow = (skill, tier) => {
    setSelectedTier(tier);
    setSelectedDateTime(null); // No specific date/time
    setShowServiceModal(false);
    setShowPlaceOrderPage(true);
  };

  // Handle date/time selection
  const handleDateTimeSelect = (skill, tier, dateTime) => {
    setSelectedDateTime(dateTime);
    setShowDateTimeModal(false);
    setShowPlaceOrderPage(true);
  };

  // Handle back button click in place order page
  const handlePlaceOrderBack = () => {
    setShowPlaceOrderPage(false);

    // If we have a date/time, go back to date/time selection
    if (selectedDateTime) {
      setShowDateTimeModal(true);
    } else {
      // Otherwise, go back to service selection
      setShowServiceModal(true);
    }
  };

  // Handle order placed
  const handleOrderPlaced = (order) => {
    // Reset state
    setSelectedSkill(null);
    setSelectedTier(null);
    setSelectedDateTime(null);
    setShowPlaceOrderPage(false);

    // If onOrder is provided, call it with the order
    if (onOrder) {
      onOrder(order);
    }
  };

  // If showing place order page, render it
  if (showPlaceOrderPage) {
    return (
      <PlaceOrderPage
        talent={talent}
        skill={selectedSkill}
        tier={selectedTier}
        dateTime={selectedDateTime}
        onBack={handlePlaceOrderBack}
        onOrderPlaced={handleOrderPlaced}
      />
    );
  }

  return (
    <div className="animate-fade-in">
      {/* Header with skills count */}
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-bold">Skills ({skills.length})</h2>
        {hasMoreSkills && (
          <button
            onClick={toggleShowAllSkills}
            className="text-blue-600 text-sm font-medium hover:underline"
          >
            {showAllSkills ? 'See Less' : 'See All'}
          </button>
        )}
      </div>

      {/* Skills list */}
      {skills.length > 0 ? (
        <div className="space-y-4">
          {displayedSkills.map((skill) => (
            <div
              key={skill.id}
              className="bg-blue-50 rounded-xl p-4 border border-blue-100"
            >
              {/* Skill header */}
              <div className="flex justify-between items-center mb-3">
                <div className="flex items-center">
                  <div className="w-12 h-12 rounded-lg overflow-hidden bg-white border border-gray-200 mr-3 flex items-center justify-center">
                    {skill.image ? (
                      <img
                        key={`skill-${skill.id}-image`} // Add a key to force re-render when skill changes
                        src={skill.image}
                        alt={skill.name}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          // Prevent infinite loop by checking if we're already using the fallback
                          if (!e.target.src.includes('via.placeholder.com')) {
                            console.log(`Skill image for ${skill.name} failed to load, using fallback`);
                            e.target.src = 'https://via.placeholder.com/48?text=Skill';
                          }
                          // Remove the error handler to prevent further attempts
                          e.target.onerror = null;
                        }}
                      />
                    ) : (
                      <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                      </svg>
                    )}
                  </div>
                  <div>
                    <h3 className="font-medium">{skill.name}</h3>
                    <p className="text-xs text-gray-600">
                      {skill.ordersCount} {skill.ordersCount === 1 ? 'person' : 'people'} ordered
                    </p>
                  </div>
                </div>
                <button
                  className="bg-blue-500 hover:bg-blue-600 text-white text-sm py-1.5 px-4 rounded-lg transition-colors"
                  onClick={() => handleOrderClick(skill)}
                >
                  Order
                </button>
              </div>

              {/* Rates list */}
              <div className="space-y-3 mt-4">
                {skill.rates && skill.rates.map((rate, index) => (
                  <div
                    key={index}
                    className="flex justify-between items-center bg-white p-3 rounded-lg border border-gray-100"
                  >
                    <div className="flex items-center">
                      <div className="bg-indigo-700 w-6 h-6 rounded-full flex items-center justify-center mr-2">
                        <span className="text-white text-xs">₩</span>
                      </div>
                      <span className="text-sm font-medium">{rate.price} / {rate.unit}</span>
                    </div>
                    <div className="flex items-center">
                      <span className="text-sm text-gray-700 mr-3">{rate.type}</span>
                      <button
                        className="bg-blue-100 hover:bg-blue-200 text-blue-700 text-xs py-1 px-2 rounded transition-colors"
                        onClick={() => handleOrderClick(skill, rate)}
                      >
                        Order
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}

          {/* See All / See Less button (mobile only) */}
          {hasMoreSkills && (
            <button
              onClick={toggleShowAllSkills}
              className="w-full py-2 text-blue-600 text-sm font-medium border border-blue-200 rounded-lg hover:bg-blue-50 transition-colors md:hidden"
            >
              {showAllSkills ? 'See Less' : 'See All Skills'}
            </button>
          )}
        </div>
      ) : (
        <div className="text-center py-8 bg-gray-50 rounded-lg">
          <svg
            className="w-12 h-12 text-gray-300 mx-auto mb-3"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
          </svg>
          <p className="text-gray-500">No skills listed</p>
        </div>
      )}

      {/* Service Selection Modal */}
      <ServiceSelectionModal
        isOpen={showServiceModal}
        onClose={() => setShowServiceModal(false)}
        skill={selectedSkill}
        onSelectDate={handleServiceSelect}
        onOrderNow={handleOrderNow}
      />

      {/* Date/Time Selection Modal */}
      <DateTimeSelectionModal
        isOpen={showDateTimeModal}
        onClose={() => setShowDateTimeModal(false)}
        skill={selectedSkill}
        tier={selectedTier}
        talent={talent}
        onContinue={handleDateTimeSelect}
      />
    </div>
  );
};

export default SkillsTab;
