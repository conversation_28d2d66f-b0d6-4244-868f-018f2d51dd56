/**
 * Profile Settings Component
 * 
 * This component provides settings for the user's profile.
 */

import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useProfile } from '../../contexts/ProfileContext';
import { useAuth } from '../../contexts/AuthContext';
import useTranslation from '../../hooks/useTranslation';

/**
 * ProfileSettings component
 */
const ProfileSettings = () => {
  // Get profile and auth context
  const { profile, loading, error } = useProfile();
  const { logout } = useAuth();
  
  // Navigation
  const navigate = useNavigate();
  
  // Use the translation hook
  const { t, currentLanguage, changeLanguage } = useTranslation('profile');
  
  // Settings state
  const [notificationSettings, setNotificationSettings] = useState({
    email_notifications: true,
    push_notifications: true,
    message_notifications: true,
    order_notifications: true,
    marketing_emails: false
  });
  
  const [privacySettings, setPrivacySettings] = useState({
    profile_visibility: 'public',
    show_online_status: true,
    show_last_active: true,
    allow_messages_from: 'everyone'
  });
  
  // Handle notification toggle
  const handleNotificationToggle = (setting) => {
    setNotificationSettings(prev => ({
      ...prev,
      [setting]: !prev[setting]
    }));
  };
  
  // Handle privacy setting change
  const handlePrivacyChange = (setting, value) => {
    setPrivacySettings(prev => ({
      ...prev,
      [setting]: value
    }));
  };
  
  // Handle language change
  const handleLanguageChange = (e) => {
    changeLanguage(e.target.value);
  };
  
  // Handle logout
  const handleLogout = async () => {
    try {
      await logout();
      navigate('/');
    } catch (err) {
      console.error('Logout error:', err);
    }
  };
  
  // If loading, show skeleton
  if (loading) {
    return (
      <div className="w-full bg-gray-100 rounded-xl overflow-hidden p-6 animate-pulse">
        <div className="h-8 bg-gray-300 rounded w-1/3 mb-6"></div>
        <div className="space-y-6">
          {[...Array(3)].map((_, index) => (
            <div key={index}>
              <div className="h-6 bg-gray-300 rounded w-1/4 mb-4"></div>
              <div className="space-y-3">
                {[...Array(3)].map((_, idx) => (
                  <div key={idx} className="h-10 bg-gray-200 rounded w-full"></div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }
  
  // If error, show error message
  if (error) {
    return (
      <div className="w-full bg-red-50 border border-red-200 rounded-xl p-4 text-red-700">
        <p className="font-medium">{t('profile.error.title', 'Error loading settings')}</p>
        <p className="text-sm mt-1">{error}</p>
      </div>
    );
  }
  
  return (
    <div className="w-full bg-white rounded-xl overflow-hidden shadow-sm border border-gray-100 p-4 sm:p-6">
      <h2 className="text-xl font-bold text-gray-900 mb-6">
        {t('profile.settings.title', 'Profile Settings')}
      </h2>
      
      <div className="space-y-8">
        {/* Notification Settings */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            {t('profile.settings.notifications', 'Notification Settings')}
          </h3>
          
          <div className="space-y-4">
            {/* Email Notifications */}
            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-sm font-medium text-gray-900">
                  {t('profile.settings.emailNotifications', 'Email Notifications')}
                </h4>
                <p className="text-xs text-gray-500">
                  {t('profile.settings.emailNotificationsDesc', 'Receive notifications via email')}
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input 
                  type="checkbox" 
                  className="sr-only peer" 
                  checked={notificationSettings.email_notifications}
                  onChange={() => handleNotificationToggle('email_notifications')}
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
            
            {/* Push Notifications */}
            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-sm font-medium text-gray-900">
                  {t('profile.settings.pushNotifications', 'Push Notifications')}
                </h4>
                <p className="text-xs text-gray-500">
                  {t('profile.settings.pushNotificationsDesc', 'Receive notifications on your device')}
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input 
                  type="checkbox" 
                  className="sr-only peer" 
                  checked={notificationSettings.push_notifications}
                  onChange={() => handleNotificationToggle('push_notifications')}
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
            
            {/* Message Notifications */}
            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-sm font-medium text-gray-900">
                  {t('profile.settings.messageNotifications', 'Message Notifications')}
                </h4>
                <p className="text-xs text-gray-500">
                  {t('profile.settings.messageNotificationsDesc', 'Get notified when you receive new messages')}
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input 
                  type="checkbox" 
                  className="sr-only peer" 
                  checked={notificationSettings.message_notifications}
                  onChange={() => handleNotificationToggle('message_notifications')}
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
            
            {/* Order Notifications */}
            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-sm font-medium text-gray-900">
                  {t('profile.settings.orderNotifications', 'Order Notifications')}
                </h4>
                <p className="text-xs text-gray-500">
                  {t('profile.settings.orderNotificationsDesc', 'Get notified about order updates')}
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input 
                  type="checkbox" 
                  className="sr-only peer" 
                  checked={notificationSettings.order_notifications}
                  onChange={() => handleNotificationToggle('order_notifications')}
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
            
            {/* Marketing Emails */}
            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-sm font-medium text-gray-900">
                  {t('profile.settings.marketingEmails', 'Marketing Emails')}
                </h4>
                <p className="text-xs text-gray-500">
                  {t('profile.settings.marketingEmailsDesc', 'Receive promotional emails and updates')}
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input 
                  type="checkbox" 
                  className="sr-only peer" 
                  checked={notificationSettings.marketing_emails}
                  onChange={() => handleNotificationToggle('marketing_emails')}
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
          </div>
        </div>
        
        {/* Privacy Settings */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            {t('profile.settings.privacy', 'Privacy Settings')}
          </h3>
          
          <div className="space-y-4">
            {/* Profile Visibility */}
            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-1">
                {t('profile.settings.profileVisibility', 'Profile Visibility')}
              </h4>
              <p className="text-xs text-gray-500 mb-2">
                {t('profile.settings.profileVisibilityDesc', 'Control who can see your profile')}
              </p>
              <select
                value={privacySettings.profile_visibility}
                onChange={(e) => handlePrivacyChange('profile_visibility', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="public">{t('profile.settings.public', 'Public - Anyone can view')}</option>
                <option value="registered">{t('profile.settings.registered', 'Registered Users Only')}</option>
                <option value="private">{t('profile.settings.private', 'Private - Only people you approve')}</option>
              </select>
            </div>
            
            {/* Online Status */}
            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-sm font-medium text-gray-900">
                  {t('profile.settings.onlineStatus', 'Show Online Status')}
                </h4>
                <p className="text-xs text-gray-500">
                  {t('profile.settings.onlineStatusDesc', 'Let others see when you are online')}
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input 
                  type="checkbox" 
                  className="sr-only peer" 
                  checked={privacySettings.show_online_status}
                  onChange={() => handlePrivacyChange('show_online_status', !privacySettings.show_online_status)}
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
            
            {/* Last Active */}
            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-sm font-medium text-gray-900">
                  {t('profile.settings.lastActive', 'Show Last Active')}
                </h4>
                <p className="text-xs text-gray-500">
                  {t('profile.settings.lastActiveDesc', 'Let others see when you were last active')}
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input 
                  type="checkbox" 
                  className="sr-only peer" 
                  checked={privacySettings.show_last_active}
                  onChange={() => handlePrivacyChange('show_last_active', !privacySettings.show_last_active)}
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
            
            {/* Message Privacy */}
            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-1">
                {t('profile.settings.messagePrivacy', 'Message Privacy')}
              </h4>
              <p className="text-xs text-gray-500 mb-2">
                {t('profile.settings.messagePrivacyDesc', 'Control who can send you messages')}
              </p>
              <select
                value={privacySettings.allow_messages_from}
                onChange={(e) => handlePrivacyChange('allow_messages_from', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="everyone">{t('profile.settings.everyone', 'Everyone')}</option>
                <option value="followers">{t('profile.settings.followers', 'Followers Only')}</option>
                <option value="nobody">{t('profile.settings.nobody', 'Nobody')}</option>
              </select>
            </div>
          </div>
        </div>
        
        {/* Language Settings */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            {t('profile.settings.language', 'Language Settings')}
          </h3>
          
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-1">
              {t('profile.settings.preferredLanguage', 'Preferred Language')}
            </h4>
            <p className="text-xs text-gray-500 mb-2">
              {t('profile.settings.preferredLanguageDesc', 'Select your preferred language for the application')}
            </p>
            <select
              value={currentLanguage}
              onChange={handleLanguageChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="en">{t('profile.settings.english', 'English')}</option>
              <option value="ms">{t('profile.settings.malay', 'Bahasa Melayu')}</option>
              <option value="cn">{t('profile.settings.chinese', '中文')}</option>
            </select>
          </div>
        </div>
        
        {/* Account Actions */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            {t('profile.settings.account', 'Account Actions')}
          </h3>
          
          <div className="space-y-3">
            <button
              onClick={handleLogout}
              className="w-full flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200"
            >
              <div className="flex items-center">
                <svg className="w-5 h-5 text-gray-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
                <span className="font-medium text-gray-900">{t('profile.settings.logout', 'Logout')}</span>
              </div>
              <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
              </svg>
            </button>
            
            <button
              onClick={() => navigate('/profile/change-password')}
              className="w-full flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200"
            >
              <div className="flex items-center">
                <svg className="w-5 h-5 text-gray-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                </svg>
                <span className="font-medium text-gray-900">{t('profile.settings.changePassword', 'Change Password')}</span>
              </div>
              <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
              </svg>
            </button>
            
            <button
              onClick={() => navigate('/profile/delete-account')}
              className="w-full flex items-center justify-between p-3 border border-red-200 rounded-lg hover:bg-red-50 transition-colors duration-200"
            >
              <div className="flex items-center">
                <svg className="w-5 h-5 text-red-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
                <span className="font-medium text-red-600">{t('profile.settings.deleteAccount', 'Delete Account')}</span>
              </div>
              <svg className="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileSettings;
