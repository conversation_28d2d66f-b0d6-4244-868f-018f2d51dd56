import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { getCdnUrl } from '../../utils/cdnUtils';

/**
 * ProfileHeader component displays the top section of a talent's profile
 * including cover image, profile picture, name, level, and basic information
 */
const ProfileHeader = ({ talent }) => {
  // Default values in case talent data is incomplete
  const {
    name = 'User',
    level = 1,
    gender = 'other',
    userId = 'mx00000',
    isOnline = false,
    lastActive = null
  } = talent || {};

  // Default fallback images
  const fallbackProfileImage = 'https://via.placeholder.com/150?text=No+Image';
  const fallbackCoverImage = '/images/default-cover.jpg';

  // State to track actual image sources
  const [profileImageSrc, setProfileImageSrc] = useState(fallbackProfileImage);
  const [coverImageSrc, setCoverImageSrc] = useState(
    talent?.cover_image ? getCdnUrl(talent.cover_image) : fallbackCoverImage
  );

  // Update image sources when talent changes
  useEffect(() => {
    if (talent) {
      if (talent.profileImage) {
        setProfileImageSrc(talent.profileImage);
      }
      if (talent.coverImage) {
        setCoverImageSrc(talent.coverImage);
      }
    }
  }, [talent]);

  // Format the last active time
  const formatLastActive = (lastActive) => {
    if (!lastActive) return '';

    // This is a simple implementation - in a real app, you'd use a library like date-fns
    const now = new Date();
    const lastActiveDate = new Date(lastActive);
    const diffInDays = Math.floor((now - lastActiveDate) / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) return 'Today';
    if (diffInDays === 1) return 'Yesterday';
    return `${diffInDays}d ago`;
  };

  return (
    <div className="relative">
      {/* Cover Image */}
      <div className="h-48 md:h-64 w-full relative">
        <img
          src={coverImageSrc}
          alt=""
          className="w-full h-full object-cover"
          onError={(e) => {
            // Only set fallback once to prevent infinite loop
            if (e.target.src !== fallbackCoverImage) {
              console.log('Cover image failed to load, using fallback');
              setCoverImageSrc(fallbackCoverImage);
            }
          }}
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
      </div>

      {/* Profile Info */}
      <div className="absolute bottom-0 left-0 w-full p-4 text-white">
        <div className="flex items-end">
          {/* Profile Picture */}
          <div className="relative mr-3">
            <div className="w-20 h-20 rounded-full border-4 border-white overflow-hidden shadow-lg">
              <img
                src={talent?.profile_picture ? getCdnUrl(talent.profile_picture) : '/images/default-avatar.png'}
                alt={name}
                className="w-full h-full object-cover"
                onError={(e) => {
                  e.target.src = '/images/default-avatar.png';
                }}
              />
            </div>

            {/* Online Status */}
            {isOnline ? (
              <div className="absolute bottom-1 right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white"></div>
            ) : lastActive ? (
              <div className="absolute -bottom-1 -right-1 bg-gray-800 text-white text-[8px] px-1 py-0.5 rounded-full border border-white whitespace-nowrap">
                {formatLastActive(lastActive)}
              </div>
            ) : null}
          </div>

          {/* Name & Info */}
          <div>
            <div className="flex items-center">
              <h1 className="text-xl font-bold">{name}</h1>
              <span className="ml-2 bg-gradient-to-r from-indigo-600 to-blue-500 text-white text-xs font-bold px-2.5 py-1 rounded-full flex items-center">
                <span className="mr-0.5">LV</span>
                <span className="text-sm">{level}</span>
              </span>
              <span className="ml-2 text-lg">
                {gender === 'male' ? '♂' : gender === 'female' ? '♀' : '⚧'}
              </span>
            </div>
            <p className="text-sm opacity-90">ID: {userId}</p>
            <Link to="/availability" className="text-sm text-blue-300 flex items-center mt-1 hover:underline">
              View Availability
              <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
              </svg>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileHeader;
