/**
 * Profile Page Component
 *
 * This component is the main profile page that displays the user's profile.
 */

import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useProfile } from '../../contexts/ProfileContext';
import { useAuth } from '../../contexts/AuthContext';
import useTranslation from '../../hooks/useTranslation';
import ProfileHeader from './ProfileHeader';
import ProfileStats from './ProfileStats';
import ProfileBio from './ProfileBio';
import ProfileServices from './ProfileServices';
import ProfileVoiceNote from './ProfileVoiceNote';

/**
 * ProfilePage component
 */
const ProfilePage = () => {
  // Get profile and auth context
  const { profile, loading, error, initialLoadDone } = useProfile();
  const { user, isAuthenticated } = useAuth();

  // Get URL parameters
  const { userId } = useParams();

  // Navigation
  const navigate = useNavigate();

  // State for active tab
  const [activeTab, setActiveTab] = useState('overview');

  // Use the translation hook
  const { t } = useTranslation('profile');

  // Determine if this is the current user's profile
  const isCurrentUser = !userId || (profile && user && profile.id === user.id);

  // Handle edit profile
  const handleEditProfile = () => {
    navigate('/edit-profile');
  };

  // These handlers are no longer needed as the service management is now handled
  // directly in the ProfileServices component

  // Handle edit bio
  const handleEditBio = () => {
    navigate('/profile/bio/edit');
  };

  // If not authenticated, redirect to login
  useEffect(() => {
    if (initialLoadDone && !isAuthenticated) {
      navigate('/');
    }
  }, [initialLoadDone, isAuthenticated, navigate]);

  // If loading, show skeleton
  if (loading && !profile) {
    return (
      <div className="container mx-auto px-4 py-6 max-w-6xl">
        <div className="w-full bg-gray-100 rounded-xl overflow-hidden animate-pulse mb-6">
          <div className="h-48 bg-gray-200"></div>
          <div className="p-4 flex flex-col sm:flex-row items-center">
            <div className="w-24 h-24 rounded-full bg-gray-300 -mt-12 border-4 border-white"></div>
            <div className="ml-0 sm:ml-4 mt-4 sm:mt-0 w-full">
              <div className="h-6 bg-gray-300 rounded w-1/3 mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
              <div className="flex flex-wrap gap-2">
                <div className="h-8 bg-gray-200 rounded w-20"></div>
                <div className="h-8 bg-gray-200 rounded w-20"></div>
                <div className="h-8 bg-gray-200 rounded w-20"></div>
              </div>
            </div>
          </div>
        </div>

        <div className="flex border-b border-gray-200 mb-6">
          {['overview', 'services', 'reviews', 'media'].map((tab) => (
            <div key={tab} className="h-10 bg-gray-200 rounded w-24 mr-4 mb-4"></div>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <div className="h-64 bg-gray-200 rounded-xl mb-6"></div>
            <div className="h-64 bg-gray-200 rounded-xl"></div>
          </div>
          <div className="lg:col-span-1">
            <div className="h-64 bg-gray-200 rounded-xl"></div>
          </div>
        </div>
      </div>
    );
  }

  // If error, show error message
  if (error) {
    return (
      <div className="container mx-auto px-4 py-6 max-w-6xl">
        <div className="w-full bg-red-50 border border-red-200 rounded-xl p-6 text-red-700">
          <h2 className="text-xl font-bold mb-2">{t('profile.error.title', 'Error loading profile')}</h2>
          <p className="mb-4">{error}</p>
          <button
            onClick={() => navigate(-1)}
            className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white font-medium rounded-lg transition-colors duration-200"
          >
            {t('profile.actions.goBack', 'Go Back')}
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6 max-w-6xl">
      {/* Profile Header */}
      <div className="mb-6">
        <ProfileHeader
          isCurrentUser={isCurrentUser}
          onEditProfile={handleEditProfile}
        />
      </div>

      {/* Profile Tabs */}
      <div className="flex border-b border-gray-200 mb-6 overflow-x-auto scrollbar-hide">
        <button
          className={`px-4 py-2 font-medium text-sm border-b-2 transition-colors duration-200 whitespace-nowrap ${
            activeTab === 'overview'
              ? 'border-blue-600 text-blue-600'
              : 'border-transparent text-gray-600 hover:text-gray-900 hover:border-gray-300'
          }`}
          onClick={() => setActiveTab('overview')}
        >
          {t('profile.tabs.overview', 'Overview')}
        </button>

        <button
          className={`px-4 py-2 font-medium text-sm border-b-2 transition-colors duration-200 whitespace-nowrap ${
            activeTab === 'services'
              ? 'border-blue-600 text-blue-600'
              : 'border-transparent text-gray-600 hover:text-gray-900 hover:border-gray-300'
          }`}
          onClick={() => setActiveTab('services')}
        >
          {t('profile.tabs.services', 'Services')}
        </button>

        <button
          className={`px-4 py-2 font-medium text-sm border-b-2 transition-colors duration-200 whitespace-nowrap ${
            activeTab === 'reviews'
              ? 'border-blue-600 text-blue-600'
              : 'border-transparent text-gray-600 hover:text-gray-900 hover:border-gray-300'
          }`}
          onClick={() => setActiveTab('reviews')}
        >
          {t('profile.tabs.reviews', 'Reviews')}
        </button>

        <button
          className={`px-4 py-2 font-medium text-sm border-b-2 transition-colors duration-200 whitespace-nowrap ${
            activeTab === 'media'
              ? 'border-blue-600 text-blue-600'
              : 'border-transparent text-gray-600 hover:text-gray-900 hover:border-gray-300'
          }`}
          onClick={() => setActiveTab('media')}
        >
          {t('profile.tabs.media', 'Media')}
        </button>

        {isCurrentUser && (
          <button
            className={`px-4 py-2 font-medium text-sm border-b-2 transition-colors duration-200 whitespace-nowrap ${
              activeTab === 'settings'
                ? 'border-blue-600 text-blue-600'
                : 'border-transparent text-gray-600 hover:text-gray-900 hover:border-gray-300'
            }`}
            onClick={() => setActiveTab('settings')}
          >
            {t('profile.tabs.settings', 'Settings')}
          </button>
        )}
      </div>

      {/* Profile Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Overview Tab */}
          {activeTab === 'overview' && (
            <>
              <ProfileBio
                onEdit={handleEditBio}
                isCurrentUser={isCurrentUser}
              />

              <ProfileVoiceNote
                isCurrentUser={isCurrentUser}
              />

              <ProfileServices
                isCurrentUser={isCurrentUser}
              />
            </>
          )}

          {/* Services Tab */}
          {activeTab === 'services' && (
            <ProfileServices
              isCurrentUser={isCurrentUser}
            />
          )}

          {/* Reviews Tab */}
          {activeTab === 'reviews' && (
            <div className="w-full bg-white rounded-xl overflow-hidden shadow-sm border border-gray-100 p-4 sm:p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">
                {t('profile.reviews.title', 'Reviews')}
              </h2>

              <div className="bg-gray-50 border border-gray-200 rounded-xl p-4 text-gray-700 text-center">
                <p className="font-medium">{t('profile.reviews.empty.title', 'No reviews yet')}</p>
                <p className="text-sm mt-1">
                  {t('profile.reviews.empty.message', 'Reviews will appear here once users start leaving feedback.')}
                </p>
              </div>
            </div>
          )}

          {/* Media Tab */}
          {activeTab === 'media' && (
            <div className="w-full bg-white rounded-xl overflow-hidden shadow-sm border border-gray-100 p-4 sm:p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-semibold text-gray-900">
                  {t('profile.media.title', 'Media Gallery')}
                </h2>

                {isCurrentUser && (
                  <button
                    onClick={() => navigate('/profile/media/upload')}
                    className="px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200 flex items-center"
                  >
                    <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                    </svg>
                    {t('profile.media.upload', 'Upload Media')}
                  </button>
                )}
              </div>

              <div className="bg-gray-50 border border-gray-200 rounded-xl p-4 text-gray-700 text-center">
                <p className="font-medium">{t('profile.media.empty.title', 'No media yet')}</p>
                <p className="text-sm mt-1">
                  {isCurrentUser
                    ? t('profile.media.empty.currentUser', 'Upload images or videos to showcase your gaming highlights.')
                    : t('profile.media.empty.otherUser', 'This user has not uploaded any media yet.')}
                </p>

                {isCurrentUser && (
                  <button
                    onClick={() => navigate('/profile/media/upload')}
                    className="mt-3 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200"
                  >
                    {t('profile.media.uploadFirst', 'Upload Your First Media')}
                  </button>
                )}
              </div>
            </div>
          )}

          {/* Settings Tab */}
          {activeTab === 'settings' && isCurrentUser && (
            <div className="w-full bg-white rounded-xl overflow-hidden shadow-sm border border-gray-100 p-4 sm:p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">
                {t('profile.settings.title', 'Profile Settings')}
              </h2>

              <div className="space-y-4">
                <button
                  onClick={() => navigate('/edit-profile')}
                  className="w-full flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                >
                  <div className="flex items-center">
                    <svg className="w-5 h-5 text-gray-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                    <span className="font-medium text-gray-900">{t('profile.settings.editProfile', 'Edit Profile')}</span>
                  </div>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                  </svg>
                </button>

                <button
                  onClick={() => navigate('/profile/bio/edit')}
                  className="w-full flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                >
                  <div className="flex items-center">
                    <svg className="w-5 h-5 text-gray-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                    <span className="font-medium text-gray-900">{t('profile.settings.editBio', 'Edit Biography')}</span>
                  </div>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                  </svg>
                </button>

                <button
                  onClick={() => navigate('/availability')}
                  className="w-full flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                >
                  <div className="flex items-center">
                    <svg className="w-5 h-5 text-gray-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <span className="font-medium text-gray-900">{t('profile.settings.manageAvailability', 'Manage Availability')}</span>
                  </div>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                  </svg>
                </button>

                <button
                  onClick={() => navigate('/profile/services')}
                  className="w-full flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                >
                  <div className="flex items-center">
                    <svg className="w-5 h-5 text-gray-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                    <span className="font-medium text-gray-900">{t('profile.settings.manageServices', 'Manage Services')}</span>
                  </div>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                  </svg>
                </button>

                <button
                  onClick={() => navigate('/profile/privacy')}
                  className="w-full flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                >
                  <div className="flex items-center">
                    <svg className="w-5 h-5 text-gray-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                    <span className="font-medium text-gray-900">{t('profile.settings.privacySettings', 'Privacy Settings')}</span>
                  </div>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                  </svg>
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Sidebar */}
        <div className="lg:col-span-1">
          <ProfileStats />
        </div>
      </div>
    </div>
  );
};

export default ProfilePage;
