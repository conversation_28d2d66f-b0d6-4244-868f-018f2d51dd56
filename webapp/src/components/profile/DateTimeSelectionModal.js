import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import AvailabilityCalendar from '../availability/AvailabilityCalendar';

/**
 * DateTimeSelectionModal component
 *
 * This modal allows users to select a date and time for a service.
 *
 * @param {Object} props
 * @param {boolean} props.isOpen - Whether the modal is open
 * @param {Function} props.onClose - Function to call when the modal is closed
 * @param {Object} props.skill - The skill/service being ordered
 * @param {Object} props.tier - The selected service tier
 * @param {Function} props.onContinue - Function to call when date/time is selected and user wants to continue
 * @param {Object} props.talent - The talent providing the service
 */
const DateTimeSelectionModal = ({
  isOpen,
  onClose,
  skill,
  tier,
  onContinue,
  talent
}) => {
  const [selectedDate, setSelectedDate] = useState(null);
  const [selectedTime, setSelectedTime] = useState(null);
  const [availableTimeSlots, setAvailableTimeSlots] = useState([]);
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [calendarDays, setCalendarDays] = useState([]);

  // Generate calendar days when month changes
  useEffect(() => {
    if (currentMonth) {
      generateCalendarDays(currentMonth);
    }
  }, [currentMonth]);

  // Generate time slots when date changes
  useEffect(() => {
    if (selectedDate) {
      generateTimeSlots(selectedDate);
    } else {
      setAvailableTimeSlots([]);
      setSelectedTime(null);
    }
  }, [selectedDate]);

  // Generate calendar days for the current month
  const generateCalendarDays = (date) => {
    const year = date.getFullYear();
    const month = date.getMonth();

    // Get the first day of the month
    const firstDay = new Date(year, month, 1);
    // Get the last day of the month
    const lastDay = new Date(year, month + 1, 0);

    // Get the day of the week for the first day (0 = Sunday, 6 = Saturday)
    const firstDayOfWeek = firstDay.getDay();

    // Calculate days from previous month to show
    const daysFromPrevMonth = firstDayOfWeek;

    // Calculate total days to show (previous month + current month + next month)
    const totalDays = 42; // 6 rows of 7 days

    // Generate array of calendar days
    const days = [];

    // Add days from previous month
    const prevMonth = new Date(year, month, 0);
    const prevMonthDays = prevMonth.getDate();

    for (let i = prevMonthDays - daysFromPrevMonth + 1; i <= prevMonthDays; i++) {
      days.push({
        date: new Date(year, month - 1, i),
        day: i,
        isCurrentMonth: false,
        isToday: false,
        isSelectable: false
      });
    }

    // Add days from current month
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    for (let i = 1; i <= lastDay.getDate(); i++) {
      const date = new Date(year, month, i);
      date.setHours(0, 0, 0, 0);

      // Check if date is today
      const isToday = date.getTime() === today.getTime();

      // Check if date is selectable (not in the past)
      const isSelectable = date.getTime() >= today.getTime();

      days.push({
        date,
        day: i,
        isCurrentMonth: true,
        isToday,
        isSelectable
      });
    }

    // Add days from next month
    const remainingDays = totalDays - days.length;

    for (let i = 1; i <= remainingDays; i++) {
      days.push({
        date: new Date(year, month + 1, i),
        day: i,
        isCurrentMonth: false,
        isToday: false,
        isSelectable: false
      });
    }

    setCalendarDays(days);
  };

  // Generate available time slots for the selected date
  const generateTimeSlots = (date) => {
    // In a real app, this would fetch available time slots from the API
    // For now, we'll generate some mock time slots

    const slots = [];
    const startHour = 9; // 9 AM
    const endHour = 21; // 9 PM

    for (let hour = startHour; hour <= endHour; hour++) {
      // Add slots for each hour (e.g., 9:00, 9:30, 10:00, etc.)
      const isAvailable = Math.random() > 0.3; // 70% chance of being available

      slots.push({
        time: `${hour}:00`,
        hour,
        minute: 0,
        isAvailable
      });

      // Add half-hour slots
      const isHalfHourAvailable = Math.random() > 0.3;

      slots.push({
        time: `${hour}:30`,
        hour,
        minute: 30,
        isAvailable: isHalfHourAvailable
      });
    }

    setAvailableTimeSlots(slots);
  };

  // Handle date selection
  const handleDateSelect = (day) => {
    if (day.isSelectable) {
      setSelectedDate(day.date);
    }
  };

  // Handle time selection
  const handleTimeSelect = (timeSlot) => {
    if (timeSlot.isAvailable) {
      setSelectedTime(timeSlot);
    }
  };

  // Handle continue button click
  const handleContinue = () => {
    if (selectedDate && selectedTime && onContinue) {
      // Create a date object with the selected date and time
      const dateTime = new Date(selectedDate);
      dateTime.setHours(selectedTime.hour, selectedTime.minute, 0, 0);

      onContinue(skill, tier, dateTime);
    }
  };

  // Handle previous month button click
  const handlePrevMonth = () => {
    const prevMonth = new Date(currentMonth);
    prevMonth.setMonth(prevMonth.getMonth() - 1);
    setCurrentMonth(prevMonth);
  };

  // Handle next month button click
  const handleNextMonth = () => {
    const nextMonth = new Date(currentMonth);
    nextMonth.setMonth(nextMonth.getMonth() + 1);
    setCurrentMonth(nextMonth);
  };

  // Format date for display
  const formatDate = (date) => {
    if (!date) return '';

    const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
    return date.toLocaleDateString(undefined, options);
  };

  // Format month for display
  const formatMonth = (date) => {
    if (!date) return '';

    const options = { year: 'numeric', month: 'long' };
    return date.toLocaleDateString(undefined, options);
  };

  // Modal animation variants
  const backdropVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 },
    exit: { opacity: 0 }
  };

  const modalVariants = {
    hidden: { opacity: 0, y: 50, scale: 0.95 },
    visible: { opacity: 1, y: 0, scale: 1, transition: { duration: 0.3 } },
    exit: { opacity: 0, y: 50, scale: 0.95, transition: { duration: 0.2 } }
  };

  // If modal is not open or no skill/tier is provided, don't render anything
  if (!isOpen || !skill || !tier) return null;

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
          initial="hidden"
          animate="visible"
          exit="exit"
          variants={backdropVariants}
          onClick={onClose}
        >
          <motion.div
            className="bg-white rounded-xl w-full max-w-lg overflow-hidden"
            variants={modalVariants}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
              <h2 className="text-xl font-bold text-gray-900">Select Date & Time</h2>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* Content */}
            <div className="px-6 py-4">
              {/* Service Info */}
              <div className="mb-4 p-3 bg-indigo-50 rounded-lg">
                <div className="flex items-center">
                  <div className="w-10 h-10 rounded-full bg-indigo-100 flex items-center justify-center mr-3">
                    {skill.icon ? (
                      <img src={skill.icon} alt={skill.name} className="w-6 h-6" />
                    ) : (
                      <svg className="w-5 h-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    )}
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-900">{skill.name} - {tier.name}</h3>
                    <p className="text-sm text-gray-600">{tier.price} credits / {tier.unit || 'round'}</p>
                  </div>
                </div>
              </div>

              {/* Availability Calendar */}
              <AvailabilityCalendar
                talent={talent}
                onSelectTimeSlot={(dateTime) => {
                  // Create a date object with the selected date and time
                  if (dateTime) {
                    // Extract the date and time
                    const selectedDate = new Date(dateTime);
                    const hour = selectedDate.getHours();
                    const minute = selectedDate.getMinutes();

                    // Create a time slot object
                    const timeSlot = {
                      time: `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`,
                      hour,
                      minute,
                      isAvailable: true
                    };

                    // Set the selected date and time
                    setSelectedDate(selectedDate);
                    setSelectedTime(timeSlot);
                  }
                }}
                readOnly={false}
              />
            </div>

            {/* Footer */}
            <div className="px-6 py-4 border-t border-gray-200 flex justify-between">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleContinue}
                disabled={!selectedDate || !selectedTime}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Continue
              </button>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default DateTimeSelectionModal;
