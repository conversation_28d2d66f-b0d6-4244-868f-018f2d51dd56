import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import useTranslation from '../../hooks/useTranslation';

/**
 * ServiceDeleteConfirmationModal component
 * 
 * A modal for confirming service deletion.
 * 
 * @param {Object} props
 * @param {boolean} props.isOpen - Whether the modal is open
 * @param {Function} props.onClose - Function called when modal is closed
 * @param {Object} props.service - Service to delete
 * @param {Function} props.onConfirm - Function called when deletion is confirmed
 * @param {boolean} props.isLoading - Whether the deletion is in progress
 * @param {string} props.error - Error message to display
 */
const ServiceDeleteConfirmationModal = ({
  isOpen,
  onClose,
  service,
  onConfirm,
  isLoading = false,
  error = null
}) => {
  const { t } = useTranslation('profile');
  const [isClosing, setIsClosing] = useState(false);
  
  // <PERSON>le escape key press
  useEffect(() => {
    const handleEscapeKey = (e) => {
      if (e.key === 'Escape' && isOpen && !isLoading) {
        handleClose();
      }
    };
    
    document.addEventListener('keydown', handleEscapeKey);
    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [isOpen, isLoading]);
  
  // Handle close with animation
  const handleClose = () => {
    if (isLoading) return;
    
    setIsClosing(true);
    setTimeout(() => {
      setIsClosing(false);
      onClose();
    }, 300);
  };
  
  // Handle confirm
  const handleConfirm = () => {
    if (isLoading) return;
    onConfirm(service.id);
  };
  
  // Modal animation variants
  const backdropVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 },
    exit: { opacity: 0 }
  };
  
  const modalVariants = {
    hidden: { opacity: 0, y: 50, scale: 0.95 },
    visible: { opacity: 1, y: 0, scale: 1, transition: { duration: 0.3 } },
    exit: { opacity: 0, y: 50, scale: 0.95, transition: { duration: 0.2 } }
  };
  
  if (!isOpen && !isClosing || !service) return null;
  
  return (
    <AnimatePresence>
      <motion.div
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
        initial="hidden"
        animate="visible"
        exit="exit"
        variants={backdropVariants}
        onClick={handleClose}
      >
        <motion.div
          className="bg-white rounded-xl w-full max-w-md overflow-hidden"
          variants={modalVariants}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h2 className="text-xl font-bold text-gray-900">
              {t('services.delete.title', 'Delete Service')}
            </h2>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50"
              disabled={isLoading}
              aria-label={t('services.delete.close', 'Close')}
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          {/* Content */}
          <div className="px-6 py-4">
            {error && (
              <div className="mb-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                {error}
              </div>
            )}
            
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 flex-shrink-0 bg-red-100 rounded-full flex items-center justify-center text-red-600 mr-4">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-900">
                  {t('services.delete.confirmTitle', 'Confirm Deletion')}
                </h3>
                <p className="text-sm text-gray-500">
                  {t('services.delete.confirmText', 'This action cannot be undone.')}
                </p>
              </div>
            </div>
            
            <p className="mb-4">
              {t('services.delete.message', 'Are you sure you want to delete the following service?')}
            </p>
            
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-4">
              <h4 className="font-medium text-gray-900">{service.title}</h4>
              <p className="text-sm text-gray-600 mt-1 line-clamp-2">{service.description}</p>
              
              {service.service_type && (
                <div className="mt-2 flex items-center">
                  <span className="text-xs font-medium text-gray-500 mr-2">
                    {t('services.delete.type', 'Type:')}
                  </span>
                  <span className="text-xs font-medium bg-blue-100 text-blue-800 px-2 py-0.5 rounded">
                    {service.service_type.name}
                  </span>
                </div>
              )}
              
              {service.status && (
                <div className="mt-2 flex items-center">
                  <span className="text-xs font-medium text-gray-500 mr-2">
                    {t('services.delete.status', 'Status:')}
                  </span>
                  <span className={`text-xs font-medium px-2 py-0.5 rounded ${
                    service.status === 'approved' 
                      ? 'bg-green-100 text-green-800' 
                      : service.status === 'pending' 
                        ? 'bg-yellow-100 text-yellow-800' 
                        : 'bg-red-100 text-red-800'
                  }`}>
                    {service.status.charAt(0).toUpperCase() + service.status.slice(1)}
                  </span>
                </div>
              )}
            </div>
          </div>
          
          {/* Footer */}
          <div className="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
              disabled={isLoading}
            >
              {t('services.delete.cancel', 'Cancel')}
            </button>
            <button
              type="button"
              onClick={handleConfirm}
              className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
              disabled={isLoading}
            >
              {isLoading ? (
                <span className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  {t('services.delete.deleting', 'Deleting...')}
                </span>
              ) : (
                t('services.delete.confirm', 'Delete Service')
              )}
            </button>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default ServiceDeleteConfirmationModal;
