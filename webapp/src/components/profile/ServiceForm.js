import React, { useState, useEffect } from 'react';
import useTranslation from '../../hooks/useTranslation';

/**
 * ServiceForm component
 
 * A form for creating and editing user services.
 *
 * @param {Object} props
 * @param {Object} props.initialValues - Initial form values
 * @param {Function} props.onSubmit - Function called when form is submitted
 * @param {Function} props.onCancel - Function called when form is cancelled
 * @param {Array} props.serviceCategories - Available service categories
 * @param {Array} props.serviceTypes - Available service types
 * @param {Array} props.serviceStyles - Available service styles
 * @param {Array} props.pricingOptionTypes - Available pricing option types
 * @param {boolean} props.isLoading - Whether the form is in a loading state
 * @param {string} props.error - Error message to display
 */
const ServiceForm = ({
  initialValues = {},
  onSubmit,
  onCancel,
  serviceCategories = [],
  serviceTypes = [],
  serviceStyles = [],
  pricingOptionTypes = [],
  isLoading = false,
  error = null
}) => {
  const { t } = useTranslation('profile');
  const [selectedStyles, setSelectedStyles] = useState([]);
  const [formValues, setFormValues] = useState({
    id: initialValues.id || null,
    service_category_id: initialValues.service_category_id || '',
    service_type_id: initialValues.service_type_id || '',
    pricing_option_id: initialValues.pricing_option_id || '',
    title: initialValues.title || '',
    description: initialValues.description || '',
    service_type_title: initialValues.service_type_title || '',
    service_type_description: initialValues.service_type_description || '',
    price: initialValues.price || '',
    service_elements: initialValues.service_elements || [],
    service_style: initialValues.service_style || []
  });
  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});

  // Handle form input changes
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormValues({
      ...formValues,
      [name]: value
    });

    // Mark field as touched
    if (!touched[name]) {
      setTouched({
        ...touched,
        [name]: true
      });
    }

    // Validate field
    validateField(name, value);
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();

    // Mark all fields as touched
    const allTouched = Object.keys(formValues).reduce((acc, key) => {
      acc[key] = true;
      return acc;
    }, {});
    setTouched(allTouched);

    // Validate all fields
    const formErrors = validateForm();

    if (Object.keys(formErrors).length === 0) {
      // Format service_elements as an array if it's a string
      let formattedValues = { ...formValues };

      if (typeof formattedValues.service_elements === 'string') {
        formattedValues.service_elements = formattedValues.service_elements
          .split('\n')
          .map(item => item.trim())
          .filter(item => item !== '');
      }

      // Add selected styles if category is Gaming
      if (formattedValues.service_category_id === 1) {
        formattedValues.service_style = selectedStyles.map(style => ({
          service_style_id: style.id,
          is_active: true,
          price: style.price || null
        }));
      }

      onSubmit(formattedValues);
    }
  };

  // Validate a single field
  const validateField = (name, value) => {
    let fieldErrors = {};

    switch (name) {
      case 'service_category_id':
        if (!value) {
          fieldErrors[name] = t('services.form.validation.categoryRequired', 'Category is required');
        }
        break;

      case 'service_type_id':
        if (formValues.service_category_id === 1 && !value) {
          fieldErrors[name] = t('services.form.validation.typeRequired', 'Service type is required');
        }
        break;

      case 'pricing_option_id':
        if (!value) {
          fieldErrors[name] = t('services.form.validation.pricingRequired', 'Pricing option is required');
        }
        break;

      case 'title':
        if (!value) {
          fieldErrors[name] = t('services.form.validation.titleRequired', 'Title is required');
        } else if (value.length > 255) {
          fieldErrors[name] = t('services.form.validation.titleTooLong', 'Title must be less than 255 characters');
        }
        break;

      case 'description':
        if (!value) {
          fieldErrors[name] = t('services.form.validation.descriptionRequired', 'Description is required');
        }
        break;

      case 'service_type_title':
        if (formValues.service_category_id === 2 && !value) {
          fieldErrors[name] = t('services.form.validation.serviceTitleRequired', 'Service type title is required');
        } else if (value && value.length > 255) {
          fieldErrors[name] = t('services.form.validation.titleTooLong', 'Title must be less than 255 characters');
        }
        break;

      case 'service_type_description':
        if (formValues.service_category_id === 2 && !value) {
          fieldErrors[name] = t('services.form.validation.serviceDescriptionRequired', 'Service type description is required');
        }
        break;

      case 'price':
        if (formValues.service_category_id === 2) {
          if (!value && value !== 0) {
            fieldErrors[name] = t('services.form.validation.priceRequired', 'Price is required');
          } else if (value < 0) {
            fieldErrors[name] = t('services.form.validation.pricePositive', 'Price must be positive');
          }
        }
        break;

      default:
        break;
    }

    setErrors({
      ...errors,
      ...fieldErrors
    });

    return fieldErrors;
  };

  // Validate the entire form
  const validateForm = () => {
    const formErrors = {};

    // Validate each field
    Object.keys(formValues).forEach(key => {
      const fieldErrors = validateField(key, formValues[key]);
      Object.assign(formErrors, fieldErrors);
    });

    // Additional validation for selected styles
    if (formValues.service_category_id === 1 && selectedStyles.length === 0) {
      formErrors.service_style = t('services.form.validation.styleRequired', 'At least one style must be selected');
    }

    setErrors(formErrors);
    return formErrors;
  };

  // Initialize selected styles from initial values
  useEffect(() => {
    if (initialValues.service_style && initialValues.service_style.length > 0) {
      const styles = initialValues.service_style.map(style => {
        const fullStyle = serviceStyles.find(s => s.id === style.service_style_id);
        return {
          ...fullStyle,
          price: style.price
        };
      });
      setSelectedStyles(styles);
    }
  }, [initialValues.service_style, serviceStyles]);

  // Handle service category change
  const handleCategoryChange = (e) => {
    const categoryId = parseInt(e.target.value);

    // Update form values
    setFormValues({
      ...formValues,
      service_category_id: categoryId,
      // Reset related fields when category changes
      ...(categoryId === 1 ? {
        service_type_title: '',
        service_type_description: '',
        price: ''
      } : {}),
      ...(categoryId === 2 ? {
        service_type_id: ''
      } : {})
    });

    // Mark field as touched
    setTouched({
      ...touched,
      service_category_id: true
    });

    // Validate field
    validateField('service_category_id', categoryId);

    // Reset selected styles if switching to Talent category
    if (categoryId === 2) {
      setSelectedStyles([]);
    }
  };

  // Handle style selection
  const handleStyleSelect = (style) => {
    const isSelected = selectedStyles.some(s => s.id === style.id);

    if (isSelected) {
      setSelectedStyles(selectedStyles.filter(s => s.id !== style.id));
    } else {
      setSelectedStyles([...selectedStyles, { ...style, price: style.preset_price || null }]);
    }
  };

  // Handle style price change
  const handleStylePriceChange = (styleId, price) => {
    setSelectedStyles(
      selectedStyles.map(style =>
        style.id === styleId
          ? { ...style, price: price === '' ? '' : parseInt(price) }
          : style
      )
    );
  };

  // Format service elements for display
  const formatServiceElements = () => {
    if (Array.isArray(formValues.service_elements)) {
      return formValues.service_elements.join('\n');
    }
    return formValues.service_elements || '';
  };

  // Handle blur event
  const handleBlur = (e) => {
    const { name } = e.target;
    setTouched({
      ...touched,
      [name]: true
    });
    validateField(name, formValues[name]);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          {error}
        </div>
      )}

      {/* Service Category */}
      <div>
        <label htmlFor="service_category_id" className="block text-sm font-medium text-gray-700 mb-1">
          {t('services.form.category', 'Service Category')} *
        </label>
        <select
          id="service_category_id"
          name="service_category_id"
          className={`block w-full rounded-lg border ${
            touched.service_category_id && errors.service_category_id
              ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
              : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'
          } px-3 py-2 shadow-sm focus:outline-none focus:ring-1`}
          value={formValues.service_category_id}
          onChange={handleCategoryChange}
          onBlur={handleBlur}
          disabled={isLoading}
        >
          <option value="">{t('services.form.selectCategory', 'Select a category')}</option>
          {serviceCategories.map(category => (
            <option key={category.id} value={category.id}>{category.name}</option>
          ))}
        </select>
        {touched.service_category_id && errors.service_category_id && (
          <p className="mt-1 text-sm text-red-600">{errors.service_category_id}</p>
        )}
      </div>

      {/* Service Type (for Gaming category) */}
      {formValues.service_category_id === 1 && (
        <div>
          <label htmlFor="service_type_id" className="block text-sm font-medium text-gray-700 mb-1">
            {t('services.form.type', 'Service Type')} *
          </label>
          <select
            id="service_type_id"
            name="service_type_id"
            className={`block w-full rounded-lg border ${
              touched.service_type_id && errors.service_type_id
                ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'
            } px-3 py-2 shadow-sm focus:outline-none focus:ring-1`}
            value={formValues.service_type_id}
            onChange={handleChange}
            onBlur={handleBlur}
            disabled={isLoading}
          >
            <option value="">{t('services.form.selectType', 'Select a type')}</option>
            {serviceTypes.filter(type => type.service_category_id === 1).map(type => (
              <option key={type.id} value={type.id}>{type.name}</option>
            ))}
          </select>
          {touched.service_type_id && errors.service_type_id && (
            <p className="mt-1 text-sm text-red-600">{errors.service_type_id}</p>
          )}
        </div>
      )}

      {/* Custom Service Type (for Talent category) */}
      {formValues.service_category_id === 2 && (
        <>
          <div>
            <label htmlFor="service_type_title" className="block text-sm font-medium text-gray-700 mb-1">
              {t('services.form.customTypeTitle', 'Custom Service Type Title')} *
            </label>
            <input
              type="text"
              id="service_type_title"
              name="service_type_title"
              className={`block w-full rounded-lg border ${
                touched.service_type_title && errors.service_type_title
                  ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                  : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'
              } px-3 py-2 shadow-sm focus:outline-none focus:ring-1`}
              value={formValues.service_type_title}
              onChange={handleChange}
              onBlur={handleBlur}
              disabled={isLoading}
            />
            {touched.service_type_title && errors.service_type_title && (
              <p className="mt-1 text-sm text-red-600">{errors.service_type_title}</p>
            )}
          </div>

          <div>
            <label htmlFor="service_type_description" className="block text-sm font-medium text-gray-700 mb-1">
              {t('services.form.customTypeDescription', 'Custom Service Type Description')} *
            </label>
            <textarea
              id="service_type_description"
              name="service_type_description"
              rows="3"
              className={`block w-full rounded-lg border ${
                touched.service_type_description && errors.service_type_description
                  ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                  : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'
              } px-3 py-2 shadow-sm focus:outline-none focus:ring-1`}
              value={formValues.service_type_description}
              onChange={handleChange}
              onBlur={handleBlur}
              disabled={isLoading}
            />
            {touched.service_type_description && errors.service_type_description && (
              <p className="mt-1 text-sm text-red-600">{errors.service_type_description}</p>
            )}
          </div>

          <div>
            <label htmlFor="price" className="block text-sm font-medium text-gray-700 mb-1">
              {t('services.form.price', 'Price')} *
            </label>
            <div className="relative">
              <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                <span className="text-gray-500 sm:text-sm">₩</span>
              </div>
              <input
                type="number"
                id="price"
                name="price"
                min="0"
                className={`block w-full rounded-lg border ${
                  touched.price && errors.price
                    ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                    : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'
                } pl-7 pr-12 py-2 shadow-sm focus:outline-none focus:ring-1`}
                value={formValues.price}
                onChange={handleChange}
                onBlur={handleBlur}
                disabled={isLoading}
              />
              <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                <span className="text-gray-500 sm:text-sm">/hr</span>
              </div>
            </div>
            {touched.price && errors.price && (
              <p className="mt-1 text-sm text-red-600">{errors.price}</p>
            )}
          </div>
        </>
      )}

      {/* Service Styles (for Gaming category) */}
      {formValues.service_category_id === 1 && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {t('services.form.styles', 'Service Styles')} *
          </label>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {serviceStyles.filter(style => style.service_type_id === parseInt(formValues.service_type_id)).map(style => {
              const isSelected = selectedStyles.some(s => s.id === style.id);
              const selectedStyle = selectedStyles.find(s => s.id === style.id);

              return (
                <div
                  key={style.id}
                  className={`border rounded-lg p-4 cursor-pointer transition-all ${
                    isSelected
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-blue-300'
                  }`}
                  onClick={() => handleStyleSelect(style)}
                >
                  <div className="flex justify-between items-center">
                    <div>
                      <h5 className="font-medium text-gray-900">{style.name}</h5>
                      {style.description && (
                        <p className="text-sm text-gray-600 mt-1">{style.description}</p>
                      )}
                    </div>
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        checked={isSelected}
                        onChange={() => {}}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    </div>
                  </div>

                  {isSelected && (
                    <div className="mt-3 pt-3 border-t border-gray-200">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        {t('services.form.stylePrice', 'Price for this style')}
                      </label>
                      <div className="relative">
                        <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                          <span className="text-gray-500 sm:text-sm">₩</span>
                        </div>
                        <input
                          type="number"
                          min="0"
                          className="block w-full rounded-lg border border-gray-300 pl-7 pr-12 py-2 shadow-sm focus:outline-none focus:ring-1 focus:border-blue-500 focus:ring-blue-500"
                          value={selectedStyle?.price || ''}
                          onChange={(e) => {
                            e.stopPropagation();
                            handleStylePriceChange(style.id, e.target.value);
                          }}
                          onClick={(e) => e.stopPropagation()}
                          disabled={isLoading}
                        />
                        <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                          <span className="text-gray-500 sm:text-sm">/hr</span>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
          {formValues.service_type_id && serviceStyles.filter(style => style.service_type_id === parseInt(formValues.service_type_id)).length === 0 && (
            <p className="text-sm text-gray-500 mt-2">{t('services.form.noStyles', 'No styles available for this service type')}</p>
          )}
          {!formValues.service_type_id && (
            <p className="text-sm text-gray-500 mt-2">{t('services.form.selectTypeFirst', 'Select a service type to see available styles')}</p>
          )}
          {selectedStyles.length === 0 && touched.service_style && errors.service_style && (
            <p className="mt-1 text-sm text-red-600">{errors.service_style}</p>
          )}
        </div>
      )}

      {/* Pricing Option */}
      <div>
        <label htmlFor="pricing_option_id" className="block text-sm font-medium text-gray-700 mb-1">
          {t('services.form.pricingOption', 'Pricing Option')} *
        </label>
        <select
          id="pricing_option_id"
          name="pricing_option_id"
          className={`block w-full rounded-lg border ${
            touched.pricing_option_id && errors.pricing_option_id
              ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
              : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'
          } px-3 py-2 shadow-sm focus:outline-none focus:ring-1`}
          value={formValues.pricing_option_id}
          onChange={handleChange}
          onBlur={handleBlur}
          disabled={isLoading}
        >
          <option value="">{t('services.form.selectPricingOption', 'Select a pricing option')}</option>
          {pricingOptionTypes.map(option => (
            <option key={option.id} value={option.id}>{option.name}</option>
          ))}
        </select>
        {touched.pricing_option_id && errors.pricing_option_id && (
          <p className="mt-1 text-sm text-red-600">{errors.pricing_option_id}</p>
        )}
      </div>

      {/* Service Title */}
      <div>
        <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
          {t('services.form.title', 'Service Title')} *
        </label>
        <input
          type="text"
          id="title"
          name="title"
          className={`block w-full rounded-lg border ${
            touched.title && errors.title
              ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
              : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'
          } px-3 py-2 shadow-sm focus:outline-none focus:ring-1`}
          value={formValues.title}
          onChange={handleChange}
          onBlur={handleBlur}
          disabled={isLoading}
        />
        {touched.title && errors.title && (
          <p className="mt-1 text-sm text-red-600">{errors.title}</p>
        )}
      </div>

      {/* Service Description */}
      <div>
        <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
          {t('services.form.description', 'Service Description')} *
        </label>
        <textarea
          id="description"
          name="description"
          rows="4"
          className={`block w-full rounded-lg border ${
            touched.description && errors.description
              ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
              : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'
          } px-3 py-2 shadow-sm focus:outline-none focus:ring-1`}
          value={formValues.description}
          onChange={handleChange}
          onBlur={handleBlur}
          disabled={isLoading}
        />
        {touched.description && errors.description && (
          <p className="mt-1 text-sm text-red-600">{errors.description}</p>
        )}
      </div>

      {/* Service Elements */}
      <div>
        <label htmlFor="service_elements" className="block text-sm font-medium text-gray-700 mb-1">
          {t('services.form.elements', 'Service Elements')} <span className="text-gray-500 text-xs">{t('services.form.optional', '(Optional)')}</span>
        </label>
        <p className="text-xs text-gray-500 mb-2">{t('services.form.elementsHelp', 'Enter each element on a new line')}</p>
        <textarea
          id="service_elements"
          name="service_elements"
          rows="4"
          className="block w-full rounded-lg border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
          value={formatServiceElements()}
          onChange={(e) => setFormValues({
            ...formValues,
            service_elements: e.target.value
          })}
          onBlur={handleBlur}
          disabled={isLoading}
          placeholder={t('services.form.elementsPlaceholder', 'e.g.\nStrategy Development\nAim Training\nGame Sense Improvement')}
        />
      </div>

      {/* Form Actions */}
      <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          disabled={isLoading}
        >
          {t('services.form.cancel', 'Cancel')}
        </button>
        <button
          type="submit"
          className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          disabled={isLoading}
        >
          {isLoading ? (
            <span className="flex items-center">
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {t('services.form.saving', 'Saving...')}
            </span>
          ) : (
            initialValues.id
              ? t('services.form.update', 'Update Service')
              : t('services.form.create', 'Create Service')
          )}
        </button>
      </div>
    </form>
  );
};

export default ServiceForm;
