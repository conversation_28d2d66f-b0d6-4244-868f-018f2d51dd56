import React, { useState, useEffect } from 'react';

/**
 * TabContent component serves as a container for the active tab content
 * It provides a consistent layout and styling for all tab content
 * with smooth transitions between tabs
 */
const TabContent = ({ activeTab, children }) => {
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [displayedChildren, setDisplayedChildren] = useState(children);

  // Handle tab changes with animation
  useEffect(() => {
    // Start transition
    setIsTransitioning(true);

    // After a short delay, update the displayed content
    const timer = setTimeout(() => {
      setDisplayedChildren(children);
      setIsTransitioning(false);
    }, 300); // Match this with the CSS transition duration

    return () => clearTimeout(timer);
  }, [activeTab, children]);

  return (
    <div className="bg-gray-50 min-h-[300px] pb-20 md:pb-4">
      <div
        className={`p-4 transition-opacity duration-300 bg-transparent${
          isTransitioning ? 'opacity-0' : 'opacity-100'
        }`}
      >
        {displayedChildren}
      </div>
    </div>
  );
};

export default TabContent;
