/**
 * Profile Edit Form Component
 * 
 * This component provides a form for editing the user's profile information.
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useProfile } from '../../contexts/ProfileContext';
import useTranslation from '../../hooks/useTranslation';

/**
 * ProfileEditForm component
 */
const ProfileEditForm = () => {
  // Get profile context
  const { profile, loading, error, updateProfile } = useProfile();
  
  // Navigation
  const navigate = useNavigate();
  
  // Use the translation hook
  const { t } = useTranslation('profile');
  
  // Form state
  const [formData, setFormData] = useState({
    first_name: '',
    last_name: '',
    nickname: '',
    email: '',
    gender: '',
    date_of_birth: '',
    avatar_url: '',
    cover_image_url: ''
  });
  
  // Form submission state
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState('');
  const [submitSuccess, setSubmitSuccess] = useState(false);
  
  // Avatar preview
  const [avatarPreview, setAvatarPreview] = useState('');
  const [avatarFile, setAvatarFile] = useState(null);
  
  // Cover image preview
  const [coverPreview, setCoverPreview] = useState('');
  const [coverFile, setCoverFile] = useState(null);
  
  // Initialize form data when profile is loaded
  useEffect(() => {
    if (profile) {
      setFormData({
        first_name: profile.first_name || '',
        last_name: profile.last_name || '',
        nickname: profile.nickname || '',
        email: profile.email || '',
        gender: profile.gender || '',
        date_of_birth: profile.date_of_birth || '',
        avatar_url: profile.avatar_url || '',
        cover_image_url: profile.cover_image_url || ''
      });
      
      if (profile.avatar_url) {
        setAvatarPreview(profile.avatar_url);
      }
      
      if (profile.cover_image_url) {
        setCoverPreview(profile.cover_image_url);
      }
    }
  }, [profile]);
  
  // Handle form input changes
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  // Handle avatar file selection
  const handleAvatarChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setAvatarFile(file);
      
      // Create preview URL
      const reader = new FileReader();
      reader.onloadend = () => {
        setAvatarPreview(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };
  
  // Handle cover image file selection
  const handleCoverChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setCoverFile(file);
      
      // Create preview URL
      const reader = new FileReader();
      reader.onloadend = () => {
        setCoverPreview(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };
  
  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    setIsSubmitting(true);
    setSubmitError('');
    setSubmitSuccess(false);
    
    try {
      // TODO: Handle file uploads for avatar and cover image
      // For now, we'll just update the profile data
      
      const response = await updateProfile(formData);
      
      if (response.success) {
        setSubmitSuccess(true);
        
        // Redirect back to profile page after a short delay
        setTimeout(() => {
          navigate('/profile');
        }, 1500);
      } else {
        setSubmitError(response.error || t('profile.edit.error', 'Failed to update profile'));
      }
    } catch (err) {
      console.error('Profile update error:', err);
      setSubmitError(err.message || t('profile.edit.error', 'Failed to update profile'));
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // If loading, show skeleton
  if (loading && !profile) {
    return (
      <div className="w-full bg-gray-100 rounded-xl overflow-hidden p-6 animate-pulse">
        <div className="h-8 bg-gray-300 rounded w-1/3 mb-6"></div>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 mb-6">
          {[...Array(6)].map((_, index) => (
            <div key={index}>
              <div className="h-5 bg-gray-300 rounded w-1/3 mb-2"></div>
              <div className="h-10 bg-gray-200 rounded w-full"></div>
            </div>
          ))}
        </div>
        <div className="h-10 bg-gray-200 rounded w-32 mt-6"></div>
      </div>
    );
  }
  
  return (
    <div className="w-full bg-white rounded-xl overflow-hidden shadow-sm border border-gray-100 p-4 sm:p-6">
      <h2 className="text-xl font-bold text-gray-900 mb-6">
        {t('profile.edit.title', 'Edit Profile')}
      </h2>
      
      {submitError && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6 text-red-700">
          <p className="font-medium">{t('profile.edit.errorTitle', 'Error')}</p>
          <p className="text-sm mt-1">{submitError}</p>
        </div>
      )}
      
      {submitSuccess && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6 text-green-700">
          <p className="font-medium">{t('profile.edit.successTitle', 'Success')}</p>
          <p className="text-sm mt-1">{t('profile.edit.successMessage', 'Your profile has been updated successfully.')}</p>
        </div>
      )}
      
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Avatar and Cover Image */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          {/* Avatar Upload */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t('profile.edit.avatar', 'Profile Picture')}
            </label>
            <div className="flex items-center space-x-4">
              <div className="w-20 h-20 rounded-full overflow-hidden bg-gray-100 border border-gray-300">
                {avatarPreview ? (
                  <img 
                    src={avatarPreview} 
                    alt="Avatar preview" 
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-400 to-indigo-500 text-white text-2xl font-bold">
                    {formData.nickname?.charAt(0).toUpperCase() || formData.first_name?.charAt(0).toUpperCase() || '?'}
                  </div>
                )}
              </div>
              <div>
                <label className="block">
                  <span className="sr-only">{t('profile.edit.chooseAvatar', 'Choose profile picture')}</span>
                  <input 
                    type="file" 
                    accept="image/*"
                    onChange={handleAvatarChange}
                    className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                  />
                </label>
                <p className="text-xs text-gray-500 mt-1">
                  {t('profile.edit.avatarRequirements', 'PNG, JPG or GIF (max. 2MB)')}
                </p>
              </div>
            </div>
          </div>
          
          {/* Cover Image Upload */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t('profile.edit.coverImage', 'Cover Image')}
            </label>
            <div className="space-y-2">
              <div className="h-32 rounded-lg overflow-hidden bg-gray-100 border border-gray-300">
                {coverPreview ? (
                  <img 
                    src={coverPreview} 
                    alt="Cover preview" 
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center bg-gradient-to-r from-blue-500 to-indigo-600 text-white text-lg font-medium">
                    {t('profile.edit.noCover', 'No cover image')}
                  </div>
                )}
              </div>
              <label className="block">
                <span className="sr-only">{t('profile.edit.chooseCover', 'Choose cover image')}</span>
                <input 
                  type="file" 
                  accept="image/*"
                  onChange={handleCoverChange}
                  className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                />
              </label>
              <p className="text-xs text-gray-500">
                {t('profile.edit.coverRequirements', 'PNG or JPG (recommended: 1200x400px, max. 5MB)')}
              </p>
            </div>
          </div>
        </div>
        
        {/* Basic Information */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
          {/* First Name */}
          <div>
            <label htmlFor="first_name" className="block text-sm font-medium text-gray-700 mb-1">
              {t('profile.edit.firstName', 'First Name')}
            </label>
            <input
              type="text"
              id="first_name"
              name="first_name"
              value={formData.first_name}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              required
            />
          </div>
          
          {/* Last Name */}
          <div>
            <label htmlFor="last_name" className="block text-sm font-medium text-gray-700 mb-1">
              {t('profile.edit.lastName', 'Last Name')}
            </label>
            <input
              type="text"
              id="last_name"
              name="last_name"
              value={formData.last_name}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              required
            />
          </div>
          
          {/* Nickname */}
          <div>
            <label htmlFor="nickname" className="block text-sm font-medium text-gray-700 mb-1">
              {t('profile.edit.nickname', 'Nickname')}
            </label>
            <input
              type="text"
              id="nickname"
              name="nickname"
              value={formData.nickname}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              required
            />
          </div>
          
          {/* Email */}
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
              {t('profile.edit.email', 'Email')}
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              required
            />
          </div>
          
          {/* Gender */}
          <div>
            <label htmlFor="gender" className="block text-sm font-medium text-gray-700 mb-1">
              {t('profile.edit.gender', 'Gender')}
            </label>
            <select
              id="gender"
              name="gender"
              value={formData.gender}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">{t('profile.edit.selectGender', 'Select gender')}</option>
              <option value="male">{t('profile.edit.male', 'Male')}</option>
              <option value="female">{t('profile.edit.female', 'Female')}</option>
              <option value="other">{t('profile.edit.other', 'Other')}</option>
              <option value="prefer_not_to_say">{t('profile.edit.preferNotToSay', 'Prefer not to say')}</option>
            </select>
          </div>
          
          {/* Date of Birth */}
          <div>
            <label htmlFor="date_of_birth" className="block text-sm font-medium text-gray-700 mb-1">
              {t('profile.edit.dateOfBirth', 'Date of Birth')}
            </label>
            <input
              type="date"
              id="date_of_birth"
              name="date_of_birth"
              value={formData.date_of_birth}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>
        
        {/* Form Actions */}
        <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
          <button
            type="button"
            onClick={() => navigate('/profile')}
            className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            disabled={isSubmitting}
          >
            {t('profile.edit.cancel', 'Cancel')}
          </button>
          <button
            type="submit"
            className="px-4 py-2 border border-transparent rounded-lg shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <div className="flex items-center">
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                </svg>
                {t('profile.edit.saving', 'Saving...')}
              </div>
            ) : t('profile.edit.saveChanges', 'Save Changes')}
          </button>
        </div>
      </form>
    </div>
  );
};

export default ProfileEditForm;
