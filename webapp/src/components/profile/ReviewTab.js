import React, { useState } from 'react';

/**
 * ReviewTab component displays reviews for a talent
 */
const ReviewTab = ({ talent }) => {
  const [showAllReviews, setShowAllReviews] = useState(false);
  
  // Default values in case talent data is incomplete
  const {
    reviews = [],
    rating = 0,
    reviewCount = 0
  } = talent || {};
  
  // Determine if we need to show "See All" button
  const hasMoreReviews = reviews.length > 3;
  
  // Get reviews to display (all or just first 3)
  const displayedReviews = showAllReviews ? reviews : reviews.slice(0, 3);
  
  // Toggle showing all reviews
  const toggleShowAllReviews = () => {
    setShowAllReviews(!showAllReviews);
  };
  
  // Generate star rating display
  const renderStars = (rating) => {
    return (
      <div className="flex">
        {[...Array(5)].map((_, i) => (
          <svg
            key={i}
            className={`w-4 h-4 ${i < rating ? 'text-yellow-500' : 'text-gray-300'}`}
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
          </svg>
        ))}
      </div>
    );
  };

  return (
    <div className="animate-fade-in">
      {/* Header with rating summary */}
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-bold">Reviews ({reviewCount})</h2>
        <div className="flex items-center">
          <div className="text-yellow-500 font-bold mr-2">{rating.toFixed(1)}</div>
          {renderStars(rating)}
        </div>
      </div>
      
      {/* Reviews list */}
      {reviews.length > 0 ? (
        <div className="space-y-4">
          {displayedReviews.map((review, index) => (
            <div 
              key={review.id || index} 
              className="bg-white rounded-lg p-4 shadow-sm border border-gray-100"
            >
              <div className="flex justify-between mb-2">
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-gray-200 rounded-full overflow-hidden mr-2">
                    {review.avatar ? (
                      <img 
                        src={review.avatar} 
                        alt={review.name} 
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center bg-blue-500 text-white font-bold">
                        {review.name ? review.name.charAt(0).toUpperCase() : '?'}
                      </div>
                    )}
                  </div>
                  <div>
                    <h3 className="font-medium text-sm">{review.name}</h3>
                    <div className="text-xs text-gray-500">{review.date}</div>
                  </div>
                </div>
                <div>
                  {renderStars(review.rating)}
                </div>
              </div>
              <p className="text-sm text-gray-700 mt-2">{review.comment}</p>
            </div>
          ))}
          
          {/* See All / See Less button */}
          {hasMoreReviews && (
            <button 
              onClick={toggleShowAllReviews}
              className="w-full py-2 text-blue-600 text-sm font-medium border border-blue-200 rounded-lg hover:bg-blue-50 transition-colors"
            >
              {showAllReviews ? 'See Less' : 'See All Reviews'}
            </button>
          )}
        </div>
      ) : (
        <div className="text-center py-8 bg-gray-50 rounded-lg">
          <svg 
            className="w-12 h-12 text-gray-300 mx-auto mb-3" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
          </svg>
          <p className="text-gray-500">No reviews yet</p>
        </div>
      )}
    </div>
  );
};

export default ReviewTab;
