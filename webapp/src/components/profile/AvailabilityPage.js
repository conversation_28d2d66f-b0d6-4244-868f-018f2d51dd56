/**
 * Availability Page Component
 * 
 * This component is the main page for managing user availability.
 */

import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useProfile } from '../../contexts/ProfileContext';
import { useAuth } from '../../contexts/AuthContext';
import useTranslation from '../../hooks/useTranslation';
import AvailabilityCalendar from './AvailabilityCalendar';

/**
 * AvailabilityPage component
 */
const AvailabilityPage = () => {
  // Get profile and auth context
  const { profile, loading, error, initialLoadDone } = useProfile();
  const { user, isAuthenticated } = useAuth();
  
  // Navigation
  const navigate = useNavigate();
  
  // Use the translation hook
  const { t } = useTranslation('profile');
  
  // If not authenticated, redirect to login
  useEffect(() => {
    if (initialLoadDone && !isAuthenticated) {
      navigate('/');
    }
  }, [initialLoadDone, isAuthenticated, navigate]);
  
  // If loading, show skeleton
  if (loading && !profile) {
    return (
      <div className="container mx-auto px-4 py-6 max-w-6xl">
        <div className="w-full bg-gray-100 rounded-xl overflow-hidden p-6 animate-pulse">
          <div className="h-8 bg-gray-300 rounded w-1/3 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="h-64 bg-gray-200 rounded"></div>
            <div className="space-y-4">
              <div className="h-8 bg-gray-300 rounded w-1/2 mb-2"></div>
              <div className="space-y-2">
                {[...Array(7)].map((_, index) => (
                  <div key={index} className="h-10 bg-gray-200 rounded"></div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
  
  // If error, show error message
  if (error) {
    return (
      <div className="container mx-auto px-4 py-6 max-w-6xl">
        <div className="w-full bg-red-50 border border-red-200 rounded-xl p-6 text-red-700">
          <h2 className="text-xl font-bold mb-2">{t('profile.error.title', 'Error loading profile')}</h2>
          <p className="mb-4">{error}</p>
          <button
            onClick={() => navigate(-1)}
            className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white font-medium rounded-lg transition-colors duration-200"
          >
            {t('profile.actions.goBack', 'Go Back')}
          </button>
        </div>
      </div>
    );
  }
  
  return (
    <div className="container mx-auto px-4 py-6 max-w-6xl">
      <div className="mb-6">
        <button
          onClick={() => navigate('/profile')}
          className="flex items-center text-blue-600 hover:text-blue-800 transition-colors duration-200"
        >
          <svg className="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
          {t('profile.actions.backToProfile', 'Back to Profile')}
        </button>
      </div>
      
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">
          {t('profile.availability.pageTitle', 'Manage Your Availability')}
        </h1>
        <p className="text-gray-600 mt-1">
          {t('profile.availability.pageDescription', 'Set your regular weekly schedule and add exceptions for specific dates.')}
        </p>
      </div>
      
      <AvailabilityCalendar />
      
      <div className="mt-6 bg-blue-50 border border-blue-200 rounded-xl p-4 text-blue-800">
        <h3 className="font-semibold mb-2">
          {t('profile.availability.tip', 'Tip')}
        </h3>
        <p className="text-sm">
          {t('profile.availability.tipText', 'Your availability settings help others know when they can book your services. Make sure to keep it updated and add exceptions for holidays or days off.')}
        </p>
      </div>
    </div>
  );
};

export default AvailabilityPage;
