import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

/**
 * ServiceSelectionModal component
 *
 * This modal allows users to select a service type and tier when ordering from a talent.
 *
 * @param {Object} props
 * @param {boolean} props.isOpen - Whether the modal is open
 * @param {Function} props.onClose - Function to call when the modal is closed
 * @param {Object} props.skill - The skill/service being ordered
 * @param {Function} props.onSelectDate - Function to call when "Select Date" is clicked
 * @param {Function} props.onOrderNow - Function to call when "Order For Now" is clicked
 */
const ServiceSelectionModal = ({
  isOpen,
  onClose,
  skill,
  onSelectDate,
  onOrderNow
}) => {
  const [selectedTier, setSelectedTier] = useState(null);

  // Reset selected tier when skill changes or modal opens
  useEffect(() => {
    console.log('ServiceSelectionModal - useEffect triggered with:', { skill, isOpen });

    if (skill && skill.tiers && skill.tiers.length > 0) {
      console.log('ServiceSelectionModal - Setting selectedTier to first tier:', skill.tiers[0]);
      setSelectedTier(skill.tiers[0]);
    } else {
      console.log('ServiceSelectionModal - No tiers available, setting selectedTier to null');
      setSelectedTier(null);
    }
  }, [skill, isOpen]);

  // Handle tier selection
  const handleTierSelect = (tier) => {
    console.log('ServiceSelectionModal - handleTierSelect called with:', tier);
    setSelectedTier(tier);
  };

  // Handle "Select Date" button click
  const handleSelectDate = () => {
    console.log('ServiceSelectionModal - handleSelectDate called with:', { skill, selectedTier });
    if (selectedTier && onSelectDate) {
      console.log('ServiceSelectionModal - Calling onSelectDate');
      onSelectDate(skill, selectedTier);
    } else {
      console.log('ServiceSelectionModal - Cannot call onSelectDate, missing selectedTier or onSelectDate');
    }
  };

  // Handle "Order For Now" button click
  const handleOrderNow = () => {
    console.log('ServiceSelectionModal - handleOrderNow called with:', { skill, selectedTier });
    if (selectedTier && onOrderNow) {
      console.log('ServiceSelectionModal - Calling onOrderNow');
      onOrderNow(skill, selectedTier);
    } else {
      console.log('ServiceSelectionModal - Cannot call onOrderNow, missing selectedTier or onOrderNow');
    }
  };

  // Modal animation variants
  const backdropVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 },
    exit: { opacity: 0 }
  };

  const modalVariants = {
    hidden: { opacity: 0, y: 50, scale: 0.95 },
    visible: { opacity: 1, y: 0, scale: 1, transition: { duration: 0.3 } },
    exit: { opacity: 0, y: 50, scale: 0.95, transition: { duration: 0.2 } }
  };

  // If modal is not open or no skill is provided, don't render anything
  if (!isOpen || !skill) return null;

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
          initial="hidden"
          animate="visible"
          exit="exit"
          variants={backdropVariants}
          onClick={onClose}
        >
          <motion.div
            className="bg-white rounded-xl w-full max-w-md overflow-hidden"
            variants={modalVariants}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
              <h2 className="text-xl font-bold text-gray-900">Select Service Type</h2>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* Content */}
            <div className="px-6 py-4">
              {/* Service Info */}
              <div className="mb-6">
                <div className="flex items-center mb-2">
                  <div className="w-12 h-12 rounded-full bg-indigo-100 flex items-center justify-center mr-3">
                    {skill.icon ? (
                      <img src={skill.icon} alt={skill.name} className="w-8 h-8" />
                    ) : (
                      <svg className="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    )}
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-gray-900">{skill.name}</h3>
                    {skill.ordersCount > 0 && (
                      <p className="text-sm text-gray-500">
                        {skill.ordersCount} {skill.ordersCount === 1 ? 'person' : 'people'} ordered
                      </p>
                    )}
                  </div>
                </div>

                {skill.description && (
                  <p className="text-sm text-gray-600 mt-2">{skill.description}</p>
                )}
              </div>

              {/* Service Tiers */}
              <div className="mb-6">
                <h4 className="text-sm font-medium text-gray-700 mb-3">Select Service Tier</h4>

                <div className="space-y-3">
                  {skill.tiers && skill.tiers.map((tier) => (
                    <div
                      key={tier.id}
                      className={`border rounded-lg p-4 cursor-pointer transition-all ${
                        selectedTier && selectedTier.id === tier.id
                          ? 'border-indigo-500 bg-indigo-50'
                          : 'border-gray-200 hover:border-indigo-300'
                      }`}
                      onClick={() => handleTierSelect(tier)}
                    >
                      <div className="flex justify-between items-center">
                        <div>
                          <h5 className="font-medium text-gray-900">{tier.name}</h5>
                          {tier.description && (
                            <p className="text-sm text-gray-600 mt-1">{tier.description}</p>
                          )}
                        </div>
                        <div className="text-lg font-bold text-indigo-600">
                          {tier.price} <span className="text-sm font-normal">/ {tier.unit || 'round'}</span>
                        </div>
                      </div>
                    </div>
                  ))}

                  {(!skill.tiers || skill.tiers.length === 0) && (
                    <div className="text-center py-4 text-gray-500">
                      No service tiers available
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleSelectDate}
                disabled={!selectedTier}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Select Date
              </button>
              <button
                type="button"
                onClick={handleOrderNow}
                disabled={!selectedTier}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Order For Now
              </button>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default ServiceSelectionModal;
