import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import orderAPI from '../../services/orderService';
import OrderPaymentStatus from '../OrderPaymentStatus';
import OrderPaymentHistory from '../OrderPaymentHistory';
import InsufficientBalanceModal from '../InsufficientBalanceModal';

/**
 * TalentOrderDetails component
 *
 * This component displays the details of an order in the talent profile
 * and its payment status.
 *
 * @param {Object} props
 * @param {number} props.orderId - The ID of the order
 * @param {Function} props.onClose - Function to call when the close button is clicked
 */
const TalentOrderDetails = ({ orderId, onClose }) => {
  const [order, setOrder] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showTopUpModal, setShowTopUpModal] = useState(false);

  // Fetch order details on mount and when orderId changes
  useEffect(() => {
    if (orderId) {
      fetchOrderDetails();
    }
  }, [orderId]);

  // Fetch order details
  const fetchOrderDetails = async () => {
    if (!orderId) return;

    setIsLoading(true);
    setError(null);

    try {
      const response = await orderAPI.getOrderDetails(orderId);
      setOrder(response.data);
    } catch (err) {
      console.error('Error fetching order details:', err);
      setError('Failed to load order details');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle payment success
  const handlePaymentSuccess = () => {
    // Refresh order details to get updated payment status
    fetchOrderDetails();
  };

  // Handle top-up click
  const handleTopUpClick = () => {
    setShowTopUpModal(true);
  };

  // Handle top-up success
  const handleTopUpSuccess = () => {
    setShowTopUpModal(false);
    // Refresh order details after top-up
    fetchOrderDetails();
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return '';

    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Get status badge
  const getStatusBadge = (status) => {
    const statusConfig = {
      pending: {
        bgColor: 'bg-yellow-100',
        textColor: 'text-yellow-800',
        label: 'Pending'
      },
      accepted: {
        bgColor: 'bg-green-100',
        textColor: 'text-green-800',
        label: 'Accepted'
      },
      rejected: {
        bgColor: 'bg-red-100',
        textColor: 'text-red-800',
        label: 'Rejected'
      },
      completed: {
        bgColor: 'bg-blue-100',
        textColor: 'text-blue-800',
        label: 'Completed'
      },
      cancelled: {
        bgColor: 'bg-gray-100',
        textColor: 'text-gray-800',
        label: 'Cancelled'
      }
    };

    const config = statusConfig[status] || {
      bgColor: 'bg-gray-100',
      textColor: 'text-gray-800',
      label: status
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.bgColor} ${config.textColor}`}>
        {config.label}
      </span>
    );
  };

  // If no orderId, don't render anything
  if (!orderId) return null;

  // If loading
  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="w-8 h-8 border-t-2 border-b-2 border-indigo-500 rounded-full animate-spin"></div>
        <span className="ml-2 text-gray-600">Loading order details...</span>
      </div>
    );
  }

  // If error
  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-50 rounded-lg p-4">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
              <div className="mt-4">
                <button
                  type="button"
                  onClick={fetchOrderDetails}
                  className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                >
                  Try Again
                </button>
                {onClose && (
                  <button
                    type="button"
                    onClick={onClose}
                    className="ml-3 inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    Close
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // If no order
  if (!order) {
    return (
      <div className="p-6">
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-gray-800">Order Not Found</h3>
              <div className="mt-2 text-sm text-gray-600">
                <p>The order you're looking for could not be found.</p>
              </div>
              {onClose && (
                <div className="mt-4">
                  <button
                    type="button"
                    onClick={onClose}
                    className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    Close
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
      className="bg-white rounded-lg shadow-sm overflow-hidden"
    >
      <div className="px-6 py-5 border-b border-gray-200">
        <div className="flex justify-between items-center">
          <h1 className="text-xl font-bold text-gray-900">
            Order #{order.id}
          </h1>
          <div className="flex items-center space-x-2">
            {getStatusBadge(order.status)}
            {onClose && (
              <button
                type="button"
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
          </div>
        </div>
      </div>

      <div className="px-6 py-5">
        {/* Payment Status */}
        <OrderPaymentStatus
          order={order}
          onPaymentSuccess={handlePaymentSuccess}
          onTopUpClick={handleTopUpClick}
        />

        {/* Order Details */}
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden mb-6">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Order Details</h2>
          </div>

          <div className="px-6 py-4">
            <dl className="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-6">
              <div>
                <dt className="text-sm font-medium text-gray-500">Service</dt>
                <dd className="mt-1 text-sm text-gray-900">{order.service_name || 'N/A'}</dd>
              </div>

              <div>
                <dt className="text-sm font-medium text-gray-500">Price</dt>
                <dd className="mt-1 text-sm text-gray-900 font-semibold">{order.price || order.amount || 0} Credits</dd>
              </div>

              <div>
                <dt className="text-sm font-medium text-gray-500">Created At</dt>
                <dd className="mt-1 text-sm text-gray-900">{formatDate(order.created_at)}</dd>
              </div>

              <div>
                <dt className="text-sm font-medium text-gray-500">Updated At</dt>
                <dd className="mt-1 text-sm text-gray-900">{formatDate(order.updated_at)}</dd>
              </div>

              <div>
                <dt className="text-sm font-medium text-gray-500">Customer</dt>
                <dd className="mt-1 text-sm text-gray-900">{order.customer?.name || 'N/A'}</dd>
              </div>

              <div>
                <dt className="text-sm font-medium text-gray-500">Talent</dt>
                <dd className="mt-1 text-sm text-gray-900">{order.talent?.name || 'N/A'}</dd>
              </div>

              {order.notes && (
                <div className="col-span-2">
                  <dt className="text-sm font-medium text-gray-500">Notes</dt>
                  <dd className="mt-1 text-sm text-gray-900 whitespace-pre-wrap">{order.notes}</dd>
                </div>
              )}
            </dl>
          </div>
        </div>

        {/* Payment History */}
        <OrderPaymentHistory
          orderId={order.id}
          orderType="talent_order"
        />
      </div>

      {/* Insufficient Balance Modal */}
      <InsufficientBalanceModal
        isOpen={showTopUpModal}
        onClose={() => setShowTopUpModal(false)}
        requiredAmount={order.price || order.amount || 0}
        currentBalance={0} // This will be fetched by the modal
        onTopUpSuccess={handleTopUpSuccess}
      />
    </motion.div>
  );
};

export default TalentOrderDetails;
