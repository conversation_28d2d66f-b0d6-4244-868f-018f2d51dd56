import React, { useState, useEffect, useRef } from 'react';

/**
 * TabNavigation component provides a tabbed interface for navigating
 * between different sections of the talent profile
 * with smooth active indicator animation
 */
const TabNavigation = ({ activeTab, onTabChange }) => {
  // Available tabs
  const tabs = ['About', 'Review', 'Skills', 'Post', 'Mission'];

  // Refs for measuring tab positions
  const tabsRef = useRef([]);
  const tabContainerRef = useRef(null);

  // State for active indicator position and width
  const [indicatorStyle, setIndicatorStyle] = useState({
    left: 0,
    width: 0,
    opacity: 0
  });

  // Update indicator position when active tab changes
  useEffect(() => {
    const updateIndicator = () => {
      const activeIndex = tabs.findIndex(tab => tab.toLowerCase() === activeTab);

      if (activeIndex >= 0 && tabsRef.current[activeIndex]) {
        const tabElement = tabsRef.current[activeIndex];
        const containerRect = tabContainerRef.current.getBoundingClientRect();
        const tabRect = tabElement.getBoundingClientRect();

        setIndicatorStyle({
          left: tabRect.left - containerRect.left,
          width: tabRect.width,
          opacity: 1
        });
      }
    };

    // Update on mount and when active tab changes
    updateIndicator();

    // Also update on window resize
    window.addEventListener('resize', updateIndicator);
    return () => window.removeEventListener('resize', updateIndicator);
  }, [activeTab, tabs]);

  return (
    <div className="bg-white border-b border-gray-200 sticky top-0 z-20">
      <div
        className="flex overflow-x-auto hide-scrollbar relative"
        ref={tabContainerRef}
      >
        {tabs.map((tab, index) => {
          const tabKey = tab.toLowerCase();
          const isActive = activeTab === tabKey;

          return (
            <button
              key={tab}
              ref={el => tabsRef.current[index] = el}
              className={`py-3 px-4 text-sm font-medium whitespace-nowrap transition-colors ${
                isActive
                  ? 'justify-between text-blue-600 bg-transparent hover:bg-blue-200'
                  : 'justify-between text-gray-500 bg-transparent hover:text-gray-700 hover:bg-blue-200 hover:bg-gray-50'
              }`}
              onClick={() => onTabChange(tabKey)}
              aria-selected={isActive}
              role="tab"
            >
              {tab}
            </button>
          );
        })}

        {/* Animated active indicator */}
        <div
          className="absolute bottom-0 h-0.5 bg-white transition-all duration-300 ease-in-out"
          style={{
            left: `${indicatorStyle.left}px`,
            width: `${indicatorStyle.width}px`,
            opacity: indicatorStyle.opacity
          }}
        />
      </div>
    </div>
  );
};

export default TabNavigation;
