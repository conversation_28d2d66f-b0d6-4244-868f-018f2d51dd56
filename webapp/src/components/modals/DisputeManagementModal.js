/**
 * Dispute Management Modal Component
 * 
 * A comprehensive modal for viewing and managing user disputes with:
 * - Dispute history list with filtering
 * - Status badges and visual indicators
 * - Pagination support
 * - Glassmorphism styling
 * - Integration with dispute creation
 */

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import disputeService from '../../services/disputeService';
import DisputeCreationModal from './DisputeCreationModal';
import { InlineLoader } from '../ui/LoadingIndicator';

const DisputeManagementModal = ({
    isOpen = false,
    onClose,
    disputes = [],
    loading = false,
    error = null,
    onRefresh
}) => {
    // Ensure disputes is always an array
    const safeDisputes = Array.isArray(disputes) ? disputes : [];
    const [filteredDisputes, setFilteredDisputes] = useState([]);
    const [statusFilter, setStatusFilter] = useState('all');
    const [isCreationModalOpen, setIsCreationModalOpen] = useState(false);
    const [selectedDispute, setSelectedDispute] = useState(null);
    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 5;

    // Filter disputes based on status
    useEffect(() => {
        if (statusFilter === 'all') {
            setFilteredDisputes(safeDisputes);
        } else {
            setFilteredDisputes(safeDisputes.filter(dispute => dispute.status === statusFilter));
        }
        setCurrentPage(1); // Reset to first page when filter changes
    }, [safeDisputes, statusFilter]);

    // Pagination calculations
    const totalPages = Math.ceil(filteredDisputes.length / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const currentDisputes = Array.isArray(filteredDisputes) ? 
        filteredDisputes.slice(startIndex, endIndex) : [];

    // Animation variants
    const backdropVariants = {
        hidden: { opacity: 0 },
        visible: { opacity: 1 }
    };

    const modalVariants = {
        hidden: { opacity: 0, scale: 0.95, y: 20 },
        visible: { opacity: 1, scale: 1, y: 0 },
        exit: { opacity: 0, scale: 0.95, y: 20 }
    };

    const handleStatusFilterChange = (status) => {
        setStatusFilter(status);
    };

    const handleDisputeClick = (dispute) => {
        setSelectedDispute(dispute);
    };

    const handleCreateDispute = () => {
        setIsCreationModalOpen(true);
    };

    const handleCreationSuccess = () => {
        setIsCreationModalOpen(false);
        if (onRefresh) {
            onRefresh();
        }
    };

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const getStatusBadge = (status) => {
        return disputeService.getStatusBadge(status);
    };

    if (!isOpen) return null;

    return (
        <AnimatePresence>
            {isOpen && (
                <motion.div
                    className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
                    variants={backdropVariants}
                    initial="hidden"
                    animate="visible"
                    exit="hidden"
                    onClick={onClose}
                >
                    <motion.div
                        className="relative bg-gradient-to-br from-white/95 to-white/90 backdrop-blur-2xl border border-white/30 rounded-3xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden"
                        variants={modalVariants}
                        initial="hidden"
                        animate="visible"
                        exit="exit"
                        onClick={(e) => e.stopPropagation()}
                    >
                        {/* Animated background decorations */}
                        <div className="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-red-400/20 to-orange-400/20 rounded-full blur-2xl animate-pulse" />
                        <div className="absolute -bottom-10 -left-10 w-24 h-24 bg-gradient-to-br from-red-400/20 to-pink-400/20 rounded-full blur-xl animate-pulse delay-1000" />

                        {/* Header */}
                        <div className="relative z-10 p-6 border-b border-white/20">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-3">
                                    <div className="p-3 bg-gradient-to-br from-red-500 to-orange-600 rounded-2xl shadow-lg">
                                        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <h2 className="text-2xl font-bold bg-gradient-to-r from-red-700 to-orange-700 bg-clip-text text-transparent">
                                            Dispute Management
                                        </h2>
                                        <p className="text-gray-600 text-sm">
                                            View and manage your order disputes
                                        </p>
                                    </div>
                                </div>
                                <button
                                    onClick={onClose}
                                    className="p-2 hover:bg-white/20 rounded-xl transition-colors"
                                >
                                    <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </button>
                            </div>
                        </div>

                        {/* Content */}
                        <div className="relative z-10 p-6 overflow-y-auto max-h-[calc(90vh-8rem)]">
                            {/* Filter and Actions Bar */}
                            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
                                {/* Status Filter */}
                                <div className="flex flex-wrap gap-2">
                                    {['all', 'submitted', 'in_review', 'resolved', 'rejected'].map((status) => (
                                        <button
                                            key={status}
                                            onClick={() => handleStatusFilterChange(status)}
                                            className={`px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 ${
                                                statusFilter === status
                                                    ? 'bg-gradient-to-r from-red-500 to-orange-600 text-white shadow-lg'
                                                    : 'bg-white/50 text-gray-700 hover:bg-white/70 border border-gray-200'
                                            }`}
                                        >
                                            {status === 'all' ? 'All' : status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                                        </button>
                                    ))}
                                </div>

                                {/* Create Dispute Button */}
                                <motion.button
                                    onClick={handleCreateDispute}
                                    className="px-6 py-3 bg-gradient-to-r from-red-500 to-orange-600 text-white font-semibold rounded-xl hover:from-red-600 hover:to-orange-700 transition-all duration-300 shadow-lg flex items-center space-x-2"
                                    whileHover={{ scale: 1.02 }}
                                    whileTap={{ scale: 0.98 }}
                                >
                                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                    </svg>
                                    <span>Create Dispute</span>
                                </motion.button>
                            </div>

                            {/* Loading State */}
                            {loading && (
                                <div className="text-center py-12">
                                    <InlineLoader size="large" color="red" />
                                    <p className="text-gray-600 mt-4">Loading disputes...</p>
                                </div>
                            )}

                            {/* Error State */}
                            {error && !loading && (
                                <div className="text-center py-12">
                                    <div className="p-6 bg-red-50 rounded-2xl border border-red-100 max-w-md mx-auto">
                                        <svg className="w-12 h-12 text-red-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        <p className="text-red-600 font-medium mb-3">{error}</p>
                                        {onRefresh && (
                                            <button
                                                onClick={onRefresh}
                                                className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
                                            >
                                                Retry
                                            </button>
                                        )}
                                    </div>
                                </div>
                            )}

                            {/* Empty State */}
                            {!loading && !error && (!Array.isArray(filteredDisputes) || filteredDisputes.length === 0) && (
                                <div className="text-center py-12">
                                    <div className="p-8 bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl border border-gray-200 max-w-md mx-auto">
                                        <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                        </svg>
                                        <p className="text-gray-600 font-medium text-lg mb-2">
                                            {statusFilter === 'all' ? 'No disputes found' : `No ${statusFilter.replace('_', ' ')} disputes`}
                                        </p>
                                        <p className="text-gray-500 text-sm">
                                            {statusFilter === 'all' 
                                                ? 'You haven\'t created any disputes yet.'
                                                : 'Try changing the filter to see other disputes.'
                                            }
                                        </p>
                                    </div>
                                </div>
                            )}

                            {/* Disputes List */}
                            {!loading && !error && Array.isArray(currentDisputes) && currentDisputes.length > 0 && (
                                <div className="space-y-4">
                                    {currentDisputes.map((dispute, index) => {
                                        const statusBadge = getStatusBadge(dispute.status);
                                        return (
                                            <motion.div
                                                key={dispute.id}
                                                className="group p-6 bg-gradient-to-r from-white/80 to-white/60 backdrop-blur-sm rounded-2xl border border-white/50 hover:border-red-200 transition-all duration-300 hover:shadow-lg cursor-pointer"
                                                initial={{ opacity: 0, y: 20 }}
                                                animate={{ opacity: 1, y: 0 }}
                                                transition={{ duration: 0.3, delay: index * 0.1 }}
                                                whileHover={{ scale: 1.01, y: -2 }}
                                                onClick={() => handleDisputeClick(dispute)}
                                            >
                                                <div className="flex items-start justify-between">
                                                    <div className="flex-1">
                                                        <div className="flex items-center space-x-3 mb-3">
                                                            <span className="text-2xl">{statusBadge.icon}</span>
                                                            <div>
                                                                <h3 className="font-bold text-gray-800 text-lg group-hover:text-red-700 transition-colors">
                                                                    Dispute #{dispute.id}
                                                                </h3>
                                                                <p className="text-sm text-gray-600">
                                                                    Order #{dispute.order_id || dispute.order?.id || 'N/A'}
                                                                </p>
                                                            </div>
                                                        </div>
                                                        
                                                        <p className="text-gray-700 mb-3 line-clamp-2">
                                                            {dispute.description || 'No description provided'}
                                                        </p>
                                                        
                                                        <div className="flex items-center space-x-4 text-sm text-gray-500">
                                                            <span>Created: {formatDate(dispute.created_at)}</span>
                                                            {dispute.dispute_type && (
                                                                <span>Type: {dispute.dispute_type.name || dispute.dispute_type}</span>
                                                            )}
                                                        </div>
                                                    </div>
                                                    
                                                    <div className="text-right ml-4">
                                                        <span className={`inline-flex px-3 py-1 text-xs font-medium rounded-full border ${statusBadge.className}`}>
                                                            {statusBadge.label}
                                                        </span>
                                                        {dispute.media && dispute.media.length > 0 && (
                                                            <div className="mt-2 flex items-center text-xs text-gray-500">
                                                                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.586-6.586a2 2 0 00-2.828-2.828z" />
                                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4" />
                                                                </svg>
                                                                {dispute.media.length} attachment{dispute.media.length > 1 ? 's' : ''}
                                                            </div>
                                                        )}
                                                    </div>
                                                </div>
                                            </motion.div>
                                        );
                                    })}
                                </div>
                            )}

                            {/* Pagination */}
                            {!loading && !error && totalPages > 1 && Array.isArray(filteredDisputes) && (
                                <div className="flex justify-center items-center space-x-2 mt-8">
                                    <button
                                        onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                                        disabled={currentPage === 1}
                                        className="px-4 py-2 bg-white/50 text-gray-700 rounded-lg hover:bg-white/70 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                                    >
                                        Previous
                                    </button>
                                    
                                    <div className="flex space-x-1">
                                        {[...Array(Math.max(0, totalPages))].map((_, index) => (
                                            <button
                                                key={index + 1}
                                                onClick={() => setCurrentPage(index + 1)}
                                                className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                                                    currentPage === index + 1
                                                        ? 'bg-gradient-to-r from-red-500 to-orange-600 text-white'
                                                        : 'bg-white/50 text-gray-700 hover:bg-white/70'
                                                }`}
                                            >
                                                {index + 1}
                                            </button>
                                        ))}
                                    </div>
                                    
                                    <button
                                        onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                                        disabled={currentPage === totalPages}
                                        className="px-4 py-2 bg-white/50 text-gray-700 rounded-lg hover:bg-white/70 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                                    >
                                        Next
                                    </button>
                                </div>
                            )}
                        </div>
                    </motion.div>

                    {/* Dispute Creation Modal */}
                    <DisputeCreationModal
                        isOpen={isCreationModalOpen}
                        onClose={() => setIsCreationModalOpen(false)}
                        onSuccess={handleCreationSuccess}
                    />
                </motion.div>
            )}
        </AnimatePresence>
    );
};

export default DisputeManagementModal;
