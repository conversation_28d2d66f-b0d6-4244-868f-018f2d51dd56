import React, { useState, useEffect, useRef } from 'react';
import axios from 'axios';
import { motion, AnimatePresence } from 'framer-motion';
import { Link } from 'react-router-dom';

// Alphabetical sections for game categories
const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.split('');

// Alphabetical letters array
const LETTERS = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];

const GamesModal = ({ isOpen, onClose }) => {
    const [activeTab, setActiveTab] = useState('popular');
    const [games, setGames] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const modalRef = useRef(null);

    // Prevent body scroll when modal is open
    useEffect(() => {
        if (isOpen) {
            document.body.style.overflow = 'hidden';
        } else {
            document.body.style.overflow = 'unset';
        }
        return () => {
            document.body.style.overflow = 'unset';
        };
    }, [isOpen]);

    // Close on click outside
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (modalRef.current && !modalRef.current.contains(event.target)) {
                onClose();
            }
        };

        if (isOpen) {
            document.addEventListener('mousedown', handleClickOutside);
        }
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [isOpen, onClose]);

    // Fetch games data
    useEffect(() => {
        const fetchGames = async () => {
            if (!isOpen) return;

            setLoading(true);
            setError(null);

            try {
                // Use the real API endpoint to fetch games
                const response = await axios.get(`${process.env.REACT_APP_API_URL || 'http://localhost:8001/api'}/service-types`);

                if (response && response.data) {
                    // Transform the API data to match our component's expected format
                    const formattedGames = response.data.map(game => ({
                        id: game.id,
                        name: game.name,
                        image: game.image_url || '/images/games/default-game.jpg',
                        rating: game.rating || game.average_rating || 4.0,
                        popular: game.is_popular || (game.usage_count && game.usage_count > 100) || false
                    }));

                    setGames(formattedGames);
                } else {
                    setGames([]);
                    setError('No games found');
                }
            } catch (err) {
                console.error('Error fetching games:', err);
                setError('Failed to load games');
                setGames([]);
            } finally {
                setLoading(false);
            }
        };

        fetchGames();
    }, [isOpen]);

    // Group games alphabetically
    const alphabeticalGames = {};
    LETTERS.forEach(letter => {
        alphabeticalGames[letter] = games.filter(game =>
            game.name.toUpperCase().startsWith(letter)
        );
    });

    // Get popular games
    const popularGames = games.filter(game => game.popular);

    // Enhanced game card with better visuals and interactions
    const renderGameCard = (game) => (
        <Link
            to={`/game/${game.id}`}
            key={game.id}
            className="bg-gradient-to-b from-white to-indigo-50/30 rounded-2xl shadow-md overflow-hidden transition-all duration-300 hover:scale-105 hover:shadow-lg border border-indigo-100/50 group relative"
        >
            {/* Image Container with Overlay */}
            <div className="h-40 overflow-hidden relative">
                <img
                    src={game.image || "https://via.placeholder.com/300x180?text=Game+Image"}
                    alt={game.name}
                    className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110 filter group-hover:brightness-110"
                />

                {/* Gradient Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent opacity-60 group-hover:opacity-40 transition-opacity"></div>

                {/* Game Rating Badge */}
                <div className="absolute top-3 right-3 bg-white/90 backdrop-blur-sm px-2 py-1 rounded-lg shadow-md flex items-center">
                    <svg className="w-3.5 h-3.5 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                    <span className="text-xs font-medium text-gray-800 ml-1">{game.rating}</span>
                </div>

                {/* Popular Badge */}
                {game.popular && (
                    <div className="absolute top-3 left-3 bg-gradient-to-r from-indigo-600 to-blue-600 text-white px-2.5 py-1 rounded-lg shadow-md flex items-center">
                        <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                        </svg>
                        <span className="text-xs font-medium">Popular</span>
                    </div>
                )}

                {/* Game Name on Image */}
                <div className="absolute bottom-0 left-0 w-full p-3">
                    <h3 className="font-bold text-white text-shadow-sm truncate">{game.name}</h3>
                </div>
            </div>

            {/* Game Info Section */}
            <div className="p-3">
                {/* Game Stats */}
                <div className="flex justify-between items-center text-xs text-gray-600">
                    <div className="flex items-center">
                        <svg className="w-3.5 h-3.5 mr-1 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                        <span>{Math.floor(Math.random() * 10) + 1}k players</span>
                    </div>
                    <div className="flex items-center">
                        <svg className="w-3.5 h-3.5 mr-1 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span>{Math.floor(Math.random() * 60) + 30} min avg</span>
                    </div>
                </div>

                {/* View Button - Appears on Hover */}
                <div className="mt-3 opacity-0 group-hover:opacity-100 transition-opacity transform translate-y-2 group-hover:translate-y-0">
                    <button className="w-full py-1.5 bg-indigo-600 hover:bg-indigo-700 text-white text-xs font-medium rounded-lg transition-colors flex items-center justify-center">
                        <span>View Talents</span>
                        <svg className="w-3.5 h-3.5 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </button>
                </div>
            </div>
        </Link>
    );

    if (!isOpen) return null;

    return (
        <AnimatePresence>
            {isOpen && (
                <div className="fixed inset-0 bg-black/70 z-50 flex items-center justify-center p-4 backdrop-blur-sm">
                    <motion.div
                        initial={{ opacity: 0, scale: 0.95 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0.95 }}
                        transition={{ duration: 0.3, ease: "easeOut" }}
                        className="bg-white rounded-2xl shadow-2xl w-full max-w-6xl max-h-[90vh] overflow-hidden flex flex-col border border-indigo-100/50"
                        ref={modalRef}
                    >
                        {/* Enhanced Modal Header with Banner */}
                        <div className="relative">
                            {/* Background Banner */}
                            <div className="h-32 bg-gradient-to-r from-indigo-600 to-blue-600 relative overflow-hidden">
                                {/* Background Pattern */}
                                <div className="absolute inset-0 bg-grid-white/[0.05] bg-[length:20px_20px]"></div>

                                {/* Decorative Elements */}
                                <div className="absolute -top-10 -right-10 w-40 h-40 bg-white/10 rounded-full blur-xl"></div>
                                <div className="absolute -bottom-10 -left-10 w-32 h-32 bg-white/10 rounded-full blur-lg"></div>

                                {/* Close Button */}
                                <button
                                    onClick={onClose}
                                    className="absolute top-4 right-4 p-2 rounded-full bg-white/20 backdrop-blur-sm hover:bg-white/30 transition-colors text-white z-10"
                                >
                                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </button>

                                {/* Header Content */}
                                <div className="absolute inset-0 flex items-center p-6">
                                    <div className="w-16 h-16 rounded-xl bg-white/10 backdrop-blur-sm flex items-center justify-center mr-4 shadow-lg">
                                        <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a2 2 0 110-4h1a1 1 0 001-1V7a1 1 0 011-1h3a1 1 0 001-1V4z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <h2 className="text-2xl font-bold text-white">Game Library</h2>
                                        <p className="text-indigo-100 text-sm mt-1">Discover and connect with players across multiple games</p>
                                    </div>
                                </div>
                            </div>

                            {/* Enhanced Tab Navigation */}
                            <div className="bg-white px-6 py-4 border-b border-indigo-100/50 flex flex-wrap items-center justify-between gap-4">
                                <div className="flex items-center">
                                    <span className="text-gray-500 text-sm mr-3">Browse by:</span>
                                    <div className="flex space-x-2">
                                        <button
                                            className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
                                                activeTab === 'popular'
                                                    ? 'bg-indigo-600 text-white shadow-md'
                                                    : 'bg-indigo-50 text-indigo-700 hover:bg-indigo-100'
                                            }`}
                                            onClick={() => setActiveTab('popular')}
                                        >
                                            <div className="flex items-center">
                                                <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                                                </svg>
                                                <span>Popular Games</span>
                                            </div>
                                        </button>
                                        <button
                                            className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
                                                activeTab === 'alphabetical'
                                                    ? 'bg-indigo-600 text-white shadow-md'
                                                    : 'bg-indigo-50 text-indigo-700 hover:bg-indigo-100'
                                            }`}
                                            onClick={() => setActiveTab('alphabetical')}
                                        >
                                            <div className="flex items-center">
                                                <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 4h13M3 8h9m-9 4h6m4 0l4-4m0 0l4 4m-4-4v12" />
                                                </svg>
                                                <span>A-Z Directory</span>
                                            </div>
                                        </button>
                                    </div>
                                </div>

                                {/* Search Input */}
                                <div className="relative">
                                    <input
                                        type="text"
                                        placeholder="Search games..."
                                        className="py-2 pl-9 pr-4 rounded-lg text-sm border border-indigo-100 focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 w-64"
                                    />
                                    <svg className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                    </svg>
                                </div>
                            </div>
                        </div>

                        {/* Enhanced Modal Content */}
                        <div className="overflow-y-auto p-6 flex-grow custom-scrollbar">
                            {loading ? (
                                <div className="flex items-center justify-center h-64">
                                    <div className="flex flex-col items-center">
                                        <div className="flex space-x-2 mb-3">
                                            <div className="w-3 h-3 bg-indigo-500 rounded-full animate-bounce" style={{ animationDelay: "0ms" }}></div>
                                            <div className="w-3 h-3 bg-indigo-500 rounded-full animate-bounce" style={{ animationDelay: "150ms" }}></div>
                                            <div className="w-3 h-3 bg-indigo-500 rounded-full animate-bounce" style={{ animationDelay: "300ms" }}></div>
                                        </div>
                                        <p className="text-indigo-500 text-sm">Loading games...</p>
                                    </div>
                                </div>
                            ) : error ? (
                                <div className="text-center p-8 bg-red-50 rounded-xl border border-red-100">
                                    <svg className="w-12 h-12 text-red-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <h3 className="text-lg font-bold text-red-700 mb-2">Unable to Load Games</h3>
                                    <p className="text-red-600 mb-4">{error}</p>
                                    <button
                                        className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors shadow-md"
                                        onClick={() => window.location.reload()}
                                    >
                                        Retry
                                    </button>
                                </div>
                            ) : activeTab === 'popular' ? (
                                <div>
                                    <h3 className="text-xl font-bold text-gray-800 mb-5 flex items-center">
                                        <svg className="w-5 h-5 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                                        </svg>
                                        Popular Games
                                    </h3>
                                    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 xl:grid-cols-5 gap-5">
                                        {popularGames.length > 0 ? (
                                            popularGames.map(game => renderGameCard(game))
                                        ) : (
                                            <div className="col-span-full text-center py-12 bg-indigo-50 rounded-xl border border-indigo-100">
                                                <svg className="w-12 h-12 text-indigo-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                </svg>
                                                <p className="text-indigo-700 font-medium">No popular games found</p>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            ) : (
                                <div>
                                    <h3 className="text-xl font-bold text-gray-800 mb-5 flex items-center">
                                        <svg className="w-5 h-5 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 4h13M3 8h9m-9 4h6m4 0l4-4m0 0l4 4m-4-4v12" />
                                        </svg>
                                        Games Directory
                                    </h3>

                                    {/* Alphabet Quick Navigation */}
                                    <div className="flex flex-wrap gap-1 mb-6 bg-indigo-50 p-3 rounded-lg border border-indigo-100">
                                        {alphabet.map(letter => {
                                            const hasGames = alphabeticalGames[letter].length > 0;
                                            return (
                                                <button
                                                    key={letter}
                                                    className={`w-7 h-7 flex items-center justify-center rounded-md text-xs font-medium ${
                                                        hasGames
                                                            ? 'bg-white text-indigo-700 shadow-sm hover:bg-indigo-600 hover:text-white transition-colors'
                                                            : 'text-indigo-300 cursor-default'
                                                    }`}
                                                    disabled={!hasGames}
                                                    onClick={() => {
                                                        if (hasGames) {
                                                            document.getElementById(`section-${letter}`).scrollIntoView({ behavior: 'smooth' });
                                                        }
                                                    }}
                                                >
                                                    {letter}
                                                </button>
                                            );
                                        })}
                                    </div>

                                    <div className="space-y-8">
                                        {LETTERS.map(letter => {
                                            const gamesForLetter = alphabeticalGames[letter];
                                            if (gamesForLetter.length === 0) return null;

                                            return (
                                                <div key={letter} id={`section-${letter}`} className="scroll-mt-4">
                                                    <div className="flex items-center mb-4">
                                                        <div className="w-10 h-10 rounded-xl bg-indigo-600 text-white flex items-center justify-center font-bold mr-3 shadow-md">
                                                            {letter}
                                                        </div>
                                                        <h3 className="text-lg font-bold text-gray-800 border-b border-indigo-100 pb-1 flex-grow">
                                                            {letter} Games
                                                        </h3>
                                                    </div>
                                                    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 xl:grid-cols-5 gap-5">
                                                        {gamesForLetter.map(game => renderGameCard(game))}
                                                    </div>
                                                </div>
                                            );
                                        })}
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* Footer with Stats */}
                        <div className="bg-indigo-50/80 backdrop-blur-sm border-t border-indigo-100 p-4">
                            <div className="flex flex-wrap justify-between items-center">
                                <p className="text-sm text-gray-600">
                                    Showing {games.length} games across multiple platforms
                                </p>
                                <div className="flex items-center space-x-4">
                                    <button
                                        onClick={onClose}
                                        className="px-4 py-2 bg-white border border-indigo-200 text-indigo-700 rounded-lg text-sm font-medium hover:bg-indigo-50 transition-colors"
                                    >
                                        Close
                                    </button>
                                </div>
                            </div>
                        </div>
                    </motion.div>
                </div>
            )}
        </AnimatePresence>
    );
};

export default GamesModal;