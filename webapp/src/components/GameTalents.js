import React, { useState, useEffect, useMemo } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useHomepage } from '../contexts/HomepageContext';
import api from '../services/api';
import { getCdnUrl } from '../utils/cdnUtils';

const TalentCard = ({ talent, onClick }) => (
    <div
        className="cursor-pointer relative bg-gradient-to-b from-white to-indigo-50/30 rounded-2xl overflow-hidden shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-indigo-100/50 group animate-fade-in"
        onClick={onClick}
    >
        {/* Enhanced Profile Image Section */}
        <div className="relative rounded-t-2xl overflow-hidden h-40">
            {/* Hot Badge with Enhanced Animation */}
            {talent.isHot && (
                <div className="absolute top-3 left-3 bg-gradient-to-r from-orange-500 to-red-500 text-white px-3 py-1 rounded-full z-10 flex items-center shadow-lg">
                    <svg className="w-3.5 h-3.5 mr-1.5 animate-pulse" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z" clipRule="evenodd" />
                    </svg>
                    <span className="font-bold text-xs">HOT</span>
                </div>
            )}

            {/* Enhanced Image with Better Hover Effects */}
            <img
                src={getCdnUrl(talent.image)}
                alt={talent.name}
                className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110 filter group-hover:brightness-110"
            />

            {/* Enhanced Gradient Overlay */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/30 to-transparent opacity-70 group-hover:opacity-60 transition-opacity"></div>

            {/* Quick Action Buttons */}
            <div className="absolute bottom-3 right-3 flex space-x-2 opacity-0 group-hover:opacity-100 transform translate-y-2 group-hover:translate-y-0 transition-all duration-300">
                <button className="w-8 h-8 rounded-full bg-white/90 backdrop-blur-sm flex items-center justify-center text-indigo-600 shadow-md hover:bg-white transition-colors">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                </button>
                <button className="w-8 h-8 rounded-full bg-white/90 backdrop-blur-sm flex items-center justify-center text-indigo-600 shadow-md hover:bg-white transition-colors">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                    </svg>
                </button>
            </div>

            {/* Level Badge */}
            <div className="absolute bottom-3 left-3 bg-white/90 backdrop-blur-sm px-2.5 py-1 rounded-lg shadow-md flex items-center">
                <span className="font-bold text-xs text-indigo-800">LV{talent.level}</span>
            </div>
        </div>

        {/* Enhanced Profile Info Section */}
        <div className="p-4 relative">
            {/* Name and Gender with Better Styling */}
            <div className="flex justify-between items-center mb-2">
                <div className="flex items-center">
                    <h3 className="font-semibold text-gray-800 group-hover:text-indigo-600 transition-colors">{talent.name}</h3>
                    <span className={`ml-1.5 ${talent.gender === 'female' ? 'text-pink-500' : 'text-blue-500'}`}>
                        {talent.gender === 'male' ? '♂' : '♀'}
                    </span>
                </div>

                {/* Rating with Enhanced Styling */}
                <div className="flex items-center bg-yellow-50 px-2 py-1 rounded-lg border border-yellow-100 group-hover:bg-yellow-100 transition-colors shadow-sm">
                    <svg className="w-3.5 h-3.5 mr-1 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                    <span className="text-xs font-medium text-gray-700">4.5</span>
                </div>
            </div>

            {/* Bio with Better Text Styling */}
            <p className="text-sm text-gray-600 line-clamp-2 mb-3">{talent.bio}</p>

            {/* Enhanced Status and Skills */}
            <div className="flex justify-between items-center">
                {/* Availability Status */}
                <div className="flex items-center bg-green-50 px-2 py-1 rounded-lg border border-green-100 shadow-sm">
                    <span className="w-2 h-2 bg-green-500 rounded-full mr-1.5 animate-pulse"></span>
                    <span className="text-xs font-medium text-green-800">Available Now</span>
                </div>

                {/* Skills Tags */}
                <div className="flex space-x-1">
                    {talent.skills && talent.skills.map((skill, index) => (
                        <span key={index} className="text-xs bg-indigo-50 text-indigo-700 px-2 py-0.5 rounded-md border border-indigo-100">
                            {skill}
                        </span>
                    ))}
                    {!talent.skills && (
                        <span className="text-xs bg-indigo-50 text-indigo-700 px-2 py-0.5 rounded-md border border-indigo-100">
                            Pro Player
                        </span>
                    )}
                </div>
            </div>
        </div>
    </div>
);

const GameTalents = ({ isModal = false, isOpen, onClose, gameId: propGameId, gameName: propGameName }) => {
    const params = useParams();
    const navigate = useNavigate();
    const [talents, setTalents] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    // Get homepage context for refreshing data
    const { refresh, silentRefresh } = useHomepage();

    const gameId = propGameId || params.gameId;
    const gameName = propGameName || params.gameName;

    // Transform API response to component format
    const transformTalentData = useMemo(() => (talent) => {
        return {
            id: talent.id,
            name: talent.name || talent.display_name || talent.nickname,
            level: talent.level || talent.experience_level || 75,
            gender: talent.gender || 'unknown',
            bio: talent.bio || talent.biography || "Professional gamer ready to help you improve your skills.",
            image: talent.profile_image || talent.avatar || talent.profile_picture || '/images/talents/default-avatar.jpg',
            isHot: talent.is_hot || talent.is_featured || false,
            skills: talent.skills || talent.specialties || ['Gaming'],
            rating: talent.rating || talent.average_rating || 4.5,
            hourlyRate: talent.hourly_rate || talent.rate || 20
        };
    }, []);

    useEffect(() => {
        if (isModal && !isOpen) return;

        const fetchTalents = async () => {
            setLoading(true);
            try {
                // Make a real API call to fetch talents for the game
                const response = await api.get(`/talents/by-game/${gameId}`);

                // Process the API response
                if (response && response.data) {
                    // Transform the API data to match our component's expected format
                    const formattedTalents = response.data.map(transformTalentData);
                    setTalents(formattedTalents);
                } else {
                    // Handle empty response
                    setTalents([]);
                    setError('No talents found for this game');
                }
            } catch (err) {
                console.error('Error fetching talents for game:', err);
                setError('Failed to load talents for this game');

                // Provide fallback empty state
                setTalents([]);

                // Refresh homepage data in the background to ensure we have the latest data
                silentRefresh();
            } finally {
                setLoading(false);
            }
        };

        fetchTalents();
    }, [isModal, isOpen, gameId, transformTalentData, silentRefresh]);

    const handleTalentClick = (talentId) => {
        if (isModal) {
            onClose();
        }
        navigate(`/talents/${talentId}`);
    };

    const LoadingView = () => (
        <div className="flex items-center justify-center h-32">
            <div className="w-10 h-10 border-t-4 border-blue-600 border-solid rounded-full animate-spin mx-auto"></div>
            </div>
        );

    const ErrorView = () => (
        <div className="text-center p-4">
            <p className="text-red-500 mb-2">{error}</p>
            {!isModal && (
                    <button
                        onClick={() => navigate(-1)}
                        className="px-6 py-2 rounded-lg font-medium text-white bg-blue-600 hover:bg-blue-700"
                    >
                        Go Back
                    </button>
            )}
            </div>
        );

    const Header = () => (
        <div className={`bg-white ${isModal ? 'border-b border-indigo-100/50 shadow-sm' : 'shadow-lg sticky top-0 z-50 backdrop-filter backdrop-blur-md bg-opacity-90'}`}>
            <div className="container mx-auto px-4">
                <div className="flex justify-between items-center py-4">
                    <div className="flex items-center">
                        <button
                            onClick={isModal ? onClose : () => navigate(-1)}
                            className="mr-3 p-2 rounded-full hover:bg-indigo-50 text-indigo-600 transition-colors"
                        >
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                            </svg>
                        </button>
                        <div>
                            <h1 className="text-xl font-bold text-gray-800">{gameName || 'Mobile Legend Bang Bang'}</h1>
                            <p className="text-xs text-gray-500 mt-0.5">Find the perfect gaming partner</p>
                        </div>
                    </div>

                    <div className="flex items-center space-x-2">
                        <button
                            onClick={() => {
                                setLoading(true);
                                refresh().then(() => {
                                    // Refetch talents after refreshing homepage data
                                    api.get(`/talents/by-game/${gameId}`)
                                        .then(response => {
                                            if (response && response.data) {
                                                const formattedTalents = response.data.map(transformTalentData);
                                                setTalents(formattedTalents);
                                                setError(null);
                                            }
                                        })
                                        .catch(err => {
                                            console.error('Error refreshing talents:', err);
                                            setError('Failed to refresh talents');
                                        })
                                        .finally(() => {
                                            setLoading(false);
                                        });
                                });
                            }}
                            className="p-2 rounded-full bg-indigo-50 hover:bg-indigo-100 transition-colors text-indigo-600"
                            aria-label="Refresh talents"
                        >
                            <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                        </button>
                        <div className="relative">
                            <input
                                type="text"
                                placeholder="Search talents..."
                                className="py-1.5 pl-8 pr-4 rounded-full text-sm border border-indigo-100 focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 w-40 md:w-auto"
                            />
                            <svg className="w-4 h-4 absolute left-2.5 top-1/2 transform -translate-y-1/2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                        </div>
                        <button className="p-2 rounded-full bg-indigo-50 hover:bg-indigo-100 transition-colors text-indigo-600">
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );

    const Content = () => (
        <div className={`${isModal ? 'p-5' : 'container mx-auto px-5 py-6'}`}>
            {/* Filters and Sort Options */}
            {!isModal && (
                <div className="mb-6 flex flex-wrap items-center justify-between gap-4">
                    <div className="flex flex-wrap gap-2">
                        <button className="px-3 py-1.5 bg-indigo-600 text-white rounded-lg text-sm font-medium flex items-center">
                            <span>All</span>
                        </button>
                        <button className="px-3 py-1.5 bg-white border border-gray-200 text-gray-700 rounded-lg text-sm font-medium flex items-center hover:bg-indigo-50 transition-colors">
                            <span>Available Now</span>
                        </button>
                        <button className="px-3 py-1.5 bg-white border border-gray-200 text-gray-700 rounded-lg text-sm font-medium flex items-center hover:bg-indigo-50 transition-colors">
                            <span>Top Rated</span>
                        </button>
                        <button className="px-3 py-1.5 bg-white border border-gray-200 text-gray-700 rounded-lg text-sm font-medium flex items-center hover:bg-indigo-50 transition-colors">
                            <span>New</span>
                        </button>
                    </div>
                    <div className="flex items-center">
                        <span className="text-sm text-gray-500 mr-2">Sort by:</span>
                        <select className="bg-white border border-gray-200 rounded-lg text-sm py-1.5 px-3 text-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
                            <option>Recommended</option>
                            <option>Price: Low to High</option>
                            <option>Price: High to Low</option>
                            <option>Rating</option>
                        </select>
                    </div>
                </div>
            )}

            {/* Talent Grid with Responsive Layout */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-5">
                {talents.map((talent) => (
                    <TalentCard
                        key={talent.id}
                        talent={talent}
                        onClick={() => handleTalentClick(talent.id)}
                    />
                ))}
            </div>

            {/* Pagination for Non-Modal View */}
            {!isModal && talents.length > 0 && (
                <div className="mt-8 flex justify-center">
                    <nav className="flex items-center space-x-2">
                        <button className="p-2 rounded-lg border border-gray-200 text-gray-500 hover:bg-indigo-50 hover:text-indigo-600 transition-colors">
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                            </svg>
                        </button>
                        <button className="px-4 py-2 rounded-lg bg-indigo-600 text-white font-medium">1</button>
                        <button className="px-4 py-2 rounded-lg hover:bg-indigo-50 text-gray-700 font-medium">2</button>
                        <button className="px-4 py-2 rounded-lg hover:bg-indigo-50 text-gray-700 font-medium">3</button>
                        <button className="p-2 rounded-lg border border-gray-200 text-gray-500 hover:bg-indigo-50 hover:text-indigo-600 transition-colors">
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                            </svg>
                        </button>
                    </nav>
                </div>
            )}
        </div>
    );

    if (isModal) {
        if (!isOpen) return null;

        return (
            <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4 animate-fade-in">
                <div
                    className="bg-white rounded-2xl w-full max-w-3xl max-h-[90vh] overflow-hidden flex flex-col animate-modal-slide-in shadow-2xl border border-indigo-100/50"
                    onClick={(e) => e.stopPropagation()}
                >
                    <Header />

                    {/* Game Info Banner (Only in Modal) */}
                    <div className="relative h-32 bg-gradient-to-r from-indigo-600 to-blue-600 overflow-hidden">
                        {/* Background Pattern */}
                        <div className="absolute inset-0 bg-grid-white/[0.05] bg-[length:20px_20px]"></div>

                        {/* Decorative Elements */}
                        <div className="absolute -top-10 -right-10 w-40 h-40 bg-white/10 rounded-full blur-xl"></div>
                        <div className="absolute -bottom-10 -left-10 w-32 h-32 bg-white/10 rounded-full blur-lg"></div>

                        {/* Game Info */}
                        <div className="absolute inset-0 flex items-center p-6">
                            <div className="w-16 h-16 rounded-xl bg-white/10 backdrop-blur-sm flex items-center justify-center mr-4 shadow-lg">
                                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a2 2 0 110-4h1a1 1 0 001-1V7a1 1 0 011-1h3a1 1 0 001-1V4z" />
                                </svg>
                            </div>
                            <div>
                                <h2 className="text-2xl font-bold text-white">{gameName || 'Mobile Legend Bang Bang'}</h2>
                                <div className="flex items-center mt-1">
                                    <div className="flex items-center bg-white/20 backdrop-blur-sm px-2 py-0.5 rounded-lg mr-3">
                                        <svg className="w-3.5 h-3.5 mr-1 text-yellow-300" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                        </svg>
                                        <span className="text-xs font-medium text-white">4.8</span>
                                    </div>
                                    <div className="flex items-center text-white/80 text-xs">
                                        <svg className="w-3.5 h-3.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                                        </svg>
                                        <span>10.2k players</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Content Area */}
                    <div className="overflow-y-auto flex-grow custom-scrollbar">
                        {loading ? (
                            <div className="flex items-center justify-center h-64">
                                <LoadingView />
                            </div>
                        ) : error ? (
                            <div className="flex items-center justify-center h-64">
                                <ErrorView />
                            </div>
                        ) : (
                            <Content />
                        )}
                    </div>

                    {/* Footer with Action Button */}
                    <div className="p-4 border-t border-gray-100 bg-gray-50/80 backdrop-blur-sm">
                        <div className="flex justify-between items-center">
                            <p className="text-sm text-gray-500">Showing {talents.length} available talents</p>
                            <button
                                onClick={onClose}
                                className="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg text-sm font-medium transition-colors"
                            >
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gradient-to-b from-indigo-50 to-white">
            <Header />

            {/* Hero Banner for Full Page View */}
            <div className="bg-gradient-to-r from-indigo-600 to-blue-600 py-12 mb-8">
                <div className="container mx-auto px-5">
                    <div className="flex flex-col md:flex-row items-center justify-between">
                        <div className="mb-6 md:mb-0 md:mr-8">
                            <h1 className="text-3xl md:text-4xl font-bold text-white mb-3">{gameName || 'Mobile Legend Bang Bang'}</h1>
                            <p className="text-indigo-100 text-lg max-w-xl">Find the perfect gaming partner to level up your gameplay experience. Connect with skilled players ready to team up!</p>

                            <div className="flex items-center mt-4 space-x-4">
                                <div className="flex items-center bg-white/20 backdrop-blur-sm px-3 py-1 rounded-lg">
                                    <svg className="w-4 h-4 mr-1.5 text-yellow-300" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                    </svg>
                                    <span className="text-white font-medium">4.8 Rating</span>
                                </div>
                                <div className="flex items-center bg-white/20 backdrop-blur-sm px-3 py-1 rounded-lg">
                                    <svg className="w-4 h-4 mr-1.5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                                    </svg>
                                    <span className="text-white font-medium">10.2k Players</span>
                                </div>
                            </div>
                        </div>

                        <div className="w-full md:w-auto">
                            <div className="relative w-full md:w-64 h-32 bg-white/10 backdrop-blur-sm rounded-xl overflow-hidden shadow-lg">
                                <div className="absolute inset-0 bg-grid-white/[0.05] bg-[length:20px_20px]"></div>
                                <div className="absolute inset-0 flex items-center justify-center">
                                    <svg className="w-16 h-16 text-white/80" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a2 2 0 110-4h1a1 1 0 001-1V7a1 1 0 011-1h3a1 1 0 001-1V4z" />
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Main Content */}
            {loading ? (
                <div className="flex items-center justify-center min-h-[40vh]">
                    <LoadingView />
                </div>
            ) : error ? (
                <div className="flex items-center justify-center min-h-[40vh]">
                    <ErrorView />
                </div>
            ) : (
                <Content />
            )}
        </div>
    );
};

export default GameTalents;