{"common": {"wallet": "Wallet", "balance": "Balance", "available": "Available", "pending": "Pending", "total": "Total", "currency": "<PERSON><PERSON><PERSON><PERSON>", "amount": "Amount", "date": "Date", "status": "Status", "description": "Description", "reference": "Reference", "type": "Type"}, "dashboard": {"title": "X Wallet", "subtitle": "Manage your funds", "currentBalance": "Current Balance", "pendingBalance": "Pending Balance", "totalBalance": "Total Balance", "quickActions": "Quick Actions", "recentTransactions": "Recent Transactions", "viewAll": "View All", "noTransactions": "No transactions yet"}, "transactions": {"title": "Transactions", "subtitle": "View your transaction history", "filter": {"title": "Filter Transactions", "all": "All Transactions", "topUp": "Top Up", "withdrawal": "<PERSON><PERSON><PERSON>", "payment": "Payment", "refund": "Refund", "reward": "<PERSON><PERSON>", "dateRange": "Date Range", "startDate": "Start Date", "endDate": "End Date", "apply": "Apply Filters", "clear": "Clear Filters"}, "status": {"completed": "Completed", "pending": "Pending", "failed": "Failed", "cancelled": "Cancelled", "refunded": "Refunded"}, "types": {"topUp": "Top Up", "withdrawal": "<PERSON><PERSON><PERSON>", "payment": "Payment", "refund": "Refund", "reward": "<PERSON><PERSON>", "transfer": "Transfer", "missionPayout": "Mission Payout", "serviceFee": "Service Fee"}, "details": {"title": "Transaction Details", "id": "Transaction ID", "date": "Date & Time", "amount": "Amount", "status": "Status", "type": "Type", "description": "Description", "reference": "Reference", "paymentMethod": "Payment Method", "recipient": "Recipient", "sender": "Sender", "fee": "Fee", "total": "Total"}, "empty": "No transactions found", "loadMore": "Load More"}, "topUp": {"title": "Top Up", "subtitle": "Add funds to your wallet", "amount": "Amount", "selectAmount": "Select Amount", "customAmount": "Custom Amount", "paymentMethod": "Payment Method", "addPaymentMethod": "Add Payment Method", "summary": "Summary", "subtotal": "Subtotal", "fee": "Processing Fee", "total": "Total", "button": "Top Up Now", "success": "Top up successful!", "error": "Top up failed", "processing": "Processing payment..."}, "withdraw": {"title": "Withdraw", "subtitle": "Withdraw funds from your wallet", "amount": "Amount", "availableBalance": "Available Balance", "withdrawalMethod": "<PERSON><PERSON><PERSON> Method", "addWithdrawalMethod": "<PERSON><PERSON>", "bankAccount": "Bank Account", "addBankAccount": "Add Bank Account", "summary": "Summary", "subtotal": "Subtotal", "fee": "Processing Fee", "total": "Total", "button": "Withdraw Now", "success": "Withdrawal request submitted!", "error": "<PERSON><PERSON>wal request failed", "processing": "Processing withdrawal...", "minimumAmount": "Minimum withdrawal amount is {{amount}}", "maximumAmount": "Maximum withdrawal amount is {{amount}}"}, "bankAccounts": {"title": "Bank Accounts", "subtitle": "Manage your bank accounts", "addAccount": "Add Bank Account", "editAccount": "Edit Bank Account", "deleteAccount": "Delete Bank Account", "setDefault": "Set as <PERSON><PERSON><PERSON>", "fields": {"accountName": "Account Name", "accountNumber": "Account Number", "bankName": "Bank Name", "branchCode": "Branch Code", "swiftCode": "SWIFT Code", "country": "Country"}, "placeholders": {"accountName": "Enter account name", "accountNumber": "Enter account number", "bankName": "Select bank", "branchCode": "Enter branch code", "swiftCode": "Enter SWIFT code", "country": "Select country"}, "success": {"add": "Bank account added successfully!", "edit": "Bank account updated successfully!", "delete": "Bank account deleted successfully!", "default": "Default bank account updated successfully!"}, "error": {"add": "Failed to add bank account", "edit": "Failed to update bank account", "delete": "Failed to delete bank account", "default": "Failed to update default bank account"}, "info": {"securityTitle": "Bank Account Security", "securityDesc": "Your bank account details are securely stored. We will never share these details with other users or third parties without your consent.", "primaryDesc": "The primary account will be used as the default option for withdrawals."}, "confirmDelete": "Are you sure you want to delete this bank account?", "noAccounts": "No bank accounts added yet"}}