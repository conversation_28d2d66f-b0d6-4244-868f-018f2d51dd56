{"i18n": {"demo": {"title": "Internationalization Demo", "subtitle": "Explore the multilingual capabilities of Mission X", "currentLanguage": "Current Language", "languageCode": "Language Code", "textDirection": "Text Direction", "languageInfo": "This demo automatically adapts to the selected language, including text direction, date formats, and number formats.", "translationExamples": "Translation Examples", "greetings": "Greetings", "hello": "Hello", "welcome": "Welcome to Mission X", "thankYou": "Thank you for your interest", "navigation": "Navigation", "home": "Home", "profile": "Profile", "settings": "Settings", "logout": "Logout", "messages": "Messages", "success": "Operation completed successfully", "error": "An error occurred. Please try again.", "loading": "Loading...", "buttons": "Buttons", "save": "Save", "cancel": "Cancel", "submit": "Submit", "delete": "Delete", "rtlSupport": "RTL Support", "rtlDescription": "Mission X supports Right-to-Left (RTL) languages like Arabic and Hebrew. When you switch to an RTL language, the entire interface adapts automatically.", "rtlFeatures": "RTL Features", "rtlFeature1": "Text alignment automatically switches to right-aligned", "rtlFeature2": "Directional icons are flipped", "rtlFeature3": "Form elements adapt to RTL layout", "rtlFeature4": "Navigation and menus adjust for RTL reading", "rtlFeature5": "Margins and paddings are mirrored"}, "formattingDemo": {"title": "Internationalization Formatting Demo", "description": "This demo shows how dates, times, numbers, and currencies are formatted based on the current language ({{language}}).", "dateFormatting": "Date Formatting", "shortDate": "Short Date", "longDate": "Long Date", "customDate": "Custom Date Format", "relativeTime": "Relative Time", "past": "Past", "future": "Future", "timeFormatting": "Time Formatting", "shortTime": "Short Time", "longTime": "Long Time with Seconds", "timeWithTimeZone": "Time with Timezone", "numberFormatting": "Number Formatting", "decimal": "Decimal Number", "integer": "Integer", "percentage": "Percentage", "currencyFormatting": "<PERSON><PERSON><PERSON><PERSON>ing", "localCurrency": "Local Currency (MYR)", "usd": "US Dollar", "eur": "Euro", "jpy": "Japanese Yen"}}}