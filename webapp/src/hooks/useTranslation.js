import { useTranslation as useI18nTranslation } from 'react-i18next';
import { useCallback } from 'react';
import { useLanguage } from '../contexts/LanguageContext';
import { namespaces } from '../translations';

/**
 * Custom hook for translation functionality
 * Extends react-i18next's useTranslation hook with additional features
 *
 * @param {string|string[]} ns - Namespace or array of namespaces to use
 * @returns {object} Enhanced translation utilities
 */
export const useTranslation = (ns) => {
  // Use the base hook from react-i18next with optional namespace
  const { t, i18n: i18nInstance } = useI18nTranslation(ns);

  // Use the language context
  const {
    currentLanguage,
    isLanguageLoading,
    supportedLanguages,
    changeLanguage: contextChangeLanguage,
    getLanguageDisplayName,
    dir
  } = useLanguage();

  /**
   * Function to change the current language
   * This is a wrapper around the context's changeLanguage function
   *
   * @param {string} language - Language code to change to
   * @param {Object} options - Additional options
   */
  const changeLanguage = useCallback((language, options = {}) => {
    contextChangeLanguage(language, options);
  }, [contextChangeLanguage]);

  /**
   * Translate with a specific namespace
   *
   * @param {string} key - Translation key
   * @param {string} namespace - Namespace to use
   * @param {object} options - Translation options
   * @returns {string} Translated text
   */
  const tWithNamespace = useCallback((key, namespace, options = {}) => {
    return t(key, { ...options, ns: namespace });
  }, [t]);

  /**
   * Format a date according to the current locale
   *
   * @param {Date|string|number} date - The date to format
   * @param {Object} options - Intl.DateTimeFormat options
   * @returns {string} The formatted date
   */
  const formatDate = useCallback((date, options = {}) => {
    const dateObj = date instanceof Date ? date : new Date(date);
    return new Intl.DateTimeFormat(currentLanguage, options).format(dateObj);
  }, [currentLanguage]);

  /**
   * Format a number according to the current locale
   *
   * @param {number} number - The number to format
   * @param {Object} options - Intl.NumberFormat options
   * @returns {string} The formatted number
   */
  const formatNumber = useCallback((number, options = {}) => {
    return new Intl.NumberFormat(currentLanguage, options).format(number);
  }, [currentLanguage]);

  // Return extended functionality
  return {
    // Original translation function
    t,
    // Translation function with explicit namespace
    tns: tWithNamespace,
    // i18n instance for advanced usage
    i18n: i18nInstance,
    // Function to change language
    changeLanguage,
    // Current language code
    currentLanguage,
    // Loading state
    isLanguageLoading,
    // Get display name of current language
    currentLanguageDisplayName: getLanguageDisplayName(currentLanguage),
    // Function to get display name of any language
    getLanguageDisplayName,
    // List of supported languages
    supportedLanguages,
    // Available namespaces
    availableNamespaces: namespaces,
    // Text direction
    dir,
    // Formatting functions
    formatDate,
    formatNumber
  };
};

export default useTranslation;