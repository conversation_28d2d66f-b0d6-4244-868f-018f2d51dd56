import { useState, useEffect, useCallback, useRef } from 'react';
import globalBalanceService from '../services/globalBalanceService';

/**
 * Custom hook for global balance management
 *
 * This hook provides a consistent interface for accessing and updating
 * user balance across all components. It automatically subscribes to
 * balance updates and provides loading states.
 *
 * @param {Object} options - Hook configuration options
 * @param {boolean} options.autoLoad - Whether to automatically load balance on mount
 * @param {boolean} options.enablePolling - Whether to enable periodic balance polling
 * @param {number} options.pollingInterval - Polling interval in milliseconds
 * @returns {Object} Balance state and actions
 */
const useGlobalBalance = (options = {}) => {
  const {
    autoLoad = true,
    enablePolling = false,
    pollingInterval = 60000 // 1 minute
  } = options;

  // State
  const [balance, setBalance] = useState(globalBalanceService.getCachedBalance());
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [lastUpdate, setLastUpdate] = useState(null);

  // Refs for cleanup
  const unsubscribeRef = useRef(null);
  const pollingIntervalRef = useRef(null);
  const mountedRef = useRef(true);

  /**
   * Handle balance updates from the global service
   */
  const handleBalanceUpdate = useCallback((newBalance) => {
    if (!mountedRef.current) return;

    setBalance(newBalance);
    setLastUpdate(Date.now());
    setError(null);
  }, []);

  /**
   * Load balance from the service
   */
  const loadBalance = useCallback(async (forceRefresh = false) => {
    if (!mountedRef.current) return;

    try {
      setLoading(true);
      setError(null);

      const newBalance = await globalBalanceService.getBalance(forceRefresh);

      if (mountedRef.current) {
        setBalance(newBalance);
        setLastUpdate(Date.now());
      }

      return newBalance;
    } catch (err) {
      if (mountedRef.current) {
        setError(err.message || 'Failed to load balance');
        console.error('Error loading balance:', err);
      }
      throw err;
    } finally {
      if (mountedRef.current) {
        setLoading(false);
      }
    }
  }, []);

  /**
   * Refresh balance (force reload)
   */
  const refreshBalance = useCallback(async () => {
    return loadBalance(true);
  }, [loadBalance]);

  /**
   * Update balance manually (for optimistic updates)
   */
  const updateBalance = useCallback((newBalance) => {
    globalBalanceService.updateBalance(newBalance);
  }, []);

  /**
   * Invalidate balance cache
   */
  const invalidateBalance = useCallback(() => {
    globalBalanceService.invalidateCache();
  }, []);

  /**
   * Get formatted balance string
   */
  const formatBalance = useCallback((balanceValue = balance) => {
    return globalBalanceService.formatBalance(balanceValue);
  }, [balance]);

  /**
   * Get balance status information
   */
  const getBalanceStatus = useCallback(() => {
    return globalBalanceService.getBalanceStatus();
  }, []);

  // Subscribe to balance updates on mount
  useEffect(() => {
    // Subscribe to global balance updates
    unsubscribeRef.current = globalBalanceService.subscribe(handleBalanceUpdate);

    // Load initial balance if autoLoad is enabled
    if (autoLoad) {
      loadBalance();
    }

    // Setup polling if enabled
    if (enablePolling && pollingInterval > 0) {
      pollingIntervalRef.current = setInterval(() => {
        loadBalance(true);
      }, pollingInterval);
    }

    // Cleanup function
    return () => {
      mountedRef.current = false;

      // Unsubscribe from balance updates
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
      }

      // Clear polling interval
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
      }
    };
  }, [autoLoad, enablePolling, pollingInterval, loadBalance, handleBalanceUpdate]);

  // Update mounted ref on unmount
  useEffect(() => {
    return () => {
      mountedRef.current = false;
    };
  }, []);

  return {
    // State
    balance,
    loading,
    error,
    lastUpdate,

    // Actions
    loadBalance,
    refreshBalance,
    updateBalance,
    invalidateBalance,

    // Utilities
    formatBalance,
    getBalanceStatus,

    // Status checks
    hasBalance: balance !== null,
    isStale: lastUpdate && (Date.now() - lastUpdate) > 30000, // 30 seconds
    isCached: globalBalanceService.hasCache()
  };
};

export default useGlobalBalance;
