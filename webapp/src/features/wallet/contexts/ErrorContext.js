import React, { createContext, useContext, useReducer, useCallback, useEffect } from 'react';
import errorHandlingService from '../../../services/errorHandlingService';
import { WALLET_ERROR_TYPES, API_ERROR_TYPES } from '../../../utils/errorTypes';

/**
 * Wallet Error Context
 * 
 * This context provides centralized error state management for wallet operations,
 * including error tracking, recovery mechanisms, and user guidance.
 */

// Error action types
const ERROR_ACTIONS = {
  SET_ERROR: 'SET_ERROR',
  CLEAR_ERROR: 'CLEAR_ERROR',
  CLEAR_ALL_ERRORS: 'CLEAR_ALL_ERRORS',
  SET_RECOVERY_STATE: 'SET_RECOVERY_STATE',
  INCREMENT_RETRY_COUNT: 'INCREMENT_RETRY_COUNT',
  RESET_RETRY_COUNT: 'RESET_RETRY_COUNT',
  SET_ERROR_HISTORY: 'SET_ERROR_HISTORY'
};

// Initial state
const initialState = {
  // Current errors by context
  errors: {
    balance: null,
    transactions: null,
    payments: null,
    withdrawals: null,
    verification: null,
    general: null
  },
  
  // Recovery state
  recovery: {
    inProgress: false,
    context: null,
    attempts: 0,
    lastAttempt: null
  },
  
  // Error statistics
  stats: {
    totalErrors: 0,
    errorsByType: {},
    recentErrors: []
  },
  
  // UI state
  showErrorToast: false,
  dismissedErrors: new Set()
};

// Error reducer
const errorReducer = (state, action) => {
  switch (action.type) {
    case ERROR_ACTIONS.SET_ERROR:
      const { context, error } = action.payload;
      return {
        ...state,
        errors: {
          ...state.errors,
          [context]: error
        },
        stats: {
          ...state.stats,
          totalErrors: state.stats.totalErrors + 1,
          errorsByType: {
            ...state.stats.errorsByType,
            [error.type]: (state.stats.errorsByType[error.type] || 0) + 1
          },
          recentErrors: [error, ...state.stats.recentErrors.slice(0, 9)]
        },
        showErrorToast: error.severity === 'high' || error.severity === 'critical'
      };

    case ERROR_ACTIONS.CLEAR_ERROR:
      const { context: clearContext } = action.payload;
      return {
        ...state,
        errors: {
          ...state.errors,
          [clearContext]: null
        },
        showErrorToast: false
      };

    case ERROR_ACTIONS.CLEAR_ALL_ERRORS:
      return {
        ...state,
        errors: {
          balance: null,
          transactions: null,
          payments: null,
          withdrawals: null,
          verification: null,
          general: null
        },
        showErrorToast: false
      };

    case ERROR_ACTIONS.SET_RECOVERY_STATE:
      return {
        ...state,
        recovery: {
          ...state.recovery,
          ...action.payload
        }
      };

    case ERROR_ACTIONS.INCREMENT_RETRY_COUNT:
      return {
        ...state,
        recovery: {
          ...state.recovery,
          attempts: state.recovery.attempts + 1,
          lastAttempt: Date.now()
        }
      };

    case ERROR_ACTIONS.RESET_RETRY_COUNT:
      return {
        ...state,
        recovery: {
          ...state.recovery,
          attempts: 0,
          lastAttempt: null
        }
      };

    default:
      return state;
  }
};

// Create context
const WalletErrorContext = createContext();

// Custom hook to use wallet error context
export const useWalletError = () => {
  const context = useContext(WalletErrorContext);
  if (!context) {
    throw new Error('useWalletError must be used within a WalletErrorProvider');
  }
  return context;
};

// Wallet Error Provider component
export const WalletErrorProvider = ({ children }) => {
  const [state, dispatch] = useReducer(errorReducer, initialState);

  /**
   * Set an error for a specific context
   */
  const setError = useCallback((context, error, metadata = {}) => {
    // Process error through error handling service
    const processedError = errorHandlingService.processError(error, context, metadata);
    
    dispatch({
      type: ERROR_ACTIONS.SET_ERROR,
      payload: { context, error: processedError }
    });

    // Auto-clear non-critical errors after 10 seconds
    if (processedError.severity === 'low' || processedError.severity === 'medium') {
      setTimeout(() => {
        clearError(context);
      }, 10000);
    }
  }, []);

  /**
   * Clear error for a specific context
   */
  const clearError = useCallback((context) => {
    dispatch({
      type: ERROR_ACTIONS.CLEAR_ERROR,
      payload: { context }
    });
    
    // Clear retry attempts for this context
    errorHandlingService.clearRetryAttempts(context);
  }, []);

  /**
   * Clear all errors
   */
  const clearAllErrors = useCallback(() => {
    dispatch({ type: ERROR_ACTIONS.CLEAR_ALL_ERRORS });
  }, []);

  /**
   * Start error recovery process
   */
  const startRecovery = useCallback((context, recoveryFunction) => {
    dispatch({
      type: ERROR_ACTIONS.SET_RECOVERY_STATE,
      payload: {
        inProgress: true,
        context,
        attempts: 0
      }
    });

    return recoveryFunction();
  }, []);

  /**
   * Complete error recovery
   */
  const completeRecovery = useCallback((success = true) => {
    if (success) {
      // Clear the error that was being recovered from
      if (state.recovery.context) {
        clearError(state.recovery.context);
      }
    }

    dispatch({
      type: ERROR_ACTIONS.SET_RECOVERY_STATE,
      payload: {
        inProgress: false,
        context: null,
        attempts: 0,
        lastAttempt: null
      }
    });
  }, [state.recovery.context, clearError]);

  /**
   * Retry an operation with error handling
   */
  const retryOperation = useCallback(async (context, operation, maxRetries = 3) => {
    const currentError = state.errors[context];
    
    if (!currentError || !currentError.canRetry) {
      return { success: false, error: 'Operation cannot be retried' };
    }

    if (state.recovery.attempts >= maxRetries) {
      return { success: false, error: 'Maximum retry attempts exceeded' };
    }

    try {
      dispatch({ type: ERROR_ACTIONS.INCREMENT_RETRY_COUNT });
      errorHandlingService.recordRetryAttempt(context);

      const result = await operation();
      
      // Success - clear error and reset retry count
      clearError(context);
      dispatch({ type: ERROR_ACTIONS.RESET_RETRY_COUNT });
      
      return { success: true, result };
    } catch (error) {
      // Retry failed - set new error
      setError(context, error, { isRetry: true, attempt: state.recovery.attempts + 1 });
      
      return { success: false, error };
    }
  }, [state.errors, state.recovery.attempts, setError, clearError]);

  /**
   * Get error for a specific context
   */
  const getError = useCallback((context) => {
    return state.errors[context];
  }, [state.errors]);

  /**
   * Check if there are any errors
   */
  const hasErrors = useCallback(() => {
    return Object.values(state.errors).some(error => error !== null);
  }, [state.errors]);

  /**
   * Check if there are any critical errors
   */
  const hasCriticalErrors = useCallback(() => {
    return Object.values(state.errors).some(error => 
      error && error.severity === 'critical'
    );
  }, [state.errors]);

  /**
   * Get all current errors
   */
  const getAllErrors = useCallback(() => {
    return Object.entries(state.errors)
      .filter(([_, error]) => error !== null)
      .map(([context, error]) => ({ context, ...error }));
  }, [state.errors]);

  /**
   * Get error statistics
   */
  const getErrorStats = useCallback(() => {
    return {
      ...state.stats,
      serviceStats: errorHandlingService.getErrorStats()
    };
  }, [state.stats]);

  /**
   * Dismiss error toast
   */
  const dismissErrorToast = useCallback(() => {
    dispatch({
      type: ERROR_ACTIONS.SET_RECOVERY_STATE,
      payload: { showErrorToast: false }
    });
  }, []);

  // Auto-recovery for certain error types
  useEffect(() => {
    const autoRecoverableErrors = [
      API_ERROR_TYPES.NETWORK_ERROR,
      WALLET_ERROR_TYPES.BALANCE_FETCH_FAILED,
      WALLET_ERROR_TYPES.SERVICE_UNAVAILABLE
    ];

    Object.entries(state.errors).forEach(([context, error]) => {
      if (error && autoRecoverableErrors.includes(error.type) && !state.recovery.inProgress) {
        // Auto-retry after 5 seconds for network errors
        if (error.retryCount < 2) {
          setTimeout(() => {
            // This would trigger a retry in the consuming component
            console.log(`Auto-retry scheduled for ${context} error`);
          }, 5000);
        }
      }
    });
  }, [state.errors, state.recovery.inProgress]);

  const value = {
    // State
    errors: state.errors,
    recovery: state.recovery,
    stats: state.stats,
    showErrorToast: state.showErrorToast,

    // Actions
    setError,
    clearError,
    clearAllErrors,
    startRecovery,
    completeRecovery,
    retryOperation,

    // Getters
    getError,
    hasErrors,
    hasCriticalErrors,
    getAllErrors,
    getErrorStats,

    // UI Actions
    dismissErrorToast
  };

  return (
    <WalletErrorContext.Provider value={value}>
      {children}
    </WalletErrorContext.Provider>
  );
};

export default WalletErrorContext;
