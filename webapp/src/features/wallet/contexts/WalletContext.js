import React, { createContext, useContext, useReducer, useCallback } from 'react';
import { CreditService, ValidationService, WithdrawalService, WalletVerificationService } from '../services';
import globalBalanceService from '../../../services/globalBalanceService';
import errorHandlingService from '../../../services/errorHandlingService';
import errorRecoveryService from '../services/ErrorRecoveryService';
import { WalletErrorProvider, useWalletError } from './ErrorContext';
import { WALLET_ERROR_TYPES, API_ERROR_TYPES } from '../../../utils/errorTypes';
import { walletConstants } from '../constants';

// Create the context
const WalletContext = createContext();

// Action types
const WALLET_ACTIONS = {
  SET_LOADING: 'SET_LOADING',
  SET_ERROR: 'SET_ERROR',
  SET_BALANCE: 'SET_BALANCE',
  SET_TRANSACTIONS: 'SET_TRANSACTIONS',
  ADD_TRANSACTION: 'ADD_TRANSACTION',
  UPDATE_TRANSACTION: 'UPDATE_TRANSACTION',
  SET_CREDIT_PACKAGES: 'SET_CREDIT_PACKAGES',
  SET_PAYMENT_STATUS: 'SET_PAYMENT_STATUS',
  SET_INITIALIZED: 'SET_INITIALIZED',
  CLEAR_ERROR: 'CLEAR_ERROR',
  RESET_STATE: 'RESET_STATE',

  // Withdrawal actions
  SET_WITHDRAWALS: 'SET_WITHDRAWALS',
  ADD_WITHDRAWAL: 'ADD_WITHDRAWAL',
  UPDATE_WITHDRAWAL: 'UPDATE_WITHDRAWAL',
  SET_WITHDRAWAL_ELIGIBILITY: 'SET_WITHDRAWAL_ELIGIBILITY',
  SET_WITHDRAWAL_CURRENCIES: 'SET_WITHDRAWAL_CURRENCIES',
  SET_WITHDRAWAL_STATUS: 'SET_WITHDRAWAL_STATUS',

  // Verification actions
  SET_VERIFICATION_STATUS: 'SET_VERIFICATION_STATUS'
};

// Initial state
const initialState = {
  // Balance state
  balance: null,
  balanceLoading: false,
  balanceError: null,
  lastBalanceUpdate: null,

  // Transactions state
  transactions: [],
  transactionsLoading: false,
  transactionsError: null,
  lastTransactionUpdate: null,
  transactionFilters: {
    type: null,
    limit: 20,
    offset: 0
  },

  // Credit packages state
  creditPackages: [],
  packagesLoading: false,
  packagesError: null,

  // Payment state
  paymentStatus: null,
  paymentLoading: false,
  paymentError: null,

  // Withdrawal state
  withdrawals: [],
  withdrawalsLoading: false,
  withdrawalsError: null,
  lastWithdrawalUpdate: null,
  withdrawalFilters: {
    status: null,
    limit: 20,
    offset: 0
  },

  // Withdrawal eligibility state
  withdrawalEligibility: null,
  eligibilityLoading: false,
  eligibilityError: null,
  lastEligibilityCheck: null,

  // Withdrawal currencies state
  withdrawalCurrencies: [],
  currenciesLoading: false,
  currenciesError: null,

  // Current withdrawal status
  withdrawalStatus: null,
  withdrawalLoading: false,
  withdrawalError: null,

  // Verification state
  verificationStatus: null,
  verificationLoading: false,
  verificationError: null,
  lastVerificationCheck: null,

  // Global state
  isInitialized: false,
  globalError: null
};

// Reducer function
const walletReducer = (state, action) => {
  switch (action.type) {
    case WALLET_ACTIONS.SET_LOADING:
      return {
        ...state,
        [`${action.payload.type}Loading`]: action.payload.loading
      };

    case WALLET_ACTIONS.SET_ERROR:
      return {
        ...state,
        [`${action.payload.type}Error`]: action.payload.error,
        [`${action.payload.type}Loading`]: false
      };

    case WALLET_ACTIONS.SET_BALANCE:
      return {
        ...state,
        balance: action.payload.balance,
        balanceLoading: false,
        balanceError: null,
        lastBalanceUpdate: Date.now()
      };

    case WALLET_ACTIONS.SET_TRANSACTIONS:
      return {
        ...state,
        transactions: action.payload.replace ? action.payload.transactions :
                     [...state.transactions, ...action.payload.transactions],
        transactionsLoading: false,
        transactionsError: null,
        lastTransactionUpdate: Date.now()
      };

    case WALLET_ACTIONS.ADD_TRANSACTION:
      return {
        ...state,
        transactions: [action.payload.transaction, ...state.transactions]
      };

    case WALLET_ACTIONS.UPDATE_TRANSACTION:
      return {
        ...state,
        transactions: state.transactions.map(tx =>
          tx.id === action.payload.transactionId
            ? { ...tx, ...action.payload.updates }
            : tx
        )
      };

    case WALLET_ACTIONS.SET_CREDIT_PACKAGES:
      return {
        ...state,
        creditPackages: action.payload.packages,
        packagesLoading: false,
        packagesError: null
      };

    case WALLET_ACTIONS.SET_PAYMENT_STATUS:
      return {
        ...state,
        paymentStatus: action.payload.status,
        paymentLoading: false,
        paymentError: null
      };

    case WALLET_ACTIONS.SET_INITIALIZED:
      return {
        ...state,
        isInitialized: action.payload.initialized
      };

    case WALLET_ACTIONS.CLEAR_ERROR:
      return {
        ...state,
        [`${action.payload.type}Error`]: null
      };

    case WALLET_ACTIONS.SET_WITHDRAWALS:
      return {
        ...state,
        withdrawals: action.payload.replace ? action.payload.withdrawals :
                    [...state.withdrawals, ...action.payload.withdrawals],
        withdrawalsLoading: false,
        withdrawalsError: null,
        lastWithdrawalUpdate: Date.now()
      };

    case WALLET_ACTIONS.ADD_WITHDRAWAL:
      return {
        ...state,
        withdrawals: [action.payload.withdrawal, ...state.withdrawals]
      };

    case WALLET_ACTIONS.UPDATE_WITHDRAWAL:
      return {
        ...state,
        withdrawals: Array.isArray(state.withdrawals)
          ? state.withdrawals.map(withdrawal =>
              withdrawal && withdrawal.id === action.payload.withdrawalId
                ? { ...withdrawal, ...action.payload.updates }
                : withdrawal
            ).filter(Boolean)
          : []
      };

    case WALLET_ACTIONS.SET_WITHDRAWAL_ELIGIBILITY:
      return {
        ...state,
        withdrawalEligibility: action.payload.eligibility,
        eligibilityLoading: false,
        eligibilityError: null,
        lastEligibilityCheck: Date.now()
      };

    case WALLET_ACTIONS.SET_WITHDRAWAL_CURRENCIES:
      return {
        ...state,
        withdrawalCurrencies: action.payload.currencies,
        currenciesLoading: false,
        currenciesError: null
      };

    case WALLET_ACTIONS.SET_WITHDRAWAL_STATUS:
      return {
        ...state,
        withdrawalStatus: action.payload.status,
        withdrawalLoading: false,
        withdrawalError: null
      };

    case WALLET_ACTIONS.SET_VERIFICATION_STATUS:
      return {
        ...state,
        verificationStatus: action.payload.status,
        verificationLoading: false,
        verificationError: null,
        lastVerificationCheck: Date.now()
      };

    case WALLET_ACTIONS.RESET_STATE:
      return {
        ...initialState,
        isInitialized: false
      };

    default:
      return state;
  }
};

/**
 * WalletProvider component
 *
 * Provides global wallet state management including balance, transactions,
 * payment status, and credit packages. Integrates with CreditService for
 * data operations and caching.
 */
export const WalletProvider = ({ children }) => {
  const [state, dispatch] = useReducer(walletReducer, initialState);

  /**
   * Load user balance with enhanced error handling and fallback mechanisms
   */
  const loadBalance = useCallback(async (forceRefresh = false, options = {}) => {
    const { skipErrorHandling = false, context = 'balance' } = options;

    try {
      dispatch({ type: WALLET_ACTIONS.SET_LOADING, payload: { type: 'balance', loading: true } });

      // Use global balance service for synchronized balance
      const balance = await globalBalanceService.getBalance(forceRefresh);

      // Ensure balance is a valid number
      if (typeof balance !== 'number' || balance < 0) {
        throw new Error(`Invalid balance value received: ${balance}`);
      }

      dispatch({
        type: WALLET_ACTIONS.SET_BALANCE,
        payload: { balance: { credits: balance } }
      });

      // Clear any previous balance errors on success
      if (!skipErrorHandling) {
        errorRecoveryService.clearRecoveryAttempts(context);
      }

      return balance;
    } catch (error) {
      console.error('Error loading balance:', error);

      if (!skipErrorHandling) {
        // Process error through enhanced error handling
        const processedError = errorHandlingService.processError(error, context, {
          operation: 'loadBalance',
          forceRefresh,
          timestamp: Date.now()
        });

        // Set error state
        dispatch({
          type: WALLET_ACTIONS.SET_ERROR,
          payload: { type: 'balance', error: processedError }
        });

        // For authentication errors, clear the balance
        if (processedError.type === API_ERROR_TYPES.AUTHENTICATION_ERROR) {
          dispatch({
            type: WALLET_ACTIONS.SET_BALANCE,
            payload: { balance: null }
          });
        }
      } else {
        // Simple error handling for internal retries
        dispatch({
          type: WALLET_ACTIONS.SET_ERROR,
          payload: { type: 'balance', error: error.message }
        });
      }

      throw error;
    }
  }, []);

  /**
   * Load transaction history
   */
  const loadTransactions = useCallback(async (options = {}) => {
    const {
      limit = state.transactionFilters.limit,
      offset = state.transactionFilters.offset,
      type = state.transactionFilters.type,
      replace = false,
      forceRefresh = false
    } = options;

    try {
      dispatch({ type: WALLET_ACTIONS.SET_LOADING, payload: { type: 'transactions', loading: true } });

      const transactions = await CreditService.getTransactionHistory({
        limit,
        offset,
        type,
        forceRefresh
      });

      dispatch({
        type: WALLET_ACTIONS.SET_TRANSACTIONS,
        payload: { transactions, replace }
      });

      return transactions;
    } catch (error) {
      console.error('Error loading transactions:', error);
      dispatch({
        type: WALLET_ACTIONS.SET_ERROR,
        payload: { type: 'transactions', error: error.message }
      });
      throw error;
    }
  }, [state.transactionFilters]);

  /**
   * Load credit packages
   */
  const loadCreditPackages = useCallback(async (channel = 'default') => {
    try {
      dispatch({ type: WALLET_ACTIONS.SET_LOADING, payload: { type: 'packages', loading: true } });

      const packages = await CreditService.getCreditPackages(channel);

      dispatch({
        type: WALLET_ACTIONS.SET_CREDIT_PACKAGES,
        payload: { packages }
      });

      return packages;
    } catch (error) {
      console.error('Error loading credit packages:', error);
      dispatch({
        type: WALLET_ACTIONS.SET_ERROR,
        payload: { type: 'packages', error: error.message }
      });
      throw error;
    }
  }, []);

  /**
   * Process payment for credit package
   */
  const processPayment = useCallback(async (packageId, options = {}) => {
    try {
      dispatch({ type: WALLET_ACTIONS.SET_LOADING, payload: { type: 'payment', loading: true } });

      const result = await CreditService.processPayment(packageId, {
        ...options,
        onSuccess: (payment) => {
          dispatch({
            type: WALLET_ACTIONS.SET_PAYMENT_STATUS,
            payload: { status: { ...payment, status: 'success' } }
          });

          // Invalidate global balance cache and refresh balance after successful payment
          globalBalanceService.invalidateCache();
          loadBalance(true);

          if (options.onSuccess) {
            options.onSuccess(payment);
          }
        },
        onError: (error) => {
          dispatch({
            type: WALLET_ACTIONS.SET_ERROR,
            payload: { type: 'payment', error: error.message }
          });

          if (options.onError) {
            options.onError(error);
          }
        }
      });

      return result;
    } catch (error) {
      console.error('Error processing payment:', error);
      dispatch({
        type: WALLET_ACTIONS.SET_ERROR,
        payload: { type: 'payment', error: error.message }
      });
      throw error;
    }
  }, []);

  /**
   * Validate transaction
   */
  const validateTransaction = useCallback(async (amount, type = 'purchase') => {
    try {
      const validation = await CreditService.validateTransaction(amount, type);
      return validation;
    } catch (error) {
      console.error('Error validating transaction:', error);
      return {
        isValid: false,
        error: error.message,
        code: 'VALIDATION_ERROR'
      };
    }
  }, []);

  /**
   * Retry failed payment
   */
  const retryPayment = useCallback(async (transactionId) => {
    try {
      dispatch({ type: WALLET_ACTIONS.SET_LOADING, payload: { type: 'payment', loading: true } });

      const result = await CreditService.retryFailedPayment(transactionId);

      if (result.success) {
        dispatch({
          type: WALLET_ACTIONS.SET_PAYMENT_STATUS,
          payload: { status: result.payment }
        });
      } else {
        dispatch({
          type: WALLET_ACTIONS.SET_ERROR,
          payload: { type: 'payment', error: result.error }
        });
      }

      return result;
    } catch (error) {
      console.error('Error retrying payment:', error);
      dispatch({
        type: WALLET_ACTIONS.SET_ERROR,
        payload: { type: 'payment', error: error.message }
      });
      throw error;
    }
  }, []);

  /**
   * Check payment status
   */
  const checkPaymentStatus = useCallback(async (transactionId) => {
    try {
      const result = await CreditService.checkPaymentStatus(transactionId);

      if (result.success) {
        dispatch({
          type: WALLET_ACTIONS.SET_PAYMENT_STATUS,
          payload: { status: result.payment }
        });

        // If payment is successful, refresh balance and transactions
        if (result.status === 'success') {
          globalBalanceService.invalidateCache();
          loadBalance(true);
          loadTransactions({ limit: 10, offset: 0, replace: true, forceRefresh: true });
        }
      }

      return result;
    } catch (error) {
      console.error('Error checking payment status:', error);
      throw error;
    }
  }, []);

  /**
   * Clear specific error
   */
  const clearError = useCallback((type) => {
    dispatch({
      type: WALLET_ACTIONS.CLEAR_ERROR,
      payload: { type }
    });
  }, []);

  /**
   * Format currency amount
   */
  const formatCurrency = useCallback((amount, currency = 'MYR') => {
    return ValidationService.formatAmount(amount, currency);
  }, []);

  /**
   * Format credits
   */
  const formatCredits = useCallback((amount) => {
    return ValidationService.formatCredits(amount);
  }, []);

  /**
   * Load verification status
   */
  const loadVerificationStatus = useCallback(async (forceRefresh = false) => {
    try {
      dispatch({ type: WALLET_ACTIONS.SET_LOADING, payload: { type: 'verification', loading: true } });

      const verificationStatus = await WalletVerificationService.getVerificationStatus(forceRefresh);

      dispatch({
        type: WALLET_ACTIONS.SET_VERIFICATION_STATUS,
        payload: { status: verificationStatus }
      });

      return verificationStatus;
    } catch (error) {
      console.error('Error loading verification status:', error);
      dispatch({
        type: WALLET_ACTIONS.SET_ERROR,
        payload: { type: 'verification', error: error.message }
      });
      throw error;
    }
  }, []);

  /**
   * Load withdrawal eligibility
   */
  const loadWithdrawalEligibility = useCallback(async (forceRefresh = false) => {
    try {
      dispatch({ type: WALLET_ACTIONS.SET_LOADING, payload: { type: 'eligibility', loading: true } });

      const eligibility = await WithdrawalService.checkEligibility(forceRefresh);

      dispatch({
        type: WALLET_ACTIONS.SET_WITHDRAWAL_ELIGIBILITY,
        payload: { eligibility }
      });

      return eligibility;
    } catch (error) {
      console.error('Error loading withdrawal eligibility:', error);
      dispatch({
        type: WALLET_ACTIONS.SET_ERROR,
        payload: { type: 'eligibility', error: error.message }
      });
      throw error;
    }
  }, []);

  /**
   * Load withdrawal currencies
   */
  const loadWithdrawalCurrencies = useCallback(async (forceRefresh = false) => {
    try {
      dispatch({ type: WALLET_ACTIONS.SET_LOADING, payload: { type: 'currencies', loading: true } });

      const currencies = await WithdrawalService.getAvailableCurrencies(forceRefresh);

      dispatch({
        type: WALLET_ACTIONS.SET_WITHDRAWAL_CURRENCIES,
        payload: { currencies }
      });

      return currencies;
    } catch (error) {
      console.error('Error loading withdrawal currencies:', error);
      dispatch({
        type: WALLET_ACTIONS.SET_ERROR,
        payload: { type: 'currencies', error: error.message }
      });
      throw error;
    }
  }, []);

  /**
   * Load withdrawal history (verification-aware)
   */
  const loadWithdrawals = useCallback(async (options = {}) => {
    const {
      limit = state.withdrawalFilters.limit,
      offset = state.withdrawalFilters.offset,
      status = state.withdrawalFilters.status,
      replace = false,
      forceRefresh = false,
      skipVerificationCheck = false
    } = options;

    try {
      dispatch({ type: WALLET_ACTIONS.SET_LOADING, payload: { type: 'withdrawals', loading: true } });

      // Check verification status first (unless explicitly skipped)
      if (!skipVerificationCheck) {
        const verificationStatus = state.verificationStatus || await loadVerificationStatus();

        if (!WalletVerificationService.shouldMakeWithdrawalAPICalls(verificationStatus)) {
          console.log('Skipping withdrawal API call - user not verified');

          // Create verification requirement response
          const verificationRequirement = WalletVerificationService.createVerificationRequirement(
            verificationStatus,
            'Verification required to view withdrawal history'
          );

          // Set empty withdrawals array
          dispatch({
            type: WALLET_ACTIONS.SET_WITHDRAWALS,
            payload: { withdrawals: [], replace: true }
          });

          // Set verification error message
          dispatch({
            type: WALLET_ACTIONS.SET_ERROR,
            payload: {
              type: 'withdrawals',
              error: verificationRequirement
            }
          });

          return []; // Return empty array for components
        }
      }

      const response = await WithdrawalService.getWithdrawalHistory({
        limit,
        offset,
        status,
        forceRefresh
      });

      // Handle verification requirement responses
      if (response && typeof response === 'object' &&
          (response.requiresEmailVerification || response.requiresKycVerification || response.requiresVerification)) {

        // Set empty withdrawals array
        dispatch({
          type: WALLET_ACTIONS.SET_WITHDRAWALS,
          payload: { withdrawals: [], replace: true }
        });

        // Set verification error message
        dispatch({
          type: WALLET_ACTIONS.SET_ERROR,
          payload: {
            type: 'withdrawals',
            error: {
              message: response.message,
              requiresEmailVerification: response.requiresEmailVerification,
              requiresKycVerification: response.requiresKycVerification,
              requiresVerification: response.requiresVerification,
              isVerificationError: true
            }
          }
        });

        return []; // Return empty array for components
      }

      // Handle normal withdrawal data
      const withdrawals = Array.isArray(response) ? response : (response?.withdrawals || []);

      dispatch({
        type: WALLET_ACTIONS.SET_WITHDRAWALS,
        payload: { withdrawals, replace }
      });

      return withdrawals;
    } catch (error) {
      console.error('Error loading withdrawals:', error);
      dispatch({
        type: WALLET_ACTIONS.SET_ERROR,
        payload: {
          type: 'withdrawals',
          error: {
            message: error.message || 'Failed to load withdrawal history',
            isVerificationError: false
          }
        }
      });
      throw error;
    }
  }, [state.withdrawalFilters]);

  /**
   * Process withdrawal request
   */
  const processWithdrawal = useCallback(async (withdrawalData) => {
    try {
      dispatch({ type: WALLET_ACTIONS.SET_LOADING, payload: { type: 'withdrawal', loading: true } });

      // Include current balance in withdrawal data for validation
      const dataWithBalance = {
        ...withdrawalData,
        currentBalance: state.balance?.credits || 0
      };

      const result = await WithdrawalService.processWithdrawal(dataWithBalance);

      if (result.success) {
        // Add withdrawal to state
        dispatch({
          type: WALLET_ACTIONS.ADD_WITHDRAWAL,
          payload: { withdrawal: result.withdrawal }
        });

        // Set withdrawal status
        dispatch({
          type: WALLET_ACTIONS.SET_WITHDRAWAL_STATUS,
          payload: { status: result.withdrawal }
        });

        // Invalidate global balance cache and refresh balance after successful withdrawal
        globalBalanceService.invalidateCache();
        loadBalance(true);

        return result;
      } else {
        dispatch({
          type: WALLET_ACTIONS.SET_ERROR,
          payload: { type: 'withdrawal', error: result.error || 'Withdrawal failed' }
        });
        return result;
      }
    } catch (error) {
      console.error('Error processing withdrawal:', error);
      dispatch({
        type: WALLET_ACTIONS.SET_ERROR,
        payload: { type: 'withdrawal', error: error.message }
      });
      throw error;
    }
  }, [state.balance]);

  /**
   * Validate withdrawal request
   */
  const validateWithdrawal = useCallback(async (withdrawalData) => {
    try {
      // Include current balance in withdrawal data for validation
      const dataWithBalance = {
        ...withdrawalData,
        currentBalance: state.balance?.credits || 0
      };

      const validation = await WithdrawalService.validateWithdrawal(dataWithBalance);
      return validation;
    } catch (error) {
      console.error('Error validating withdrawal:', error);
      return {
        isValid: false,
        errors: [error.message],
        code: 'VALIDATION_ERROR'
      };
    }
  }, []);

  /**
   * Get withdrawal details by ID
   */
  const getWithdrawalDetails = useCallback(async (withdrawalId) => {
    try {
      const withdrawal = await WithdrawalService.getWithdrawalDetails(withdrawalId);
      return withdrawal;
    } catch (error) {
      console.error('Error getting withdrawal details:', error);
      throw error;
    }
  }, []);

  /**
   * Calculate currency conversion
   */
  const calculateConversion = useCallback((creditAmount, currency = 'MYR') => {
    return WithdrawalService.calculateConversion(creditAmount, currency);
  }, []);

  /**
   * Format withdrawal currency
   */
  const formatWithdrawalCurrency = useCallback((amount, currency = 'MYR') => {
    return WithdrawalService.formatCurrency(amount, currency);
  }, []);

  /**
   * Get withdrawal validation rules
   */
  const getWithdrawalRules = useCallback(() => {
    return WithdrawalService.getValidationRules();
  }, []);

  /**
   * Refresh all wallet data including verification status
   */
  const refreshWallet = useCallback(async () => {
    try {
      // Load verification status first
      const verificationStatus = await loadVerificationStatus(true);

      // Then load other data conditionally
      const promises = [
        loadBalance(true),
        loadTransactions({ limit: 20, offset: 0, replace: true, forceRefresh: true }),
        loadCreditPackages()
      ];

      // Only load withdrawal data if user is verified
      if (WalletVerificationService.shouldMakeWithdrawalAPICalls(verificationStatus)) {
        promises.push(
          loadWithdrawals({ limit: 20, offset: 0, replace: true, forceRefresh: true, skipVerificationCheck: true }),
          loadWithdrawalEligibility(true),
          loadWithdrawalCurrencies(true)
        );
      }

      await Promise.allSettled(promises);
    } catch (error) {
      console.error('Error refreshing wallet:', error);
    }
  }, [loadBalance, loadTransactions, loadCreditPackages, loadWithdrawals, loadWithdrawalEligibility, loadWithdrawalCurrencies, loadVerificationStatus]);

  // Initialize wallet with essential data - DISABLED to prevent infinite loops
  // TODO: Re-enable when withdrawal components are actually used
  // const initializeWallet = useCallback(async () => {
  //   try {
  //     dispatch({ type: WALLET_ACTIONS.SET_LOADING, payload: { type: 'balance', loading: true } });

  //     // Load balance and recent transactions in parallel
  //     await Promise.allSettled([
  //       loadBalance(),
  //       loadTransactions({ limit: 10, offset: 0, replace: true })
  //     ]);

  //     // Mark as initialized even if some operations failed
  //     dispatch({
  //       type: WALLET_ACTIONS.SET_LOADING,
  //       payload: { type: 'balance', loading: false }
  //     });

  //     // Set initialized flag using dispatch
  //     dispatch({
  //       type: WALLET_ACTIONS.SET_INITIALIZED,
  //       payload: { initialized: true }
  //     });
  //   } catch (error) {
  //     console.error('Error initializing wallet:', error);
  //     dispatch({
  //       type: WALLET_ACTIONS.SET_ERROR,
  //       payload: { type: 'global', error: 'Failed to initialize wallet' }
  //     });
  //   }
  // }, [loadBalance, loadTransactions]);

  // Initialize wallet data on mount - DISABLED to prevent infinite loops
  // TODO: Re-enable when withdrawal components are actually used
  // useEffect(() => {
  //   if (!state.isInitialized) {
  //     initializeWallet();
  //   }
  // }, [state.isInitialized, initializeWallet]);

  /**
   * Get wallet statistics based on transactions
   * @param {string} timeframe - Time period for stats ('7d', '30d', '90d', '1y')
   * @returns {Promise<Object>} Wallet statistics
   */
  const getWalletStats = useCallback(async (timeframe = '30d') => {
    try {
      // First, ensure we have transactions loaded
      await loadTransactions({ 
        limit: 100, 
        forceRefresh: true,
        replace: true 
      });
      
      // Calculate date range based on timeframe
      const now = new Date();
      let startDate = new Date();
      
      switch (timeframe) {
        case '7d':
          startDate.setDate(now.getDate() - 7);
          break;
        case '30d':
          startDate.setDate(now.getDate() - 30);
          break;
        case '90d':
          startDate.setDate(now.getDate() - 90);
          break;
        case '1y':
          startDate.setFullYear(now.getFullYear() - 1);
          break;
        default:
          startDate.setDate(now.getDate() - 30); // Default to 30 days
      }
      
      // Filter transactions by date
      // Ensure state.transactions is an array before filtering
      const transactions = Array.isArray(state.transactions) ? state.transactions : [];
      const filteredTransactions = transactions.filter(tx => {
        const txDate = new Date(tx.created_at || tx.date);
        return txDate >= startDate && txDate <= now;
      });
      
      // Calculate statistics
      const creditsEarned = filteredTransactions.length > 0 ? 
        filteredTransactions
          .filter(tx => tx.transaction_type === 'add' || tx.type === 'credit')
          .reduce((sum, tx) => sum + (tx.credits || tx.amount || 0), 0)
        : 0;
        
      const creditsSpent = filteredTransactions.length > 0 ?
        filteredTransactions
          .filter(tx => tx.transaction_type === 'deduct' || tx.type === 'debit')
          .reduce((sum, tx) => sum + Math.abs(tx.credits || tx.amount || 0), 0)
        : 0;
      
      // Calculate trends (comparing to previous period)
      const previousStartDate = new Date(startDate);
      switch (timeframe) {
        case '7d':
          previousStartDate.setDate(previousStartDate.getDate() - 7);
          break;
        case '30d':
          previousStartDate.setDate(previousStartDate.getDate() - 30);
          break;
        case '90d':
          previousStartDate.setDate(previousStartDate.getDate() - 90);
          break;
        case '1y':
          previousStartDate.setFullYear(previousStartDate.getFullYear() - 1);
          break;
      }
      
      // Return calculated stats
      return {
        timeframe,
        totalTransactions: filteredTransactions.length,
        creditsEarned,
        creditsSpent,
        netFlow: creditsEarned - creditsSpent,
        balanceTrend: 0, // Placeholder for trend calculation
        earningsTrend: 0, // Placeholder for trend calculation
        spendingTrend: 0, // Placeholder for trend calculation
        netFlowTrend: 0, // Placeholder for trend calculation
        successRateTrend: 0, // Placeholder for trend calculation
        avgTransactionTrend: 0, // Placeholder for trend calculation
        successRate: 100, // Assuming all transactions are successful
        avgTransactionAmount: filteredTransactions.length > 0 
          ? (creditsEarned + creditsSpent) / filteredTransactions.length 
          : 0
      };
    } catch (error) {
      console.error('Error calculating wallet stats:', error);
      throw error;
    }
  }, [state.transactions, loadTransactions]);

  // Context value
  const value = {
    // State
    ...state,

    // Actions
    loadBalance,
    loadTransactions,
    loadCreditPackages,
    processPayment,
    validateTransaction,
    retryPayment,
    checkPaymentStatus,
    clearError,
    refreshWallet,
    getWalletStats, // Add the new function

    // Withdrawal actions
    loadWithdrawalEligibility,
    loadWithdrawalCurrencies,
    loadWithdrawals,
    processWithdrawal,
    validateWithdrawal,
    getWithdrawalDetails,

    // Verification actions
    loadVerificationStatus,

    // Utilities
    formatCurrency,
    formatCredits,
    calculateConversion,
    formatWithdrawalCurrency,
    getWithdrawalRules,

    // Constants
    constants: walletConstants
  };

  return (
    <WalletErrorProvider>
      <WalletContext.Provider value={value}>
        {children}
      </WalletContext.Provider>
    </WalletErrorProvider>
  );
};

/**
 * Custom hook to use wallet context
 */
export const useWallet = () => {
  const context = useContext(WalletContext);
  if (!context) {
    throw new Error('useWallet must be used within a WalletProvider');
  }
  return context;
};

export default WalletContext;
