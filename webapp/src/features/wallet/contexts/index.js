/**
 * Wallet Contexts Barrel Export
 *
 * This file provides a centralized export point for all wallet-related contexts.
 * It includes the WalletContext and its associated hooks.
 */

// Context and Provider
export { default as WalletContext, WalletProvider } from './WalletContext';

// Custom hooks
export { useWallet } from './WalletContext';

/**
 * Combined provider for all wallet contexts
 *
 * This component wraps children with all necessary wallet-related contexts.
 * Use this when you need full wallet functionality in your component tree.
 *
 * Note: Import WalletProvider directly from './WalletContext' to avoid circular dependencies
 * Example: import { WalletProvider } from './WalletContext';
 */

/**
 * Context configuration and utilities
 */
export const walletContextConfig = {
  // Auto-refresh intervals
  balanceRefreshInterval: 30000, // 30 seconds
  transactionRefreshInterval: 60000, // 1 minute

  // Cache settings
  enableCaching: true,
  cacheTimeout: 5 * 60 * 1000, // 5 minutes

  // Error handling
  maxRetryAttempts: 3,
  retryDelay: 1000, // 1 second

  // UI settings
  showLoadingStates: true,
  showErrorMessages: true,
  autoRefreshOnFocus: true
};

/**
 * Context utilities for debugging and development
 */
export const walletContextUtils = {
  /**
   * Get current wallet state (for debugging)
   * Note: This should only be used in development
   */
  getWalletState: () => {
    if (process.env.NODE_ENV === 'development') {
      // This would need to be implemented with a ref or dev tools
      console.warn('getWalletState is only available in development mode');
    }
  },

  /**
   * Reset wallet context state
   * Useful for testing or when user logs out
   */
  resetWalletContext: () => {
    // This would trigger a reset action in the context
    console.log('Wallet context reset requested');
  },

  /**
   * Validate context integration
   * Checks if all required contexts are properly connected
   */
  validateContextIntegration: () => {
    // Implementation would check context availability
    return {
      walletContext: true,
      // Add other context checks as needed
    };
  }
};
