import { WALLET_ERROR_TYPES, API_ERROR_TYPES } from '../../../utils/errorTypes';
import globalBalanceService from '../../../services/globalBalanceService';

/**
 * Error Recovery Service
 * 
 * This service provides automatic error recovery mechanisms for wallet operations,
 * including retry logic, fallback strategies, and user guidance for error resolution.
 */
class ErrorRecoveryService {
  constructor() {
    this.recoveryStrategies = new Map();
    this.fallbackData = new Map();
    this.recoveryAttempts = new Map();
    this.maxRecoveryAttempts = 3;
    
    // Initialize recovery strategies
    this.initializeRecoveryStrategies();
    
    // Bind methods
    this.recover = this.recover.bind(this);
    this.canRecover = this.canRecover.bind(this);
    this.getFallbackData = this.getFallbackData.bind(this);
  }

  /**
   * Initialize recovery strategies for different error types
   */
  initializeRecoveryStrategies() {
    // Network error recovery
    this.recoveryStrategies.set(API_ERROR_TYPES.NETWORK_ERROR, {
      strategy: 'exponential_backoff',
      maxAttempts: 3,
      baseDelay: 1000,
      maxDelay: 10000,
      fallbackEnabled: true
    });

    // Balance fetch error recovery
    this.recoveryStrategies.set(WALLET_ERROR_TYPES.BALANCE_FETCH_FAILED, {
      strategy: 'immediate_retry_with_fallback',
      maxAttempts: 2,
      baseDelay: 500,
      fallbackEnabled: true,
      fallbackValue: 0
    });

    // Service unavailable recovery
    this.recoveryStrategies.set(WALLET_ERROR_TYPES.SERVICE_UNAVAILABLE, {
      strategy: 'exponential_backoff',
      maxAttempts: 2,
      baseDelay: 2000,
      maxDelay: 8000,
      fallbackEnabled: true
    });

    // Transaction failed recovery
    this.recoveryStrategies.set(WALLET_ERROR_TYPES.TRANSACTION_FAILED, {
      strategy: 'user_initiated_retry',
      maxAttempts: 1,
      fallbackEnabled: false
    });

    // Payment timeout recovery
    this.recoveryStrategies.set(WALLET_ERROR_TYPES.PAYMENT_TIMEOUT, {
      strategy: 'status_check_retry',
      maxAttempts: 3,
      baseDelay: 3000,
      fallbackEnabled: false
    });
  }

  /**
   * Attempt to recover from an error
   * @param {Object} error - The standardized error object
   * @param {Function} retryFunction - Function to retry the failed operation
   * @param {Object} options - Recovery options
   * @returns {Promise<Object>} Recovery result
   */
  async recover(error, retryFunction, options = {}) {
    const { context = 'unknown', metadata = {} } = options;
    const strategy = this.recoveryStrategies.get(error.type);
    
    if (!strategy) {
      return {
        success: false,
        reason: 'no_recovery_strategy',
        message: 'No recovery strategy available for this error type'
      };
    }

    const attemptKey = `${context}_${error.type}`;
    const currentAttempts = this.recoveryAttempts.get(attemptKey) || 0;

    if (currentAttempts >= strategy.maxAttempts) {
      return {
        success: false,
        reason: 'max_attempts_exceeded',
        message: 'Maximum recovery attempts exceeded',
        fallbackData: strategy.fallbackEnabled ? this.getFallbackData(error.type, context) : null
      };
    }

    // Increment attempt counter
    this.recoveryAttempts.set(attemptKey, currentAttempts + 1);

    try {
      const result = await this.executeRecoveryStrategy(strategy, retryFunction, currentAttempts);
      
      if (result.success) {
        // Clear attempt counter on success
        this.recoveryAttempts.delete(attemptKey);
      }
      
      return result;
    } catch (recoveryError) {
      console.error('Recovery attempt failed:', recoveryError);
      
      return {
        success: false,
        reason: 'recovery_failed',
        message: 'Recovery attempt failed',
        error: recoveryError,
        fallbackData: strategy.fallbackEnabled ? this.getFallbackData(error.type, context) : null
      };
    }
  }

  /**
   * Execute a specific recovery strategy
   * @param {Object} strategy - Recovery strategy configuration
   * @param {Function} retryFunction - Function to retry
   * @param {number} attemptNumber - Current attempt number
   * @returns {Promise<Object>} Strategy execution result
   */
  async executeRecoveryStrategy(strategy, retryFunction, attemptNumber) {
    switch (strategy.strategy) {
      case 'exponential_backoff':
        return this.exponentialBackoffRecovery(strategy, retryFunction, attemptNumber);
      
      case 'immediate_retry_with_fallback':
        return this.immediateRetryWithFallback(strategy, retryFunction);
      
      case 'user_initiated_retry':
        return this.userInitiatedRetry(strategy, retryFunction);
      
      case 'status_check_retry':
        return this.statusCheckRetry(strategy, retryFunction, attemptNumber);
      
      default:
        throw new Error(`Unknown recovery strategy: ${strategy.strategy}`);
    }
  }

  /**
   * Exponential backoff recovery strategy
   */
  async exponentialBackoffRecovery(strategy, retryFunction, attemptNumber) {
    const delay = Math.min(
      strategy.baseDelay * Math.pow(2, attemptNumber),
      strategy.maxDelay
    );

    console.log(`Exponential backoff recovery: waiting ${delay}ms before attempt ${attemptNumber + 1}`);
    
    await this.delay(delay);
    
    const result = await retryFunction();
    
    return {
      success: true,
      result,
      strategy: 'exponential_backoff',
      delay
    };
  }

  /**
   * Immediate retry with fallback strategy
   */
  async immediateRetryWithFallback(strategy, retryFunction) {
    try {
      const result = await retryFunction();
      
      return {
        success: true,
        result,
        strategy: 'immediate_retry'
      };
    } catch (error) {
      // Use fallback data
      const fallbackData = strategy.fallbackValue !== undefined ? 
        strategy.fallbackValue : 
        this.getFallbackData(error.type);

      return {
        success: true,
        result: fallbackData,
        strategy: 'fallback',
        usingFallback: true
      };
    }
  }

  /**
   * User-initiated retry strategy
   */
  async userInitiatedRetry(strategy, retryFunction) {
    // This strategy requires user action, so we return instructions
    return {
      success: false,
      reason: 'user_action_required',
      message: 'User must manually retry this operation',
      strategy: 'user_initiated',
      retryFunction
    };
  }

  /**
   * Status check retry strategy (for payments)
   */
  async statusCheckRetry(strategy, retryFunction, attemptNumber) {
    const delay = strategy.baseDelay;
    
    console.log(`Status check retry: waiting ${delay}ms before attempt ${attemptNumber + 1}`);
    
    await this.delay(delay);
    
    // For payment status checks, we might want to check status first
    const result = await retryFunction();
    
    return {
      success: true,
      result,
      strategy: 'status_check',
      delay
    };
  }

  /**
   * Check if an error can be recovered from
   * @param {Object} error - The error object
   * @param {string} context - Error context
   * @returns {boolean} Whether recovery is possible
   */
  canRecover(error, context = 'unknown') {
    const strategy = this.recoveryStrategies.get(error.type);
    
    if (!strategy) {
      return false;
    }

    const attemptKey = `${context}_${error.type}`;
    const currentAttempts = this.recoveryAttempts.get(attemptKey) || 0;
    
    return currentAttempts < strategy.maxAttempts;
  }

  /**
   * Get fallback data for an error type
   * @param {string} errorType - The error type
   * @param {string} context - Error context
   * @returns {*} Fallback data
   */
  getFallbackData(errorType, context = 'unknown') {
    const fallbackKey = `${context}_${errorType}`;
    
    // Check for cached fallback data
    if (this.fallbackData.has(fallbackKey)) {
      return this.fallbackData.get(fallbackKey);
    }

    // Generate fallback data based on error type and context
    switch (errorType) {
      case WALLET_ERROR_TYPES.BALANCE_FETCH_FAILED:
        // Try to get cached balance from global service
        const cachedBalance = globalBalanceService.getCachedBalance();
        return cachedBalance !== null ? cachedBalance : 0;
      
      case WALLET_ERROR_TYPES.TRANSACTION_FAILED:
        return [];
      
      case WALLET_ERROR_TYPES.SERVICE_UNAVAILABLE:
        return {
          message: 'Service temporarily unavailable',
          retryAfter: 60000 // 1 minute
        };
      
      default:
        return null;
    }
  }

  /**
   * Set fallback data for a specific error type and context
   * @param {string} errorType - The error type
   * @param {string} context - Error context
   * @param {*} data - Fallback data
   */
  setFallbackData(errorType, context, data) {
    const fallbackKey = `${context}_${errorType}`;
    this.fallbackData.set(fallbackKey, data);
  }

  /**
   * Clear recovery attempts for a context
   * @param {string} context - The context to clear
   */
  clearRecoveryAttempts(context) {
    const keysToDelete = [];
    
    for (const key of this.recoveryAttempts.keys()) {
      if (key.startsWith(`${context}_`)) {
        keysToDelete.push(key);
      }
    }
    
    keysToDelete.forEach(key => this.recoveryAttempts.delete(key));
  }

  /**
   * Get recovery statistics
   * @returns {Object} Recovery statistics
   */
  getRecoveryStats() {
    const stats = {
      totalAttempts: 0,
      byErrorType: {},
      byContext: {}
    };

    for (const [key, attempts] of this.recoveryAttempts.entries()) {
      const [context, errorType] = key.split('_', 2);
      
      stats.totalAttempts += attempts;
      stats.byErrorType[errorType] = (stats.byErrorType[errorType] || 0) + attempts;
      stats.byContext[context] = (stats.byContext[context] || 0) + attempts;
    }

    return stats;
  }

  /**
   * Utility function to create a delay
   * @param {number} ms - Milliseconds to delay
   * @returns {Promise} Promise that resolves after the delay
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Reset all recovery attempts
   */
  reset() {
    this.recoveryAttempts.clear();
    this.fallbackData.clear();
  }
}

// Create and export singleton instance
const errorRecoveryService = new ErrorRecoveryService();

export default errorRecoveryService;
