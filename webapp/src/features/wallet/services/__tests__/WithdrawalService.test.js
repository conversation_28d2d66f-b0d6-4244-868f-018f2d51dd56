/**
 * WithdrawalService Test Suite
 *
 * Basic tests to verify WithdrawalService functionality
 */

import WithdrawalService from '../WithdrawalService';

describe('WithdrawalService', () => {
  beforeEach(() => {
    // Clear cache before each test
    WithdrawalService.clearCache();
  });

  describe('Validation Rules', () => {
    test('should return validation rules', () => {
      const rules = WithdrawalService.getValidationRules();
      
      expect(rules).toHaveProperty('MIN_WITHDRAWAL_AMOUNT');
      expect(rules).toHaveProperty('MAX_WITHDRAWAL_AMOUNT');
      expect(rules).toHaveProperty('SUPPORTED_CURRENCIES');
      expect(rules).toHaveProperty('CONVERSION_RATES');
      
      expect(rules.MIN_WITHDRAWAL_AMOUNT).toBe(10);
      expect(rules.MAX_WITHDRAWAL_AMOUNT).toBe(10000);
      expect(rules.SUPPORTED_CURRENCIES).toContain('MYR');
      expect(rules.SUPPORTED_CURRENCIES).toContain('USD');
      expect(rules.SUPPORTED_CURRENCIES).toContain('SGD');
    });
  });

  describe('Currency Conversion', () => {
    test('should calculate MYR conversion correctly', () => {
      const result = WithdrawalService.calculateConversion(100, 'MYR');
      
      expect(result.creditAmount).toBe(100);
      expect(result.currency).toBe('MYR');
      expect(result.fiatAmount).toBe(25); // 100 * 0.25
      expect(result.rate).toBe(0.25);
      expect(result.formatted).toContain('25.00');
    });

    test('should calculate USD conversion correctly', () => {
      const result = WithdrawalService.calculateConversion(100, 'USD');
      
      expect(result.creditAmount).toBe(100);
      expect(result.currency).toBe('USD');
      expect(result.fiatAmount).toBe(6); // 100 * 0.06
      expect(result.rate).toBe(0.06);
    });

    test('should calculate SGD conversion correctly', () => {
      const result = WithdrawalService.calculateConversion(100, 'SGD');
      
      expect(result.creditAmount).toBe(100);
      expect(result.currency).toBe('SGD');
      expect(result.fiatAmount).toBe(8); // 100 * 0.08
      expect(result.rate).toBe(0.08);
    });
  });

  describe('Currency Formatting', () => {
    test('should format MYR correctly', () => {
      const formatted = WithdrawalService.formatCurrency(25.50, 'MYR');
      expect(formatted).toContain('25.50');
    });

    test('should format USD correctly', () => {
      const formatted = WithdrawalService.formatCurrency(6.00, 'USD');
      expect(formatted).toContain('6.00');
    });

    test('should format credits correctly', () => {
      const formatted = WithdrawalService.formatCredits(1000);
      expect(formatted).toBe('1,000 credits');
    });

    test('should format large credit amounts correctly', () => {
      const formatted = WithdrawalService.formatCredits(1234567);
      expect(formatted).toBe('1,234,567 credits');
    });
  });

  describe('Default Currencies', () => {
    test('should return default currencies', () => {
      const currencies = WithdrawalService.getDefaultCurrencies();
      
      expect(currencies).toHaveLength(3);
      expect(currencies[0].code).toBe('MYR');
      expect(currencies[0].is_default).toBe(true);
      expect(currencies[1].code).toBe('USD');
      expect(currencies[2].code).toBe('SGD');
    });
  });

  describe('Status Helpers', () => {
    test('should return correct status colors', () => {
      expect(WithdrawalService.getStatusColor('pending')).toContain('yellow');
      expect(WithdrawalService.getStatusColor('processing')).toContain('blue');
      expect(WithdrawalService.getStatusColor('completed')).toContain('green');
      expect(WithdrawalService.getStatusColor('failed')).toContain('red');
      expect(WithdrawalService.getStatusColor('cancelled')).toContain('gray');
    });

    test('should return correct status text', () => {
      expect(WithdrawalService.getStatusText('pending')).toBe('Pending Review');
      expect(WithdrawalService.getStatusText('processing')).toBe('Processing');
      expect(WithdrawalService.getStatusText('completed')).toBe('Completed');
      expect(WithdrawalService.getStatusText('failed')).toBe('Failed');
      expect(WithdrawalService.getStatusText('cancelled')).toBe('Cancelled');
    });
  });

  describe('Cache Management', () => {
    test('should clear cache successfully', () => {
      // This test mainly ensures the method doesn't throw
      expect(() => {
        WithdrawalService.clearCache();
      }).not.toThrow();
    });

    test('should clear withdrawal context successfully', () => {
      // This test mainly ensures the method doesn't throw
      expect(() => {
        WithdrawalService.clearWithdrawalContext();
      }).not.toThrow();
    });
  });

  describe('Validation (Basic)', () => {
    test('should validate basic withdrawal data structure', async () => {
      const withdrawalData = {
        amount: 100,
        currency: 'MYR',
        bankAccountId: 'test-bank-123',
        currentBalance: 500
      };

      // Note: This test may fail if external services are not mocked
      // In a real test environment, we would mock the external dependencies
      try {
        const result = await WithdrawalService.validateWithdrawal(withdrawalData);
        expect(result).toHaveProperty('isValid');
        expect(result).toHaveProperty('creditAmount');
        expect(result).toHaveProperty('fiatAmount');
      } catch (error) {
        // Expected if external services are not available
        expect(error).toBeDefined();
      }
    });
  });
});
