/**
 * CreditService Test Suite
 * 
 * Basic tests to verify CreditService functionality
 */

import CreditService from '../CreditService';
import ValidationService from '../ValidationService';

// Mock the wallet API
jest.mock('../../../../services/walletService', () => ({
  getBalance: jest.fn(),
  createPayment: jest.fn(),
  getTransactions: jest.fn(),
  retryPayment: jest.fn(),
  checkPaymentStatus: jest.fn(),
  getCreditPackages: jest.fn()
}));

describe('CreditService', () => {
  beforeEach(() => {
    // Clear cache before each test
    CreditService.clearCache();
    
    // Clear localStorage
    localStorage.clear();
  });

  describe('getBalance', () => {
    it('should return balance from API', async () => {
      const mockBalance = 100.50;
      const walletAPI = require('../../../../services/walletService').default;
      walletAPI.getBalance.mockResolvedValue({ data: { balance: mockBalance } });

      const balance = await CreditService.getBalance();
      expect(balance).toBe(mockBalance);
    });

    it('should use cached balance when available', async () => {
      const mockBalance = 200.75;
      const walletAPI = require('../../../../services/walletService').default;
      walletAPI.getBalance.mockResolvedValue({ data: { balance: mockBalance } });

      // First call
      await CreditService.getBalance();
      
      // Second call should use cache
      const balance = await CreditService.getBalance();
      
      expect(balance).toBe(mockBalance);
      expect(walletAPI.getBalance).toHaveBeenCalledTimes(1);
    });
  });

  describe('validateTransaction', () => {
    it('should validate valid transaction', async () => {
      const walletAPI = require('../../../../services/walletService').default;
      walletAPI.getBalance.mockResolvedValue({ data: { balance: 100 } });

      const result = await CreditService.validateTransaction(50, 'purchase');
      
      expect(result.isValid).toBe(true);
      expect(result.currentBalance).toBe(100);
      expect(result.remainingBalance).toBe(50);
    });

    it('should reject transaction with insufficient balance', async () => {
      const walletAPI = require('../../../../services/walletService').default;
      walletAPI.getBalance.mockResolvedValue({ data: { balance: 30 } });

      const result = await CreditService.validateTransaction(50, 'purchase');
      
      expect(result.isValid).toBe(false);
      expect(result.code).toBe('INSUFFICIENT_BALANCE');
      expect(result.shortfall).toBe(20);
    });

    it('should reject invalid amounts', async () => {
      const result = await CreditService.validateTransaction(0, 'purchase');
      
      expect(result.isValid).toBe(false);
      expect(result.code).toBe('INVALID_AMOUNT');
    });
  });

  describe('formatCurrency', () => {
    it('should format MYR currency correctly', () => {
      const formatted = CreditService.formatCurrency(123.45, 'MYR');
      expect(formatted).toMatch(/123\.45/);
    });

    it('should handle invalid currency gracefully', () => {
      const formatted = CreditService.formatCurrency(123.45, 'INVALID');
      expect(formatted).toBe('INVALID 123.45');
    });
  });

  describe('cache management', () => {
    it('should clear cache correctly', () => {
      CreditService.cache.balance = 100;
      CreditService.cache.lastBalanceUpdate = Date.now();
      
      CreditService.clearCache();
      
      expect(CreditService.cache.balance).toBeNull();
      expect(CreditService.cache.lastBalanceUpdate).toBeNull();
    });

    it('should invalidate balance cache', () => {
      CreditService.cache.balance = 100;
      CreditService.cache.lastBalanceUpdate = Date.now();
      
      CreditService.invalidateBalanceCache();
      
      expect(CreditService.cache.balance).toBeNull();
      expect(CreditService.cache.lastBalanceUpdate).toBeNull();
    });
  });
});

describe('ValidationService', () => {
  describe('validateAmount', () => {
    it('should validate correct amounts', () => {
      const result = ValidationService.validateAmount(100.50);
      
      expect(result.isValid).toBe(true);
      expect(result.amount).toBe(100.50);
    });

    it('should reject negative amounts', () => {
      const result = ValidationService.validateAmount(-10);
      
      expect(result.isValid).toBe(false);
      expect(result.code).toBe('AMOUNT_TOO_LOW');
    });

    it('should reject non-numeric amounts', () => {
      const result = ValidationService.validateAmount('invalid');
      
      expect(result.isValid).toBe(false);
      expect(result.code).toBe('INVALID_NUMBER');
    });

    it('should handle string numbers', () => {
      const result = ValidationService.validateAmount('123.45');
      
      expect(result.isValid).toBe(true);
      expect(result.amount).toBe(123.45);
    });
  });

  describe('formatAmount', () => {
    it('should format amounts correctly', () => {
      const formatted = ValidationService.formatAmount(123.45, 'MYR');
      expect(formatted).toMatch(/123\.45/);
    });
  });

  describe('formatCredits', () => {
    it('should format credits correctly', () => {
      const formatted = ValidationService.formatCredits(1234.56);
      expect(formatted).toBe('1,234.56');
    });

    it('should handle non-numbers gracefully', () => {
      const formatted = ValidationService.formatCredits('invalid');
      expect(formatted).toBe('0');
    });
  });
});
