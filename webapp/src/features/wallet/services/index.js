// Import services for internal use
import CreditService from './CreditService';
import ValidationService from './ValidationService';
import WithdrawalService from './WithdrawalService';

/**
 * Wallet Services Barrel Export
 *
 * This file provides a centralized export point for all wallet-related services.
 * It allows for clean imports and better organization of service dependencies.
 */

// Core wallet services
export { default as CreditService } from './CreditService';
export { default as ValidationService } from './ValidationService';
export { default as WithdrawalService } from './WithdrawalService';
export { default as WalletVerificationService } from './VerificationService';

// Re-export existing wallet services for convenience
export { default as walletAPI } from '../../../services/walletService';
export { transactionService } from '../../../services/transactionService';
export { default as currencyService } from '../../../services/currencyService';

/**
 * Service factory for creating service instances with custom configurations
 */

export const createWalletServices = (config = {}) => {
  return {
    creditService: CreditService,
    validationService: ValidationService,
    withdrawalService: WithdrawalService,
    // Add other services as needed
  };
};

/**
 * Default service configuration
 */
export const defaultServiceConfig = {
  cacheEnabled: true,
  balanceCacheDuration: 5 * 60 * 1000, // 5 minutes
  transactionCacheDuration: 1 * 60 * 1000, // 1 minute
  retryAttempts: 3,
  retryDelay: 1000, // 1 second
};

/**
 * Service utilities
 */
export const serviceUtils = {
  /**
   * Clear all service caches
   */
  clearAllCaches: () => {
    CreditService.clearCache();
    WithdrawalService.clearCache();
  },

  /**
   * Invalidate balance across all services
   */
  invalidateBalance: () => {
    CreditService.invalidateBalanceCache();
  },

  /**
   * Invalidate transactions across all services
   */
  invalidateTransactions: () => {
    CreditService.invalidateTransactionCache();
  },

  /**
   * Validate amount using ValidationService
   */
  validateAmount: (amount, options) => {
    return ValidationService.validateAmount(amount, options);
  },

  /**
   * Validate transaction using ValidationService
   */
  validateTransaction: (transaction, userLimits) => {
    return ValidationService.validateTransaction(transaction, userLimits);
  },

  /**
   * Format amount for display
   */
  formatAmount: (amount, currency) => {
    return ValidationService.formatAmount(amount, currency);
  },

  /**
   * Format credits for display
   */
  formatCredits: (amount) => {
    return ValidationService.formatCredits(amount);
  },

  /**
   * Check withdrawal eligibility
   */
  checkWithdrawalEligibility: async (forceRefresh = false) => {
    return WithdrawalService.checkEligibility(forceRefresh);
  },

  /**
   * Validate withdrawal request
   */
  validateWithdrawal: async (withdrawalData) => {
    return WithdrawalService.validateWithdrawal(withdrawalData);
  },

  /**
   * Calculate currency conversion
   */
  calculateConversion: (creditAmount, currency) => {
    return WithdrawalService.calculateConversion(creditAmount, currency);
  },

  /**
   * Format currency amount
   */
  formatCurrency: (amount, currency) => {
    return WithdrawalService.formatCurrency(amount, currency);
  },

  /**
   * Get withdrawal validation rules
   */
  getWithdrawalRules: () => {
    return WithdrawalService.getValidationRules();
  }
};
