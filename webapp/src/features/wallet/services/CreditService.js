import walletAPI from '../../../services/walletService';
import { transactionService } from '../../../services/transactionService';

/**
 * CreditService - Frontend service abstraction for credit operations
 *
 * This service provides a high-level interface for credit-related operations,
 * wrapping the lower-level walletService with additional validation, error handling,
 * and business logic specific to credit management.
 */
class CreditService {
  constructor() {
    this.cache = {
      balance: null,
      lastBalanceUpdate: null,
      transactions: [],
      lastTransactionUpdate: null
    };

    // Cache duration in milliseconds (5 minutes for balance, 1 minute for transactions)
    this.BALANCE_CACHE_DURATION = 5 * 60 * 1000;
    this.TRANSACTION_CACHE_DURATION = 1 * 60 * 1000;
  }

  /**
   * Get current credit balance with caching
   * @param {boolean} forceRefresh - Force refresh from server
   * @returns {Promise<number>} Current balance
   */
  async getBalance(forceRefresh = false) {
    try {
      const now = Date.now();
      const cacheValid = this.cache.lastBalanceUpdate &&
                        (now - this.cache.lastBalanceUpdate) < this.BALANCE_CACHE_DURATION;

      if (!forceRefresh && cacheValid && this.cache.balance !== null) {
        return this.cache.balance;
      }

      const response = await walletAPI.getBalance();
      const balance = response.data?.balance || 0;

      // Update cache
      this.cache.balance = balance;
      this.cache.lastBalanceUpdate = now;

      return balance;
    } catch (error) {
      console.error('Error fetching balance:', error);

      // Return cached balance if available, otherwise 0
      if (this.cache.balance !== null) {
        return this.cache.balance;
      }

      throw new Error('Unable to fetch balance. Please check your connection and try again.');
    }
  }

  /**
   * Validate transaction amount and user balance
   * @param {number} amount - Transaction amount
   * @param {string} type - Transaction type ('purchase', 'withdrawal', etc.)
   * @returns {Promise<Object>} Validation result
   */
  async validateTransaction(amount, type = 'purchase') {
    try {
      // Basic amount validation
      if (!amount || amount <= 0) {
        return {
          isValid: false,
          error: 'Amount must be greater than zero',
          code: 'INVALID_AMOUNT'
        };
      }

      // Get current balance
      const currentBalance = await this.getBalance();

      // Check if user has sufficient balance for debit transactions
      if (['purchase', 'withdrawal', 'gift'].includes(type) && amount > currentBalance) {
        return {
          isValid: false,
          error: `Insufficient balance. You have ${currentBalance} credits, but need ${amount} credits.`,
          code: 'INSUFFICIENT_BALANCE',
          currentBalance,
          requiredAmount: amount,
          shortfall: amount - currentBalance
        };
      }

      // Use transaction service for additional validation (only for debit transactions)
      if (['purchase', 'withdrawal', 'gift'].includes(type)) {
        const transactionValidation = await transactionService.validateTransaction(amount);

        if (!transactionValidation.isValid) {
          return {
            isValid: false,
            error: transactionValidation.message,
            code: 'INSUFFICIENT_BALANCE',
            currentBalance: transactionValidation.currentBalance
          };
        }
      }

      return {
        isValid: true,
        currentBalance,
        transactionAmount: amount,
        remainingBalance: currentBalance - (type === 'topup' ? 0 : amount)
      };
    } catch (error) {
      console.error('Error validating transaction:', error);
      return {
        isValid: false,
        error: 'Unable to validate transaction. Please try again.',
        code: 'VALIDATION_ERROR'
      };
    }
  }

  /**
   * Process credit package payment
   * @param {string} packageId - Credit package ID
   * @param {Object} options - Payment options
   * @returns {Promise<Object>} Payment result
   */
  async processPayment(packageId, options = {}) {
    try {
      const {
        returnUrl = `${window.location.origin}/payment-return`,
        onSuccess,
        onError
      } = options;

      // Create payment through wallet service
      const response = await walletAPI.createPayment(packageId, returnUrl);

      if (!response.data?.payment) {
        throw new Error('Invalid payment response from server');
      }

      const payment = response.data.payment;

      // Validate payment data
      if (!payment.transaction_id || !payment.redirect_url) {
        throw new Error('Payment response missing required fields');
      }

      // Store payment context for return handling
      this.storePaymentContext({
        transactionId: payment.transaction_id,
        packageId,
        amount: payment.amount,
        context: 'wallet-topup'
      });

      // Invalidate balance cache since payment is initiated
      this.invalidateBalanceCache();

      if (onSuccess) {
        onSuccess(payment);
      }

      return {
        success: true,
        payment,
        redirectUrl: payment.redirect_url
      };
    } catch (error) {
      console.error('Error processing payment:', error);

      // Enhanced error handling
      let errorMessage = 'Payment processing failed. Please try again.';
      let errorCode = 'PAYMENT_ERROR';

      if (error.response) {
        // Handle API errors
        const apiError = error.response.data;
        errorMessage = apiError.message || errorMessage;
        errorCode = apiError.code || errorCode;

        // Handle specific error cases
        if (error.response.status === 400) {
          errorMessage = 'Invalid payment request. Please check your details.';
          errorCode = 'INVALID_PAYMENT';
        } else if (error.response.status === 402) {
          errorMessage = 'Payment failed. Please try a different payment method.';
          errorCode = 'PAYMENT_DECLINED';
        } else if (error.response.status === 503) {
          errorMessage = 'Payment service is temporarily unavailable. Please try again later.';
          errorCode = 'SERVICE_UNAVAILABLE';
        }
      } else if (error.request) {
        // Network error
        errorMessage = 'Network error. Please check your connection and try again.';
        errorCode = 'NETWORK_ERROR';
      }

      if (options.onError) {
        options.onError({ message: errorMessage, code: errorCode });
      }

      return {
        success: false,
        error: errorMessage,
        code: errorCode
      };
    }
  }

  /**
   * Get transaction history with caching and filtering
   * @param {Object} filters - Transaction filters
   * @returns {Promise<Array>} Transaction history
   */
  async getTransactionHistory(filters = {}) {
    try {
      const {
        limit = 50,
        offset = 0,
        type = null,
        forceRefresh = false
      } = filters;

      const now = Date.now();
      const cacheKey = `${limit}-${offset}-${type}`;
      const cacheValid = this.cache.lastTransactionUpdate &&
                        (now - this.cache.lastTransactionUpdate) < this.TRANSACTION_CACHE_DURATION;

      if (!forceRefresh && cacheValid && this.cache.transactions[cacheKey]) {
        return this.cache.transactions[cacheKey];
      }

      const response = await walletAPI.getTransactions({ limit, offset, type });
      const transactions = response.data?.transactions || [];

      // Update cache
      this.cache.transactions[cacheKey] = transactions;
      this.cache.lastTransactionUpdate = now;

      return transactions;
    } catch (error) {
      console.error('Error fetching transaction history:', error);

      // Return cached transactions if available
      const cacheKey = `${filters.limit || 50}-${filters.offset || 0}-${filters.type || null}`;
      if (this.cache.transactions[cacheKey]) {
        return this.cache.transactions[cacheKey];
      }

      throw new Error('Unable to fetch transaction history. Please try again.');
    }
  }

  /**
   * Retry a failed payment
   * @param {string} transactionId - Failed transaction ID
   * @returns {Promise<Object>} Retry result
   */
  async retryFailedPayment(transactionId) {
    try {
      if (!transactionId) {
        throw new Error('Transaction ID is required for retry');
      }

      const response = await walletAPI.retryPayment(transactionId);

      if (!response.data?.payment) {
        throw new Error('Invalid retry response from server');
      }

      const payment = response.data.payment;

      // Validate payment data
      if (!payment.transaction_id || !payment.redirect_url) {
        throw new Error('Payment response missing required fields');
      }

      // Update payment context
      this.storePaymentContext({
        transactionId: payment.transaction_id,
        amount: payment.amount,
        context: 'wallet-topup-retry'
      });

      // Invalidate balance cache
      this.invalidateBalanceCache();

      return {
        success: true,
        payment,
        redirectUrl: payment.redirect_url
      };
    } catch (error) {
      console.error('Error retrying payment:', error);

      // Enhanced error handling
      let errorMessage = 'Payment retry failed. Please try again.';
      let errorCode = 'RETRY_ERROR';

      if (error.response) {
        // Handle API errors
        const apiError = error.response.data;
        errorMessage = apiError.message || errorMessage;
        errorCode = apiError.code || errorCode;

        // Handle specific error cases
        if (error.response.status === 404) {
          errorMessage = 'Transaction not found. Please start a new payment.';
          errorCode = 'TRANSACTION_NOT_FOUND';
        } else if (error.response.status === 409) {
          errorMessage = 'Transaction is already completed or cancelled.';
          errorCode = 'TRANSACTION_FINALIZED';
        } else if (error.response.status === 503) {
          errorMessage = 'Payment service is temporarily unavailable. Please try again later.';
          errorCode = 'SERVICE_UNAVAILABLE';
        }
      } else if (error.request) {
        // Network error
        errorMessage = 'Network error. Please check your connection and try again.';
        errorCode = 'NETWORK_ERROR';
      }

      return {
        success: false,
        error: errorMessage,
        code: errorCode
      };
    }
  }

  /**
   * Check payment status
   * @param {string} transactionId - Transaction ID to check
   * @returns {Promise<Object>} Payment status
   */
  async checkPaymentStatus(transactionId) {
    try {
      const response = await walletAPI.checkPaymentStatus(transactionId);

      // If payment is successful, invalidate balance cache
      if (response.data?.status === 'success') {
        this.invalidateBalanceCache();
        this.invalidateTransactionCache();
      }

      return {
        success: true,
        status: response.data?.status,
        payment: response.data?.payment
      };
    } catch (error) {
      console.error('Error checking payment status:', error);

      return {
        success: false,
        error: 'Unable to check payment status. Please try again.',
        code: 'STATUS_CHECK_ERROR'
      };
    }
  }

  /**
   * Get available credit packages
   * @param {string} channel - Payment channel (optional)
   * @returns {Promise<Array>} Available packages
   */
  async getCreditPackages(channel = null) {
    try {
      const response = await walletAPI.getCreditPackages(channel);
      return response.data?.packages || [];
    } catch (error) {
      console.error('Error fetching credit packages:', error);
      throw new Error('Unable to fetch credit packages. Please try again.');
    }
  }

  /**
   * Store payment context in localStorage
   * @param {Object} context - Payment context
   */
  storePaymentContext(context) {
    try {
      localStorage.setItem('paymentContext', 'wallet-topup');
      localStorage.setItem('paymentReferenceId', context.packageId || '');
      localStorage.setItem('paymentAmount', context.amount?.toString() || '');
      localStorage.setItem('paymentReturnPath', '/wallet');
      localStorage.setItem('pendingPaymentId', context.transactionId || '');
    } catch (error) {
      console.warn('Unable to store payment context:', error);
    }
  }

  /**
   * Clear payment context from localStorage
   */
  clearPaymentContext() {
    try {
      localStorage.removeItem('paymentContext');
      localStorage.removeItem('paymentReferenceId');
      localStorage.removeItem('paymentAmount');
      localStorage.removeItem('paymentReturnPath');
      localStorage.removeItem('pendingPaymentId');
    } catch (error) {
      console.warn('Unable to clear payment context:', error);
    }
  }

  /**
   * Invalidate balance cache
   */
  invalidateBalanceCache() {
    this.cache.balance = null;
    this.cache.lastBalanceUpdate = null;
  }

  /**
   * Invalidate transaction cache
   */
  invalidateTransactionCache() {
    this.cache.transactions = [];
    this.cache.lastTransactionUpdate = null;
  }

  /**
   * Clear all caches
   */
  clearCache() {
    this.invalidateBalanceCache();
    this.invalidateTransactionCache();
  }

  /**
   * Format currency amount
   * @param {number} amount - Amount to format
   * @param {string} currency - Currency code
   * @returns {string} Formatted amount
   */
  formatCurrency(amount, currency = 'MYR') {
    try {
      return new Intl.NumberFormat('en-MY', {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: 2
      }).format(amount);
    } catch (error) {
      return `${currency} ${amount.toFixed(2)}`;
    }
  }
}

// Create and export singleton instance
const creditService = new CreditService();
export default creditService;
