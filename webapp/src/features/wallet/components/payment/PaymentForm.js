import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faCreditCard,
  faSpinner,
  faCheck,
  faExclamationTriangle,
  faInfoCircle,
  faTimes
} from '@fortawesome/free-solid-svg-icons';
import { useWallet } from '../../contexts';

/**
 * PaymentForm Component
 *
 * Standalone payment form component extracted from TopUpModal.
 * Handles package selection, payment method selection, and form submission.
 */
const PaymentForm = ({
  onSubmit,
  onCancel,
  isLoading = false,
  showPackages = true,
  showPaymentMethods = true,
  selectedPackage = null,
  onPackageChange,
  className = '',
  size = 'medium' // 'small', 'medium', 'large'
}) => {
  const {
    creditPackages,
    packagesLoading,
    loadCreditPackages,
    validateTransaction,
    formatCurrency,
    formatCredits
  } = useWallet();

  const [formData, setFormData] = useState({
    packageId: selectedPackage?.id || '',
    paymentMethod: 'fpx',
    agreeToTerms: false
  });

  const [errors, setErrors] = useState({});
  const [validation, setValidation] = useState(null);

  // Load credit packages on mount
  useEffect(() => {
    if (showPackages && creditPackages.length === 0 && !packagesLoading) {
      loadCreditPackages();
    }
  }, [showPackages, creditPackages.length, packagesLoading, loadCreditPackages]);

  // Update form when selectedPackage prop changes
  useEffect(() => {
    if (selectedPackage) {
      setFormData(prev => ({ ...prev, packageId: selectedPackage.id }));
      if (onPackageChange) {
        onPackageChange(selectedPackage);
      }
    }
  }, [selectedPackage, onPackageChange]);

  // Validate transaction when package changes
  useEffect(() => {
    if (formData.packageId) {
      const selectedPkg = creditPackages.find(pkg => pkg.id === formData.packageId);
      if (selectedPkg) {
        validatePackageTransaction(selectedPkg);
      }
    }
  }, [formData.packageId, creditPackages]);

  const validatePackageTransaction = async (pkg) => {
    try {
      const result = await validateTransaction(pkg.credits, 'topup');
      setValidation(result);
    } catch (error) {
      console.error('Validation error:', error);
      setValidation({
        isValid: false,
        error: 'Unable to validate transaction'
      });
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
  };

  const handlePackageSelect = (pkg) => {
    handleInputChange('packageId', pkg.id);
    if (onPackageChange) {
      onPackageChange(pkg);
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.packageId) {
      newErrors.packageId = 'Please select a credit package';
    }

    if (!formData.paymentMethod) {
      newErrors.paymentMethod = 'Please select a payment method';
    }

    if (!formData.agreeToTerms) {
      newErrors.agreeToTerms = 'Please agree to the terms and conditions';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    const selectedPkg = creditPackages.find(pkg => pkg.id === formData.packageId);

    if (onSubmit) {
      onSubmit({
        package: selectedPkg,
        paymentMethod: formData.paymentMethod,
        formData
      });
    }
  };

  const getSizeClasses = () => {
    const sizes = {
      small: {
        container: 'p-4',
        title: 'text-lg',
        button: 'px-4 py-2 text-sm',
        input: 'text-sm'
      },
      medium: {
        container: 'p-6',
        title: 'text-xl',
        button: 'px-6 py-3 text-base',
        input: 'text-base'
      },
      large: {
        container: 'p-8',
        title: 'text-2xl',
        button: 'px-8 py-4 text-lg',
        input: 'text-lg'
      }
    };
    return sizes[size] || sizes.medium;
  };

  const sizeClasses = getSizeClasses();
  const selectedPkg = creditPackages.find(pkg => pkg.id === formData.packageId);

  return (
    <div className={`payment-form ${className}`}>
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Package Selection */}
        {showPackages && (
          <div className="space-y-4">
            <h3 className={`font-semibold text-gray-900 ${sizeClasses.title}`}>
              Select Credit Package
            </h3>

            {packagesLoading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="animate-pulse">
                    <div className="h-32 bg-gray-200 rounded-lg"></div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {creditPackages.map((pkg) => (
                  <motion.div
                    key={pkg.id}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => handlePackageSelect(pkg)}
                    className={`
                      relative cursor-pointer rounded-lg border-2 p-4 transition-all
                      ${formData.packageId === pkg.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 bg-white hover:border-gray-300'
                      }
                    `}
                  >
                    {/* Package Details */}
                    <div className="text-center">
                      <div className="text-2xl font-bold text-gray-900">
                        {formatCredits(pkg.credits)} Credits
                      </div>
                      <div className="text-lg text-gray-600">
                        {formatCurrency(pkg.price, pkg.currency_code)}
                      </div>

                      {pkg.bonus_credits > 0 && (
                        <div className="mt-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          +{formatCredits(pkg.bonus_credits)} Bonus
                        </div>
                      )}
                    </div>

                    {/* Selected Indicator */}
                    {formData.packageId === pkg.id && (
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        className="absolute top-2 right-2 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center"
                      >
                        <FontAwesomeIcon icon={faCheck} className="text-white text-xs" />
                      </motion.div>
                    )}
                  </motion.div>
                ))}
              </div>
            )}

            {errors.packageId && (
              <p className="text-red-600 text-sm flex items-center">
                <FontAwesomeIcon icon={faExclamationTriangle} className="mr-2" />
                {errors.packageId}
              </p>
            )}
          </div>
        )}

        {/* Transaction Validation */}
        {validation && selectedPkg && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            className={`
              rounded-lg p-4 border
              ${validation.isValid
                ? 'bg-green-50 border-green-200'
                : 'bg-red-50 border-red-200'
              }
            `}
          >
            <div className="flex items-center">
              <FontAwesomeIcon
                icon={validation.isValid ? faCheck : faExclamationTriangle}
                className={`mr-2 ${validation.isValid ? 'text-green-600' : 'text-red-600'}`}
              />
              <div>
                <p className={`font-medium ${validation.isValid ? 'text-green-800' : 'text-red-800'}`}>
                  {validation.isValid ? 'Transaction Valid' : 'Transaction Issue'}
                </p>
                {validation.error && (
                  <p className="text-sm text-red-700">{validation.error}</p>
                )}
              </div>
            </div>
          </motion.div>
        )}

        {/* Payment Method Selection */}
        {showPaymentMethods && (
          <div className="space-y-4">
            <h3 className={`font-semibold text-gray-900 ${sizeClasses.title}`}>
              Payment Method
            </h3>

            <div className="space-y-3">
              {[
                { id: 'fpx', name: 'FPX Online Banking', icon: '🏦' },
                { id: 'card', name: 'Credit/Debit Card', icon: '💳' },
                { id: 'ewallet', name: 'E-Wallet', icon: '📱' }
              ].map((method) => (
                <label
                  key={method.id}
                  className={`
                    flex items-center p-4 rounded-lg border-2 cursor-pointer transition-all
                    ${formData.paymentMethod === method.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 bg-white hover:border-gray-300'
                    }
                  `}
                >
                  <input
                    type="radio"
                    name="paymentMethod"
                    value={method.id}
                    checked={formData.paymentMethod === method.id}
                    onChange={(e) => handleInputChange('paymentMethod', e.target.value)}
                    className="sr-only"
                  />
                  <span className="text-2xl mr-3">{method.icon}</span>
                  <span className="font-medium text-gray-900">{method.name}</span>
                  {formData.paymentMethod === method.id && (
                    <FontAwesomeIcon icon={faCheck} className="ml-auto text-blue-500" />
                  )}
                </label>
              ))}
            </div>

            {errors.paymentMethod && (
              <p className="text-red-600 text-sm flex items-center">
                <FontAwesomeIcon icon={faExclamationTriangle} className="mr-2" />
                {errors.paymentMethod}
              </p>
            )}
          </div>
        )}

        {/* Terms and Conditions */}
        <div className="space-y-4">
          <label className="flex items-start space-x-3 cursor-pointer">
            <input
              type="checkbox"
              checked={formData.agreeToTerms}
              onChange={(e) => handleInputChange('agreeToTerms', e.target.checked)}
              className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <span className="text-sm text-gray-700">
              I agree to the{' '}
              <a href="/terms" className="text-blue-600 hover:text-blue-700 underline">
                Terms and Conditions
              </a>{' '}
              and{' '}
              <a href="/privacy" className="text-blue-600 hover:text-blue-700 underline">
                Privacy Policy
              </a>
            </span>
          </label>

          {errors.agreeToTerms && (
            <p className="text-red-600 text-sm flex items-center">
              <FontAwesomeIcon icon={faExclamationTriangle} className="mr-2" />
              {errors.agreeToTerms}
            </p>
          )}
        </div>

        {/* Summary */}
        {selectedPkg && (
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-2">Payment Summary</h4>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span>Credits:</span>
                <span>{formatCredits(selectedPkg.credits)}</span>
              </div>
              {selectedPkg.bonus_credits > 0 && (
                <div className="flex justify-between text-green-600">
                  <span>Bonus:</span>
                  <span>+{formatCredits(selectedPkg.bonus_credits)}</span>
                </div>
              )}
              <div className="flex justify-between font-medium text-gray-900 pt-2 border-t">
                <span>Total:</span>
                <span>{formatCurrency(selectedPkg.price, selectedPkg.currency_code)}</span>
              </div>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex space-x-4">
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            type="submit"
            disabled={isLoading || !validation?.isValid}
            className={`
              flex-1 flex items-center justify-center space-x-2
              bg-blue-600 text-white rounded-lg font-medium
              hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed
              transition-colors ${sizeClasses.button}
            `}
          >
            {isLoading ? (
              <>
                <FontAwesomeIcon icon={faSpinner} className="animate-spin" />
                <span>Processing...</span>
              </>
            ) : (
              <>
                <FontAwesomeIcon icon={faCreditCard} />
                <span>Proceed to Payment</span>
              </>
            )}
          </motion.button>

          {onCancel && (
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              type="button"
              onClick={onCancel}
              disabled={isLoading}
              className={`
                px-6 py-3 border border-gray-300 text-gray-700 rounded-lg font-medium
                hover:bg-gray-50 disabled:opacity-50 transition-colors
                ${sizeClasses.button}
              `}
            >
              <FontAwesomeIcon icon={faTimes} className="mr-2" />
              Cancel
            </motion.button>
          )}
        </div>
      </form>
    </div>
  );
};

export default PaymentForm;
