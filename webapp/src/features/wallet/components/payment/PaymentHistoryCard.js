import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faHistory,
  faSpinner,
  faExclamationTriangle,
  faRefresh,
  faFilter,
  faPlus,
  faMinus,
  faArrowUp,
  faArrowDown,
  faCalendarAlt,
  faInfoCircle
} from '@fortawesome/free-solid-svg-icons';
import { useWallet } from '../../contexts/WalletContext';

/**
 * PaymentHistoryCard Component
 *
 * Modern card component that displays payment/credit transaction history with filtering,
 * pagination, and beautiful UI following Profile.js design patterns with glass-morphism effects.
 */
const PaymentHistoryCard = ({
  showFilters = true,
  showRefreshButton = true,
  limit = 10,
  size = 'medium', // 'small', 'medium', 'large'
  variant = 'card', // 'card', 'inline', 'minimal'
  className = '',
  onTransactionClick,
  autoRefresh = false,
  refreshInterval = 60000 // 1 minute
}) => {
  const {
    transactions = [], // Default to empty array
    transactionsLoading,
    transactionsError,
    loadTransactions,
    formatCredits,
    formatCurrency,
    clearError
  } = useWallet();

  const [filters, setFilters] = useState({
    type: null, // 'add', 'deduct', null for all
    limit: limit,
    offset: 0
  });
  const [showFiltersPanel, setShowFiltersPanel] = useState(false);

  // Auto-refresh functionality
  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(() => {
        handleRefresh();
      }, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval]);

  // Load transactions on mount and filter changes
  useEffect(() => {
    loadTransactions({
      ...filters,
      replace: true,
      forceRefresh: false
    });
  }, [filters]);

  const handleRefresh = () => {
    loadTransactions({
      ...filters,
      replace: true,
      forceRefresh: true
    });
  };

  const handleFilterChange = (newFilters) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters,
      offset: 0 // Reset to first page when filters change
    }));
  };

  const handleLoadMore = () => {
    loadTransactions({
      ...filters,
      offset: safeTransactions.length,
      replace: false
    });
  };

  const getSizeClasses = () => {
    const sizes = {
      small: {
        container: 'p-4',
        title: 'text-lg',
        item: 'p-3',
        text: 'text-sm'
      },
      medium: {
        container: 'p-6',
        title: 'text-xl',
        item: 'p-4',
        text: 'text-base'
      },
      large: {
        container: 'p-8',
        title: 'text-2xl',
        item: 'p-5',
        text: 'text-lg'
      }
    };
    return sizes[size] || sizes.medium;
  };

  const getTransactionIcon = (type) => {
    return type === 'add' ? faPlus : faMinus;
  };

  const getTransactionColor = (type) => {
    return type === 'add'
      ? 'text-green-600 bg-green-50 border-green-200'
      : 'text-red-600 bg-red-50 border-red-200';
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const sizeClasses = getSizeClasses();

  // Ensure transactions is always an array
  const safeTransactions = Array.isArray(transactions) ? transactions : [];

  if (variant === 'minimal') {
    return (
      <div className={`payment-history-minimal ${className}`}>
        {transactionsLoading ? (
          <div className="flex items-center justify-center py-4">
            <FontAwesomeIcon icon={faSpinner} className="animate-spin text-blue-500 mr-2" />
            <span className="text-gray-600">Loading transactions...</span>
          </div>
        ) : safeTransactions.length === 0 ? (
          <div className="text-center py-4 text-gray-500">
            No transactions found
          </div>
        ) : (
          <div className="space-y-2">
            {safeTransactions.slice(0, 3).map((transaction) => (
              <div key={transaction.id} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                <div className="flex items-center">
                  <FontAwesomeIcon
                    icon={getTransactionIcon(transaction.transaction_type)}
                    className={`w-4 h-4 mr-3 ${transaction.transaction_type === 'add' ? 'text-green-600' : 'text-red-600'}`}
                  />
                  <span className="text-sm text-gray-700 truncate max-w-32">
                    {transaction.description}
                  </span>
                </div>
                <span className={`text-sm font-medium ${transaction.transaction_type === 'add' ? 'text-green-600' : 'text-red-600'}`}>
                  {transaction.transaction_type === 'add' ? '+' : ''}{formatCredits(transaction.credits)}
                </span>
              </div>
            ))}
          </div>
        )}
      </div>
    );
  }

  return (
    <motion.div
      className={`
        relative bg-gradient-to-br from-white/90 to-white/70 backdrop-blur-2xl
        border border-white/30 shadow-2xl rounded-2xl overflow-hidden
        ${sizeClasses.container} ${className}
      `}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      whileHover={{ y: -5, boxShadow: '0 25px 50px -12px rgba(0,0,0,0.15)' }}
    >
      {/* Animated background decorations */}
      <div className="absolute -top-10 -right-10 w-32 h-32 rounded-full bg-gradient-to-br from-blue-500/20 to-indigo-500/20 blur-2xl animate-pulse" />
      <div className="absolute -bottom-10 -left-10 w-24 h-24 rounded-full bg-gradient-to-br from-purple-500/20 to-pink-500/20 blur-xl animate-pulse delay-1000" />

      {/* Content */}
      <div className="relative z-10">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center mr-4">
              <FontAwesomeIcon icon={faHistory} className="text-white text-lg" />
            </div>
            <div>
              <h3 className={`font-bold text-gray-900 ${sizeClasses.title}`}>
                Payment History
              </h3>
              <p className="text-gray-600 text-sm">
                {safeTransactions.length} transaction{safeTransactions.length !== 1 ? 's' : ''}
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            {showFilters && (
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setShowFiltersPanel(!showFiltersPanel)}
                className={`
                  p-2 rounded-lg transition-colors
                  ${showFiltersPanel
                    ? 'bg-blue-100 text-blue-600'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  }
                `}
              >
                <FontAwesomeIcon icon={faFilter} />
              </motion.button>
            )}

            {showRefreshButton && (
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleRefresh}
                disabled={transactionsLoading}
                className="p-2 rounded-lg bg-gray-100 text-gray-600 hover:bg-gray-200 transition-colors disabled:opacity-50"
              >
                <FontAwesomeIcon
                  icon={faRefresh}
                  className={transactionsLoading ? 'animate-spin' : ''}
                />
              </motion.button>
            )}
          </div>
        </div>

        {/* Filters Panel */}
        <AnimatePresence>
          {showFiltersPanel && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="mb-6 p-4 bg-gray-50/80 rounded-xl border border-gray-200/50"
            >
              <div className="flex flex-wrap gap-2">
                <button
                  onClick={() => handleFilterChange({ type: null })}
                  className={`
                    px-3 py-1 rounded-lg text-sm font-medium transition-colors
                    ${filters.type === null
                      ? 'bg-blue-500 text-white'
                      : 'bg-white text-gray-600 hover:bg-gray-100'
                    }
                  `}
                >
                  All
                </button>
                <button
                  onClick={() => handleFilterChange({ type: 'add' })}
                  className={`
                    px-3 py-1 rounded-lg text-sm font-medium transition-colors
                    ${filters.type === 'add'
                      ? 'bg-green-500 text-white'
                      : 'bg-white text-gray-600 hover:bg-gray-100'
                    }
                  `}
                >
                  Credits Added
                </button>
                <button
                  onClick={() => handleFilterChange({ type: 'deduct' })}
                  className={`
                    px-3 py-1 rounded-lg text-sm font-medium transition-colors
                    ${filters.type === 'deduct'
                      ? 'bg-red-500 text-white'
                      : 'bg-white text-gray-600 hover:bg-gray-100'
                    }
                  `}
                >
                  Credits Used
                </button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Error State */}
        {transactionsError && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="mb-6 p-4 bg-red-50 border border-red-200 rounded-xl"
          >
            <div className="flex items-center">
              <FontAwesomeIcon icon={faExclamationTriangle} className="text-red-600 mr-3" />
              <div>
                <p className="text-red-800 font-medium">Error loading transactions</p>
                <p className="text-red-600 text-sm">
                  {typeof transactionsError === 'object'
                    ? transactionsError.message || 'Failed to load transactions'
                    : transactionsError
                  }
                </p>
              </div>
            </div>
            <button
              onClick={() => clearError('transactions')}
              className="mt-2 text-red-600 hover:text-red-700 text-sm underline"
            >
              Dismiss
            </button>
          </motion.div>
        )}

        {/* Loading State */}
        {transactionsLoading && safeTransactions.length === 0 && (
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-gray-200 rounded-lg"></div>
                  <div className="flex-1">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                  <div className="h-4 bg-gray-200 rounded w-16"></div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Transactions List */}
        {!transactionsLoading && safeTransactions.length === 0 ? (
          <div className="text-center py-12">
            <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gray-100 flex items-center justify-center">
              <FontAwesomeIcon icon={faHistory} className="text-gray-400 text-xl" />
            </div>
            <h4 className="text-lg font-medium text-gray-900 mb-2">No transactions yet</h4>
            <p className="text-gray-600">Your payment history will appear here</p>
          </div>
        ) : (
          <div className="space-y-3">
            {safeTransactions.map((transaction, index) => (
              <motion.div
                key={transaction.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                onClick={() => onTransactionClick && onTransactionClick(transaction)}
                className={`
                  flex items-center justify-between p-4 rounded-xl border transition-all
                  ${onTransactionClick ? 'cursor-pointer hover:shadow-md' : ''}
                  ${getTransactionColor(transaction.transaction_type)}
                `}
              >
                <div className="flex items-center flex-1">
                  <div className={`
                    w-10 h-10 rounded-lg flex items-center justify-center mr-4
                    ${transaction.transaction_type === 'add' ? 'bg-green-100' : 'bg-red-100'}
                  `}>
                    <FontAwesomeIcon
                      icon={getTransactionIcon(transaction.transaction_type)}
                      className={transaction.transaction_type === 'add' ? 'text-green-600' : 'text-red-600'}
                    />
                  </div>

                  <div className="flex-1 min-w-0">
                    <p className="font-medium text-gray-900 truncate">
                      {transaction.description}
                    </p>
                    <div className="flex items-center text-sm text-gray-600">
                      <FontAwesomeIcon icon={faCalendarAlt} className="mr-1" />
                      {formatDate(transaction.created_at)}
                    </div>
                  </div>
                </div>

                <div className="text-right">
                  <p className={`
                    font-bold ${sizeClasses.text}
                    ${transaction.transaction_type === 'add' ? 'text-green-600' : 'text-red-600'}
                  `}>
                    {transaction.transaction_type === 'add' ? '+' : ''}{formatCredits(transaction.credits)}
                  </p>
                  <p className="text-xs text-gray-500">
                    Balance: {formatCredits(transaction.balance_after)}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>
        )}

        {/* Load More Button */}
        {safeTransactions.length > 0 && safeTransactions.length >= filters.limit && (
          <div className="mt-6 text-center">
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={handleLoadMore}
              disabled={transactionsLoading}
              className="px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-xl font-medium hover:from-blue-600 hover:to-indigo-700 transition-all disabled:opacity-50"
            >
              {transactionsLoading ? (
                <>
                  <FontAwesomeIcon icon={faSpinner} className="animate-spin mr-2" />
                  Loading...
                </>
              ) : (
                'Load More'
              )}
            </motion.button>
          </div>
        )}
      </div>
    </motion.div>
  );
};

export default PaymentHistoryCard;
