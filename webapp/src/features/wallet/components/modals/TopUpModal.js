import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faTimes,
  faPlus,
  faCreditCard,
  faShieldAlt,
  faCheckCircle,
  faExclamationTriangle
} from '@fortawesome/free-solid-svg-icons';
import { useWallet } from '../../contexts/WalletContext';
import { PaymentLoadingIndicator } from '../../../../components/common/LoadingIndicator';
import CreditPackageCard from '../../../../components/CreditPackageCard';

/**
 * Top-Up Modal Component
 * 
 * Modal-based workflow for adding credits with package selection,
 * verification checks, and payment processing.
 */
const TopUpModal = ({
  isOpen = false,
  onClose,
  onSuccess,
  className = ''
}) => {
  const {
    creditPackages,
    packagesLoading,
    packagesError,
    loadCreditPackages,
    processPayment,
    paymentLoading,
    paymentError,
    clearError
  } = useWallet();

  // Local state
  const [selectedPackage, setSelectedPackage] = useState(null);
  const parsePrice = (price) => {
    const numeric = typeof price === 'string' ? parseFloat(price) : price;
    return typeof numeric === 'number' && !isNaN(numeric) ? numeric : 0;
  };

  const formatPrice = (price) => {
    return parsePrice(price).toFixed(2);
  };
  const [step, setStep] = useState('packages'); // 'packages', 'payment', 'processing', 'success'
  const [paymentMethod, setPaymentMethod] = useState('card');

  // Load packages when modal opens
  useEffect(() => {
    if (isOpen && (!creditPackages || creditPackages.length === 0)) {
      loadCreditPackages();
    }
  }, [isOpen, creditPackages, loadCreditPackages]);

  // Reset state when modal closes
  useEffect(() => {
    if (!isOpen) {
      setSelectedPackage(null);
      setStep('packages');
      setPaymentMethod('card');
      clearError('payment');
    }
  }, [isOpen, clearError]);

  // Handle package selection
  const handlePackageSelect = (pkg) => {
    setSelectedPackage({
      ...pkg,
      price: parsePrice(pkg.price)
    });
  };

  // Handle payment processing
  const handlePayment = async () => {
    if (!selectedPackage) return;

    try {
      setStep('processing');

      const result = await processPayment(selectedPackage.id, {
        paymentMethod,
        onSuccess: (payment) => {
          setStep('success');
          setTimeout(() => {
            onSuccess && onSuccess(payment);
            onClose();
          }, 2000);
        },
        onError: (error) => {
          setStep('payment');
        }
      });

    } catch (error) {
      console.error('Payment failed:', error);
      setStep('payment');
    }
  };

  // Handle modal close
  const handleClose = () => {
    if (step !== 'processing') {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm"
        onClick={handleClose}
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          onClick={(e) => e.stopPropagation()}
          className={`
            w-full max-w-2xl max-h-[90vh] overflow-y-auto
            bg-white/95 backdrop-blur-xl rounded-3xl shadow-2xl 
            border border-white/30 ${className}
          `}
        >
          {/* Header */}
          <div className="sticky top-0 bg-white/90 backdrop-blur-sm border-b border-gray-100 p-6 rounded-t-3xl">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-green-500 to-emerald-600 flex items-center justify-center shadow-lg">
                  <FontAwesomeIcon icon={faPlus} className="text-white text-xl" />
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-gray-900">Top Up Credits</h2>
                  <p className="text-gray-600">Add credits to your wallet</p>
                </div>
              </div>

              {step !== 'processing' && (
                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={handleClose}
                  className="w-10 h-10 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors"
                >
                  <FontAwesomeIcon icon={faTimes} className="text-gray-600" />
                </motion.button>
              )}
            </div>
          </div>

          {/* Content */}
          <div className="p-6">
            {/* Package Selection Step */}
            {step === 'packages' && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                className="space-y-6"
              >
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Select Credit Package</h3>
                  <p className="text-gray-600">Choose the amount of credits you want to add</p>
                </div>

                {packagesLoading ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {[...Array(4)].map((_, i) => (
                      <div key={i} className="h-24 bg-gray-100 rounded-xl animate-pulse" />
                    ))}
                  </div>
                ) : packagesError ? (
                  <div className="text-center py-8">
                    <FontAwesomeIcon icon={faExclamationTriangle} className="text-red-500 text-3xl mb-4" />
                    <p className="text-gray-600">Failed to load credit packages</p>
                    <button
                      onClick={loadCreditPackages}
                      className="mt-4 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
                    >
                      Try Again
                    </button>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {creditPackages?.map((pkg) => (
                      <CreditPackageCard
                        key={pkg.id}
                        package={pkg}
                        isSelected={selectedPackage?.id === pkg.id}
                        onSelect={handlePackageSelect}
                      />
                    ))}
                  </div>
                )}

                {selectedPackage && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="flex justify-end pt-4 border-t border-gray-100"
                  >
                    <motion.button
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => setStep('payment')}
                      className="px-8 py-3 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-xl font-semibold hover:from-green-600 hover:to-emerald-700 transition-all shadow-lg"
                    >
                      Continue to Payment
                    </motion.button>
                  </motion.div>
                )}
              </motion.div>
            )}

            {/* Payment Step */}
            {step === 'payment' && selectedPackage && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                className="space-y-6"
              >
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Payment Details</h3>
                  <p className="text-gray-600">Review your order and complete payment</p>
                </div>

                {/* Order Summary */}
                <div className="bg-gradient-to-br from-indigo-50 to-blue-50 rounded-2xl p-6 border border-indigo-100">
                  <h4 className="font-semibold text-gray-900 mb-4">Order Summary</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Credits</span>
                      <span className="font-semibold">{selectedPackage.credits.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Price</span>
                      <span className="font-semibold">{selectedPackage.currency_code} {formatPrice(selectedPackage.price)}</span>
                    </div>
                    {selectedPackage.discount > 0 && (
                      <div className="flex justify-between text-green-600">
                        <span>Discount</span>
                        <span className="font-semibold">-{selectedPackage.discount}%</span>
                      </div>
                    )}
                    <div className="border-t border-indigo-200 pt-3 flex justify-between text-lg font-bold">
                      <span>Total</span>
                      <span>{selectedPackage.currency_code} {formatPrice(selectedPackage.price)}</span>
                    </div>
                  </div>
                </div>

                {/* Payment Method */}
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">Payment Method</h4>
                  <label className="flex items-center pr-20 p-4 border border-gray-200 rounded-xl hover:border-indigo-300 cursor-pointer transition-colors">
                    <input
                      type="radio"
                      name="paymentMethod"
                      value="card"
                      checked={paymentMethod === 'card'}
                      onChange={(e) => setPaymentMethod(e.target.value)}
                      className=""
                    />
                    <div className="flex items-center">
                      <FontAwesomeIcon icon={faCreditCard} className="text-indigo-600 mr-2" />
                      <span className="font-medium">Credit/Debit Card</span>
                    </div>
                  </label>
                </div>

                {/* Security Notice */}
                <div className="items-center bg-green-50 border border-green-200 rounded-xl p-4">
                  <div className="flex items-center space-x-3">
                    <FontAwesomeIcon icon={faShieldAlt} className="text-green-600" />
                    <div>
                      <p className="font-medium text-green-900">Secure Payment</p>
                      <p className="text-sm text-green-700">Your payment information is encrypted and secure</p>
                    </div>
                  </div>
                </div>

                {/* Error Display */}
                {paymentError && (
                  <div className="bg-red-50 border border-red-200 rounded-xl p-4">
                    <div className="flex items-center space-x-3">
                      <FontAwesomeIcon icon={faExclamationTriangle} className="text-red-600" />
                      <div>
                        <p className="font-medium text-red-900">Payment Failed</p>
                        <p className="text-sm text-red-700">{paymentError}</p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex space-x-4 pt-4 border-t border-gray-100">
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => setStep('packages')}
                    className="flex-1 py-3 border border-gray-300 text-gray-700 rounded-xl font-medium hover:bg-gray-50 transition-colors"
                  >
                    Back
                  </motion.button>
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={handlePayment}
                    disabled={paymentLoading}
                    className="flex-1 py-3 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-xl font-semibold hover:from-green-600 hover:to-emerald-700 transition-all shadow-lg disabled:opacity-50"
                  >
                    {paymentLoading ? 'Processing...' : `Pay ${selectedPackage.currency_code} ${formatPrice(selectedPackage.price)}`}
                  </motion.button>
                </div>
              </motion.div>
            )}

            {/* Processing Step */}
            {step === 'processing' && (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                className="text-center py-12"
              >
                <PaymentLoadingIndicator text="Processing your payment..." />
                <p className="text-gray-600 mt-4">Please don't close this window</p>
              </motion.div>
            )}

            {/* Success Step */}
            {step === 'success' && (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                className="text-center py-12"
              >
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                  className="w-20 h-20 mx-auto mb-6 bg-green-100 rounded-full flex items-center justify-center"
                >
                  <FontAwesomeIcon icon={faCheckCircle} className="text-green-600 text-3xl" />
                </motion.div>
                <h3 className="text-2xl font-bold text-gray-900 mb-2">Payment Successful!</h3>
                <p className="text-gray-600 mb-4">Credits have been added to your wallet</p>
                {selectedPackage && (
                  <p className="text-lg font-semibold text-green-600">
                    +{selectedPackage.credits.toLocaleString()} Credits
                  </p>
                )}
              </motion.div>
            )}
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default TopUpModal;
