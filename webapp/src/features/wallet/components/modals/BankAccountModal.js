import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faTimes,
  faUniversity,
  faPlus,
  faEdit,
  faShieldAlt
} from '@fortawesome/free-solid-svg-icons';
import BankAccountFormEnhanced from '../../../banking/components/forms/BankAccountFormEnhanced';

/**
 * Bank Account Modal Component
 *
 * Full-screen modal for adding and editing bank accounts with glass-morphism design
 * and smooth animations, following the wallet UI patterns.
 */
const BankAccountModal = ({
  isOpen = false,
  mode = 'add', // 'add' or 'edit'
  account = null, // For editing existing account
  onClose,
  onSuccess,
  className = ''
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Reset state when modal closes
  useEffect(() => {
    if (!isOpen) {
      setIsSubmitting(false);
    }
  }, [isOpen]);

  // Handle form submission
  const handleFormSuccess = async (accountData) => {
    try {
      setIsSubmitting(true);
      await onSuccess(accountData);
      // Modal will be closed by parent component
    } catch (error) {
      console.error('Bank account operation failed:', error);
      // Error is handled by parent component and context
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle form cancellation
  const handleFormCancel = () => {
    if (!isSubmitting) {
      onClose();
    }
  };

  // Handle modal close
  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.3, ease: "easeOut" }}
        className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/60 backdrop-blur-md"
        onClick={handleClose}
      >
        <motion.div
          initial={{ scale: 0.95, opacity: 0, y: 20 }}
          animate={{ scale: 1, opacity: 1, y: 0 }}
          exit={{ scale: 0.95, opacity: 0, y: 20 }}
          transition={{ duration: 0.4, ease: "easeOut" }}
          onClick={(e) => e.stopPropagation()}
          className={`
            w-full max-w-2xl max-h-[90vh] overflow-y-auto
            mx-4 sm:mx-0
            bg-gradient-to-br from-white/95 via-white/90 to-white/95
            backdrop-blur-2xl rounded-3xl
            shadow-[0_25px_50px_-12px_rgba(0,0,0,0.25)] shadow-black/10
            border border-white/20 ring-1 ring-white/10
            ${className}
          `}
        >
          {/* Header */}
          <div className="sticky top-0 bg-gradient-to-r from-white/95 to-white/90 backdrop-blur-xl border-b border-white/20 px-4 sm:px-6 py-4 sm:py-6 rounded-t-3xl">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <motion.div
                  initial={{ scale: 0, rotate: -180 }}
                  animate={{ scale: 1, rotate: 0 }}
                  transition={{ delay: 0.2, duration: 0.5, ease: "easeOut" }}
                  className="w-14 h-14 rounded-2xl bg-gradient-to-br from-green-500 via-emerald-500 to-emerald-600 flex items-center justify-center shadow-lg shadow-green-500/25"
                >
                  <FontAwesomeIcon
                    icon={mode === 'edit' ? faEdit : faPlus}
                    className="text-white text-xl"
                  />
                </motion.div>
                <div>
                  <motion.h2
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.3, duration: 0.4 }}
                    className="text-2xl font-bold text-gray-900 tracking-tight"
                  >
                    {mode === 'edit' ? 'Edit Bank Account' : 'Add Bank Account'}
                  </motion.h2>
                  <motion.p
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.4, duration: 0.4 }}
                    className="text-gray-600 text-base mt-1 font-medium"
                  >
                    {mode === 'edit'
                      ? 'Update your bank account information'
                      : 'Add a new bank account for withdrawals'
                    }
                  </motion.p>
                </div>
              </div>

              {!isSubmitting && (
                <motion.button
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.5, duration: 0.3 }}
                  whileHover={{ scale: 1.1, rotate: 90 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={handleClose}
                  className="w-12 h-12 rounded-2xl bg-gray-100/80 hover:bg-gray-200/80 backdrop-blur-sm flex items-center justify-center transition-all duration-200 border border-gray-200/50 shadow-sm"
                  aria-label="Close modal"
                >
                  <FontAwesomeIcon icon={faTimes} className="text-gray-600" />
                </motion.button>
              )}
            </div>
          </div>

          {/* Content */}
          <div className="p-4 sm:p-8 space-y-6 sm:space-y-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6, duration: 0.5 }}
              className="space-y-8"
            >
              {/* Security Notice */}
              <motion.div
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.7, duration: 0.4 }}
                className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200/60 rounded-2xl p-6 shadow-sm backdrop-blur-sm"
              >
                <div className="flex items-start space-x-4">
                  <div className="w-10 h-10 rounded-xl bg-green-100 flex items-center justify-center">
                    <FontAwesomeIcon icon={faShieldAlt} className="text-green-600" />
                  </div>
                  <div>
                    <p className="font-semibold text-green-900 text-base">Secure & Encrypted</p>
                    <p className="text-green-700 mt-1 leading-relaxed">
                      Your bank account information is encrypted and securely stored using industry-standard security protocols
                    </p>
                  </div>
                </div>
              </motion.div>

              {/* Bank Account Form */}
              <motion.div
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.8, duration: 0.4 }}
                className="bg-gradient-to-br from-gray-50/90 via-white/80 to-gray-50/90 rounded-3xl p-8 border border-gray-200/50 shadow-lg backdrop-blur-sm"
              >
                <BankAccountFormEnhanced
                  mode={mode}
                  account={account}
                  onSuccess={handleFormSuccess}
                  onCancel={handleFormCancel}
                  size="large"
                />
              </motion.div>

              {/* Additional Information */}
              <motion.div
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.9, duration: 0.4 }}
                className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200/60 rounded-2xl p-6 shadow-sm backdrop-blur-sm"
              >
                <div className="flex items-start space-x-4">
                  <div className="w-10 h-10 rounded-xl bg-blue-100 flex items-center justify-center">
                    <FontAwesomeIcon icon={faUniversity} className="text-blue-600" />
                  </div>
                  <div>
                    <p className="font-semibold text-blue-900 text-base mb-3">Important Information</p>
                    <ul className="text-blue-700 space-y-2 leading-relaxed">
                      <li className="flex items-start space-x-2">
                        <span className="w-1.5 h-1.5 rounded-full bg-blue-400 mt-2 flex-shrink-0"></span>
                        <span>Only Malaysian bank accounts are supported</span>
                      </li>
                      <li className="flex items-start space-x-2">
                        <span className="w-1.5 h-1.5 rounded-full bg-blue-400 mt-2 flex-shrink-0"></span>
                        <span>Account holder name must match your registered name</span>
                      </li>
                      <li className="flex items-start space-x-2">
                        <span className="w-1.5 h-1.5 rounded-full bg-blue-400 mt-2 flex-shrink-0"></span>
                        <span>Verification may take 1–2 business days</span>
                      </li>
                      <li className="flex items-start space-x-2">
                        <span className="w-1.5 h-1.5 rounded-full bg-blue-400 mt-2 flex-shrink-0"></span>
                        <span>You can set one account as primary for faster withdrawals</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default BankAccountModal;