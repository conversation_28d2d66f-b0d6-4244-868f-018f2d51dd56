import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faHistory,
  faSpinner,
  faRefresh,
  faFilter,
  faEye,
  faExclamationTriangle,
  faCheckCircle,
  faClock,
  faTimesCircle,
  faChevronDown,
  faChevronUp,
  faShieldAlt,
  faEnvelope,
  faIdCard
} from '@fortawesome/free-solid-svg-icons';
import { useWallet } from '../../contexts';

/**
 * WithdrawalHistoryCard Component
 *
 * Modern card component that displays withdrawal history with filtering,
 * pagination, and beautiful UI following wallet design patterns.
 */
const WithdrawalHistoryCard = ({
  showFilters = true,
  showRefreshButton = true,
  limit = 10,
  size = 'medium', // 'small', 'medium', 'large'
  variant = 'card', // 'card', 'inline', 'minimal'
  className = '',
  onWithdrawalClick,
  autoRefresh = false,
  refreshInterval = 60000 // 1 minute
}) => {
  const {
    withdrawals = [], // Default to empty array
    withdrawalsLoading,
    withdrawalsError,
    loadWithdrawals,
    formatCredits,
    formatWithdrawalCurrency,
    clearError
  } = useWallet();

  // Ensure withdrawals is always an array
  const safeWithdrawals = Array.isArray(withdrawals) ? withdrawals : [];

  // Check if error is a verification error
  const isVerificationError = withdrawalsError &&
    typeof withdrawalsError === 'object' &&
    withdrawalsError.isVerificationError;

  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastRefresh, setLastRefresh] = useState(Date.now());
  const [filters, setFilters] = useState({
    status: null,
    limit,
    offset: 0
  });
  const [showFiltersPanel, setShowFiltersPanel] = useState(false);
  const [expandedItems, setExpandedItems] = useState(new Set());

  // Auto-refresh withdrawals
  useEffect(() => {
    if (!autoRefresh || !refreshInterval) return;

    const interval = setInterval(async () => {
      try {
        await loadWithdrawals({ ...filters, forceRefresh: true });
        setLastRefresh(Date.now());
      } catch (error) {
        console.error('Auto-refresh withdrawals failed:', error);
      }
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, loadWithdrawals, filters]);

  // Load withdrawals on mount and filter changes
  useEffect(() => {
    loadWithdrawals({ ...filters, replace: true });
  }, [filters, loadWithdrawals]);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await loadWithdrawals({ ...filters, replace: true, forceRefresh: true });
      setLastRefresh(Date.now());
    } catch (error) {
      console.error('Manual refresh failed:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value,
      offset: 0 // Reset to first page when filtering
    }));
  };

  const handleLoadMore = () => {
    setFilters(prev => ({
      ...prev,
      offset: prev.offset + prev.limit
    }));
  };

  const toggleExpanded = (withdrawalId) => {
    setExpandedItems(prev => {
      const newSet = new Set(prev);
      if (newSet.has(withdrawalId)) {
        newSet.delete(withdrawalId);
      } else {
        newSet.add(withdrawalId);
      }
      return newSet;
    });
  };

  const getStatusIcon = (status) => {
    const icons = {
      'pending': faClock,
      'processing': faSpinner,
      'completed': faCheckCircle,
      'failed': faTimesCircle,
      'cancelled': faTimesCircle
    };
    return icons[status] || faClock;
  };

  const getStatusColor = (status) => {
    const colors = {
      'pending': 'text-yellow-600 bg-yellow-50 border-yellow-200',
      'processing': 'text-blue-600 bg-blue-50 border-blue-200',
      'completed': 'text-green-600 bg-green-50 border-green-200',
      'failed': 'text-red-600 bg-red-50 border-red-200',
      'cancelled': 'text-gray-600 bg-gray-50 border-gray-200'
    };
    return colors[status] || colors['pending'];
  };

  const getSizeClasses = () => {
    const sizes = {
      small: {
        container: 'p-3',
        title: 'text-base',
        text: 'text-sm',
        button: 'p-1 text-xs'
      },
      medium: {
        container: 'p-4',
        title: 'text-lg',
        text: 'text-base',
        button: 'p-2 text-sm'
      },
      large: {
        container: 'p-6',
        title: 'text-xl',
        text: 'text-lg',
        button: 'p-2 text-base'
      }
    };
    return sizes[size] || sizes.medium;
  };

  const getVariantClasses = () => {
    const variants = {
      card: 'bg-gradient-to-br from-white to-gray-50 border border-gray-200 rounded-xl shadow-lg backdrop-blur-sm',
      inline: 'bg-transparent',
      minimal: 'bg-white border border-gray-200 rounded-lg shadow-sm'
    };
    return variants[variant] || variants.card;
  };

  const sizeClasses = getSizeClasses();
  const variantClasses = getVariantClasses();

  if (withdrawalsError) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className={`
          ${variantClasses} ${sizeClasses.container} ${className}
          border-red-200 bg-gradient-to-br from-red-50 to-red-100
        `}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <FontAwesomeIcon icon={faExclamationTriangle} className="text-red-500" />
            <div>
              <p className="text-red-700 font-medium">Failed to load withdrawals</p>
              <p className="text-red-600 text-sm">
                {typeof withdrawalsError === 'object'
                  ? withdrawalsError.message || 'Failed to load withdrawals'
                  : withdrawalsError
                }
              </p>
            </div>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => clearError('withdrawals')}
              className="text-red-600 hover:text-red-700 text-sm underline"
            >
              Dismiss
            </button>
            <button
              onClick={handleRefresh}
              disabled={isRefreshing}
              className="text-red-600 hover:text-red-700"
            >
              <FontAwesomeIcon
                icon={faRefresh}
                className={isRefreshing ? 'animate-spin' : ''}
              />
            </button>
          </div>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`${variantClasses} ${sizeClasses.container} ${className} relative overflow-hidden`}
    >
      {/* Background Pattern */}
      {variant === 'card' && (
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 right-0 w-32 h-32 bg-blue-300 rounded-full -translate-y-16 translate-x-16"></div>
          <div className="absolute bottom-0 left-0 w-24 h-24 bg-indigo-300 rounded-full translate-y-12 -translate-x-12"></div>
        </div>
      )}

      <div className="relative z-10">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <FontAwesomeIcon icon={faHistory} className={`text-blue-600 ${sizeClasses.title}`} />
            <span className={`text-gray-700 font-medium ${sizeClasses.title}`}>
              Withdrawal History
            </span>
          </div>

          <div className="flex items-center space-x-2">
            {showFilters && (
              <button
                onClick={() => setShowFiltersPanel(!showFiltersPanel)}
                className={`
                  text-gray-500 hover:text-gray-700 transition-colors
                  ${sizeClasses.button}
                `}
              >
                <FontAwesomeIcon icon={faFilter} />
              </button>
            )}

            {showRefreshButton && (
              <button
                onClick={handleRefresh}
                disabled={withdrawalsLoading || isRefreshing}
                className={`
                  text-gray-500 hover:text-gray-700 transition-colors
                  disabled:opacity-50 ${sizeClasses.button}
                `}
              >
                <FontAwesomeIcon
                  icon={faRefresh}
                  className={(withdrawalsLoading || isRefreshing) ? 'animate-spin' : ''}
                />
              </button>
            )}
          </div>
        </div>

        {/* Filters Panel */}
        <AnimatePresence>
          {showFiltersPanel && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="mb-4 p-3 bg-gray-50 rounded-lg border"
            >
              <div className="flex items-center space-x-4">
                <label className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-gray-700">Status:</span>
                  <select
                    value={filters.status || ''}
                    onChange={(e) => handleFilterChange('status', e.target.value || null)}
                    className="text-sm border border-gray-300 rounded px-2 py-1"
                  >
                    <option value="">All</option>
                    <option value="pending">Pending</option>
                    <option value="processing">Processing</option>
                    <option value="completed">Completed</option>
                    <option value="failed">Failed</option>
                    <option value="cancelled">Cancelled</option>
                  </select>
                </label>

                <label className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-gray-700">Show:</span>
                  <select
                    value={filters.limit}
                    onChange={(e) => handleFilterChange('limit', parseInt(e.target.value))}
                    className="text-sm border border-gray-300 rounded px-2 py-1"
                  >
                    <option value={5}>5</option>
                    <option value={10}>10</option>
                    <option value={20}>20</option>
                    <option value={50}>50</option>
                  </select>
                </label>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Withdrawals List */}
        <div className="space-y-3">
          <AnimatePresence mode="wait">
            {withdrawalsLoading && safeWithdrawals.length === 0 ? (
              <motion.div
                key="loading"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="space-y-3"
              >
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="animate-pulse">
                    <div className="h-16 bg-gray-200 rounded-lg"></div>
                  </div>
                ))}
              </motion.div>
            ) : safeWithdrawals.length === 0 ? (
              <motion.div
                key="empty"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="text-center py-8"
              >
                {isVerificationError ? (
                  // Verification Required State
                  <div className="space-y-4">
                    <div className="w-16 h-16 mx-auto rounded-full bg-blue-100 flex items-center justify-center">
                      <FontAwesomeIcon icon={faShieldAlt} className="text-blue-600 text-2xl" />
                    </div>
                    <div>
                      <h4 className="text-lg font-medium text-blue-900 mb-2">Verification Required</h4>
                      <p className="text-blue-700 mb-4">
                        {typeof withdrawalsError === 'object'
                          ? withdrawalsError.message
                          : 'Account verification required to view withdrawal history'
                        }
                      </p>

                      {/* Verification Action Buttons */}
                      <div className="flex justify-center space-x-3">
                        {withdrawalsError?.requiresEmailVerification && (
                          <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium">
                            <FontAwesomeIcon icon={faEnvelope} className="mr-2" />
                            Verify Email
                          </button>
                        )}
                        {withdrawalsError?.requiresKycVerification && (
                          <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium">
                            <FontAwesomeIcon icon={faIdCard} className="mr-2" />
                            Complete E-KYC
                          </button>
                        )}
                        {!withdrawalsError?.requiresEmailVerification && !withdrawalsError?.requiresKycVerification && (
                          <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium">
                            <FontAwesomeIcon icon={faShieldAlt} className="mr-2" />
                            Verify Account
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                ) : (
                  // Normal Empty State
                  <div>
                    <FontAwesomeIcon icon={faHistory} className="text-gray-400 text-3xl mb-2" />
                    <p className="text-gray-500">No withdrawals found</p>
                    <p className="text-gray-400 text-sm">Your withdrawal history will appear here</p>
                  </div>
                )}
              </motion.div>
            ) : (
              <motion.div
                key="withdrawals"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="space-y-3"
              >
                {safeWithdrawals.map((withdrawal) => {
                  // Safety check for withdrawal object
                  if (!withdrawal || !withdrawal.id) {
                    return null;
                  }

                  return (
                    <motion.div
                      key={withdrawal.id}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                    >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className={`
                          w-10 h-10 rounded-full flex items-center justify-center
                          ${getStatusColor(withdrawal.status || 'pending')}
                        `}>
                          <FontAwesomeIcon
                            icon={getStatusIcon(withdrawal.status || 'pending')}
                            className={(withdrawal.status || 'pending') === 'processing' ? 'animate-spin' : ''}
                          />
                        </div>

                        <div>
                          <div className="font-medium text-gray-900">
                            {withdrawal.formattedAmount || formatCredits(withdrawal.amount || 0)}
                          </div>
                          <div className="text-sm text-gray-600">
                            {withdrawal.formattedFiatAmount || formatWithdrawalCurrency(withdrawal.fiat_amount || 0, withdrawal.fiat_currency_code || 'MYR')}
                          </div>
                          <div className="text-xs text-gray-500">
                            {withdrawal.created_at ? new Date(withdrawal.created_at).toLocaleDateString() : 'Unknown date'}
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        <span className={`
                          px-2 py-1 rounded-full text-xs font-medium
                          ${getStatusColor(withdrawal.status || 'pending')}
                        `}>
                          {withdrawal.statusText || withdrawal.status || 'pending'}
                        </span>

                        <button
                          onClick={() => toggleExpanded(withdrawal.id)}
                          className="text-gray-400 hover:text-gray-600"
                        >
                          <FontAwesomeIcon
                            icon={expandedItems.has(withdrawal.id) ? faChevronUp : faChevronDown}
                          />
                        </button>
                      </div>
                    </div>

                    {/* Expanded Details */}
                    <AnimatePresence>
                      {expandedItems.has(withdrawal.id) && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: 'auto' }}
                          exit={{ opacity: 0, height: 0 }}
                          className="mt-3 pt-3 border-t border-gray-200"
                        >
                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                              <span className="text-gray-500">Transaction ID:</span>
                              <p className="font-mono text-xs">{withdrawal.id || 'N/A'}</p>
                            </div>
                            <div>
                              <span className="text-gray-500">Rate:</span>
                              <p>{withdrawal.conversionRate || withdrawal.conversion_rate || '1.00'}</p>
                            </div>
                          </div>

                          {onWithdrawalClick && (
                            <button
                              onClick={() => onWithdrawalClick(withdrawal)}
                              className="mt-3 text-blue-600 hover:text-blue-700 text-sm flex items-center space-x-1"
                            >
                              <FontAwesomeIcon icon={faEye} />
                              <span>View Details</span>
                            </button>
                          )}
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </motion.div>
                  );
                }).filter(Boolean)}
              </motion.div>
            )}
          </AnimatePresence>

          {/* Load More Button */}
          {safeWithdrawals.length > 0 && safeWithdrawals.length >= filters.limit && (
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={handleLoadMore}
              disabled={withdrawalsLoading}
              className="w-full py-2 text-blue-600 hover:text-blue-700 text-sm font-medium"
            >
              {withdrawalsLoading ? (
                <>
                  <FontAwesomeIcon icon={faSpinner} className="animate-spin mr-2" />
                  Loading...
                </>
              ) : (
                'Load More'
              )}
            </motion.button>
          )}
        </div>

        {/* Last Updated */}
        {variant === 'card' && (
          <p className="text-xs text-gray-500 mt-4">
            Last updated: {new Date(lastRefresh).toLocaleTimeString()}
          </p>
        )}
      </div>
    </motion.div>
  );
};

export default WithdrawalHistoryCard;
