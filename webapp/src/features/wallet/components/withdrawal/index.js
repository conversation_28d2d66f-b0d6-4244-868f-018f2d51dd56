/**
 * Withdrawal Components Barrel Export
 *
 * This file provides a centralized export point for all withdrawal-related components.
 * It allows for clean imports and better organization of component dependencies.
 */

// Import components first
import WithdrawalEligibilityCard from './WithdrawalEligibilityCard';
import WithdrawalForm from './WithdrawalForm';
import WithdrawalHistoryCard from './WithdrawalHistoryCard';
import WithdrawalDashboard from './WithdrawalDashboard';

// Core withdrawal components
export { WithdrawalEligibilityCard, WithdrawalForm, WithdrawalHistoryCard, WithdrawalDashboard };

/**
 * Component factory for creating withdrawal components with default props
 */
export const createWithdrawalComponents = (defaultProps = {}) => {
  return {
    EligibilityCard: (props) => WithdrawalEligibilityCard({ ...defaultProps, ...props }),
    Form: (props) => WithdrawalForm({ ...defaultProps, ...props }),
    HistoryCard: (props) => WithdrawalHistoryCard({ ...defaultProps, ...props })
  };
};

/**
 * Withdrawal component utilities
 */
export const withdrawalComponentUtils = {
  /**
   * Get default size classes for consistent styling
   */
  getSizeClasses: (size = 'medium') => {
    const sizes = {
      small: {
        container: 'p-3',
        title: 'text-base',
        text: 'text-sm',
        button: 'px-4 py-2 text-sm',
        input: 'text-sm'
      },
      medium: {
        container: 'p-4',
        title: 'text-lg',
        text: 'text-base',
        button: 'px-6 py-3 text-base',
        input: 'text-base'
      },
      large: {
        container: 'p-6',
        title: 'text-xl',
        text: 'text-lg',
        button: 'px-8 py-4 text-lg',
        input: 'text-lg'
      }
    };
    return sizes[size] || sizes.medium;
  },

  /**
   * Get variant classes for different component styles
   */
  getVariantClasses: (variant = 'card') => {
    const variants = {
      card: 'bg-gradient-to-br from-white to-gray-50 border border-gray-200 rounded-xl shadow-lg backdrop-blur-sm',
      inline: 'bg-transparent',
      minimal: 'bg-white border border-gray-200 rounded-lg shadow-sm'
    };
    return variants[variant] || variants.card;
  },

  /**
   * Get status colors for withdrawal status display
   */
  getStatusColors: (status) => {
    const colors = {
      'pending': 'text-yellow-600 bg-yellow-50 border-yellow-200',
      'processing': 'text-blue-600 bg-blue-50 border-blue-200',
      'completed': 'text-green-600 bg-green-50 border-green-200',
      'failed': 'text-red-600 bg-red-50 border-red-200',
      'cancelled': 'text-gray-600 bg-gray-50 border-gray-200'
    };
    return colors[status] || colors['pending'];
  },

  /**
   * Format withdrawal amount for display
   */
  formatWithdrawalAmount: (amount, currency = 'MYR') => {
    try {
      const locale = currency === 'MYR' ? 'en-MY' : 'en-US';
      return new Intl.NumberFormat(locale, {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(amount);
    } catch (error) {
      const symbols = { MYR: 'RM', USD: '$', SGD: 'S$' };
      const symbol = symbols[currency] || currency;
      return `${symbol} ${amount.toFixed(2)}`;
    }
  },

  /**
   * Format credits for display
   */
  formatCredits: (amount) => {
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount) + ' credits';
  },

  /**
   * Validate withdrawal form data
   */
  validateWithdrawalForm: (formData, rules = {}) => {
    const errors = {};
    const {
      MIN_WITHDRAWAL_AMOUNT = 10,
      MAX_WITHDRAWAL_AMOUNT = 10000,
      SUPPORTED_CURRENCIES = ['MYR', 'USD', 'SGD']
    } = rules;

    // Amount validation
    const amount = parseFloat(formData.amount);
    if (!formData.amount) {
      errors.amount = 'Amount is required';
    } else if (isNaN(amount) || amount <= 0) {
      errors.amount = 'Please enter a valid amount';
    } else if (amount < MIN_WITHDRAWAL_AMOUNT) {
      errors.amount = `Minimum withdrawal is ${MIN_WITHDRAWAL_AMOUNT} credits`;
    } else if (amount > MAX_WITHDRAWAL_AMOUNT) {
      errors.amount = `Maximum withdrawal is ${MAX_WITHDRAWAL_AMOUNT} credits`;
    }

    // Currency validation
    if (!formData.currency) {
      errors.currency = 'Please select a currency';
    } else if (!SUPPORTED_CURRENCIES.includes(formData.currency)) {
      errors.currency = 'Unsupported currency';
    }

    // Bank account validation
    if (!formData.bankAccountId) {
      errors.bankAccountId = 'Please select a bank account';
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }
};

/**
 * Withdrawal component configuration
 */
export const withdrawalComponentConfig = {
  // Default props for all withdrawal components
  defaultProps: {
    size: 'medium',
    variant: 'card',
    showRefreshButton: true,
    autoRefresh: false
  },

  // Animation variants for consistent motion
  animations: {
    fadeIn: {
      initial: { opacity: 0, y: 20 },
      animate: { opacity: 1, y: 0 },
      exit: { opacity: 0, y: -20 }
    },
    slideIn: {
      initial: { opacity: 0, x: -20 },
      animate: { opacity: 1, x: 0 },
      exit: { opacity: 0, x: 20 }
    },
    scaleIn: {
      initial: { opacity: 0, scale: 0.9 },
      animate: { opacity: 1, scale: 1 },
      exit: { opacity: 0, scale: 0.9 }
    }
  },

  // Color schemes for different themes
  colorSchemes: {
    default: {
      primary: 'blue',
      success: 'green',
      warning: 'yellow',
      error: 'red',
      neutral: 'gray'
    },
    dark: {
      primary: 'indigo',
      success: 'emerald',
      warning: 'amber',
      error: 'rose',
      neutral: 'slate'
    }
  }
};

/**
 * Re-export types and interfaces (if using TypeScript)
 */
// export type { WithdrawalEligibility, WithdrawalFormData, WithdrawalHistoryItem } from './types';

/**
 * Component composition helpers
 */
export const WithdrawalComponents = {
  EligibilityCard: WithdrawalEligibilityCard,
  Form: WithdrawalForm,
  HistoryCard: WithdrawalHistoryCard
};

export default WithdrawalComponents;
