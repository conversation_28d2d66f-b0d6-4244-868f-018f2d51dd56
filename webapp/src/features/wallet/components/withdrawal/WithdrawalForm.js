import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faMoneyBillWave,
  faSpinner,
  faCheck,
  faExclamationTriangle,
  faInfoCircle,
  faTimes,
  faCalculator,
  faCreditCard,
  faExchangeAlt
} from '@fortawesome/free-solid-svg-icons';
import { useWallet } from '../../contexts';

/**
 * WithdrawalForm Component
 *
 * Modern withdrawal form component with real-time validation, currency conversion,
 * bank account selection, and beautiful UI following wallet design patterns.
 */
const WithdrawalForm = ({
  onSubmit,
  onCancel,
  isLoading = false,
  showBankAccounts = true,
  showCurrencySelection = true,
  selectedBankAccount = null,
  onBankAccountChange,
  className = '',
  size = 'medium' // 'small', 'medium', 'large'
}) => {
  const {
    balance,
    withdrawalCurrencies = [], // Default to empty array
    currenciesLoading,
    loadWithdrawalCurrencies,
    validateWithdrawal,
    calculateConversion,
    formatCredits,
    formatWithdrawalCurrency,
    getWithdrawalRules
  } = useWallet();

  // Ensure withdrawalCurrencies is always an array
  const safeCurrencies = Array.isArray(withdrawalCurrencies) ? withdrawalCurrencies : [];

  const [formData, setFormData] = useState({
    amount: '',
    currency: 'MYR',
    bankAccountId: selectedBankAccount?.id || '',
    notes: ''
  });

  const [errors, setErrors] = useState({});
  const [validation, setValidation] = useState(null);
  const [conversion, setConversion] = useState(null);
  const [isValidating, setIsValidating] = useState(false);

  // Load currencies on mount
  useEffect(() => {
    if (showCurrencySelection && withdrawalCurrencies.length === 0 && !currenciesLoading) {
      loadWithdrawalCurrencies();
    }
  }, [showCurrencySelection, withdrawalCurrencies.length, currenciesLoading]);

  // Update form when selectedBankAccount prop changes
  useEffect(() => {
    if (selectedBankAccount) {
      setFormData(prev => ({ ...prev, bankAccountId: selectedBankAccount.id }));
      if (onBankAccountChange) {
        onBankAccountChange(selectedBankAccount);
      }
    }
  }, [selectedBankAccount, onBankAccountChange]);

  // Calculate conversion when amount or currency changes
  useEffect(() => {
    if (formData.amount && formData.currency) {
      const amount = parseFloat(formData.amount);
      if (!isNaN(amount) && amount > 0) {
        const conversionResult = calculateConversion(amount, formData.currency);
        setConversion(conversionResult);
      } else {
        setConversion(null);
      }
    } else {
      setConversion(null);
    }
  }, [formData.amount, formData.currency]);

  // Validate withdrawal when form data changes
  useEffect(() => {
    if (formData.amount && formData.currency && formData.bankAccountId) {
      validateWithdrawalData();
    } else {
      setValidation(null);
    }
  }, [formData.amount, formData.currency, formData.bankAccountId]);

  const validateWithdrawalData = async () => {
    const amount = parseFloat(formData.amount);
    if (isNaN(amount) || amount <= 0) {
      setValidation(null);
      return;
    }

    setIsValidating(true);
    try {
      const result = await validateWithdrawal({
        amount,
        currency: formData.currency,
        bankAccountId: formData.bankAccountId,
        currentBalance: balance?.credits || 0
      });
      setValidation(result);
    } catch (error) {
      console.error('Validation error:', error);
      setValidation({
        isValid: false,
        errors: ['Unable to validate withdrawal'],
        code: 'VALIDATION_ERROR'
      });
    } finally {
      setIsValidating(false);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    const rules = getWithdrawalRules();

    // Amount validation
    const amount = parseFloat(formData.amount);
    if (!formData.amount) {
      newErrors.amount = 'Amount is required';
    } else if (isNaN(amount) || amount <= 0) {
      newErrors.amount = 'Please enter a valid amount';
    } else if (amount < rules.MIN_WITHDRAWAL_AMOUNT) {
      newErrors.amount = `Minimum withdrawal is ${rules.MIN_WITHDRAWAL_AMOUNT} credits`;
    } else if (amount > rules.MAX_WITHDRAWAL_AMOUNT) {
      newErrors.amount = `Maximum withdrawal is ${formatCredits(rules.MAX_WITHDRAWAL_AMOUNT)}`;
    } else if (balance && amount > balance.credits) {
      newErrors.amount = `Insufficient balance. You have ${formatCredits(balance.credits)}`;
    }

    // Currency validation
    if (!formData.currency) {
      newErrors.currency = 'Please select a currency';
    }

    // Bank account validation
    if (!formData.bankAccountId) {
      newErrors.bankAccountId = 'Please select a bank account';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    if (!validation?.isValid) {
      setErrors({ submit: 'Please fix validation errors before submitting' });
      return;
    }

    const withdrawalData = {
      amount: parseFloat(formData.amount),
      currency: formData.currency,
      bankAccountId: formData.bankAccountId,
      notes: formData.notes,
      conversion,
      validation
    };

    if (onSubmit) {
      onSubmit(withdrawalData);
    }
  };

  const getSizeClasses = () => {
    const sizes = {
      small: {
        container: 'p-4',
        title: 'text-lg',
        button: 'px-4 py-2 text-sm',
        input: 'text-sm',
        label: 'text-sm'
      },
      medium: {
        container: 'p-6',
        title: 'text-xl',
        button: 'px-6 py-3 text-base',
        input: 'text-base',
        label: 'text-base'
      },
      large: {
        container: 'p-8',
        title: 'text-2xl',
        button: 'px-8 py-4 text-lg',
        input: 'text-lg',
        label: 'text-lg'
      }
    };
    return sizes[size] || sizes.medium;
  };

  const sizeClasses = getSizeClasses();

  return (
    <div className={`withdrawal-form ${className}`}>
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Amount Input */}
        <div className="space-y-2">
          <label className={`block font-medium text-gray-700 ${sizeClasses.label}`}>
            Withdrawal Amount *
          </label>
          <div className="relative">
            <input
              type="number"
              value={formData.amount}
              onChange={(e) => handleInputChange('amount', e.target.value)}
              placeholder="Enter amount in credits"
              min="10"
              max={balance?.credits || 0}
              step="1"
              className={`
                w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                ${sizeClasses.input} pl-4 pr-12 py-3
                ${errors.amount ? 'border-red-300' : ''}
              `}
            />
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center space-x-2">
              {isValidating && (
                <FontAwesomeIcon icon={faSpinner} className="animate-spin text-gray-400" />
              )}
              <span className="text-gray-500 text-sm">Credits</span>
            </div>
          </div>

          {/* Balance Info */}
          {balance && (
            <p className="text-sm text-gray-600">
              Available: {formatCredits(balance.credits)} Credits
            </p>
          )}

          {errors.amount && (
            <p className="text-red-600 text-sm flex items-center">
              <FontAwesomeIcon icon={faExclamationTriangle} className="mr-2" />
              {errors.amount}
            </p>
          )}
        </div>

        {/* Currency Selection */}
        {showCurrencySelection && (
          <div className="space-y-2">
            <label className={`block font-medium text-gray-700 ${sizeClasses.label}`}>
              Currency *
            </label>

            {currenciesLoading ? (
              <div className="animate-pulse">
                <div className="h-12 bg-gray-200 rounded-lg"></div>
              </div>
            ) : (
              <select
                value={formData.currency}
                onChange={(e) => handleInputChange('currency', e.target.value)}
                className={`
                  w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                  ${sizeClasses.input} px-4 py-3
                  ${errors.currency ? 'border-red-300' : ''}
                `}
              >
                {safeCurrencies.map((currency) => {
                  // Safety check for currency object
                  if (!currency || !currency.code) {
                    return null;
                  }

                  return (
                    <option key={currency.code} value={currency.code}>
                      {currency.symbol || ''} {currency.name || currency.code} ({currency.code})
                    </option>
                  );
                }).filter(Boolean)}
              </select>
            )}

            {errors.currency && (
              <p className="text-red-600 text-sm flex items-center">
                <FontAwesomeIcon icon={faExclamationTriangle} className="mr-2" />
                {errors.currency}
              </p>
            )}
          </div>
        )}

        {/* Conversion Display */}
        {conversion && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            className="bg-blue-50 border border-blue-200 rounded-lg p-4"
          >
            <div className="flex items-center space-x-2 mb-2">
              <FontAwesomeIcon icon={faExchangeAlt} className="text-blue-600" />
              <span className="font-medium text-blue-800">Currency Conversion</span>
            </div>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span>Credits:</span>
                <span className="font-medium">{formatCredits(conversion.creditAmount)}</span>
              </div>
              <div className="flex justify-between">
                <span>Rate:</span>
                <span>1 Credit = {conversion.rate} {conversion.currency}</span>
              </div>
              <div className="flex justify-between font-medium text-blue-800 pt-2 border-t border-blue-200">
                <span>You'll receive:</span>
                <span>{conversion.formatted}</span>
              </div>
            </div>
          </motion.div>
        )}

        {/* Validation Status */}
        {validation && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            className={`
              rounded-lg p-4 border
              ${validation.isValid
                ? 'bg-green-50 border-green-200'
                : 'bg-red-50 border-red-200'
              }
            `}
          >
            <div className="flex items-center">
              <FontAwesomeIcon
                icon={validation.isValid ? faCheck : faExclamationTriangle}
                className={`mr-2 ${validation.isValid ? 'text-green-600' : 'text-red-600'}`}
              />
              <div>
                <p className={`font-medium ${validation.isValid ? 'text-green-800' : 'text-red-800'}`}>
                  {validation.isValid ? 'Withdrawal Valid' : 'Validation Issues'}
                </p>
                {validation.errors && validation.errors.length > 0 && (
                  <ul className="text-sm text-red-700 mt-1 space-y-1">
                    {validation.errors.map((error, index) => (
                      <li key={index}>• {error}</li>
                    ))}
                  </ul>
                )}
              </div>
            </div>
          </motion.div>
        )}

        {/* Bank Account Selection */}
        {showBankAccounts && (
          <div className="space-y-2">
            <label className={`block font-medium text-gray-700 ${sizeClasses.label}`}>
              Bank Account *
            </label>

            <div className="space-y-3">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <div className="flex items-center space-x-2">
                  <FontAwesomeIcon icon={faInfoCircle} className="text-blue-600" />
                  <span className="text-blue-800 text-sm">
                    Select a verified bank account for withdrawal
                  </span>
                </div>
              </div>

              <select
                value={formData.bankAccountId}
                onChange={(e) => handleInputChange('bankAccountId', e.target.value)}
                className={`
                  w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                  ${sizeClasses.input} px-4 py-3
                  ${errors.bankAccountId ? 'border-red-300' : ''}
                `}
              >
                <option value="">Select a bank account</option>
                {/* Bank accounts will be populated by parent component */}
              </select>
            </div>

            {errors.bankAccountId && (
              <p className="text-red-600 text-sm flex items-center">
                <FontAwesomeIcon icon={faExclamationTriangle} className="mr-2" />
                {errors.bankAccountId}
              </p>
            )}
          </div>
        )}

        {/* Notes */}
        <div className="space-y-2">
          <label className={`block font-medium text-gray-700 ${sizeClasses.label}`}>
            Notes (Optional)
          </label>
          <textarea
            value={formData.notes}
            onChange={(e) => handleInputChange('notes', e.target.value)}
            placeholder="Add any notes for this withdrawal..."
            rows={3}
            className={`
              w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500
              ${sizeClasses.input} px-4 py-3 resize-none
            `}
          />
        </div>

        {/* Submit Error */}
        {errors.submit && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-700 text-sm flex items-center">
              <FontAwesomeIcon icon={faExclamationTriangle} className="mr-2" />
              {errors.submit}
            </p>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex space-x-4">
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            type="submit"
            disabled={isLoading || !validation?.isValid || isValidating}
            className={`
              flex-1 flex items-center justify-center space-x-2
              bg-blue-600 text-white rounded-lg font-medium
              hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed
              transition-colors ${sizeClasses.button}
            `}
          >
            {isLoading ? (
              <>
                <FontAwesomeIcon icon={faSpinner} className="animate-spin" />
                <span>Processing...</span>
              </>
            ) : (
              <>
                <FontAwesomeIcon icon={faMoneyBillWave} />
                <span>Submit Withdrawal</span>
              </>
            )}
          </motion.button>

          {onCancel && (
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              type="button"
              onClick={onCancel}
              disabled={isLoading}
              className={`
                px-6 py-3 border border-gray-300 text-gray-700 rounded-lg font-medium
                hover:bg-gray-50 disabled:opacity-50 transition-colors
                ${sizeClasses.button}
              `}
            >
              <FontAwesomeIcon icon={faTimes} className="mr-2" />
              Cancel
            </motion.button>
          )}
        </div>
      </form>
    </div>
  );
};

export default WithdrawalForm;
