import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faWallet,
  faRefresh,
  faEye,
  faEyeSlash,
  faArrowUp,
  faArrowDown,
  faSpinner,
  faExclamationTriangle
} from '@fortawesome/free-solid-svg-icons';
import { useWallet } from '../../contexts';
import { BalanceLoadingIndicator } from '../../../../components/common/LoadingIndicator';

/**
 * Enhanced BalanceDisplay Component
 *
 * Credits-only balance display with real-time updates, animations, and interactive features.
 * Supports balance hiding, refresh functionality, and change indicators.
 */
const BalanceDisplay = ({
  showCurrency = false, // Changed default to false for credits-only display
  showRefreshButton = true,
  showHideButton = true,
  showChangeIndicator = true,
  showLatestTransaction = true, // New prop for latest transaction info
  size = 'large', // 'small', 'medium', 'large'
  variant = 'card', // 'card', 'inline', 'minimal'
  className = '',
  onBalanceClick,
  refreshInterval = 30000 // 30 seconds
}) => {
  const {
    balance,
    balanceLoading,
    balanceError,
    transactions,
    loadBalance,
    formatCredits,
    formatCurrency,
    clearError
  } = useWallet();

  // Get latest transaction for display
  const latestTransaction = transactions && transactions.length > 0 ? transactions[0] : null;

  const [isHidden, setIsHidden] = useState(false);
  const [previousBalance, setPreviousBalance] = useState(null);
  const [balanceChange, setBalanceChange] = useState(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastRefresh, setLastRefresh] = useState(Date.now());

  const balanceRef = useRef(balance);
  const changeTimeoutRef = useRef(null);

  // Auto-refresh balance
  useEffect(() => {
    if (!refreshInterval) return;

    const interval = setInterval(async () => {
      try {
        await loadBalance(true);
        setLastRefresh(Date.now());
      } catch (error) {
        console.error('Auto-refresh failed:', error);
      }
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [refreshInterval, loadBalance]);

  // Track balance changes
  useEffect(() => {
    if (balance !== null && balanceRef.current !== null && balance !== balanceRef.current) {
      const change = balance - balanceRef.current;
      setPreviousBalance(balanceRef.current);
      setBalanceChange(change);

      // Clear change indicator after 5 seconds
      if (changeTimeoutRef.current) {
        clearTimeout(changeTimeoutRef.current);
      }
      changeTimeoutRef.current = setTimeout(() => {
        setBalanceChange(null);
      }, 5000);
    }
    balanceRef.current = balance;
  }, [balance]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (changeTimeoutRef.current) {
        clearTimeout(changeTimeoutRef.current);
      }
    };
  }, []);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await loadBalance(true);
      setLastRefresh(Date.now());
    } catch (error) {
      console.error('Manual refresh failed:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleToggleVisibility = () => {
    setIsHidden(!isHidden);
  };

  const handleBalanceClick = () => {
    if (onBalanceClick) {
      onBalanceClick(balance);
    }
  };

  const getSizeClasses = () => {
    const sizes = {
      small: {
        container: 'p-3',
        balance: 'text-lg',
        currency: 'text-sm',
        icon: 'text-lg',
        button: 'p-1 text-xs'
      },
      medium: {
        container: 'p-4',
        balance: 'text-2xl',
        currency: 'text-base',
        icon: 'text-xl',
        button: 'p-2 text-sm'
      },
      large: {
        container: 'p-6',
        balance: 'text-3xl',
        currency: 'text-lg',
        icon: 'text-2xl',
        button: 'p-2 text-base'
      }
    };
    return sizes[size] || sizes.large;
  };

  const getVariantClasses = () => {
    const variants = {
      card: 'bg-gradient-to-br from-blue-50 to-indigo-100 border border-blue-200 rounded-xl shadow-lg backdrop-blur-sm',
      inline: 'bg-transparent',
      minimal: 'bg-white border border-gray-200 rounded-lg shadow-sm'
    };
    return variants[variant] || variants.card;
  };

  const sizeClasses = getSizeClasses();
  const variantClasses = getVariantClasses();

  if (balanceError) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className={`
          ${variantClasses} ${sizeClasses.container} ${className}
          border-red-200 bg-red-50
        `}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <FontAwesomeIcon icon={faExclamationTriangle} className="text-red-500" />
            <div>
              <p className="text-red-700 font-medium">Failed to load balance</p>
              <p className="text-red-600 text-sm">{balanceError}</p>
            </div>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => clearError('balance')}
              className="text-red-600 hover:text-red-700 text-sm underline"
            >
              Dismiss
            </button>
            <button
              onClick={handleRefresh}
              disabled={isRefreshing}
              className="text-red-600 hover:text-red-700"
            >
              <FontAwesomeIcon
                icon={faRefresh}
                className={isRefreshing ? 'animate-spin' : ''}
              />
            </button>
          </div>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`${variantClasses} ${sizeClasses.container} ${className} relative overflow-hidden`}
    >
      {/* Background Pattern */}
      {variant === 'card' && (
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 right-0 w-32 h-32 bg-blue-300 rounded-full -translate-y-16 translate-x-16"></div>
          <div className="absolute bottom-0 left-0 w-24 h-24 bg-indigo-300 rounded-full translate-y-12 -translate-x-12"></div>
        </div>
      )}

      <div className="relative z-10">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <FontAwesomeIcon icon={faWallet} className={`text-blue-600 ${sizeClasses.icon}`} />
            <span className="text-gray-700 font-medium">Balance</span>
          </div>

          <div className="flex items-center space-x-2">
            {showHideButton && (
              <button
                onClick={handleToggleVisibility}
                className={`
                  text-gray-500 hover:text-gray-700 transition-colors
                  ${sizeClasses.button}
                `}
              >
                <FontAwesomeIcon icon={isHidden ? faEye : faEyeSlash} />
              </button>
            )}

            {showRefreshButton && (
              <button
                onClick={handleRefresh}
                disabled={balanceLoading || isRefreshing}
                className={`
                  text-gray-500 hover:text-gray-700 transition-colors
                  disabled:opacity-50 ${sizeClasses.button}
                `}
              >
                <FontAwesomeIcon
                  icon={faRefresh}
                  className={(balanceLoading || isRefreshing) ? 'animate-spin' : ''}
                />
              </button>
            )}
          </div>
        </div>

        {/* Balance Display */}
        <div className="space-y-2">
          <div
            className={`
              font-bold text-gray-900 ${sizeClasses.balance}
              ${onBalanceClick ? 'cursor-pointer hover:text-blue-600 transition-colors' : ''}
            `}
            onClick={handleBalanceClick}
          >
            <AnimatePresence mode="wait">
              {balanceLoading ? (
                <motion.div
                  key="loading"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className="flex items-center space-x-2"
                >
                  <FontAwesomeIcon icon={faSpinner} className="animate-spin" />
                  <span>Loading...</span>
                </motion.div>
              ) : (
                <motion.div
                  key="balance"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="flex items-center space-x-2"
                >
                  <span>
                    {isHidden ? '••••••' : formatCredits(balance || 0)} Credits
                  </span>

                  {/* Change Indicator */}
                  {showChangeIndicator && balanceChange !== null && !isHidden && (
                    <motion.div
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.8 }}
                      className={`
                        flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium
                        ${balanceChange > 0
                          ? 'bg-green-100 text-green-700'
                          : 'bg-red-100 text-red-700'
                        }
                      `}
                    >
                      <FontAwesomeIcon
                        icon={balanceChange > 0 ? faArrowUp : faArrowDown}
                        className="text-xs"
                      />
                      <span>{formatCredits(Math.abs(balanceChange))}</span>
                    </motion.div>
                  )}
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Currency Equivalent */}
          {showCurrency && !balanceLoading && balance !== null && (
            <motion.p
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className={`text-gray-600 ${sizeClasses.currency}`}
            >
              {isHidden ? '≈ ••••••' : `≈ ${formatCurrency(balance || 0, 'MYR')}`}
            </motion.p>
          )}

          {/* Last Updated */}
          {variant === 'card' && (
            <p className="text-xs text-gray-500">
              Last updated: {new Date(lastRefresh).toLocaleTimeString()}
            </p>
          )}
        </div>
      </div>
    </motion.div>
  );
};

export default BalanceDisplay;
