import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faArrowUp,
  faArrowDown,
  faCaretUp,
  faCaretDown,
  faCalendarAlt,
  faChartLine,
  faWallet,
  faCreditCard,
  faExchangeAlt,
  faPercentage
} from '@fortawesome/free-solid-svg-icons';
import { useWallet } from '../../contexts/WalletContext';

/**
 * Enhanced Wallet Stats Component
 *
 * Displays comprehensive wallet statistics with visual hierarchy,
 * trend indicators, and interactive elements.
 */
const WalletStats = ({
  variant = 'dashboard', // 'dashboard', 'sidebar', 'compact'
  timeframe = '30d', // '7d', '30d', '90d', '1y'
  showTrends = true,
  showComparison = true,
  className = ''
}) => {
  const {
    balance,
    transactions,
    getWalletStats,
    statsLoading
  } = useWallet();

  // Local state
  const [stats, setStats] = useState(null);
  const [selectedTimeframe, setSelectedTimeframe] = useState(timeframe);
  const [loading, setLoading] = useState(true);

  // Load stats on mount and timeframe change
  useEffect(() => {
    const loadStats = async () => {
      try {
        setLoading(true);
        const walletStats = await getWalletStats(selectedTimeframe);
        setStats(walletStats);
      } catch (error) {
        console.error('Failed to load wallet stats:', error);
      } finally {
        setLoading(false);
      }
    };

    loadStats();
  }, [selectedTimeframe, getWalletStats]);

  // Calculate derived stats
  const derivedStats = React.useMemo(() => {
    if (!stats || !transactions) return null;

    const totalCreditsEarned = transactions
      .filter(t => t.type === 'credit')
      .reduce((sum, t) => sum + t.amount, 0);

    const totalCreditsSpent = transactions
      .filter(t => t.type === 'debit')
      .reduce((sum, t) => sum + t.amount, 0);

    const successRate = transactions.length > 0
      ? ((transactions.filter(t => t.status === 'completed').length / transactions.length) * 100)
      : 0;

    const avgTransactionAmount = transactions.length > 0
      ? transactions.reduce((sum, t) => sum + t.amount, 0) / transactions.length
      : 0;

    return {
      totalCreditsEarned,
      totalCreditsSpent,
      netFlow: totalCreditsEarned - totalCreditsSpent,
      successRate,
      avgTransactionAmount,
      transactionCount: transactions.length
    };
  }, [stats, transactions]);

  // Timeframe options
  const timeframeOptions = [
    { value: '7d', label: '7 Days' },
    { value: '30d', label: '30 Days' },
    { value: '90d', label: '90 Days' },
    { value: '1y', label: '1 Year' }
  ];

  // Stat card configuration
  const getStatCards = () => {
    if (!derivedStats) return [];

    return [
      {
        title: 'Current Balance',
        value: balance?.credits || 0,
        format: 'credits',
        icon: faWallet,
        color: 'from-indigo-500 to-blue-600',
        trend: stats?.balanceTrend || 0,
        description: 'Available credits'
      },
      {
        title: 'Credits Earned',
        value: derivedStats.totalCreditsEarned,
        format: 'credits',
        icon: faArrowUp,
        color: 'from-green-500 to-emerald-600',
        trend: stats?.earningsTrend || 0,
        description: `In last ${selectedTimeframe}`
      },
      {
        title: 'Credits Spent',
        value: derivedStats.totalCreditsSpent,
        format: 'credits',
        icon: faArrowDown,
        color: 'from-red-500 to-pink-600',
        trend: stats?.spendingTrend || 0,
        description: `In last ${selectedTimeframe}`
      },
      {
        title: 'Net Flow',
        value: derivedStats.netFlow,
        format: 'credits',
        icon: faExchangeAlt,
        color: derivedStats.netFlow >= 0 ? 'from-green-500 to-emerald-600' : 'from-red-500 to-pink-600',
        trend: stats?.netFlowTrend || 0,
        description: 'Earnings - Spending'
      },
      {
        title: 'Success Rate',
        value: derivedStats.successRate,
        format: 'percentage',
        icon: faPercentage,
        color: 'from-purple-500 to-violet-600',
        trend: stats?.successRateTrend || 0,
        description: 'Transaction success'
      },
      {
        title: 'Avg Transaction',
        value: derivedStats.avgTransactionAmount,
        format: 'credits',
        icon: faChartLine,
        color: 'from-orange-500 to-amber-600',
        trend: stats?.avgTransactionTrend || 0,
        description: 'Average amount'
      }
    ];
  };

  // Format value based on type
  const formatValue = (value, format) => {
    switch (format) {
      case 'credits':
        return `${Math.round(value).toLocaleString()}`;
      case 'percentage':
        return `${value.toFixed(1)}%`;
      case 'currency':
        return `$${value.toFixed(2)}`;
      default:
        return value.toString();
    }
  };

  // Render trend indicator
  const renderTrend = (trend) => {
    if (!showTrends || trend === 0) return null;

    const isPositive = trend > 0;
    return (
      <div className={`flex items-center space-x-1 text-xs ${
        isPositive ? 'text-green-600' : 'text-red-600'
      }`}>
        <FontAwesomeIcon
          icon={isPositive ? faCaretUp : faCaretDown}
          className="text-xs"
        />
        <span>{Math.abs(trend).toFixed(1)}%</span>
      </div>
    );
  };

  // Render loading state
  if (loading || statsLoading) {
    return (
      <div className={`space-y-4 ${className}`}>
        {variant === 'dashboard' && (
          <div className="flex items-center justify-between mb-6">
            <div className="h-6 bg-gray-200 rounded w-32 animate-pulse" />
            <div className="h-8 bg-gray-200 rounded w-24 animate-pulse" />
          </div>
        )}
        <div className={`grid gap-4 ${
          variant === 'dashboard' ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' :
          variant === 'sidebar' ? 'grid-cols-1' :
          'grid-cols-2'
        }`}>
          {[...Array(6)].map((_, i) => (
            <div key={i} className="h-24 bg-gray-200 rounded-xl animate-pulse" />
          ))}
        </div>
      </div>
    );
  }

  const statCards = getStatCards();

  return (
    <div className={`${className}`}>
      {/* Header with Timeframe Selector */}
      {variant === 'dashboard' && (
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-xl font-bold text-gray-900">Wallet Statistics</h2>
            <p className="text-sm text-gray-600 mt-1">Overview of your wallet activity</p>
          </div>

          <div className="flex items-center space-x-2">
            {timeframeOptions.map((option) => (
              <motion.button
                key={option.value}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setSelectedTimeframe(option.value)}
                className={`px-3 py-1 text-xs font-medium rounded-lg transition-all ${
                  selectedTimeframe === option.value
                    ? 'bg-indigo-600 text-white'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                {option.label}
              </motion.button>
            ))}
          </div>
        </div>
      )}

      {/* Stats Grid */}
      <div className={`grid gap-4 ${
        variant === 'dashboard' ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' :
        variant === 'sidebar' ? 'grid-cols-1' :
        'grid-cols-2'
      }`}>
        {statCards.map((stat, index) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: index * 0.1 }}
            className="bg-white/80 backdrop-blur-sm border border-white/30 rounded-xl p-4 hover:shadow-lg transition-all"
          >
            <div className="flex items-center justify-between mb-3">
              <div className={`w-10 h-10 rounded-lg bg-gradient-to-br ${stat.color} flex items-center justify-center shadow-lg`}>
                <FontAwesomeIcon icon={stat.icon} className="text-white text-sm" />
              </div>
              {renderTrend(stat.trend)}
            </div>

            <div className="space-y-1">
              <p className="text-2xl font-bold text-gray-900">
                {formatValue(stat.value, stat.format)}
              </p>
              <p className="text-xs font-medium text-gray-700">{stat.title}</p>
              <p className="text-xs text-gray-500">{stat.description}</p>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Summary Card for Sidebar Variant */}
      {variant === 'sidebar' && derivedStats && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.6 }}
          className="mt-6 bg-gradient-to-br from-indigo-50 to-blue-50 border border-indigo-200 rounded-xl p-4"
        >
          <div className="flex items-center mb-3">
            <FontAwesomeIcon icon={faCalendarAlt} className="text-indigo-600 mr-2" />
            <h3 className="font-semibold text-indigo-900">Period Summary</h3>
          </div>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-indigo-700">Transactions</span>
              <span className="font-semibold text-indigo-900">{derivedStats.transactionCount}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-indigo-700">Success Rate</span>
              <span className="font-semibold text-indigo-900">{derivedStats.successRate.toFixed(1)}%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-indigo-700">Net Flow</span>
              <span className={`font-semibold ${
                derivedStats.netFlow >= 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                {derivedStats.netFlow >= 0 ? '+' : ''}{formatValue(derivedStats.netFlow, 'credits')}
              </span>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default WalletStats;
