/**
 * Wallet Feature Barrel Export
 *
 * This file provides a centralized export point for all wallet-related functionality.
 * It includes services, contexts, components, hooks, and utilities.
 */

// Services
export * from './services';

// Contexts
export * from './contexts';

// Components
export * from './components';

// Note: Hooks will be added in subsequent steps
// export * from './hooks';

/**
 * Wallet feature configuration
 */
export const walletConfig = {
  // Cache settings
  cache: {
    balanceDuration: 5 * 60 * 1000, // 5 minutes
    transactionDuration: 1 * 60 * 1000, // 1 minute
  },

  // Payment settings
  payment: {
    defaultReturnUrl: '/wallet',
    maxRetryAttempts: 3,
    retryDelay: 1000,
  },

  // UI settings
  ui: {
    defaultCurrency: 'MYR',
    transactionPageSize: 20,
    balanceRefreshInterval: 30000, // 30 seconds
  }
};

/**
 * Wallet feature utilities
 */
export const walletUtils = {
  /**
   * Format credit amount for display
   * @param {number} amount - Amount to format
   * @returns {string} Formatted amount
   */
  formatCredits: (amount) => {
    if (typeof amount !== 'number') return '0';
    return amount.toLocaleString('en-MY', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    });
  },

  /**
   * Validate credit amount
   * @param {number} amount - Amount to validate
   * @returns {boolean} Is valid amount
   */
  isValidAmount: (amount) => {
    return typeof amount === 'number' && amount > 0 && isFinite(amount);
  },

  /**
   * Calculate transaction fee (if applicable)
   * @param {number} amount - Transaction amount
   * @param {string} type - Transaction type
   * @returns {number} Fee amount
   */
  calculateFee: (amount, type = 'purchase') => {
    // Currently no fees, but structure for future implementation
    return 0;
  },

  /**
   * Get transaction status color
   * @param {string} status - Transaction status
   * @returns {string} CSS color class
   */
  getStatusColor: (status) => {
    const colors = {
      'success': 'text-green-600',
      'pending': 'text-yellow-600',
      'failed': 'text-red-600',
      'cancelled': 'text-gray-600'
    };
    return colors[status] || 'text-gray-600';
  },

  /**
   * Get transaction status icon
   * @param {string} status - Transaction status
   * @returns {string} Icon name or emoji
   */
  getStatusIcon: (status) => {
    const icons = {
      'success': '✅',
      'pending': '⏳',
      'failed': '❌',
      'cancelled': '🚫'
    };
    return icons[status] || '📄';
  }
};

// Re-export constants
export { walletConstants } from './constants';
