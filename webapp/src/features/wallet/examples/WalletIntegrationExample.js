import React, { useEffect } from 'react';
import { useWallet } from '../contexts';

/**
 * WalletIntegrationExample Component
 * 
 * This component demonstrates how to integrate the new WalletContext
 * with existing wallet components. It shows the proper way to:
 * 
 * 1. Access wallet state using the useWallet hook
 * 2. Handle loading and error states
 * 3. Trigger wallet operations
 * 4. Format currency and credits
 * 
 * This can be used as a reference for updating existing components
 * like Wallet.js, BalanceCard.js, etc.
 */
const WalletIntegrationExample = () => {
  const {
    // State
    balance,
    balanceLoading,
    balanceError,
    transactions,
    transactionsLoading,
    transactionsError,
    creditPackages,
    packagesLoading,
    packagesError,
    
    // Actions
    loadBalance,
    loadTransactions,
    loadCreditPackages,
    processPayment,
    validateTransaction,
    refreshWallet,
    clearError,
    
    // Utilities
    formatCurrency,
    formatCredits
  } = useWallet();

  // Initialize wallet data on component mount
  useEffect(() => {
    // The WalletContext automatically initializes, but you can force refresh if needed
    // refreshWallet();
  }, []);

  // Handle balance refresh
  const handleRefreshBalance = async () => {
    try {
      await loadBalance(true); // Force refresh
    } catch (error) {
      console.error('Failed to refresh balance:', error);
    }
  };

  // Handle transaction validation
  const handleValidateTransaction = async (amount) => {
    try {
      const validation = await validateTransaction(amount, 'purchase');
      
      if (validation.isValid) {
        console.log('Transaction is valid:', validation);
        // Proceed with transaction
      } else {
        console.log('Transaction validation failed:', validation.error);
        // Show error to user
      }
    } catch (error) {
      console.error('Validation error:', error);
    }
  };

  // Handle payment processing
  const handleProcessPayment = async (packageId) => {
    try {
      const result = await processPayment(packageId, {
        onSuccess: (payment) => {
          console.log('Payment successful:', payment);
          // Handle success (e.g., show success message, redirect)
        },
        onError: (error) => {
          console.error('Payment failed:', error);
          // Handle error (e.g., show error message)
        }
      });

      if (result.success && result.redirectUrl) {
        // Redirect to payment gateway
        window.location.href = result.redirectUrl;
      }
    } catch (error) {
      console.error('Payment processing error:', error);
    }
  };

  // Handle loading more transactions
  const handleLoadMoreTransactions = async () => {
    try {
      await loadTransactions({
        limit: 20,
        offset: transactions.length,
        replace: false // Append to existing transactions
      });
    } catch (error) {
      console.error('Failed to load more transactions:', error);
    }
  };

  return (
    <div className="wallet-integration-example p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Wallet Integration Example</h1>
      
      {/* Balance Section */}
      <div className="bg-white rounded-lg shadow p-6 mb-6">
        <h2 className="text-lg font-semibold mb-4">Balance</h2>
        
        {balanceLoading ? (
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-48 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-32"></div>
          </div>
        ) : balanceError ? (
          <div className="text-red-600">
            <p>Error: {balanceError}</p>
            <button 
              onClick={() => clearError('balance')}
              className="text-sm underline mt-2"
            >
              Clear Error
            </button>
          </div>
        ) : (
          <div>
            <p className="text-3xl font-bold text-gray-900">
              {formatCredits(balance)} Credits
            </p>
            <p className="text-gray-600">
              ≈ {formatCurrency(balance, 'MYR')}
            </p>
            <button 
              onClick={handleRefreshBalance}
              className="mt-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Refresh Balance
            </button>
          </div>
        )}
      </div>

      {/* Transaction Validation Example */}
      <div className="bg-white rounded-lg shadow p-6 mb-6">
        <h2 className="text-lg font-semibold mb-4">Transaction Validation</h2>
        
        <div className="flex gap-2">
          <button 
            onClick={() => handleValidateTransaction(50)}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
          >
            Validate 50 Credits
          </button>
          <button 
            onClick={() => handleValidateTransaction(1000)}
            className="px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700"
          >
            Validate 1000 Credits
          </button>
        </div>
      </div>

      {/* Credit Packages Section */}
      <div className="bg-white rounded-lg shadow p-6 mb-6">
        <h2 className="text-lg font-semibold mb-4">Credit Packages</h2>
        
        {packagesLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="h-24 bg-gray-200 rounded"></div>
              </div>
            ))}
          </div>
        ) : packagesError ? (
          <div className="text-red-600">
            <p>Error: {packagesError}</p>
            <button 
              onClick={() => loadCreditPackages()}
              className="text-sm underline mt-2"
            >
              Retry
            </button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {creditPackages.map((pkg) => (
              <div key={pkg.id} className="border rounded-lg p-4">
                <h3 className="font-semibold">{pkg.credits} Credits</h3>
                <p className="text-gray-600">{formatCurrency(pkg.price, pkg.currency_code)}</p>
                <button 
                  onClick={() => handleProcessPayment(pkg.id)}
                  className="mt-2 w-full px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700"
                >
                  Purchase
                </button>
              </div>
            ))}
          </div>
        )}
        
        {creditPackages.length === 0 && !packagesLoading && (
          <button 
            onClick={() => loadCreditPackages()}
            className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
          >
            Load Credit Packages
          </button>
        )}
      </div>

      {/* Transactions Section */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold mb-4">Recent Transactions</h2>
        
        {transactionsLoading && transactions.length === 0 ? (
          <div className="space-y-2">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="animate-pulse h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        ) : transactionsError ? (
          <div className="text-red-600">
            <p>Error: {transactionsError}</p>
            <button 
              onClick={() => loadTransactions({ limit: 10, offset: 0, replace: true })}
              className="text-sm underline mt-2"
            >
              Retry
            </button>
          </div>
        ) : (
          <div>
            {transactions.length === 0 ? (
              <p className="text-gray-500">No transactions yet</p>
            ) : (
              <div className="space-y-2">
                {transactions.map((tx) => (
                  <div key={tx.id} className="flex justify-between items-center p-3 border rounded">
                    <div>
                      <p className="font-medium">{tx.description || 'Transaction'}</p>
                      <p className="text-sm text-gray-600">
                        {new Date(tx.created_at).toLocaleDateString()}
                      </p>
                    </div>
                    <div className={`font-semibold ${
                      tx.transaction_type === 'add' ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {tx.transaction_type === 'add' ? '+' : '-'}{formatCredits(Math.abs(tx.credits))}
                    </div>
                  </div>
                ))}
              </div>
            )}
            
            {transactions.length > 0 && (
              <button 
                onClick={handleLoadMoreTransactions}
                disabled={transactionsLoading}
                className="mt-4 w-full px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 disabled:opacity-50"
              >
                {transactionsLoading ? 'Loading...' : 'Load More'}
              </button>
            )}
          </div>
        )}
      </div>

      {/* Global Actions */}
      <div className="mt-6 flex gap-2">
        <button 
          onClick={refreshWallet}
          className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
        >
          Refresh All Data
        </button>
      </div>
    </div>
  );
};

export default WalletIntegrationExample;
