import api from '../../../services/api';

/**
 * Enhanced Verification Service
 *
 * Centralized service for all verification operations including
 * email verification, KYC status, bank account verification,
 * and withdrawal eligibility with caching and error handling.
 */
class VerificationService {
  constructor() {
    this.cache = {
      verificationStatus: null,
      lastStatusUpdate: null,
      withdrawalEligibility: null,
      lastEligibilityCheck: null,
      kycStatus: null,
      lastKycCheck: null
    };

    // Cache durations
    this.VERIFICATION_CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
    this.ELIGIBILITY_CACHE_DURATION = 2 * 60 * 1000; // 2 minutes
    this.KYC_CACHE_DURATION = 10 * 60 * 1000; // 10 minutes
  }

  /**
   * Check if cached data is still valid
   */
  isCacheValid(lastUpdate, duration) {
    if (!lastUpdate) return false;
    return Date.now() - lastUpdate < duration;
  }

  /**
   * Clear all verification caches
   */
  clearCache() {
    this.cache = {
      verificationStatus: null,
      lastStatusUpdate: null,
      withdrawalEligibility: null,
      lastEligibilityCheck: null,
      kycStatus: null,
      lastKycCheck: null
    };
  }

  /**
   * Clear specific cache type
   */
  clearCacheType(type) {
    switch (type) {
      case 'verification':
        this.cache.verificationStatus = null;
        this.cache.lastStatusUpdate = null;
        break;
      case 'eligibility':
        this.cache.withdrawalEligibility = null;
        this.cache.lastEligibilityCheck = null;
        break;
      case 'kyc':
        this.cache.kycStatus = null;
        this.cache.lastKycCheck = null;
        break;
      default:
        this.clearCache();
    }
  }

  /**
   * Get comprehensive verification status
   */
  async getVerificationStatus(forceRefresh = false) {
    try {
      // Return cached data if valid and not forcing refresh
      if (!forceRefresh && this.isCacheValid(this.cache.lastStatusUpdate, this.VERIFICATION_CACHE_DURATION)) {
        return this.cache.verificationStatus;
      }

      console.log('Fetching verification status from API...');
      const response = await api.get('/ekyc/status');
      const status = response.data;

      // Create unified verification object
      const verificationStatus = {
        // Email verification
        email_verified: status.email_verified || false,

        // KYC verification
        ekyc_verified: status.is_verified === true, // Backend returns is_verified boolean, not status string
        kyc_status: status.is_verified ? 'verified' : 'not_verified',
        kyc_submitted_at: status.submitted_at,
        kyc_verified_at: status.verified_at,
        kyc_rejection_reason: status.rejection_reason,

        // Bank accounts
        bank_accounts_count: status.bank_accounts_count || 0,
        has_verified_bank_account: status.has_verified_bank_account || false,

        // Withdrawal eligibility
        can_withdraw: this.calculateWithdrawalEligibility(status),
        withdrawal_requirements: this.getWithdrawalRequirements(status),

        // Verification level
        verification_level: this.calculateVerificationLevel(status),
        verification_score: this.calculateVerificationScore(status),

        // Next steps
        next_verification_step: this.getNextVerificationStep(status),
        missing_requirements: this.getMissingRequirements(status),

        // Timestamps
        last_updated: Date.now(),
        status_checked_at: new Date().toISOString()
      };

      // Cache the result
      this.cache.verificationStatus = verificationStatus;
      this.cache.lastStatusUpdate = Date.now();

      return verificationStatus;
    } catch (error) {
      console.error('Error fetching verification status:', error);

      // Return cached data if available, otherwise return default
      if (this.cache.verificationStatus) {
        return this.cache.verificationStatus;
      }

      return this.getDefaultUnverifiedStatus();
    }
  }

  /**
   * Get withdrawal eligibility with detailed requirements
   */
  async getWithdrawalEligibility(forceRefresh = false) {
    try {
      // Return cached data if valid and not forcing refresh
      if (!forceRefresh && this.isCacheValid(this.cache.lastEligibilityCheck, this.ELIGIBILITY_CACHE_DURATION)) {
        return this.cache.withdrawalEligibility;
      }

      console.log('Checking withdrawal eligibility...');
      const verificationStatus = await this.getVerificationStatus(forceRefresh);

      const eligibility = {
        can_withdraw: verificationStatus.can_withdraw,
        requirements_met: {
          email_verified: verificationStatus.email_verified,
          kyc_verified: verificationStatus.ekyc_verified,
          bank_account_added: verificationStatus.bank_accounts_count > 0
        },
        missing_requirements: verificationStatus.missing_requirements,
        withdrawal_limits: {
          daily_limit: verificationStatus.ekyc_verified ? 10000 : 1000,
          monthly_limit: verificationStatus.ekyc_verified ? 50000 : 5000,
          minimum_withdrawal: 10
        },
        estimated_processing_time: verificationStatus.ekyc_verified ? '1-2 business days' : '3-5 business days',
        verification_level: verificationStatus.verification_level,
        last_checked: Date.now()
      };

      // Cache the result
      this.cache.withdrawalEligibility = eligibility;
      this.cache.lastEligibilityCheck = Date.now();

      return eligibility;
    } catch (error) {
      console.error('Error checking withdrawal eligibility:', error);

      // Return cached data if available, otherwise return restrictive default
      if (this.cache.withdrawalEligibility) {
        return this.cache.withdrawalEligibility;
      }

      return {
        can_withdraw: false,
        requirements_met: {
          email_verified: false,
          kyc_verified: false,
          bank_account_added: false
        },
        missing_requirements: ['email_verification', 'kyc_verification', 'bank_account'],
        withdrawal_limits: {
          daily_limit: 0,
          monthly_limit: 0,
          minimum_withdrawal: 10
        },
        estimated_processing_time: 'N/A',
        verification_level: 'unverified',
        last_checked: Date.now()
      };
    }
  }

  /**
   * Get detailed KYC status
   */
  async getKYCStatus(forceRefresh = false) {
    try {
      // Return cached data if valid and not forcing refresh
      if (!forceRefresh && this.isCacheValid(this.cache.lastKycCheck, this.KYC_CACHE_DURATION)) {
        return this.cache.kycStatus;
      }

      console.log('Fetching KYC status...');
      const response = await api.get('/ekyc/status');
      const status = response.data;

      const kycStatus = {
        status: status.status || 'not_submitted',
        verification_type: status.verification_type || null,
        verification_method: status.verification_method || null,
        full_name: status.full_name || null,
        ic_number: status.ic_number || null,
        passport_number: status.passport_number || null,
        country: status.country || null,
        submitted_at: status.submitted_at,
        verified_at: status.verified_at,
        rejection_reason: status.rejection_reason,
        documents_uploaded: {
          front_ic: !!status.front_ic_photo_path,
          back_ic: !!status.back_ic_photo_path,
          selfie: !!status.selfie_photo_path,
          passport: !!status.passport_photo_path
        },
        can_resubmit: status.status === 'rejected' || status.status === 'not_submitted',
        processing_time_estimate: this.getKYCProcessingTimeEstimate(status.verification_method),
        last_updated: Date.now()
      };

      // Cache the result
      this.cache.kycStatus = kycStatus;
      this.cache.lastKycCheck = Date.now();

      return kycStatus;
    } catch (error) {
      console.error('Error fetching KYC status:', error);

      // Return cached data if available, otherwise return default
      if (this.cache.kycStatus) {
        return this.cache.kycStatus;
      }

      return {
        status: 'not_submitted',
        verification_type: null,
        verification_method: null,
        full_name: null,
        ic_number: null,
        passport_number: null,
        country: null,
        submitted_at: null,
        verified_at: null,
        rejection_reason: null,
        documents_uploaded: {
          front_ic: false,
          back_ic: false,
          selfie: false,
          passport: false
        },
        can_resubmit: true,
        processing_time_estimate: '1-3 business days',
        last_updated: Date.now()
      };
    }
  }

  /**
   * Resend email verification
   */
  async resendEmailVerification() {
    try {
      console.log('Resending email verification...');
      const response = await api.post('/users/resend-verification-email');

      // Clear verification cache to force refresh
      this.clearCacheType('verification');

      return {
        success: true,
        message: response.data?.message || 'Verification email sent successfully',
        data: response.data
      };
    } catch (error) {
      console.error('Error resending email verification:', error);
      throw {
        success: false,
        message: error.response?.data?.message || 'Failed to resend verification email',
        error: error
      };
    }
  }

  /**
   * Calculate withdrawal eligibility
   */
  calculateWithdrawalEligibility(status) {
    return status.email_verified && status.status === 'approved';
  }

  /**
   * Get withdrawal requirements
   */
  getWithdrawalRequirements(status) {
    const requirements = [];

    if (!status.email_verified) {
      requirements.push({
        type: 'email_verification',
        title: 'Email Verification',
        description: 'Verify your email address',
        completed: false,
        action: 'verify_email'
      });
    }

    if (status.status !== 'approved') {
      requirements.push({
        type: 'kyc_verification',
        title: 'Identity Verification',
        description: 'Complete KYC verification',
        completed: false,
        action: 'start_kyc'
      });
    }

    if ((status.bank_accounts_count || 0) === 0) {
      requirements.push({
        type: 'bank_account',
        title: 'Bank Account',
        description: 'Add a bank account for withdrawals',
        completed: false,
        action: 'add_bank_account'
      });
    }

    return requirements;
  }

  /**
   * Calculate verification level
   */
  calculateVerificationLevel(status) {
    const emailVerified = status.email_verified || false;
    const kycVerified = status.is_verified === true; // Backend returns is_verified boolean, not status string
    const hasBankAccount = (status.bank_accounts_count || 0) > 0;

    if (!emailVerified) return 'unverified';
    if (!kycVerified) return 'basic';
    if (!hasBankAccount) return 'verified';
    return 'complete';
  }

  /**
   * Calculate verification score (0-100)
   */
  calculateVerificationScore(status) {
    let score = 0;

    if (status.email_verified) score += 33;
    if (status.is_verified === true) score += 34; // Backend returns is_verified boolean, not status string
    if ((status.bank_accounts_count || 0) > 0) score += 33;

    return Math.min(score, 100);
  }

  /**
   * Get next verification step
   */
  getNextVerificationStep(status) {
    if (!status.email_verified) {
      return {
        step: 'email_verification',
        title: 'Verify Your Email',
        description: 'Check your email and click the verification link',
        action: 'verify_email',
        priority: 'high'
      };
    }

    if (status.status !== 'approved') {
      return {
        step: 'kyc_verification',
        title: 'Complete Identity Verification',
        description: 'Upload your documents for KYC verification',
        action: 'start_kyc',
        priority: 'high'
      };
    }

    if ((status.bank_accounts_count || 0) === 0) {
      return {
        step: 'bank_account',
        title: 'Add Bank Account',
        description: 'Add a bank account to enable withdrawals',
        action: 'add_bank_account',
        priority: 'medium'
      };
    }

    return {
      step: 'completed',
      title: 'Verification Complete',
      description: 'All verification steps completed',
      action: null,
      priority: 'none'
    };
  }

  /**
   * Get missing requirements
   */
  getMissingRequirements(status) {
    const missing = [];

    if (!status.email_verified) {
      missing.push('email_verification');
    }

    if (status.status !== 'approved') {
      missing.push('kyc_verification');
    }

    if ((status.bank_accounts_count || 0) === 0) {
      missing.push('bank_account');
    }

    return missing;
  }

  /**
   * Get KYC processing time estimate
   */
  getKYCProcessingTimeEstimate(method) {
    switch (method) {
      case 'tencent':
        return 'Instant to 1 hour';
      case 'manual':
        return '1-3 business days';
      default:
        return '1-3 business days';
    }
  }

  /**
   * Get default unverified status
   */
  getDefaultUnverifiedStatus() {
    return {
      email_verified: false,
      ekyc_verified: false,
      kyc_status: 'not_submitted',
      kyc_submitted_at: null,
      kyc_verified_at: null,
      kyc_rejection_reason: null,
      bank_accounts_count: 0,
      has_verified_bank_account: false,
      can_withdraw: false,
      withdrawal_requirements: this.getWithdrawalRequirements({
        email_verified: false,
        status: 'not_submitted',
        bank_accounts_count: 0
      }),
      verification_level: 'unverified',
      verification_score: 0,
      next_verification_step: {
        step: 'email_verification',
        title: 'Verify Your Email',
        description: 'Check your email and click the verification link',
        action: 'verify_email',
        priority: 'high'
      },
      missing_requirements: ['email_verification', 'kyc_verification', 'bank_account'],
      last_updated: Date.now(),
      status_checked_at: new Date().toISOString()
    };
  }

  /**
   * Subscribe to verification status changes (for real-time updates)
   */
  subscribeToStatusChanges(callback) {
    // Store callback for future real-time updates
    this.statusChangeCallback = callback;

    // Return unsubscribe function
    return () => {
      this.statusChangeCallback = null;
    };
  }

  /**
   * Notify status change subscribers
   */
  notifyStatusChange(status) {
    if (this.statusChangeCallback) {
      this.statusChangeCallback(status);
    }
  }

  /**
   * Get verification requirements for specific action
   */
  getRequirementsForAction(action) {
    const requirements = {
      withdraw: ['email_verification', 'kyc_verification'],
      bank_account: ['email_verification', 'kyc_verification'],
      high_limit_transaction: ['email_verification', 'kyc_verification', 'bank_account']
    };

    return requirements[action] || [];
  }

  /**
   * Check if user meets requirements for specific action
   */
  async canPerformAction(action) {
    try {
      const status = await this.getVerificationStatus();
      const requiredVerifications = this.getRequirementsForAction(action);

      const checks = {
        email_verification: status.email_verified,
        kyc_verification: status.ekyc_verified,
        bank_account: status.bank_accounts_count > 0
      };

      return requiredVerifications.every(req => checks[req]);
    } catch (error) {
      console.error('Error checking action requirements:', error);
      return false;
    }
  }

  /**
   * Get verification status summary for display
   */
  async getVerificationSummary() {
    try {
      const status = await this.getVerificationStatus();

      return {
        level: status.verification_level,
        score: status.verification_score,
        completedSteps: status.missing_requirements.length === 0 ? 3 : 3 - status.missing_requirements.length,
        totalSteps: 3,
        canWithdraw: status.can_withdraw,
        nextAction: status.next_verification_step,
        benefits: this.getVerificationBenefits(status.verification_level)
      };
    } catch (error) {
      console.error('Error getting verification summary:', error);
      return null;
    }
  }

  /**
   * Get benefits for verification level
   */
  getVerificationBenefits(level) {
    const benefits = {
      unverified: [
        'Basic account access',
        'Limited transaction history'
      ],
      basic: [
        'Email notifications',
        'Basic transaction history',
        'Customer support access'
      ],
      verified: [
        'Full withdrawal access',
        'Higher transaction limits',
        'Priority customer support',
        'Advanced features'
      ],
      complete: [
        'Maximum transaction limits',
        'Instant withdrawals',
        'VIP customer support',
        'Exclusive features',
        'Premium benefits'
      ]
    };

    return benefits[level] || benefits.unverified;
  }
}

// Create singleton instance
const verificationService = new VerificationService();

export default verificationService;
