import * as Yup from 'yup';
import malaysianBankService from '../services/MalaysianBankService';

/**
 * Bank Account Validation Schemas
 * 
 * Comprehensive validation using Yup that aligns with backend validation rules
 * and provides real-time validation feedback.
 */

// DuitNow account number validation
const validateDuitNowAccount = (value) => {
  if (!value) return false;
  
  // DuitNow accounts are prefixed with 'D' followed by digits
  const duitNowPattern = /^D\d{10,15}$/;
  return duitNowPattern.test(value);
};

// Malaysian bank account number validation
const validateMalaysianBankAccount = async (value, context) => {
  if (!value || !context.parent.malaysian_bank_id) {
    return false;
  }

  try {
    const bank = await malaysianBankService.getBankById(context.parent.malaysian_bank_id);
    if (!bank) return false;

    const validation = await malaysianBankService.validateAccountNumber(value, context.parent.malaysian_bank_id);
    return validation.isValid;
  } catch (error) {
    console.error('Account validation error:', error);
    return false;
  }
};

// Account holder name validation
const validateAccountHolderName = (value) => {
  if (!value) return false;
  
  // Malaysian bank account holder names: letters, spaces, apostrophes, hyphens
  // Length: 2-100 characters
  const namePattern = /^[A-Za-z\s'-]{2,100}$/;
  return namePattern.test(value.trim());
};

// Base validation schema
export const bankAccountValidationSchema = Yup.object().shape({
  malaysian_bank_id: Yup.number()
    .required('Please select a bank')
    .positive('Please select a valid bank')
    .integer('Please select a valid bank'),

  account_number: Yup.string()
    .required('Account number is required')
    .min(8, 'Account number must be at least 8 digits')
    .max(20, 'Account number cannot exceed 20 digits')
    .matches(/^[D]?[0-9]+$/, 'Account number can only contain digits (and D prefix for DuitNow)')
    .test('valid-account-format', 'Invalid account number format for selected bank', validateMalaysianBankAccount)
    .test('duitnow-format', 'DuitNow accounts must start with D followed by 10-15 digits', function(value) {
      if (!value) return true;
      
      // If it starts with D, validate as DuitNow
      if (value.startsWith('D')) {
        return validateDuitNowAccount(value);
      }
      
      return true;
    }),

  account_holder_name: Yup.string()
    .required('Account holder name is required')
    .min(2, 'Name must be at least 2 characters')
    .max(100, 'Name cannot exceed 100 characters')
    .test('valid-name-format', 'Name can only contain letters, spaces, apostrophes, and hyphens', validateAccountHolderName)
    .transform((value) => value?.trim().toUpperCase()), // Malaysian banks typically use uppercase

  is_primary: Yup.boolean()
    .default(false)
});

// Validation schema for adding new account
export const addBankAccountSchema = bankAccountValidationSchema;

// Validation schema for updating existing account
export const updateBankAccountSchema = bankAccountValidationSchema.shape({
  malaysian_bank_id: Yup.number()
    .positive('Please select a valid bank')
    .integer('Please select a valid bank'),
    
  account_number: Yup.string()
    .min(8, 'Account number must be at least 8 digits')
    .max(20, 'Account number cannot exceed 20 digits')
    .matches(/^[D]?[0-9]+$/, 'Account number can only contain digits (and D prefix for DuitNow)')
    .test('valid-account-format', 'Invalid account number format for selected bank', validateMalaysianBankAccount)
    .test('duitnow-format', 'DuitNow accounts must start with D followed by 10-15 digits', function(value) {
      if (!value) return true;
      
      if (value.startsWith('D')) {
        return validateDuitNowAccount(value);
      }
      
      return true;
    }),

  account_holder_name: Yup.string()
    .min(2, 'Name must be at least 2 characters')
    .max(100, 'Name cannot exceed 100 characters')
    .test('valid-name-format', 'Name can only contain letters, spaces, apostrophes, and hyphens', validateAccountHolderName)
    .transform((value) => value?.trim().toUpperCase())
});

// Real-time validation helpers
export const validateField = async (fieldName, value, formValues = {}) => {
  try {
    const schema = Yup.reach(bankAccountValidationSchema, fieldName);
    await schema.validate(value, { context: { parent: formValues } });
    return { isValid: true, error: null };
  } catch (error) {
    return { isValid: false, error: error.message };
  }
};

// Validate entire form
export const validateBankAccountForm = async (values) => {
  try {
    await bankAccountValidationSchema.validate(values, { abortEarly: false });
    return { isValid: true, errors: {} };
  } catch (error) {
    const errors = {};
    
    if (error.inner) {
      error.inner.forEach(err => {
        errors[err.path] = err.message;
      });
    }
    
    return { isValid: false, errors };
  }
};

// Format account number for display
export const formatAccountNumber = (accountNumber, bankCode) => {
  if (!accountNumber) return '';
  
  // Handle DuitNow accounts
  if (accountNumber.startsWith('D')) {
    return accountNumber; // DuitNow accounts are displayed as-is
  }
  
  // Format based on bank-specific patterns
  const clean = accountNumber.replace(/[\s-]/g, '');
  
  switch (bankCode) {
    case 'MBB': // Maybank: 1234-5678-90123
    case 'CIMB': // CIMB: 1234-5678-90123
      if (clean.length === 13) {
        return clean.replace(/(\d{4})(\d{4})(\d{5})/, '$1-$2-$3');
      }
      break;
      
    case 'PBB': // Public Bank
      if (clean.length === 10) {
        return clean.replace(/(\d{3})(\d{3})(\d{4})/, '$1-$2-$3');
      } else if (clean.length === 12) {
        return clean.replace(/(\d{4})(\d{4})(\d{4})/, '$1-$2-$3');
      }
      break;
      
    case 'RHB': // RHB: 12345-678-901234
      if (clean.length === 14) {
        return clean.replace(/(\d{5})(\d{3})(\d{6})/, '$1-$2-$3');
      }
      break;
      
    case 'HLB': // Hong Leong
      if (clean.length >= 11) {
        return clean.replace(/(\d{3})(\d{4})(\d+)/, '$1-$2-$3');
      }
      break;
      
    default:
      // Generic formatting for other banks
      if (clean.length >= 10) {
        return clean.replace(/(\d{4})(\d{4})(\d+)/, '$1-$2-$3');
      }
  }
  
  return clean;
};

// Mask account number for display
export const maskAccountNumber = (accountNumber) => {
  if (!accountNumber) return '';
  
  const clean = accountNumber.replace(/[\s-]/g, '');
  
  if (clean.length <= 4) {
    return '*'.repeat(clean.length);
  }
  
  // Show first 2 and last 4 digits
  const firstPart = clean.substring(0, 2);
  const lastPart = clean.substring(clean.length - 4);
  const middlePart = '*'.repeat(clean.length - 6);
  
  return `${firstPart}${middlePart}${lastPart}`;
};

// Get account number placeholder based on bank
export const getAccountNumberPlaceholder = (bankCode) => {
  const patterns = {
    'MBB': 'Enter 13 digits (e.g., ***********23)',
    'CIMB': 'Enter 13 digits (e.g., ***********23)',
    'PBB': 'Enter 10-12 digits (e.g., **********)',
    'RHB': 'Enter 14 digits (e.g., ***********234)',
    'HLB': 'Enter 11-15 digits (e.g., ***********)',
    'DUITNOW': 'Enter DuitNow ID (e.g., D**********)'
  };
  
  return patterns[bankCode] || 'Enter account number';
};

// Check if account supports DuitNow
export const supportsDuitNow = (bankCode) => {
  const duitNowBanks = ['MBB', 'CIMB', 'PBB', 'RHB', 'HLB', 'AMBANK', 'BSN'];
  return duitNowBanks.includes(bankCode);
};

export default {
  bankAccountValidationSchema,
  addBankAccountSchema,
  updateBankAccountSchema,
  validateField,
  validateBankAccountForm,
  formatAccountNumber,
  maskAccountNumber,
  getAccountNumberPlaceholder,
  supportsDuitNow,
  validateDuitNowAccount,
  validateMalaysianBankAccount,
  validateAccountHolderName
};
