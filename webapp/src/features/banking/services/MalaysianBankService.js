import bankAccountService from '../../../services/bankAccountService';
import errorHandlingService from '../../../services/errorHandlingService';
import { WALLET_ERROR_TYPES, API_ERROR_TYPES } from '../../../utils/errorTypes';

/**
 * Malaysian Bank Service
 *
 * Service for managing Malaysian banks, validation, and bank-specific operations.
 * Provides bank information, validation rules, and account number formatting.
 */
class MalaysianBankService {
  constructor() {
    this.banks = [];
    this.banksLoaded = false;
    this.loadingPromise = null;

    // Bind methods
    this.getBanks = this.getBanks.bind(this);
    this.getBankById = this.getBankById.bind(this);
    this.getBankByCode = this.getBankByCode.bind(this);
    this.validateAccountNumber = this.validateAccountNumber.bind(this);
    this.formatAccountNumber = this.formatAccountNumber.bind(this);
  }

  /**
   * Get all Malaysian banks
   * @param {boolean} forceRefresh - Force refresh from API
   * @returns {Promise<Array>} Array of Malaysian banks
   */
  async getBanks(forceRefresh = false) {
    // Return cached banks if available and not forcing refresh
    if (this.banksLoaded && !forceRefresh) {
      return this.banks;
    }

    // If already loading, return the existing promise
    if (this.loadingPromise) {
      return this.loadingPromise;
    }

    // Create loading promise
    this.loadingPromise = this.loadBanksFromAPI();

    try {
      const banks = await this.loadingPromise;
      this.banks = banks;
      this.banksLoaded = true;
      return banks;
    } finally {
      this.loadingPromise = null;
    }
  }

  /**
   * Load banks from API
   * @private
   */
  async loadBanksFromAPI() {
    try {
      console.log('🏦 MalaysianBankService: Loading banks from backend API...');
      const response = await bankAccountService.getMalaysianBanks();

      // Handle different response structures
      const banks = response.data || response || [];

      if (Array.isArray(banks) && banks.length > 0) {
        console.log('🏦 MalaysianBankService: Successfully loaded', banks.length, 'banks from backend API');
        console.log('🏦 MalaysianBankService: Bank list:', banks.map(b => `${b.name} (${b.code})`).join(', '));
        return banks;
      } else {
        console.warn('🏦 MalaysianBankService: Backend API returned empty or invalid bank list');
        console.log('🏦 MalaysianBankService: Response data:', response);
        console.log('🏦 MalaysianBankService: Using fallback banks as backup');
        return this.getFallbackBanks();
      }
    } catch (error) {
      console.error('🏦 MalaysianBankService: Failed to load banks from backend API:', error);
      console.log('🏦 MalaysianBankService: Error details:', {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        url: error.config?.url
      });
      console.log('🏦 MalaysianBankService: Using fallback banks due to API failure');

      // Return fallback banks if API fails
      return this.getFallbackBanks();
    }
  }

  /**
   * Get fallback banks when API fails
   * @private
   */
  getFallbackBanks() {
    return [
      {
        id: 1,
        code: 'MBB',
        name: 'Maybank',
        swift_code: 'MBBEMYKL',
        logo_url: '/images/banks/maybank.png',
        account_number_format: '13 digits',
        account_number_regex: '^[0-9]{13}$',
        description: 'Malayan Banking Berhad',
        is_active: true,
        display_order: 1
      },
      {
        id: 2,
        code: 'CIMB',
        name: 'CIMB Bank',
        swift_code: 'CIBBMYKL',
        logo_url: '/images/banks/cimb.png',
        account_number_format: '13 digits',
        account_number_regex: '^[0-9]{13}$',
        description: 'CIMB Bank Berhad',
        is_active: true,
        display_order: 2
      },
      {
        id: 3,
        code: 'PBB',
        name: 'Public Bank',
        swift_code: 'PBBEMYKL',
        logo_url: '/images/banks/public-bank.png',
        account_number_format: '10-12 digits',
        account_number_regex: '^[0-9]{10,12}$',
        description: 'Public Bank Berhad',
        is_active: true,
        display_order: 3
      },
      {
        id: 4,
        code: 'RHB',
        name: 'RHB Bank',
        swift_code: 'RHBBMYKL',
        logo_url: '/images/banks/rhb.png',
        account_number_format: '14 digits',
        account_number_regex: '^[0-9]{14}$',
        description: 'RHB Bank Berhad',
        is_active: true,
        display_order: 4
      },
      {
        id: 5,
        code: 'HLB',
        name: 'Hong Leong Bank',
        swift_code: 'HLBBMYKL',
        logo_url: '/images/banks/hong-leong.png',
        account_number_format: '11-15 digits',
        account_number_regex: '^[0-9]{11,15}$',
        description: 'Hong Leong Bank Berhad',
        is_active: true,
        display_order: 5
      },
      {
        id: 6,
        code: 'AMB',
        name: 'AmBank',
        swift_code: 'ARBKMYKL',
        logo_url: '/images/banks/ambank.png',
        account_number_format: '12 digits',
        account_number_regex: '^[0-9]{12}$',
        description: 'AmBank (M) Berhad',
        is_active: true,
        display_order: 6
      },
      {
        id: 7,
        code: 'BIMB',
        name: 'Bank Islam',
        swift_code: 'BIMBMYKL',
        logo_url: '/images/banks/bank-islam.png',
        account_number_format: '14 digits',
        account_number_regex: '^[0-9]{14}$',
        description: 'Bank Islam Malaysia Berhad',
        is_active: true,
        display_order: 7
      },
      {
        id: 8,
        code: 'BKRM',
        name: 'Bank Rakyat',
        swift_code: 'BKRMMYKL',
        logo_url: '/images/banks/bank-rakyat.png',
        account_number_format: '12 digits',
        account_number_regex: '^[0-9]{12}$',
        description: 'Bank Kerjasama Rakyat Malaysia Berhad',
        is_active: true,
        display_order: 8
      },
      {
        id: 9,
        code: 'OCBC',
        name: 'OCBC Bank',
        swift_code: 'OCBCMYKL',
        logo_url: '/images/banks/ocbc.png',
        account_number_format: '10-12 digits',
        account_number_regex: '^[0-9]{10,12}$',
        description: 'OCBC Bank (Malaysia) Berhad',
        is_active: true,
        display_order: 9
      },
      {
        id: 10,
        code: 'SCB',
        name: 'Standard Chartered',
        swift_code: 'SCBLMYKX',
        logo_url: '/images/banks/standard-chartered.png',
        account_number_format: '10-14 digits',
        account_number_regex: '^[0-9]{10,14}$',
        description: 'Standard Chartered Bank Malaysia Berhad',
        is_active: true,
        display_order: 10
      }
    ];
  }

  /**
   * Get bank by ID
   * @param {number} id - Bank ID
   * @returns {Promise<Object|null>} Bank object or null
   */
  async getBankById(id) {
    const banks = await this.getBanks();
    return banks.find(bank => bank.id === id) || null;
  }

  /**
   * Get bank by code
   * @param {string} code - Bank code (e.g., 'MBB', 'CIMB')
   * @returns {Promise<Object|null>} Bank object or null
   */
  async getBankByCode(code) {
    const banks = await this.getBanks();
    return banks.find(bank => bank.code === code) || null;
  }

  /**
   * Validate account number for a specific bank
   * @param {string} accountNumber - Account number to validate
   * @param {number} bankId - Bank ID
   * @returns {Promise<Object>} Validation result
   */
  async validateAccountNumber(accountNumber, bankId) {
    try {
      const bank = await this.getBankById(bankId);

      if (!bank) {
        return {
          isValid: false,
          error: 'Bank not found',
          errorCode: 'BANK_NOT_FOUND'
        };
      }

      // Remove spaces and special characters
      const cleanAccountNumber = accountNumber.replace(/[\s-]/g, '');

      // Check if account number matches bank's regex pattern
      const regex = new RegExp(bank.account_number_regex);
      const isValid = regex.test(cleanAccountNumber);

      if (!isValid) {
        return {
          isValid: false,
          error: `Account number must be ${bank.account_number_format}`,
          errorCode: 'INVALID_FORMAT',
          expectedFormat: bank.account_number_format
        };
      }

      return {
        isValid: true,
        cleanAccountNumber,
        formattedAccountNumber: this.formatAccountNumber(cleanAccountNumber, bank)
      };

    } catch (error) {
      console.error('Account number validation failed:', error);
      return {
        isValid: false,
        error: 'Validation failed',
        errorCode: 'VALIDATION_ERROR'
      };
    }
  }

  /**
   * Format account number for display
   * @param {string} accountNumber - Account number to format
   * @param {Object} bank - Bank object
   * @returns {string} Formatted account number
   */
  formatAccountNumber(accountNumber, bank) {
    if (!accountNumber || !bank) return accountNumber;

    const clean = accountNumber.replace(/[\s-]/g, '');

    // Format based on bank-specific rules
    switch (bank.code) {
      case 'MBB': // Maybank: 1234-5678-90123
        return clean.replace(/(\d{4})(\d{4})(\d{5})/, '$1-$2-$3');

      case 'CIMB': // CIMB: 1234-5678-90123
        return clean.replace(/(\d{4})(\d{4})(\d{5})/, '$1-$2-$3');

      case 'PBB': // Public Bank: varies, simple grouping
        if (clean.length === 10) {
          return clean.replace(/(\d{3})(\d{3})(\d{4})/, '$1-$2-$3');
        } else if (clean.length === 12) {
          return clean.replace(/(\d{4})(\d{4})(\d{4})/, '$1-$2-$3');
        }
        return clean;

      case 'RHB': // RHB: 12345-678-901234
        return clean.replace(/(\d{5})(\d{3})(\d{6})/, '$1-$2-$3');

      case 'HLB': // Hong Leong: varies
        if (clean.length >= 11) {
          return clean.replace(/(\d{3})(\d{4})(\d+)/, '$1-$2-$3');
        }
        return clean;

      default:
        // Generic formatting: group by 4 digits
        return clean.replace(/(\d{4})/g, '$1-').replace(/-$/, '');
    }
  }

  /**
   * Get bank logo URL with fallback
   * @param {Object} bank - Bank object
   * @returns {string} Logo URL
   */
  getBankLogoUrl(bank) {
    if (!bank) return '/images/banks/default.png';

    // Return bank logo or fallback to default
    return bank.logo_url || `/images/banks/${bank.code.toLowerCase()}.png`;
  }

  /**
   * Search banks by name or code
   * @param {string} query - Search query
   * @returns {Promise<Array>} Filtered banks
   */
  async searchBanks(query) {
    const banks = await this.getBanks();

    if (!query) return banks;

    const lowerQuery = query.toLowerCase();
    return banks.filter(bank =>
      bank.name.toLowerCase().includes(lowerQuery) ||
      bank.code.toLowerCase().includes(lowerQuery) ||
      bank.description.toLowerCase().includes(lowerQuery)
    );
  }

  /**
   * Get popular banks (top 5 by display order)
   * @returns {Promise<Array>} Popular banks
   */
  async getPopularBanks() {
    const banks = await this.getBanks();
    return banks
      .filter(bank => bank.is_active)
      .sort((a, b) => a.display_order - b.display_order)
      .slice(0, 5);
  }

  /**
   * Clear cached banks
   */
  clearCache() {
    this.banks = [];
    this.banksLoaded = false;
    this.loadingPromise = null;
  }

  /**
   * Get bank statistics
   * @returns {Promise<Object>} Bank statistics
   */
  async getBankStats() {
    const banks = await this.getBanks();

    return {
      totalBanks: banks.length,
      activeBanks: banks.filter(bank => bank.is_active).length,
      bankCodes: banks.map(bank => bank.code),
      lastUpdated: new Date().toISOString()
    };
  }
}

// Create and export singleton instance
const malaysianBankService = new MalaysianBankService();

export default malaysianBankService;
