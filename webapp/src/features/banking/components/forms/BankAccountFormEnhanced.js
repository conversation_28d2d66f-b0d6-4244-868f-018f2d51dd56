import React, { useState, useEffect } from 'react';
import { Formik, Form, Field, ErrorMessage } from 'formik';
import { motion } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faSpinner,
  faCheck,
  faExclamationTriangle,
  faTimes,
  faInfoCircle,
  faSearch,
  faCheckCircle
} from '@fortawesome/free-solid-svg-icons';

import { useBankAccountsEnhanced } from '../../hooks/useBankAccountsEnhanced';
import {
  addBankAccountSchema,
  updateBankAccountSchema,
  getAccountNumberPlaceholder,
  supportsDuitNow
} from '../../validation/bankAccountValidation';

/**
 * Enhanced Bank Account Form Component
 *
 * Modern form using Formik + Yup validation that aligns with backend API structure.
 * Supports both add and edit modes with comprehensive validation and error handling.
 */
const BankAccountFormEnhanced = ({
  account = null,
  mode = 'add', // 'add' or 'edit'
  onSuccess,
  onCancel,
  className = '',
  size = 'medium'
}) => {
  const {
    malaysianBanks,
    addBankAccount,
    updateBankAccount,
    isOperationLoading,
    getOperationError,
    clearError
  } = useBankAccountsEnhanced();

  const [selectedBank, setSelectedBank] = useState(null);
  const [bankSearchQuery, setBankSearchQuery] = useState('');
  const [isValidatingAccount, setIsValidatingAccount] = useState(false);

  // Get initial values
  const getInitialValues = () => {
    if (mode === 'edit' && account) {
      return {
        malaysian_bank_id: account.malaysian_bank_id || '',
        account_number: account.account_number || '',
        account_holder_name: account.account_holder_name || '',
        is_primary: account.is_primary || false
      };
    }

    return {
      malaysian_bank_id: '',
      account_number: '',
      account_holder_name: '',
      is_primary: false
    };
  };

  // Set selected bank when editing
  useEffect(() => {
    if (mode === 'edit' && account && malaysianBanks.length > 0) {
      const bank = malaysianBanks.find(b => b.id === account.malaysian_bank_id);
      setSelectedBank(bank);
    }
  }, [mode, account, malaysianBanks]);

  // Get validation schema based on mode
  const getValidationSchema = () => {
    return mode === 'edit' ? updateBankAccountSchema : addBankAccountSchema;
  };

  // Handle form submission
  const handleSubmit = async (values, { setSubmitting, setFieldError }) => {
    try {
      clearError('operations', mode);

      let result;
      if (mode === 'edit' && account) {
        result = await updateBankAccount(account.id, values);
      } else {
        result = await addBankAccount(values);
      }

      if (onSuccess) {
        onSuccess(result);
      }
    } catch (error) {
      console.error('Form submission error:', error);

      // Handle validation errors from backend
      if (error.response?.data?.errors) {
        const backendErrors = error.response.data.errors;
        Object.keys(backendErrors).forEach(field => {
          const messages = Array.isArray(backendErrors[field])
            ? backendErrors[field]
            : [backendErrors[field]];
          setFieldError(field, messages[0]);
        });
      }
    } finally {
      setSubmitting(false);
    }
  };

  // Handle bank selection
  const handleBankSelect = (bankId, setFieldValue) => {
    const bank = malaysianBanks.find(b => b.id === parseInt(bankId));
    setSelectedBank(bank);
    setFieldValue('malaysian_bank_id', bankId);

    // Clear account number when bank changes
    setFieldValue('account_number', '');
  };

  // Real-time account number validation
  const validateAccountNumber = async (accountNumber, setFieldError) => {
    if (!accountNumber || !selectedBank) return;

    setIsValidatingAccount(true);

    try {
      // Add your real-time validation logic here
      // This would typically call your validation service
      await new Promise(resolve => setTimeout(resolve, 500)); // Simulate API call

      // Clear any previous errors if validation passes
      setFieldError('account_number', undefined);
    } catch (error) {
      setFieldError('account_number', 'Invalid account number format');
    } finally {
      setIsValidatingAccount(false);
    }
  };

  // Get size classes
  const getSizeClasses = () => {
    const sizes = {
      small: {
        container: 'p-4',
        input: 'px-3 py-2 text-sm',
        button: 'px-4 py-2 text-sm',
        label: 'text-sm'
      },
      medium: {
        container: 'p-6',
        input: 'px-4 py-3 text-base',
        button: 'px-6 py-3 text-base',
        label: 'text-base'
      },
      large: {
        container: 'p-8',
        input: 'px-6 py-4 text-lg',
        button: 'px-8 py-4 text-lg',
        label: 'text-lg'
      }
    };
    return sizes[size] || sizes.medium;
  };

  const sizeClasses = getSizeClasses();
  const isLoading = isOperationLoading(mode, account?.id);
  const operationError = getOperationError(mode, account?.id);

  // Filter banks based on search
  const filteredBanks = malaysianBanks.filter(bank =>
    !bankSearchQuery ||
    bank.name.toLowerCase().includes(bankSearchQuery.toLowerCase()) ||
    bank.code.toLowerCase().includes(bankSearchQuery.toLowerCase())
  );

  return (
    <div className={`bank-account-form-enhanced ${className}`}>
      <Formik
        initialValues={getInitialValues()}
        validationSchema={getValidationSchema()}
        onSubmit={handleSubmit}
        enableReinitialize={true}
      >
        {({ values, errors, touched, setFieldValue, setFieldError, isSubmitting }) => (
          <Form className="space-y-6">
            {/* Loading Overlay */}
            {(isSubmitting || isLoading) && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="absolute inset-0 bg-white/80 backdrop-blur-sm rounded-3xl flex items-center justify-center z-10"
              >
                <div className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mb-4 mx-auto shadow-lg">
                    <FontAwesomeIcon icon={faSpinner} className="text-white text-2xl animate-spin" />
                  </div>
                  <p className="text-gray-900 font-semibold text-lg">
                    {mode === 'edit' ? 'Updating Account...' : 'Adding Account...'}
                  </p>
                  <p className="text-gray-600 text-sm mt-1">
                    Please wait while we process your request
                  </p>
                </div>
              </motion.div>
            )}

            {/* Operation Error */}
            {operationError && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-red-50 border border-red-200 rounded-xl p-4"
              >
                <div className="flex items-center">
                  <FontAwesomeIcon icon={faExclamationTriangle} className="text-red-600 mr-3" />
                  <div>
                    <p className="text-red-800 font-medium">Error</p>
                    <p className="text-red-600 text-sm">{operationError}</p>
                  </div>
                </div>
              </motion.div>
            )}

            {/* Malaysian Bank Selection */}
            <div className="space-y-2">
              <label className={`block font-medium text-gray-700 ${sizeClasses.label}`}>
                Malaysian Bank *
              </label>

              {/* Bank Search */}
              <div className="relative mb-3">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FontAwesomeIcon icon={faSearch} className="text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search banks..."
                  value={bankSearchQuery}
                  onChange={(e) => setBankSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                />
              </div>

              {/* Bank Selection */}
              <Field name="malaysian_bank_id">
                {({ field }) => (
                  <select
                    {...field}
                    onChange={(e) => handleBankSelect(e.target.value, setFieldValue)}
                    className={`
                      w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500
                      ${sizeClasses.input} ${errors.malaysian_bank_id && touched.malaysian_bank_id ? 'border-red-300' : ''}
                    `}
                  >
                    <option value="">Select your bank</option>
                    {filteredBanks.map((bank) => (
                      <option key={bank.id} value={bank.id}>
                        {bank.name} ({bank.code})
                      </option>
                    ))}
                  </select>
                )}
              </Field>

              <ErrorMessage name="malaysian_bank_id" component="div" className="text-red-600 text-sm flex items-center">
                <FontAwesomeIcon icon={faExclamationTriangle} className="mr-2" />
                <ErrorMessage name="malaysian_bank_id" />
              </ErrorMessage>

              {/* Selected Bank Info */}
              {selectedBank && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-green-50 border border-green-200 rounded-lg p-3"
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                      <FontAwesomeIcon icon={faCheckCircle} className="text-green-600" />
                    </div>
                    <div>
                      <p className="font-medium text-green-900">{selectedBank.name}</p>
                      <p className="text-sm text-green-700">
                        Format: {selectedBank.account_number_format}
                        {supportsDuitNow(selectedBank.code) && (
                          <span className="ml-2 text-blue-600">• DuitNow supported</span>
                        )}
                      </p>
                    </div>
                  </div>
                </motion.div>
              )}
            </div>

            {/* Account Number */}
            <div className="space-y-2">
              <label className={`block font-medium text-gray-700 ${sizeClasses.label}`}>
                Account Number *
              </label>
              <div className="relative">
                <Field name="account_number">
                  {({ field }) => (
                    <input
                      {...field}
                      type="text"
                      placeholder={selectedBank ? getAccountNumberPlaceholder(selectedBank.code) : "Select a bank first"}
                      disabled={!selectedBank}
                      onChange={(e) => {
                        field.onChange(e);
                        validateAccountNumber(e.target.value, setFieldError);
                      }}
                      className={`
                        w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500
                        ${sizeClasses.input} ${errors.account_number && touched.account_number ? 'border-red-300' : ''}
                        ${!selectedBank ? 'bg-gray-100 cursor-not-allowed' : ''}
                      `}
                    />
                  )}
                </Field>
                {isValidatingAccount && (
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <FontAwesomeIcon icon={faSpinner} className="animate-spin text-gray-400" />
                  </div>
                )}
              </div>

              <ErrorMessage name="account_number" component="div" className="text-red-600 text-sm flex items-center">
                <FontAwesomeIcon icon={faExclamationTriangle} className="mr-2" />
                <ErrorMessage name="account_number" />
              </ErrorMessage>

              {selectedBank && (
                <p className="text-gray-500 text-sm flex items-center">
                  <FontAwesomeIcon icon={faInfoCircle} className="mr-2" />
                  Format: {selectedBank.account_number_format} (digits only, no spaces or dashes)
                  {supportsDuitNow(selectedBank.code) && (
                    <span className="ml-2 text-blue-600">• DuitNow: Start with 'D'</span>
                  )}
                </p>
              )}
            </div>

            {/* Account Holder Name */}
            <div className="space-y-2">
              <label className={`block font-medium text-gray-700 ${sizeClasses.label}`}>
                Account Holder Name *
              </label>
              <Field name="account_holder_name">
                {({ field }) => (
                  <input
                    {...field}
                    type="text"
                    placeholder="Enter account holder name"
                    className={`
                      w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500
                      ${sizeClasses.input} ${errors.account_holder_name && touched.account_holder_name ? 'border-red-300' : ''}
                    `}
                  />
                )}
              </Field>

              <ErrorMessage name="account_holder_name" component="div" className="text-red-600 text-sm flex items-center">
                <FontAwesomeIcon icon={faExclamationTriangle} className="mr-2" />
                <ErrorMessage name="account_holder_name" />
              </ErrorMessage>
            </div>

            {/* Primary Account */}
            <div className="space-y-2">
              <label className="flex items-center space-x-3 cursor-pointer">
                <Field name="is_primary" type="checkbox" className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded" />
                <span className="text-gray-700">
                  Set as primary account for withdrawals
                </span>
              </label>
              <p className="text-gray-500 text-sm ml-7">
                Your primary account will be selected by default for withdrawals
              </p>
            </div>

            {/* Action Buttons */}
            <div className="flex space-x-4 pt-4 border-t border-gray-100">
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                type="submit"
                disabled={isSubmitting || isLoading}
                className={`
                  flex-1 flex items-center justify-center space-x-2
                  bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-lg font-medium
                  hover:from-green-600 hover:to-emerald-700 disabled:opacity-50 disabled:cursor-not-allowed
                  transition-all ${sizeClasses.button}
                `}
              >
                {isSubmitting || isLoading ? (
                  <>
                    <FontAwesomeIcon icon={faSpinner} className="animate-spin" />
                    <span>{mode === 'edit' ? 'Updating...' : 'Adding...'}</span>
                  </>
                ) : (
                  <>
                    <FontAwesomeIcon icon={faCheck} />
                    <span>{mode === 'edit' ? 'Update Account' : 'Add Account'}</span>
                  </>
                )}
              </motion.button>

              {onCancel && (
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  type="button"
                  onClick={onCancel}
                  disabled={isSubmitting || isLoading}
                  className={`
                    px-6 py-3 border border-gray-300 text-gray-700 rounded-lg font-medium
                    hover:bg-gray-50 disabled:opacity-50 transition-colors
                    ${sizeClasses.button}
                  `}
                >
                  <FontAwesomeIcon icon={faTimes} className="mr-2" />
                  Cancel
                </motion.button>
              )}
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default BankAccountFormEnhanced;
