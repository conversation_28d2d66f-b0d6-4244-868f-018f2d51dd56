import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faMoneyBillWave, 
  faSpinner, 
  faCheck,
  faExclamationTriangle,
  faTimes,
  faInfoCircle,
  faExchangeAlt
} from '@fortawesome/free-solid-svg-icons';
import { useWallet } from '../../../wallet/contexts';
import { useBankAccounts } from '../../contexts';
import { CurrencySelector } from '../selectors';
import { bankingUtils } from '../../index';

/**
 * WithdrawalForm Component
 * 
 * Enhanced withdrawal form with real-time validation, currency conversion,
 * bank account selection, and beautiful UI.
 */
const WithdrawalForm = ({
  onSubmit,
  onCancel,
  isLoading = false,
  className = '',
  size = 'medium'
}) => {
  const { balance, formatCredits, validateTransaction } = useWallet();
  const { 
    accounts, 
    currencies, 
    processWithdrawal, 
    canWithdraw,
    getVerifiedAccounts 
  } = useBankAccounts();

  const [formData, setFormData] = useState({
    amount: '',
    currency: 'MYR',
    bankAccountId: '',
    notes: ''
  });

  const [errors, setErrors] = useState({});
  const [conversion, setConversion] = useState(null);
  const [isValidating, setIsValidating] = useState(false);

  const verifiedAccounts = getVerifiedAccounts();

  // Auto-select primary account
  useEffect(() => {
    const primaryAccount = verifiedAccounts.find(acc => acc.is_primary);
    if (primaryAccount && !formData.bankAccountId) {
      setFormData(prev => ({ ...prev, bankAccountId: primaryAccount.id }));
    }
  }, [verifiedAccounts, formData.bankAccountId]);

  // Calculate conversion when amount or currency changes
  useEffect(() => {
    if (formData.amount && formData.currency) {
      calculateConversion();
    }
  }, [formData.amount, formData.currency]);

  const calculateConversion = () => {
    const amount = parseFloat(formData.amount);
    if (isNaN(amount) || amount <= 0) {
      setConversion(null);
      return;
    }

    // Mock conversion rate - replace with actual API call
    const conversionRate = formData.currency === 'MYR' ? 1.0 : 0.21;
    
    const conversionData = bankingUtils.calculateConversion(
      amount, 
      conversionRate, 
      formData.currency
    );
    
    setConversion(conversionData);
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }

    // Real-time validation for amount
    if (field === 'amount') {
      validateAmount(value);
    }
  };

  const validateAmount = async (amount) => {
    if (!amount) return;

    setIsValidating(true);
    
    try {
      const numAmount = parseFloat(amount);
      
      // Basic validation
      const validation = bankingUtils.validateWithdrawalAmount(numAmount, balance);
      
      if (!validation.isValid) {
        setErrors(prev => ({ ...prev, amount: validation.error }));
      } else {
        setErrors(prev => ({ ...prev, amount: null }));
        
        // Additional transaction validation
        const txValidation = await validateTransaction(numAmount, 'withdrawal');
        if (!txValidation.isValid) {
          setErrors(prev => ({ ...prev, amount: txValidation.error }));
        }
      }
    } catch (error) {
      console.error('Amount validation error:', error);
    } finally {
      setIsValidating(false);
    }
  };

  const validateForm = () => {
    const newErrors = {};

    // Amount validation
    if (!formData.amount) {
      newErrors.amount = 'Amount is required';
    } else {
      const numAmount = parseFloat(formData.amount);
      const validation = bankingUtils.validateWithdrawalAmount(numAmount, balance);
      if (!validation.isValid) {
        newErrors.amount = validation.error;
      }
    }

    // Currency validation
    if (!formData.currency) {
      newErrors.currency = 'Currency is required';
    }

    // Bank account validation
    if (!formData.bankAccountId) {
      newErrors.bankAccountId = 'Bank account is required';
    }

    // Check if user can withdraw
    if (!canWithdraw()) {
      newErrors.general = 'You need to verify your account and add a verified bank account to withdraw';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      const withdrawalData = {
        amount: parseFloat(formData.amount),
        currency: formData.currency,
        bank_account_id: formData.bankAccountId,
        notes: formData.notes,
        conversion_rate: conversion?.conversionRate || 1.0,
        converted_amount: conversion?.convertedAmount || parseFloat(formData.amount)
      };

      const result = await processWithdrawal(withdrawalData);

      if (onSubmit) {
        onSubmit(result);
      }
    } catch (error) {
      console.error('Error submitting withdrawal:', error);
      setErrors({ submit: error.message || 'Failed to process withdrawal' });
    }
  };

  const getSizeClasses = () => {
    const sizes = {
      small: {
        container: 'p-4',
        input: 'px-3 py-2 text-sm',
        button: 'px-4 py-2 text-sm',
        label: 'text-sm'
      },
      medium: {
        container: 'p-6',
        input: 'px-4 py-3 text-base',
        button: 'px-6 py-3 text-base',
        label: 'text-base'
      },
      large: {
        container: 'p-8',
        input: 'px-6 py-4 text-lg',
        button: 'px-8 py-4 text-lg',
        label: 'text-lg'
      }
    };
    return sizes[size] || sizes.medium;
  };

  const sizeClasses = getSizeClasses();
  const selectedAccount = verifiedAccounts.find(acc => acc.id === formData.bankAccountId);

  return (
    <div className={`withdrawal-form ${className}`}>
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Form Header */}
        <div className="text-center">
          <div className="mx-auto w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
            <FontAwesomeIcon icon={faMoneyBillWave} className="text-green-600 text-xl" />
          </div>
          <h3 className="text-xl font-semibold text-gray-900">
            Withdraw Credits
          </h3>
          <p className="text-gray-600 mt-2">
            Convert your credits to cash and withdraw to your bank account
          </p>
        </div>

        {/* Balance Display */}
        <div className="bg-blue-50 rounded-lg p-4">
          <div className="flex justify-between items-center">
            <span className="text-gray-700">Available Balance:</span>
            <span className="text-xl font-bold text-blue-600">
              {formatCredits(balance || 0)} Credits
            </span>
          </div>
        </div>

        {/* Amount Input */}
        <div className="space-y-2">
          <label className={`block font-medium text-gray-700 ${sizeClasses.label}`}>
            Withdrawal Amount *
          </label>
          <div className="relative">
            <input
              type="number"
              value={formData.amount}
              onChange={(e) => handleInputChange('amount', e.target.value)}
              placeholder="Enter amount in credits"
              min="10"
              max={balance || 0}
              step="0.01"
              className={`
                w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500
                ${sizeClasses.input} ${errors.amount ? 'border-red-300' : ''}
              `}
            />
            {isValidating && (
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                <FontAwesomeIcon icon={faSpinner} className="animate-spin text-gray-400" />
              </div>
            )}
          </div>
          {errors.amount && (
            <p className="text-red-600 text-sm flex items-center">
              <FontAwesomeIcon icon={faExclamationTriangle} className="mr-2" />
              {errors.amount}
            </p>
          )}
          <p className="text-gray-500 text-sm flex items-center">
            <FontAwesomeIcon icon={faInfoCircle} className="mr-2" />
            Minimum: 10 credits, Maximum: {formatCredits(balance || 0)} credits
          </p>
        </div>

        {/* Currency Selection */}
        <div className="space-y-2">
          <label className={`block font-medium text-gray-700 ${sizeClasses.label}`}>
            Withdrawal Currency *
          </label>
          <CurrencySelector
            selectedCurrency={formData.currency}
            onCurrencyChange={(currency) => handleInputChange('currency', currency.code)}
            availableCurrencies={currencies}
            showConversionRates={true}
            showSearch={false}
            size={size}
            error={errors.currency}
          />
        </div>

        {/* Conversion Display */}
        {conversion && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            className="bg-green-50 border border-green-200 rounded-lg p-4"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <FontAwesomeIcon icon={faExchangeAlt} className="text-green-600 mr-2" />
                <span className="font-medium text-green-800">Conversion</span>
              </div>
              <div className="text-right">
                <div className="text-lg font-bold text-green-800">
                  {conversion.formatted}
                </div>
                <div className="text-sm text-green-600">
                  Rate: 1 Credit = {conversion.conversionRate.toFixed(4)} {conversion.currency}
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {/* Bank Account Selection */}
        <div className="space-y-2">
          <label className={`block font-medium text-gray-700 ${sizeClasses.label}`}>
            Bank Account *
          </label>
          
          {verifiedAccounts.length === 0 ? (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <p className="text-yellow-800 flex items-center">
                <FontAwesomeIcon icon={faExclamationTriangle} className="mr-2" />
                No verified bank accounts found. Please add and verify a bank account first.
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {verifiedAccounts.map((account) => (
                <label
                  key={account.id}
                  className={`
                    flex items-center p-4 rounded-lg border-2 cursor-pointer transition-all
                    ${formData.bankAccountId === account.id
                      ? 'border-green-500 bg-green-50'
                      : 'border-gray-200 bg-white hover:border-gray-300'
                    }
                  `}
                >
                  <input
                    type="radio"
                    name="bankAccount"
                    value={account.id}
                    checked={formData.bankAccountId === account.id}
                    onChange={(e) => handleInputChange('bankAccountId', e.target.value)}
                    className="sr-only"
                  />
                  <div className="flex-1">
                    <div className="flex items-center">
                      <span className="text-xl mr-3">
                        {bankingUtils.getBankIcon(account.bank_name)}
                      </span>
                      <div>
                        <div className="font-medium text-gray-900">
                          {account.bank_name}
                        </div>
                        <div className="text-sm text-gray-600">
                          {bankingUtils.formatAccountNumber(account.account_number)}
                        </div>
                        <div className="text-sm text-gray-500">
                          {account.account_holder_name}
                        </div>
                      </div>
                    </div>
                  </div>
                  {account.is_primary && (
                    <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full mr-2">
                      Primary
                    </span>
                  )}
                  {formData.bankAccountId === account.id && (
                    <FontAwesomeIcon icon={faCheck} className="text-green-500" />
                  )}
                </label>
              ))}
            </div>
          )}

          {errors.bankAccountId && (
            <p className="text-red-600 text-sm flex items-center">
              <FontAwesomeIcon icon={faExclamationTriangle} className="mr-2" />
              {errors.bankAccountId}
            </p>
          )}
        </div>

        {/* Notes */}
        <div className="space-y-2">
          <label className={`block font-medium text-gray-700 ${sizeClasses.label}`}>
            Notes (Optional)
          </label>
          <textarea
            value={formData.notes}
            onChange={(e) => handleInputChange('notes', e.target.value)}
            placeholder="Add any notes for this withdrawal..."
            rows="3"
            className={`
              w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500
              ${sizeClasses.input}
            `}
          />
        </div>

        {/* Summary */}
        {selectedAccount && conversion && (
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-3">Withdrawal Summary</h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Amount:</span>
                <span>{formatCredits(formData.amount)} Credits</span>
              </div>
              <div className="flex justify-between">
                <span>You'll receive:</span>
                <span className="font-medium">{conversion.formatted}</span>
              </div>
              <div className="flex justify-between">
                <span>To account:</span>
                <span>{selectedAccount.bank_name} {bankingUtils.maskAccountNumber(selectedAccount.account_number)}</span>
              </div>
              <div className="flex justify-between text-gray-600">
                <span>Processing time:</span>
                <span>1-3 business days</span>
              </div>
            </div>
          </div>
        )}

        {/* General Error */}
        {errors.general && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-700 flex items-center">
              <FontAwesomeIcon icon={faExclamationTriangle} className="mr-2" />
              {errors.general}
            </p>
          </div>
        )}

        {/* Submit Error */}
        {errors.submit && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-700 flex items-center">
              <FontAwesomeIcon icon={faExclamationTriangle} className="mr-2" />
              {errors.submit}
            </p>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex space-x-4">
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            type="submit"
            disabled={isLoading || !canWithdraw() || verifiedAccounts.length === 0}
            className={`
              flex-1 flex items-center justify-center space-x-2
              bg-green-600 text-white rounded-lg font-medium
              hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed
              transition-colors ${sizeClasses.button}
            `}
          >
            {isLoading ? (
              <>
                <FontAwesomeIcon icon={faSpinner} className="animate-spin" />
                <span>Processing...</span>
              </>
            ) : (
              <>
                <FontAwesomeIcon icon={faMoneyBillWave} />
                <span>Submit Withdrawal</span>
              </>
            )}
          </motion.button>

          {onCancel && (
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              type="button"
              onClick={onCancel}
              disabled={isLoading}
              className={`
                px-6 py-3 border border-gray-300 text-gray-700 rounded-lg font-medium
                hover:bg-gray-50 disabled:opacity-50 transition-colors
                ${sizeClasses.button}
              `}
            >
              <FontAwesomeIcon icon={faTimes} className="mr-2" />
              Cancel
            </motion.button>
          )}
        </div>
      </form>
    </div>
  );
};

export default WithdrawalForm;
