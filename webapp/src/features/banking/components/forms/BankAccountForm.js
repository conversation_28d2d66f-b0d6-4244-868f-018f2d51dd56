import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faBank,
  faSpinner,
  faCheck,
  faExclamationTriangle,
  faTimes,
  faInfoCircle,
  faSearch,
  faCheckCircle
} from '@fortawesome/free-solid-svg-icons';
import { useBankAccounts } from '../../contexts';
import malaysianBankService from '../../services/MalaysianBankService';
import { validateBankAccountData, getAccountNumberPlaceholder } from '../../utils/bankingUtils';

/**
 * BankAccountForm Component
 *
 * Enhanced form for adding/editing bank accounts with validation,
 * Malaysian bank support, and beautiful UI.
 */
const BankAccountForm = ({
  account = null, // For editing existing account
  onSubmit,
  onCancel,
  isLoading = false,
  mode = 'add', // 'add' or 'edit'
  className = '',
  size = 'medium'
}) => {
  const { addBankAccount, updateBankAccount } = useBankAccounts();

  const [formData, setFormData] = useState({
    malaysian_bank_id: '',
    account_number: '',
    account_holder_name: '',
    is_primary: false
  });

  const [errors, setErrors] = useState({});
  const [isValidating, setIsValidating] = useState(false);
  const [malaysianBanks, setMalaysianBanks] = useState([]);
  const [banksLoading, setBanksLoading] = useState(true);
  const [selectedBank, setSelectedBank] = useState(null);
  const [bankSearchQuery, setBankSearchQuery] = useState('');

  // Load Malaysian banks
  useEffect(() => {
    const loadBanks = async () => {
      try {
        setBanksLoading(true);
        const banks = await malaysianBankService.getBanks();
        setMalaysianBanks(banks);
      } catch (error) {
        console.error('Failed to load Malaysian banks:', error);
      } finally {
        setBanksLoading(false);
      }
    };

    loadBanks();
  }, []);

  // Populate form for editing
  useEffect(() => {
    if (mode === 'edit' && account) {
      setFormData({
        malaysian_bank_id: account.malaysian_bank_id || '',
        account_number: account.account_number || '',
        account_holder_name: account.account_holder_name || '',
        is_primary: account.is_primary || false
      });

      // Set selected bank for editing
      if (account.bank) {
        setSelectedBank(account.bank);
      }
    }
  }, [mode, account]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }

    // Handle bank selection
    if (field === 'malaysian_bank_id') {
      const bank = malaysianBanks.find(b => b.id === parseInt(value));
      setSelectedBank(bank);

      // Re-validate account number with new bank
      if (formData.account_number && bank) {
        validateAccountNumber(formData.account_number, bank.id);
      }
    }

    // Real-time validation for account number
    if (field === 'account_number' && selectedBank) {
      validateAccountNumber(value, selectedBank.id);
    }
  };

  const validateAccountNumber = async (accountNumber, bankId) => {
    if (!accountNumber || !bankId) return;

    setIsValidating(true);

    try {
      const validation = await malaysianBankService.validateAccountNumber(accountNumber, bankId);

      if (!validation.isValid) {
        setErrors(prev => ({ ...prev, account_number: validation.error }));
      } else {
        setErrors(prev => ({ ...prev, account_number: null }));
        // Update form data with clean account number
        setFormData(prev => ({
          ...prev,
          account_number: validation.cleanAccountNumber
        }));
      }
    } catch (error) {
      console.error('Account validation failed:', error);
      setErrors(prev => ({ ...prev, account_number: 'Validation failed' }));
    } finally {
      setIsValidating(false);
    }
  };

  const validateForm = async () => {
    const validation = await validateBankAccountData(formData);
    setErrors(validation.errors);
    return validation.isValid;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    const isValid = await validateForm();
    if (!isValid) {
      return;
    }

    try {
      let result;

      if (mode === 'edit' && account) {
        result = await updateBankAccount(account.id, formData);
      } else {
        result = await addBankAccount(formData);
      }

      if (onSubmit) {
        onSubmit(result);
      }
    } catch (error) {
      console.error('Error submitting bank account:', error);
      setErrors({ submit: error.message || 'Failed to save bank account' });
    }
  };

  const getSizeClasses = () => {
    const sizes = {
      small: {
        container: 'p-4',
        input: 'px-3 py-2 text-sm',
        button: 'px-4 py-2 text-sm',
        label: 'text-sm'
      },
      medium: {
        container: 'p-6',
        input: 'px-4 py-3 text-base',
        button: 'px-6 py-3 text-base',
        label: 'text-base'
      },
      large: {
        container: 'p-8',
        input: 'px-6 py-4 text-lg',
        button: 'px-8 py-4 text-lg',
        label: 'text-lg'
      }
    };
    return sizes[size] || sizes.medium;
  };

  const sizeClasses = getSizeClasses();

  return (
    <div className={`bank-account-form ${className}`}>
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Form Header */}
        <div className="text-center">
          <div className="mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
            <FontAwesomeIcon icon={faBank} className="text-blue-600 text-xl" />
          </div>
          <h3 className="text-xl font-semibold text-gray-900">
            {mode === 'edit' ? 'Edit Bank Account' : 'Add Bank Account'}
          </h3>
          <p className="text-gray-600 mt-2">
            {mode === 'edit'
              ? 'Update your bank account information'
              : 'Add a new bank account for withdrawals'
            }
          </p>
        </div>

        {/* Malaysian Bank Selection */}
        <div className="space-y-2">
          <label className={`block font-medium text-gray-700 ${sizeClasses.label}`}>
            Malaysian Bank *
          </label>

          {banksLoading ? (
            <div className="flex items-center justify-center py-8">
              <FontAwesomeIcon icon={faSpinner} className="animate-spin text-gray-400 mr-2" />
              <span className="text-gray-600">Loading banks...</span>
            </div>
          ) : (
            <>
              {/* Bank Search */}
              <div className="relative mb-3">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FontAwesomeIcon icon={faSearch} className="text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search banks..."
                  value={bankSearchQuery}
                  onChange={(e) => setBankSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* Bank Selection */}
              <select
                value={formData.malaysian_bank_id}
                onChange={(e) => handleInputChange('malaysian_bank_id', e.target.value)}
                className={`
                  w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                  ${sizeClasses.input} ${errors.malaysian_bank_id ? 'border-red-300' : ''}
                `}
              >
                <option value="">Select your bank</option>
                {malaysianBanks
                  .filter(bank =>
                    !bankSearchQuery ||
                    bank.name.toLowerCase().includes(bankSearchQuery.toLowerCase()) ||
                    bank.code.toLowerCase().includes(bankSearchQuery.toLowerCase())
                  )
                  .map((bank) => (
                    <option key={bank.id} value={bank.id}>
                      {bank.name} ({bank.code})
                    </option>
                  ))}
              </select>

              {/* Selected Bank Info */}
              {selectedBank && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-blue-50 border border-blue-200 rounded-lg p-3"
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <FontAwesomeIcon icon={faCheckCircle} className="text-blue-600" />
                    </div>
                    <div>
                      <p className="font-medium text-blue-900">{selectedBank.name}</p>
                      <p className="text-sm text-blue-700">
                        Account format: {selectedBank.account_number_format}
                      </p>
                    </div>
                  </div>
                </motion.div>
              )}
            </>
          )}

          {errors.malaysian_bank_id && (
            <p className="text-red-600 text-sm flex items-center">
              <FontAwesomeIcon icon={faExclamationTriangle} className="mr-2" />
              {errors.malaysian_bank_id}
            </p>
          )}
        </div>

        {/* Account Number */}
        <div className="space-y-2">
          <label className={`block font-medium text-gray-700 ${sizeClasses.label}`}>
            Account Number *
          </label>
          <div className="relative">
            <input
              type="text"
              value={formData.account_number}
              onChange={(e) => handleInputChange('account_number', e.target.value)}
              placeholder={selectedBank ? getAccountNumberPlaceholder(selectedBank.code) : "Select a bank first"}
              disabled={!selectedBank}
              className={`
                w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                ${sizeClasses.input} ${errors.account_number ? 'border-red-300' : ''}
                ${!selectedBank ? 'bg-gray-100 cursor-not-allowed' : ''}
              `}
            />
            {isValidating && (
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                <FontAwesomeIcon icon={faSpinner} className="animate-spin text-gray-400" />
              </div>
            )}
          </div>
          {errors.account_number && (
            <p className="text-red-600 text-sm flex items-center">
              <FontAwesomeIcon icon={faExclamationTriangle} className="mr-2" />
              {errors.account_number}
            </p>
          )}
          {selectedBank ? (
            <p className="text-gray-500 text-sm flex items-center">
              <FontAwesomeIcon icon={faInfoCircle} className="mr-2" />
              Format: {selectedBank.account_number_format} (digits only, no spaces or dashes)
            </p>
          ) : (
            <p className="text-gray-500 text-sm flex items-center">
              <FontAwesomeIcon icon={faInfoCircle} className="mr-2" />
              Please select a bank first to see account number format
            </p>
          )}
        </div>

        {/* Account Holder Name */}
        <div className="space-y-2">
          <label className={`block font-medium text-gray-700 ${sizeClasses.label}`}>
            Account Holder Name *
          </label>
          <input
            type="text"
            value={formData.account_holder_name}
            onChange={(e) => handleInputChange('account_holder_name', e.target.value)}
            placeholder="Enter account holder name"
            className={`
              w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500
              ${sizeClasses.input} ${errors.account_holder_name ? 'border-red-300' : ''}
            `}
          />
          {errors.account_holder_name && (
            <p className="text-red-600 text-sm flex items-center">
              <FontAwesomeIcon icon={faExclamationTriangle} className="mr-2" />
              {errors.account_holder_name}
            </p>
          )}
        </div>



        {/* Primary Account */}
        <div className="space-y-2">
          <label className="flex items-center space-x-3 cursor-pointer">
            <input
              type="checkbox"
              checked={formData.is_primary}
              onChange={(e) => handleInputChange('is_primary', e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <span className="text-gray-700">
              Set as primary account for withdrawals
            </span>
          </label>
          <p className="text-gray-500 text-sm ml-7">
            Your primary account will be selected by default for withdrawals
          </p>
        </div>

        {/* Submit Error */}
        {errors.submit && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-700 flex items-center">
              <FontAwesomeIcon icon={faExclamationTriangle} className="mr-2" />
              {errors.submit}
            </p>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex space-x-4">
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            type="submit"
            disabled={isLoading}
            className={`
              flex-1 flex items-center justify-center space-x-2
              bg-blue-600 text-white rounded-lg font-medium
              hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed
              transition-colors ${sizeClasses.button}
            `}
          >
            {isLoading ? (
              <>
                <FontAwesomeIcon icon={faSpinner} className="animate-spin" />
                <span>{mode === 'edit' ? 'Updating...' : 'Adding...'}</span>
              </>
            ) : (
              <>
                <FontAwesomeIcon icon={faCheck} />
                <span>{mode === 'edit' ? 'Update Account' : 'Add Account'}</span>
              </>
            )}
          </motion.button>

          {onCancel && (
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              type="button"
              onClick={onCancel}
              disabled={isLoading}
              className={`
                px-6 py-3 border border-gray-300 text-gray-700 rounded-lg font-medium
                hover:bg-gray-50 disabled:opacity-50 transition-colors
                ${sizeClasses.button}
              `}
            >
              <FontAwesomeIcon icon={faTimes} className="mr-2" />
              Cancel
            </motion.button>
          )}
        </div>
      </form>
    </div>
  );
};

export default BankAccountForm;
