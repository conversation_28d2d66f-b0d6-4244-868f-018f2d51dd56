import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faChevronDown, 
  faSearch, 
  faCheck,
  faExchangeAlt,
  faSpinner,
  faGlobe
} from '@fortawesome/free-solid-svg-icons';
import { useBankAccounts } from '../../contexts';

/**
 * CurrencySelector Component
 * 
 * Standalone currency selection component with search, conversion rates,
 * and beautiful animations. Supports both dropdown and modal modes.
 */
const CurrencySelector = ({
  selectedCurrency,
  onCurrencyChange,
  availableCurrencies = [],
  showConversionRates = true,
  showSearch = true,
  disabled = false,
  placeholder = "Select currency",
  mode = 'dropdown', // 'dropdown' or 'modal'
  size = 'medium', // 'small', 'medium', 'large'
  className = '',
  error = null
}) => {
  const { currencies, currenciesLoading, loadCurrencies } = useBankAccounts();
  
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [conversionRates, setConversionRates] = useState({});
  const [ratesLoading, setRatesLoading] = useState(false);
  
  const dropdownRef = useRef(null);
  const searchInputRef = useRef(null);

  // Use provided currencies or fetch from context
  const currencyList = availableCurrencies.length > 0 ? availableCurrencies : currencies;

  // Load currencies on mount
  useEffect(() => {
    if (currencyList.length === 0 && !currenciesLoading) {
      loadCurrencies();
    }
  }, [currencyList.length, currenciesLoading, loadCurrencies]);

  // Load conversion rates when showing rates
  useEffect(() => {
    if (showConversionRates && isOpen && currencyList.length > 0) {
      loadConversionRates();
    }
  }, [showConversionRates, isOpen, currencyList.length]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && showSearch && searchInputRef.current) {
      setTimeout(() => searchInputRef.current?.focus(), 100);
    }
  }, [isOpen, showSearch]);

  const loadConversionRates = async () => {
    setRatesLoading(true);
    try {
      // Mock conversion rates - replace with actual API call
      const rates = {
        'MYR': 1.0,
        'USD': 0.21,
        'SGD': 0.28,
        'EUR': 0.19,
        'GBP': 0.16
      };
      setConversionRates(rates);
    } catch (error) {
      console.error('Failed to load conversion rates:', error);
    } finally {
      setRatesLoading(false);
    }
  };

  const filteredCurrencies = currencyList.filter(currency => {
    if (!searchTerm) return true;
    
    const searchLower = searchTerm.toLowerCase();
    return (
      currency.code?.toLowerCase().includes(searchLower) ||
      currency.name?.toLowerCase().includes(searchLower) ||
      currency.symbol?.toLowerCase().includes(searchLower)
    );
  });

  const handleCurrencySelect = (currency) => {
    onCurrencyChange(currency);
    setIsOpen(false);
    setSearchTerm('');
  };

  const selectedCurrencyData = currencyList.find(c => c.code === selectedCurrency) || 
                              { code: selectedCurrency, name: selectedCurrency, symbol: selectedCurrency };

  const getSizeClasses = () => {
    const sizes = {
      small: {
        button: 'px-3 py-2 text-sm',
        dropdown: 'text-sm',
        icon: 'text-sm'
      },
      medium: {
        button: 'px-4 py-3 text-base',
        dropdown: 'text-base',
        icon: 'text-base'
      },
      large: {
        button: 'px-6 py-4 text-lg',
        dropdown: 'text-lg',
        icon: 'text-lg'
      }
    };
    return sizes[size] || sizes.medium;
  };

  const sizeClasses = getSizeClasses();

  const getCurrencyFlag = (currencyCode) => {
    const flags = {
      'MYR': '🇲🇾',
      'USD': '🇺🇸',
      'SGD': '🇸🇬',
      'EUR': '🇪🇺',
      'GBP': '🇬🇧',
      'JPY': '🇯🇵',
      'CNY': '🇨🇳',
      'THB': '🇹🇭',
      'IDR': '🇮🇩'
    };
    return flags[currencyCode] || '🌍';
  };

  return (
    <div className={`currency-selector relative ${className}`} ref={dropdownRef}>
      {/* Selector Button */}
      <button
        type="button"
        onClick={() => !disabled && setIsOpen(!isOpen)}
        disabled={disabled}
        className={`
          w-full flex items-center justify-between
          border border-gray-300 rounded-lg
          bg-white hover:bg-gray-50
          focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
          transition-all duration-200
          ${sizeClasses.button}
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
          ${error ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : ''}
          ${isOpen ? 'ring-2 ring-blue-500 border-blue-500' : ''}
        `}
      >
        <div className="flex items-center space-x-3">
          {currenciesLoading ? (
            <FontAwesomeIcon icon={faSpinner} className="animate-spin text-gray-400" />
          ) : selectedCurrency ? (
            <>
              <span className="text-xl">{getCurrencyFlag(selectedCurrency)}</span>
              <div className="text-left">
                <div className="font-medium text-gray-900">
                  {selectedCurrencyData.code}
                </div>
                {selectedCurrencyData.name && selectedCurrencyData.name !== selectedCurrencyData.code && (
                  <div className="text-sm text-gray-500">
                    {selectedCurrencyData.name}
                  </div>
                )}
              </div>
            </>
          ) : (
            <>
              <FontAwesomeIcon icon={faGlobe} className="text-gray-400" />
              <span className="text-gray-500">{placeholder}</span>
            </>
          )}
        </div>

        <FontAwesomeIcon 
          icon={faChevronDown} 
          className={`
            text-gray-400 transition-transform duration-200
            ${isOpen ? 'rotate-180' : ''}
          `}
        />
      </button>

      {/* Error Message */}
      {error && (
        <p className="mt-1 text-sm text-red-600">{error}</p>
      )}

      {/* Dropdown */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className={`
              absolute z-50 w-full mt-2
              bg-white border border-gray-200 rounded-lg shadow-lg
              backdrop-blur-sm bg-opacity-95
              max-h-80 overflow-hidden
              ${sizeClasses.dropdown}
            `}
          >
            {/* Search Input */}
            {showSearch && (
              <div className="p-3 border-b border-gray-100">
                <div className="relative">
                  <FontAwesomeIcon 
                    icon={faSearch} 
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                  />
                  <input
                    ref={searchInputRef}
                    type="text"
                    placeholder="Search currencies..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="
                      w-full pl-10 pr-4 py-2 
                      border border-gray-200 rounded-md
                      focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                      text-sm
                    "
                  />
                </div>
              </div>
            )}

            {/* Currency List */}
            <div className="max-h-60 overflow-y-auto">
              {filteredCurrencies.length === 0 ? (
                <div className="p-4 text-center text-gray-500">
                  {currenciesLoading ? (
                    <div className="flex items-center justify-center space-x-2">
                      <FontAwesomeIcon icon={faSpinner} className="animate-spin" />
                      <span>Loading currencies...</span>
                    </div>
                  ) : searchTerm ? (
                    'No currencies found'
                  ) : (
                    'No currencies available'
                  )}
                </div>
              ) : (
                filteredCurrencies.map((currency) => (
                  <motion.button
                    key={currency.code}
                    whileHover={{ backgroundColor: '#f3f4f6' }}
                    onClick={() => handleCurrencySelect(currency)}
                    className={`
                      w-full flex items-center justify-between p-3
                      hover:bg-gray-50 transition-colors
                      ${selectedCurrency === currency.code ? 'bg-blue-50' : ''}
                    `}
                  >
                    <div className="flex items-center space-x-3">
                      <span className="text-xl">{getCurrencyFlag(currency.code)}</span>
                      <div className="text-left">
                        <div className="font-medium text-gray-900">
                          {currency.code}
                          {currency.symbol && currency.symbol !== currency.code && (
                            <span className="ml-1 text-gray-500">({currency.symbol})</span>
                          )}
                        </div>
                        {currency.name && currency.name !== currency.code && (
                          <div className="text-sm text-gray-500">{currency.name}</div>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      {/* Conversion Rate */}
                      {showConversionRates && conversionRates[currency.code] && (
                        <div className="text-right">
                          <div className="text-sm text-gray-600">
                            <FontAwesomeIcon icon={faExchangeAlt} className="mr-1" />
                            {conversionRates[currency.code].toFixed(4)}
                          </div>
                          {ratesLoading && (
                            <FontAwesomeIcon icon={faSpinner} className="animate-spin text-xs text-gray-400" />
                          )}
                        </div>
                      )}

                      {/* Selected Indicator */}
                      {selectedCurrency === currency.code && (
                        <FontAwesomeIcon icon={faCheck} className="text-blue-600" />
                      )}
                    </div>
                  </motion.button>
                ))
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default CurrencySelector;
