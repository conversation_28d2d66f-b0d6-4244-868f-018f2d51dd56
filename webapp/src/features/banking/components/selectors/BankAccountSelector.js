import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faChevronDown,
  faSearch,
  faCheck,
  faPlus,
  faSpinner,
  faBank,
  faExclamationTriangle
} from '@fortawesome/free-solid-svg-icons';
import { useBankAccounts } from '../../contexts';
import { formatBankAccountForDisplay, getBankAccountStatus, maskAccountNumber } from '../../utils/bankingUtils';
import malaysianBankService from '../../services/MalaysianBankService';

/**
 * BankAccountSelector Component
 *
 * Enhanced bank account selector with search, filtering, and add account functionality.
 * Supports verified accounts only, primary account highlighting, and beautiful animations.
 */
const BankAccountSelector = ({
  selectedAccountId,
  onAccountChange,
  onAddAccount,
  verifiedOnly = true,
  showAddButton = true,
  showSearch = true,
  disabled = false,
  placeholder = "Select bank account",
  size = 'medium',
  className = '',
  error = null
}) => {
  const {
    accounts,
    accountsLoading,
    loadBankAccounts,
    getVerifiedAccounts
  } = useBankAccounts();

  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  const dropdownRef = useRef(null);
  const searchInputRef = useRef(null);

  // Get accounts based on filter
  const availableAccounts = verifiedOnly ? getVerifiedAccounts() : accounts;

  // Load accounts on mount
  useEffect(() => {
    if (availableAccounts.length === 0 && !accountsLoading) {
      loadBankAccounts();
    }
  }, [availableAccounts.length, accountsLoading, loadBankAccounts]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && showSearch && searchInputRef.current) {
      setTimeout(() => searchInputRef.current?.focus(), 100);
    }
  }, [isOpen, showSearch]);

  // Format accounts for display
  const formattedAccounts = availableAccounts.map(account => formatBankAccountForDisplay(account));

  const filteredAccounts = formattedAccounts.filter(account => {
    if (!searchTerm) return true;

    const searchLower = searchTerm.toLowerCase();
    return (
      account.bankName?.toLowerCase().includes(searchLower) ||
      account.bankCode?.toLowerCase().includes(searchLower) ||
      account.account_number?.includes(searchTerm) ||
      account.account_holder_name?.toLowerCase().includes(searchLower) ||
      account.maskedAccountNumber?.includes(searchTerm)
    );
  });

  const handleAccountSelect = (account) => {
    onAccountChange(account);
    setIsOpen(false);
    setSearchTerm('');
  };

  const handleAddAccount = () => {
    setIsOpen(false);
    if (onAddAccount) {
      onAddAccount();
    }
  };

  const selectedAccount = formattedAccounts.find(acc => acc.id === selectedAccountId);

  const getSizeClasses = () => {
    const sizes = {
      small: {
        button: 'px-3 py-2 text-sm',
        dropdown: 'text-sm',
        icon: 'text-sm'
      },
      medium: {
        button: 'px-4 py-3 text-base',
        dropdown: 'text-base',
        icon: 'text-base'
      },
      large: {
        button: 'px-6 py-4 text-lg',
        dropdown: 'text-lg',
        icon: 'text-lg'
      }
    };
    return sizes[size] || sizes.medium;
  };

  const sizeClasses = getSizeClasses();

  return (
    <div className={`bank-account-selector relative ${className}`} ref={dropdownRef}>
      {/* Selector Button */}
      <button
        type="button"
        onClick={() => !disabled && setIsOpen(!isOpen)}
        disabled={disabled}
        className={`
          w-full flex items-center justify-between
          border border-gray-300 rounded-lg
          bg-white hover:bg-gray-50
          focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
          transition-all duration-200
          ${sizeClasses.button}
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
          ${error ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : ''}
          ${isOpen ? 'ring-2 ring-blue-500 border-blue-500' : ''}
        `}
      >
        <div className="flex items-center space-x-3">
          {accountsLoading ? (
            <FontAwesomeIcon icon={faSpinner} className="animate-spin text-gray-400" />
          ) : selectedAccount ? (
            <>
              <div className="w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center">
                <FontAwesomeIcon icon={faBank} className="text-white text-xs" />
              </div>
              <div className="text-left">
                <div className="font-medium text-gray-900 flex items-center">
                  {selectedAccount.bankName}
                  {selectedAccount.is_primary && (
                    <span className="ml-2 bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">
                      Primary
                    </span>
                  )}
                </div>
                <div className="text-sm text-gray-500">
                  {selectedAccount.maskedAccountNumber}
                </div>
              </div>
            </>
          ) : (
            <>
              <FontAwesomeIcon icon={faBank} className="text-gray-400" />
              <span className="text-gray-500">{placeholder}</span>
            </>
          )}
        </div>

        <FontAwesomeIcon
          icon={faChevronDown}
          className={`
            text-gray-400 transition-transform duration-200
            ${isOpen ? 'rotate-180' : ''}
          `}
        />
      </button>

      {/* Error Message */}
      {error && (
        <p className="mt-1 text-sm text-red-600">{error}</p>
      )}

      {/* Dropdown */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className={`
              absolute z-50 w-full mt-2
              bg-white border border-gray-200 rounded-lg shadow-lg
              backdrop-blur-sm bg-opacity-95
              max-h-80 overflow-hidden
              ${sizeClasses.dropdown}
            `}
          >
            {/* Search Input */}
            {showSearch && availableAccounts.length > 3 && (
              <div className="p-3 border-b border-gray-100">
                <div className="relative">
                  <FontAwesomeIcon
                    icon={faSearch}
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                  />
                  <input
                    ref={searchInputRef}
                    type="text"
                    placeholder="Search accounts..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="
                      w-full pl-10 pr-4 py-2
                      border border-gray-200 rounded-md
                      focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                      text-sm
                    "
                  />
                </div>
              </div>
            )}

            {/* Account List */}
            <div className="max-h-60 overflow-y-auto">
              {/* Add Account Button */}
              {showAddButton && (
                <motion.button
                  whileHover={{ backgroundColor: '#f3f4f6' }}
                  onClick={handleAddAccount}
                  className="w-full flex items-center p-3 hover:bg-gray-50 transition-colors border-b border-gray-100"
                >
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                    <FontAwesomeIcon icon={faPlus} className="text-blue-600 text-sm" />
                  </div>
                  <span className="font-medium text-blue-600">Add New Bank Account</span>
                </motion.button>
              )}

              {/* Account Options */}
              {filteredAccounts.length === 0 ? (
                <div className="p-4 text-center text-gray-500">
                  {accountsLoading ? (
                    <div className="flex items-center justify-center space-x-2">
                      <FontAwesomeIcon icon={faSpinner} className="animate-spin" />
                      <span>Loading accounts...</span>
                    </div>
                  ) : searchTerm ? (
                    'No accounts found'
                  ) : verifiedOnly ? (
                    <div className="space-y-2">
                      <FontAwesomeIcon icon={faExclamationTriangle} className="text-yellow-500" />
                      <p>No verified accounts found</p>
                      <p className="text-xs">Add and verify a bank account to continue</p>
                    </div>
                  ) : (
                    'No bank accounts available'
                  )}
                </div>
              ) : (
                filteredAccounts.map((account) => (
                  <motion.button
                    key={account.id}
                    whileHover={{ backgroundColor: '#f3f4f6' }}
                    onClick={() => handleAccountSelect(account)}
                    className={`
                      w-full flex items-center justify-between p-3
                      hover:bg-gray-50 transition-colors
                      ${selectedAccountId === account.id ? 'bg-blue-50' : ''}
                    `}
                  >
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center">
                        <FontAwesomeIcon icon={faBank} className="text-white text-sm" />
                      </div>
                      <div className="text-left">
                        <div className="font-medium text-gray-900 flex items-center">
                          {account.bankName}
                          {account.is_primary && (
                            <span className="ml-2 bg-yellow-100 text-yellow-800 text-xs font-medium px-2 py-1 rounded-full">
                              Primary
                            </span>
                          )}
                        </div>
                        <div className="text-sm text-gray-500">
                          {account.maskedAccountNumber}
                        </div>
                        <div className="text-sm text-gray-400">
                          {account.account_holder_name}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      {/* Verification Status */}
                      {account.is_verified ? (
                        <span className="bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded-full">
                          Verified
                        </span>
                      ) : (
                        <span className="bg-yellow-100 text-yellow-800 text-xs font-medium px-2 py-1 rounded-full">
                          Pending
                        </span>
                      )}

                      {/* Selected Indicator */}
                      {selectedAccountId === account.id && (
                        <FontAwesomeIcon icon={faCheck} className="text-blue-600" />
                      )}
                    </div>
                  </motion.button>
                ))
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default BankAccountSelector;
