# Bank Account Management System - Integration Guide

## 🏦 Overview

This document provides comprehensive integration instructions for the enhanced Bank Account Management system that aligns with the backend API structure.

## 📋 Backend API Alignment

### API Endpoints
- **List Accounts**: `GET /user/bank-accounts` → `UserBankAccountController::index()`
- **Add Account**: `POST /user/bank-accounts` → `UserBankAccountController::store()`
- **Edit Account**: `PUT /user/bank-accounts/{id}` → `UserBankAccountController::update()`
- **Delete Account**: `DELETE /user/bank-accounts/{id}` → `UserBankAccountController::delete()`
- **Set Primary**: `POST /user/bank-accounts/{id}/set-primary` → `UserBankAccountController::setPrimary()`

### Expected Request/Response Format

#### Add Account Request
```json
{
  "malaysian_bank_id": 1,
  "account_number": "*************",
  "account_holder_name": "JOHN DOE",
  "is_primary": false
}
```

#### Response Format
```json
{
  "success": true,
  "message": "Bank account added successfully",
  "data": {
    "bank_account": {
      "id": 1,
      "malaysian_bank_id": 1,
      "account_number": "*************",
      "account_holder_name": "JOHN DOE",
      "is_primary": false,
      "is_verified": false,
      "verification_status": "pending",
      "created_at": "2024-01-01T00:00:00.000000Z",
      "updated_at": "2024-01-01T00:00:00.000000Z",
      "malaysian_bank": {
        "id": 1,
        "name": "Maybank",
        "code": "MBB",
        "swift_code": "MBBEMYKL"
      }
    }
  }
}
```

## 🔧 Integration Steps

### 1. Update Wallet.js to use Enhanced Components

```javascript
// Import enhanced hook
import { useBankAccountsEnhanced } from '../features/banking/hooks/useBankAccountsEnhanced';

// In your component
const {
  accounts,
  addBankAccount,
  updateBankAccount,
  deleteBankAccount,
  setPrimaryAccount,
  loading,
  errors
} = useBankAccountsEnhanced();

// Handle bank account operations
const handleBankAccountSuccess = async () => {
  setShowBankAccountModal(false);
  // Refresh wallet data if needed
  await refreshWallet();
};
```

### 2. Update BankAccountModal

```javascript
// Use enhanced form component
import BankAccountFormEnhanced from '../../../banking/components/forms/BankAccountFormEnhanced';

// In modal component
<BankAccountFormEnhanced
  mode={mode}
  account={account}
  onSuccess={handleFormSuccess}
  onCancel={handleFormCancel}
  size="large"
/>
```

### 3. Backend Controller Requirements

Ensure your `UserBankAccountController` handles these validation rules:

```php
// Validation rules for store/update
$rules = [
    'malaysian_bank_id' => 'required|integer|exists:malaysian_banks,id',
    'account_number' => 'required|string|min:8|max:20|regex:/^[D]?[0-9]+$/',
    'account_holder_name' => 'required|string|min:2|max:100|regex:/^[A-Za-z\s\'-]+$/',
    'is_primary' => 'boolean'
];
```

## 🔍 DuitNow Integration

### Account Number Format
- **Regular accounts**: Numeric only (8-20 digits)
- **DuitNow accounts**: Prefix 'D' + 10-15 digits (e.g., "D*************")

### Validation Logic
```javascript
// Frontend validation
const isDuitNow = accountNumber.startsWith('D');
if (isDuitNow) {
  const duitNowPattern = /^D\d{10,15}$/;
  return duitNowPattern.test(accountNumber);
}
```

### Backend Processing
```php
// In WithdrawalService.php
if (str_starts_with($account->account_number, 'D')) {
    // Handle DuitNow withdrawal
    return $this->processDuitNowWithdrawal($account, $amount);
}
```

## 🛡️ Bank Account Verification Flow

### Suggested Implementation

#### 1. Frontend Components
```javascript
// Add verification status display
const VerificationBadge = ({ status }) => {
  const statusConfig = {
    'pending': { color: 'yellow', icon: 'clock', text: 'Pending' },
    'verified': { color: 'green', icon: 'check', text: 'Verified' },
    'failed': { color: 'red', icon: 'times', text: 'Failed' }
  };
  
  return (
    <span className={`badge badge-${statusConfig[status].color}`}>
      <FontAwesomeIcon icon={statusConfig[status].icon} />
      {statusConfig[status].text}
    </span>
  );
};
```

#### 2. Backend API Endpoints (Suggested)
```php
// Add to api.php
Route::post('/user/bank-accounts/{id}/request-verification', [UserBankAccountController::class, 'requestVerification']);
Route::get('/user/bank-accounts/{id}/verification-status', [UserBankAccountController::class, 'getVerificationStatus']);
```

#### 3. Verification Process
1. **Micro-deposit verification**: Send small amounts (RM 0.01-0.99)
2. **User confirms amounts**: User enters received amounts
3. **Account verified**: Mark account as verified for withdrawals

## 📱 UI/UX Enhancements

### Loading States
```javascript
// Enhanced loading indicators
{isOperationLoading('add') && (
  <div className="loading-overlay">
    <Spinner />
    <p>Adding bank account...</p>
  </div>
)}
```

### Error Handling
```javascript
// Comprehensive error display
{getOperationError('add') && (
  <ErrorAlert 
    message={getOperationError('add')}
    onDismiss={() => clearError('operations', 'add')}
  />
)}
```

### Success Feedback
```javascript
// Toast notifications
import { toast } from 'react-hot-toast';

const handleSuccess = () => {
  toast.success('Bank account added successfully!', {
    duration: 4000,
    position: 'top-right'
  });
};
```

## 🔒 Security Considerations

### 1. Data Encryption
- Account numbers should be encrypted in database
- Use Laravel's encryption for sensitive data

### 2. Validation
- Server-side validation is mandatory
- Client-side validation for UX only

### 3. Rate Limiting
- Implement rate limiting for bank account operations
- Prevent abuse of verification requests

## 🧪 Testing Checklist

### Frontend Tests
- [ ] Form validation works correctly
- [ ] API calls are made with correct data
- [ ] Error states display properly
- [ ] Loading states work
- [ ] Modal opens/closes correctly

### Backend Tests
- [ ] All CRUD operations work
- [ ] Validation rules are enforced
- [ ] Primary account logic works
- [ ] DuitNow accounts are handled
- [ ] Error responses are consistent

### Integration Tests
- [ ] End-to-end bank account flow
- [ ] Withdrawal integration works
- [ ] Verification flow (when implemented)

## 📊 Monitoring & Analytics

### Key Metrics to Track
- Bank account addition success rate
- Verification completion rate
- Most popular banks
- Error frequency by type

### Logging
```javascript
// Enhanced error logging
console.error('Bank account operation failed:', {
  operation: 'add',
  error: error.message,
  bankId: formData.malaysian_bank_id,
  timestamp: new Date().toISOString()
});
```

## 🚀 Deployment Notes

### Environment Variables
```env
# Add to .env
BANK_VERIFICATION_ENABLED=true
DUITNOW_ENABLED=true
MICRO_DEPOSIT_MIN=0.01
MICRO_DEPOSIT_MAX=0.99
```

### Database Migrations
Ensure these fields exist in `user_bank_accounts` table:
- `malaysian_bank_id` (foreign key)
- `account_number` (encrypted)
- `account_holder_name`
- `is_primary` (boolean)
- `is_verified` (boolean)
- `verification_status` (enum)
- `verification_requested_at` (timestamp)

## 📞 Support & Troubleshooting

### Common Issues
1. **Validation errors**: Check backend validation rules match frontend
2. **DuitNow not working**: Verify bank supports DuitNow
3. **Primary account issues**: Ensure only one primary per user
4. **Modal not opening**: Check callback props are passed correctly

### Debug Mode
```javascript
// Enable debug logging
const DEBUG_BANK_ACCOUNTS = process.env.NODE_ENV === 'development';

if (DEBUG_BANK_ACCOUNTS) {
  console.log('Bank account operation:', { operation, data, result });
}
```
