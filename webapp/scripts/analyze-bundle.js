const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Set environment variable to enable bundle analyzer
process.env.ANALYZE = 'true';

// Run webpack build with analyzer
console.log('Building and analyzing bundle...');
execSync('npm run build', { stdio: 'inherit' });

// Read the stats file
const statsPath = path.join(__dirname, '../build/stats.json');
if (fs.existsSync(statsPath)) {
  const stats = JSON.parse(fs.readFileSync(statsPath, 'utf8'));
  
  // Analyze bundle sizes
  const bundles = stats.assets
    .filter(asset => asset.name.endsWith('.js'))
    .map(asset => ({
      name: asset.name,
      size: asset.size,
      sizeInMB: (asset.size / 1024 / 1024).toFixed(2)
    }))
    .sort((a, b) => b.size - a.size);

  console.log('\nBundle Analysis:');
  console.log('===============');
  bundles.forEach(bundle => {
    console.log(`${bundle.name}: ${bundle.sizeInMB}MB`);
  });

  // Check for large dependencies
  const modules = stats.modules
    .filter(module => module.size > 100000) // Modules larger than 100KB
    .map(module => ({
      name: module.name,
      size: (module.size / 1024 / 1024).toFixed(2)
    }))
    .sort((a, b) => b.size - a.size);

  console.log('\nLarge Dependencies:');
  console.log('==================');
  modules.forEach(module => {
    console.log(`${module.name}: ${module.size}MB`);
  });
} else {
  console.error('Stats file not found. Make sure the build completed successfully.');
} 