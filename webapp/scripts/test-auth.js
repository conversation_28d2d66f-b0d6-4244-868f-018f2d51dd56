/**
 * Command-line script to test authentication
 * 
 * Run with: node scripts/test-auth.js
 */

const fetch = require('node-fetch');
const chalk = require('chalk');

// Base URL for API requests
const API_BASE_URL = process.env.API_URL || 'http://localhost:8000/api';

// Test login with valid credentials
async function testLoginWithValidCredentials() {
  console.log(chalk.blue('🧪 Testing login with valid credentials...'));
  
  try {
    const response = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        mobile_number: '1234567890',
        country_code: '+60',
        password: 'password123',
      }),
    });
    
    const data = await response.json();
    console.log(chalk.gray('📊 Login response:'), data);
    
    if (data.success) {
      console.log(chalk.green('✅ Login successful!'));
      return true;
    } else {
      console.log(chalk.red('❌ Login failed:'), data.error || 'Unknown error');
      return false;
    }
  } catch (error) {
    console.error(chalk.red('❌ Error during login test:'), error);
    return false;
  }
}

// Test login with invalid credentials
async function testLoginWithInvalidCredentials() {
  console.log(chalk.blue('🧪 Testing login with invalid credentials...'));
  
  try {
    const response = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        mobile_number: '1234567890',
        country_code: '+60',
        password: 'wrongpassword',
      }),
    });
    
    const data = await response.json();
    console.log(chalk.gray('📊 Login response:'), data);
    
    if (!data.success) {
      console.log(chalk.green('✅ Login correctly failed with invalid credentials'));
      return true;
    } else {
      console.log(chalk.red('❌ Login unexpectedly succeeded with invalid credentials'));
      return false;
    }
  } catch (error) {
    console.error(chalk.red('❌ Error during invalid login test:'), error);
    return false;
  }
}

// Test OTP request
async function testOtpRequest() {
  console.log(chalk.blue('🧪 Testing OTP request...'));
  
  try {
    const response = await fetch(`${API_BASE_URL}/auth/request-otp`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        mobile_number: '1234567890',
        country_code: '+60',
        purpose: 'registration',
      }),
    });
    
    const data = await response.json();
    console.log(chalk.gray('📊 OTP request response:'), data);
    
    if (data.success) {
      console.log(chalk.green('✅ OTP request successful!'));
      return true;
    } else {
      console.log(chalk.red('❌ OTP request failed:'), data.error || 'Unknown error');
      return false;
    }
  } catch (error) {
    console.error(chalk.red('❌ Error during OTP request test:'), error);
    return false;
  }
}

// Run all tests
async function runAllTests() {
  console.log(chalk.yellow('🚀 Starting authentication tests...'));
  
  const results = {
    loginValid: await testLoginWithValidCredentials(),
    loginInvalid: await testLoginWithInvalidCredentials(),
    otpRequest: await testOtpRequest(),
  };
  
  console.log(chalk.yellow('📋 Test Results:'));
  console.table(results);
  
  const allPassed = Object.values(results).every(result => result === true);
  
  if (allPassed) {
    console.log(chalk.green('🎉 All tests passed!'));
  } else {
    console.log(chalk.yellow('⚠️ Some tests failed. Check the results above.'));
  }
}

// Run the tests
runAllTests().catch(error => {
  console.error(chalk.red('❌ Error running tests:'), error);
});
