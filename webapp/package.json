{"name": "webapp", "version": "0.1.0", "private": true, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@headlessui/react": "^2.2.2", "@heroicons/react": "^2.1.1", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-hover-card": "^1.1.13", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-slot": "^1.2.2", "@shadcn/ui": "^0.0.4", "@tailwindcss/forms": "^0.5.10", "@tanstack/react-virtual": "^3.0.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "axios": "^1.9.0", "browser-image-compression": "^2.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^2.30.0", "firebase": "^10.5.0", "formik": "^2.4.6", "framer-motion": "^12.9.4", "i18next": "^25.2.0", "i18next-browser-languagedetector": "^8.1.0", "i18next-http-backend": "^3.0.2", "lodash": "^4.17.21", "lucide-react": "^0.503.0", "luxon": "^3.6.1", "postcss-normalize": "^10.0.1", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-datepicker": "^8.3.0", "react-dom": "^18.3.1", "react-hot-toast": "^2.5.2", "react-i18next": "^15.5.1", "react-icons": "^4.12.0", "react-intersection-observer": "^9.16.0", "react-router-dom": "^7.5.2", "react-scripts": "5.0.1", "react-textarea-autosize": "^8.5.9", "react-toastify": "^11.0.5", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.2.0", "tailwind-scrollbar": "^4.0.2", "tailwindcss-animate": "^1.0.7", "typescript": "^4.9.5", "url": "^0.11.4", "web-vitals": "^4.2.4", "yup": "^1.6.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "test:auth": "node scripts/test-auth.js", "analyze": "node scripts/analyze-bundle.js", "analyze:webpack": "ANALYZE=true react-scripts build"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@tailwindcss/postcss": "^4.1.7", "autoprefixer": "^10.4.21", "chalk": "^4.1.2", "node-fetch": "^2.6.7", "postcss": "^8.5.3", "postcss-flexbugs-fixes": "^5.0.2", "postcss-loader": "^8.1.1", "postcss-preset-env": "^10.1.6", "tailwindcss": "^4.1.7"}}