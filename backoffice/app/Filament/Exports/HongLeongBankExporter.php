<?php

namespace App\Filament\Exports;

use App\Models\WithdrawalRequest;
use Filament\Actions\Exports\ExportColumn;
use Filament\Actions\Exports\Exporter;
use Illuminate\Database\Eloquent\Builder;
use OpenSpout\Common\Entity\Style\CellAlignment;
use OpenSpout\Common\Entity\Style\CellVerticalAlignment;
use OpenSpout\Common\Entity\Style\Color;
use OpenSpout\Common\Entity\Style\Style;
use OpenSpout\Common\Entity\Style\Border;
use OpenSpout\Common\Entity\Style\BorderPart;

class HongLeongBankExporter extends Exporter
{
    protected static ?string $model = WithdrawalRequest::class;

    public static function getColumns(): array
    {
        return [
            ExportColumn::make('paymentMode.payment_mode_code')
                ->label('Payment Mode')
                ->state(function ($record) {
                    $bankCode = $record->bankAccount && $record->bankAccount->bank ? strtoupper($record->bankAccount->bank->code) : '';
                    if ($bankCode === 'HLBB') {
                        return 'FT';
                    }
                    return $record->paymentMode ? $record->paymentMode->payment_mode_code : '';
                }),
            ExportColumn::make('bankAccount.bank.code')
                ->label('Beneficiary Bank Code')
                ->state(fn($record) => $record->bankAccount && $record->bankAccount->bank ? $record->bankAccount->bank->code : ''),
            ExportColumn::make('bankAccount.account_number')
                ->label('Beneficiary Account Number')
                ->state(function ($record) {
                    $accountNumber = $record->bankAccount?->account_number ?? '';
                    $paymentModeCode = strtoupper($record->paymentMode?->payment_mode_code ?? '');
                    $bankCode = strtoupper($record->bankAccount?->bank?->code ?? '');

                    // Debug logs (remove after testing)
                    // logger()->info("Payment Mode: $paymentModeCode | Bank Code: $bankCode | Account: $accountNumber");

                    if (
                        $paymentModeCode === 'DUITNW' &&
                        $bankCode !== 'MBNO'
                    ) {
                        return 'D' . $accountNumber;
                    }

                    return $accountNumber;
                }),
     
            ExportColumn::make('bankAccount.account_holder_name')
                ->label('Beneficiary Name')
                ->state(fn($record) => $record->bankAccount ? $record->bankAccount->account_holder_name : ''),
            ExportColumn::make('fiat_currency_code')
                ->label('Currency'),    
            ExportColumn::make('fiat_amount')
                ->label('Amount'),
            ExportColumn::make('recipient_reference')
                ->label('Recipient Reference'),
            ExportColumn::make('other_payment_details')
                ->label('Other Payment Details'),
            ExportColumn::make('id_validation')
                ->label('ID Validation')
                ->state(fn($record) => $record->id_validation ? 'Y' : 'N'),    
            ExportColumn::make('validationType.validation_type')
                ->label('Validation ID Type')
                ->state(fn($record) => $record->validationType ? $record->validationType->validation_type : ''),
            ExportColumn::make('id_validation_value')
                ->label('Validation ID Value'),
            ExportColumn::make('transaction_type')
                ->label('Transaction Type'),
            ExportColumn::make('transaction_code')
                ->label('Transaction Code'),    
            ExportColumn::make('purpose_of_transfer')
                ->label('Purpose of Transfer'),
            ExportColumn::make('user.email')
                ->label('Beneficiary Email Address')
                ->state(fn($record) => $record->user ? $record->user->email : ''),
            // ExportColumn::make('status')
            //     ->label('Status')
            //     ->state(fn($record) => ucfirst($record->status)),
            // ExportColumn::make('created_at')
            //     ->label('Requested At')
            //     ->state(fn($record) => $record->created_at ? date('Y-m-d H:i:s', strtotime($record->created_at)) : ''),
        ];
    }

    public function getXlsxHeaderCellStyle(): ?Style
    {
        $border = new Border(
            new BorderPart(Border::BOTTOM, Color::BLACK, Border::WIDTH_THIN),
            new BorderPart(Border::TOP, Color::BLACK, Border::WIDTH_THIN),
            new BorderPart(Border::LEFT, Color::BLACK, Border::WIDTH_THIN),
            new BorderPart(Border::RIGHT, Color::BLACK, Border::WIDTH_THIN),
        );

        return (new Style())
            ->setFontBold()
            ->setFontSize(12)
            ->setFontColor(Color::RED)
            ->setBackgroundColor(Color::rgb(198, 217, 241)) // Light blue
            ->setCellAlignment(CellAlignment::CENTER)
            ->setCellVerticalAlignment(CellVerticalAlignment::CENTER)
            ->setBorder($border);
    }

    public function getFileName(\Filament\Actions\Exports\Models\Export $export): string
    {
        return 'hong-leong-bank-export-' . date('Y-m-d');
    }

    protected function getBuilder(): Builder
    {
        return WithdrawalRequest::query();
    }
    
    public static function getQueue(): ?string
    {
        return null; // Use default queue
    }

    public static function getCompletedNotificationBody(\Filament\Actions\Exports\Models\Export $export): string
    {
        return 'Your Hong Leong Bank export has been completed and is ready to download.';
    }
}
