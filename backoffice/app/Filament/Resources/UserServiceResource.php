<?php

namespace App\Filament\Resources;

use App\Filament\Clusters\UserServices;
use App\Filament\Resources\UserServiceResource\Pages;
use App\Filament\Resources\UserServiceResource\RelationManagers;
use App\Models\UserService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Tables\Actions\Action;
use Filament\Navigation\NavigationItem;
use Filament\Notifications\Notification;

class UserServiceResource extends Resource
{
    protected static ?string $model = UserService::class;

    protected static ?string $navigationIcon = 'heroicon-o-briefcase';
    protected static ?string $navigationGroup = 'Service Management';
    protected static ?string $navigationLabel = 'User Services';
    // protected static ?string $cluster = UserServices::class;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Service Information')
                    ->schema([
                        Forms\Components\TextInput::make('title')
                            ->required()
                            ->maxLength(255)
                            ->disabled(),
                        Forms\Components\Textarea::make('description')
                            ->maxLength(65535)
                            ->disabled(),
                        Forms\Components\Select::make('status')
                            ->options([
                                'pending' => 'Pending',
                                'approved' => 'Approved',
                                'rejected' => 'Rejected',
                                'disabled' => 'Disabled',
                            ])
                            ->disabled(),
                        Forms\Components\Textarea::make('rejection_reason')
                            ->maxLength(65535)
                            ->disabled()
                            ->visible(fn($record) => $record && $record->isRejected()),
                        Forms\Components\Toggle::make('is_active')
                            ->disabled(),
                    ])->columns(2),

                Forms\Components\Section::make('Related Information')
                    ->schema([
                        Forms\Components\Select::make('user_id')
                            ->relationship('user', 'name')
                            ->disabled(),
                        Forms\Components\Select::make('service_category_id')
                            ->relationship('serviceCategory', 'name')
                            ->disabled(),
                        Forms\Components\Select::make('service_type_id')
                            ->relationship('serviceType', 'name')
                            ->disabled()
                            ->visible(fn($record) => $record && $record->service_category_id === 1),
                        Forms\Components\TextInput::make('service_type_title')
                            ->disabled()
                            ->visible(fn($record) => $record && $record->service_category_id === 2),
                        Forms\Components\Textarea::make('service_type_description')
                            ->disabled()
                            ->visible(fn($record) => $record && $record->service_category_id === 2),
                        Forms\Components\TextInput::make('price')
                            ->disabled()
                            ->visible(fn($record) => $record && $record->service_category_id === 2),
                    ])->columns(3),

                Forms\Components\Section::make('Service Elements')
                    ->schema([
                        Forms\Components\KeyValue::make('service_elements')
                            ->disabled(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->sortable()
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('user.name')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('title')
                    ->sortable()
                    ->searchable()
                    ->limit(30)
                    ->tooltip(fn($record) => $record->title),
                Tables\Columns\TextColumn::make('serviceCategory.name')
                    ->sortable()
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('serviceType.name')
                    ->sortable()
                    ->searchable()
                    ->visible(fn($record) => $record && $record->service_category_id === 1),
                Tables\Columns\TextColumn::make('service_type_title')
                    ->sortable()
                    ->searchable()
                    ->visible(fn($record) => $record && $record->service_category_id === 2),
                Tables\Columns\BadgeColumn::make('status')
                    ->colors([
                        'danger' => 'rejected',
                        'warning' => 'pending',
                        'success' => 'approved',
                        'gray' => 'disabled',
                    ]),
                Tables\Columns\IconColumn::make('is_active')
                    ->boolean(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->defaultGroup('serviceCategory.name')
            ->filters([
                Tables\Filters\SelectFilter::make('service_category_id')
                    ->relationship('serviceCategory', 'name')
                    ->preload()
                    ->label('Service Category'),
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'pending' => 'Pending',
                        'approved' => 'Approved',
                        'rejected' => 'Rejected',
                        'disabled' => 'Disabled',
                    ]),
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Active Status'),
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Action::make('approve')
                    ->label('Approve')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->visible(fn($record) => $record && $record->isPending())
                    ->action(function (UserService $record) {
                        try {
                            $revision = $record->revisions()
                                ->where('status', 'pending')
                                ->latest()
                                ->firstOrFail();

                            $record->update([
                                'title' => $revision->title,
                                'description' => $revision->description,
                                'service_elements' => $revision->service_elements,
                                'service_type_title' => $revision->service_type_title,
                                'service_type_description' => $revision->service_type_description,
                                'price' => $revision->price,
                                'status' => 'approved',
                                'is_active' => true,
                                'rejection_reason' => null,
                            ]);

                            $revision->update([
                                'status' => 'approved',
                            ]);

                            Notification::make()
                                ->title('Service approved successfully')
                                ->success()
                                ->send();
                        } catch (\Exception $e) {
                            Notification::make()
                                ->title('Failed to approve service')
                                ->body($e->getMessage())
                                ->danger()
                                ->send();
                        }
                    }),
                Action::make('reject')
                    ->label('Reject')
                    ->icon('heroicon-o-x-circle')
                    ->color('danger')
                    ->visible(fn($record) => $record && $record->isPending())
                    ->form([
                        Forms\Components\Textarea::make('rejection_reason')
                            ->label('Rejection Reason')
                            ->required(),
                    ])
                    ->action(function (UserService $record, array $data) {
                        try {
                            $revision = $record->revisions()
                                ->where('status', 'pending')
                                ->latest()
                                ->firstOrFail();

                            if ($record->revisions()->count() === 1) {
                                $record->update([
                                    'status' => 'rejected',
                                    'rejection_reason' => $data['rejection_reason'],
                                    'is_active' => false,
                                ]);
                            } else {
                                $record->update([
                                    'status' => 'rejected',
                                    'rejection_reason' => $data['rejection_reason'],
                                    'is_active' => false,
                                ]);
                            }

                            $revision->update([
                                'status' => 'rejected',
                                'rejection_reason' => $data['rejection_reason'],
                            ]);

                            Notification::make()
                                ->title('Service rejected successfully')
                                ->success()
                                ->send();
                        } catch (\Exception $e) {
                            Notification::make()
                                ->title('Failed to reject service')
                                ->body($e->getMessage())
                                ->danger()
                                ->send();
                        }
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\RevisionsRelationManager::class,
            RelationManagers\PricingOptionsRelationManager::class,
            RelationManagers\ServiceStylesRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUserServices::route('/'),
            //'approved' => Pages\ApprovedUserServices::route('/approved'),
            //'pending' => Pages\PendingUserServices::route('/pending'),
            //'rejected' => Pages\RejectedUserServices::route('/rejected'),
            'view' => Pages\ViewUserService::route('/{record}'),
        ];
    }


    // public static function getNavigationItems(): array
    // {
    //     try {
    //         $categories = \App\Models\ServiceCategory::where('is_active', true)
    //             ->orderBy('display_order')
    //             ->get();

    //         $items = [];

    //         foreach ($categories as $index => $category) {
    //             $items[] = NavigationItem::make($category->name . ' Services')
    //                 ->url(static::getUrl('index', [
    //                     'tableFilters[service_category_id][value]' => $category->id
    //                 ]))
    //                 ->icon('heroicon-o-squares-2x2')
    //                 ->sort($index + 1);
    //         }



    //         return $items;
    //     } catch (\Exception $e) {
    //         return [
    //             NavigationItem::make('Approved Services')
    //                 ->url(static::getUrl('approved'))
    //                 ->icon('heroicon-o-check')
    //                 ->sort(1),
    //             NavigationItem::make('Pending Services')
    //                 ->url(static::getUrl('pending'))
    //                 ->icon('heroicon-o-clock')
    //                 ->sort(2),
    //             NavigationItem::make('Rejected Services')
    //                 ->url(static::getUrl('rejected'))
    //                 ->icon('heroicon-o-x-circle')
    //                 ->sort(3),
    //         ];
    //     }
    // }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
