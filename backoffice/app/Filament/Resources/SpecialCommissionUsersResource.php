<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SpecialCommissionUsersResource\Pages;
use App\Models\BackendUser;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class SpecialCommissionUsersResource extends Resource
{
    protected static ?string $model = BackendUser::class;

    protected static ?string $navigationIcon = 'heroicon-o-user-group';

    protected static ?string $navigationGroup = 'Commission Module';

    protected static ?int $navigationSort = 16;

    protected static ?string $navigationLabel = 'Special Commission Users';

    // public static function canViewAny(): bool
    // {
    //     return auth()->check() && (
    //         auth()->user()->hasRole('superadmin') || 
    //         auth()->user()->can('view_any_special_commission_users')
    //     );
    // }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('User Information')
                    ->schema([
                        Forms\Components\TextInput::make('nickname')
                            ->label('Nickname')
                            ->disabled(),
                        Forms\Components\TextInput::make('mobile_number')
                            ->label('Mobile Number')
                            ->disabled(),
                    ]),
                Forms\Components\Section::make('Commission Settings')
                    ->schema([
                        Forms\Components\Toggle::make('special_commission')
                            ->label('Special Commission')
                            ->helperText('Enable to set a custom commission percentage for this user')
                            ->live()
                            ->afterStateUpdated(function ($state, callable $set) {
                                if (!$state) {
                                    $set('special_commission_percentage', null);
                                }
                            }),
                        Forms\Components\TextInput::make('special_commission_percentage')
                            ->label('Special Commission Percentage')
                            ->numeric()
                            ->minValue(0)
                            ->maxValue(100)
                            ->suffix('%')
                            ->helperText('Enter a percentage value between 0 and 100')
                            ->visible(fn($get) => $get('special_commission')),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->sortable(),
                Tables\Columns\TextColumn::make('nickname')
                    ->label('Nickname')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('mobile_number')
                    ->label('Mobile Number')
                    ->searchable(),
                Tables\Columns\IconColumn::make('special_commission')
                    ->label('Special Commission')
                    ->boolean()
                    ->sortable(),
                Tables\Columns\TextColumn::make('special_commission_percentage')
                    ->label('Commission Percentage')
                    ->suffix('%')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\Filter::make('special_commission')
                    ->query(fn(Builder $query): Builder => $query->where('special_commission', true))
                    ->label('Only Special Commission Users')
                    ->toggle(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    //
                ]),
            ])
            ->defaultSort('id', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSpecialCommissionUsers::route('/'),
            'edit' => Pages\EditSpecialCommissionUser::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->where('role', 'talent');
    }
}
