<?php

namespace App\Filament\Resources\BackendRaceResource\Pages;

use App\Filament\Resources\BackendRaceResource;
use App\Models\BackendRaceTranslation;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;

class EditBackendRace extends EditRecord
{
    protected static string $resource = BackendRaceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
    
    protected function mutateFormDataBeforeFill(array $data): array
    {
        $record = $this->getRecord();
        $translations = [];
        
        foreach ($record->translations as $translation) {
            $translations[$translation->locale] = [
                'locale' => $translation->locale,
                'name' => $translation->name,
                'description' => $translation->description,
            ];
        }
        
        $data['translations'] = $translations;
        
        return $data;
    }
    
    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        $record->update($data);
        
        if (isset($this->data['translations'])) {
            foreach ($this->data['translations'] as $locale => $translation) {
                if (!empty($translation['name'])) {
                    BackendRaceTranslation::updateOrCreate(
                        [
                            'race_id' => $record->id,
                            'locale' => $locale,
                        ],
                        [
                            'name' => $translation['name'],
                            'description' => $translation['description'] ?? null,
                        ]
                    );
                }
            }
        }
        
        return $record;
    }
}
