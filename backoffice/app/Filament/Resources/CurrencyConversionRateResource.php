<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CurrencyConversionRateResource\Pages;
use App\Models\BackendCurrencyConversionRate;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class CurrencyConversionRateResource extends Resource
{
    protected static ?string $model = BackendCurrencyConversionRate::class;
    protected static ?string $navigationIcon = 'heroicon-o-currency-dollar';
    protected static ?string $navigationLabel = 'Currency Conversion Rates';
    protected static ?string $navigationGroup = 'Payment Management';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('from_currency')
                    ->required()
                    ->options([
                        'credits' => 'Platform Credits',
                        'gift' => 'Gift Points',
                    ])
                    ->label('From Currency'),
                Forms\Components\TextInput::make('to_currency')
                    ->required()
                    ->maxLength(3)
                    ->label('To Currency (3-letter code)')
                    ->helperText('Enter a 3-letter currency code (e.g., MYR, USD, SGD)')
                    ->placeholder('MYR')
                    ->extraInputAttributes(['style' => 'text-transform: uppercase;'])
                    ->formatStateUsing(fn(?string $state): string => $state ? strtoupper($state) : '')
                    ->afterStateUpdated(fn(?string $state, callable $set) => $set('to_currency', $state ? strtoupper($state) : '')),
                Forms\Components\TextInput::make('rate')
                    ->required()
                    ->numeric()
                    ->minValue(0.000001)
                    ->step(0.000001)
                    ->label('Conversion Rate')
                    ->helperText('The rate at which the from_currency is converted to to_currency'),
                Forms\Components\Toggle::make('is_active')
                    ->default(true)
                    ->label('Active'),
            ])
            ->columns(2);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('from_currency')
                    ->sortable()
                    ->searchable()
                    ->label('From Currency')
                    ->formatStateUsing(fn(?string $state): string => match ($state) {
                        'credits' => 'Platform Credits',
                        'gift' => 'Gift Points',
                        null => '',
                        default => $state,
                    }),
                Tables\Columns\TextColumn::make('to_currency')
                    ->sortable()
                    ->searchable()
                    ->label('To Currency')
                    ->formatStateUsing(fn(?string $state): string => $state ? strtoupper($state) : ''),
                Tables\Columns\TextColumn::make('rate')
                    ->sortable()
                    ->label('Conversion Rate')
                    ->numeric(decimalPlaces: 6),
                Tables\Columns\BooleanColumn::make('is_active')
                    ->sortable()
                    ->label('Active')
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('from_currency')
                    ->options([
                        'credits' => 'Platform Credits',
                        'gift' => 'Gift Points',
                    ])
                    ->label('From Currency'),
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Active'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('toggle_active')
                    ->label(fn($record) => $record->is_active ? 'Deactivate' : 'Activate')
                    ->icon(fn($record) => $record->is_active ? 'heroicon-o-x-circle' : 'heroicon-o-check-circle')
                    ->color(fn($record) => $record->is_active ? 'danger' : 'success')
                    ->requiresConfirmation()
                    ->action(function ($record) {
                        $record->update(['is_active' => !$record->is_active]);
                    }),
                Tables\Actions\DeleteAction::make()
                    ->requiresConfirmation(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('activate')
                        ->label('Activate Selected')
                        ->icon('heroicon-o-check-circle')
                        ->color('success')
                        ->requiresConfirmation()
                        ->action(fn($records) => $records->each->update(['is_active' => true])),
                    Tables\Actions\BulkAction::make('deactivate')
                        ->label('Deactivate Selected')
                        ->icon('heroicon-o-x-circle')
                        ->color('danger')
                        ->requiresConfirmation()
                        ->action(fn($records) => $records->each->update(['is_active' => false])),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCurrencyConversionRates::route('/'),
            'create' => Pages\CreateCurrencyConversionRate::route('/create'),
            'edit' => Pages\EditCurrencyConversionRate::route('/{record}/edit'),
        ];
    }

    // public static function canViewAny(): bool
    // {
    //     return auth()->check() && (
    //         auth()->user()->hasRole('superadmin') || 
    //         auth()->user()->can('view_any_currency_conversion_rate')
    //     );
    // }
}
