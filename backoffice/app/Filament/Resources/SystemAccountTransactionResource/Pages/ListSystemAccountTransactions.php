<?php

namespace App\Filament\Resources\SystemAccountTransactionResource\Pages;

use App\Filament\Resources\SystemAccountTransactionResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use App\Filament\Widgets\SystemAccountBalanceWidget;


class ListSystemAccountTransactions extends ListRecords
{
    protected static string $resource = SystemAccountTransactionResource::class;

    protected function getHeaderWidgets(): array
    {
        return [
            SystemAccountBalanceWidget::class,
        ];
    }

    
}
