<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CarouselSlideResource\Pages;
use App\Models\CarouselSlide;
use App\Services\ImageProcessingService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;
use Illuminate\Validation\Rules\Unique;

class CarouselSlideResource extends Resource
{
    protected static ?string $model = CarouselSlide::class;
    protected static ?string $navigationIcon = 'heroicon-o-photo';
    protected static ?string $navigationGroup = 'Content Management';
    public static string $resourceType = 'carousel_slides';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Basic Information')
                    ->schema([
                        Forms\Components\TextInput::make('title')
                            ->maxLength(255),
                        Forms\Components\Grid::make()
                            ->schema([
                                Forms\Components\ViewField::make('current_image')
                                    ->label('Current Image')
                                    ->view('filament.components.current-image')
                                    ->visible(fn($record) => $record && !empty($record->media_files) && isset($record->media_files[0]['optimized'])),
                                Forms\Components\FileUpload::make('temp_media_files')
                                    ->label('Upload New Image')
                                    ->image()
                                    ->acceptedFileTypes(['image/png', 'image/jpeg', 'image/jpg'])
                                    ->maxSize(20480) // 20MB max
                                    ->storeFiles(false)
                                    ->preserveFilenames()
                                    ->helperText('Select an image to replace the current one. The file will be uploaded when you click save.'),
                            ])->columns(1),
                        Forms\Components\Textarea::make('content')
                            ->maxLength(500),
                        Forms\Components\TextInput::make('target_url')
                            ->label('Target URL')
                            ->url()
                            ->maxLength(2000),
                        Forms\Components\Toggle::make('is_clickable')
                            ->label('Is Clickable')
                            ->default(false),
                        Forms\Components\TextInput::make('display_order')
                            ->numeric()
                            ->minValue(0)
                            ->default(function () {
                                $latestRecord = CarouselSlide::orderBy('display_order', 'desc')->first();
                                return $latestRecord ? $latestRecord->display_order + 1 : 0;
                            })
                            ->label('Display Order')
                            ->unique(
                                ignoreRecord: true,
                                modifyRuleUsing: fn(Unique $rule) => $rule->whereNull('deleted_at')
                            ),
                        Forms\Components\Toggle::make('is_active')
                            ->label('Is Active')
                            ->default(true),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->sortable()
                    ->searchable()
                    ->extraHeaderAttributes(['style' => 'width: 200px;']),
                Tables\Columns\ImageColumn::make('media_files')
                    ->label('Image')
                    ->getStateUsing(function ($record) {
                        $cdnBaseUrlExternal = config('app.cdn_public_link'); // e.g., 'https://cdn.example.com/'
                        $cdnBaseUrlInternal = config('app.cdn_internal_link'); // Internal link (e.g., Docker network)
                        $placeholderUrl = config('app.cdn_default_image'); // Default placeholder image

                        if (empty($record->media_files) || !isset($record->media_files[0]['optimized'])) {
                            return $placeholderUrl;
                        }

                        $imageUrlInternal = $cdnBaseUrlInternal . $record->media_files[0]['optimized']; // For checking
                        $imageUrlExternal = $cdnBaseUrlExternal . $record->media_files[0]['optimized']; // For display

                        try {
                            $response = Http::timeout(5)
                                ->withHeaders(['User-Agent' => 'Mozilla/5.0'])
                                ->head($imageUrlInternal);

                            $state = $response->successful() ? $imageUrlExternal : $placeholderUrl;
                            Log::info('Image check result', [
                                'session_id' => session()->getId(),
                                'url' => $imageUrlInternal,
                                'status' => $response->status(),
                                'state' => $state,
                            ]);
                            return $state;
                        } catch (\Exception $e) {
                            Log::warning('Image check failed', [
                                'session_id' => session()->getId(),
                                'url' => $imageUrlInternal,
                                'error' => $e->getMessage(),
                                'state' => $placeholderUrl,
                            ]);
                            return $placeholderUrl;
                        }
                    })
                    ->url(fn($record) => !empty($record->media_files) && isset($record->media_files[0]['optimized']) ?
                        config('app.cdn_public_link') . $record->media_files[0]['optimized'] : null)
                    ->size(100)
                    ->extraHeaderAttributes(['style' => 'width: 120px;']),
                Tables\Columns\TextColumn::make('content')
                    ->limit(50)
                    ->searchable()
                    ->extraHeaderAttributes(['style' => 'width: 200px;']),
                Tables\Columns\TextColumn::make('target_url')
                    ->limit(30)
                    ->url(fn($record) => $record->target_url)
                    ->openUrlInNewTab()
                    ->extraHeaderAttributes(['style' => 'width: 200px;']),
                Tables\Columns\BooleanColumn::make('is_clickable')
                    ->sortable()
                    ->extraHeaderAttributes(['style' => 'width: 100px;']),
                Tables\Columns\TextColumn::make('display_order')
                    ->sortable()
                    ->extraHeaderAttributes(['style' => 'width: 100px;']),
                Tables\Columns\BooleanColumn::make('is_active')
                    ->sortable()
                    ->extraHeaderAttributes(['style' => 'width: 100px;']),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->action(function (array $data, CarouselSlide $record, ImageProcessingService $imageProcessingService) {
                        $mediaFiles = $record->media_files ?? [];

                        if (!empty($data['temp_media_files'])) {
                            $file = null;

                            if ($data['temp_media_files'] instanceof TemporaryUploadedFile) {
                                $file = $data['temp_media_files'];
                            } elseif (is_array($data['temp_media_files']) && isset($data['temp_media_files'][0]) && $data['temp_media_files'][0] instanceof TemporaryUploadedFile) {
                                $file = $data['temp_media_files'][0];
                            }

                            if ($file instanceof TemporaryUploadedFile) {
                                try {
                                    $metadata = $imageProcessingService->processImage(
                                        $file,
                                        0,
                                        static::$resourceType
                                    );

                                    if (!empty($record->media_files)) {
                                        foreach ($record->media_files as $oldFile) {
                                            if (isset($oldFile['original'])) {
                                                Storage::disk('cdn')->delete($oldFile['original']);
                                            }
                                            if (isset($oldFile['optimized'])) {
                                                Storage::disk('cdn')->delete($oldFile['optimized']);
                                            }
                                        }
                                    }

                                    $mediaFiles = [$metadata];
                                } catch (\Exception $e) {
                                    Log::error('Failed to process image', ['error' => $e->getMessage()]);
                                    throw new \Exception('Failed to process image: ' . $e->getMessage());
                                }
                            }
                        }

                        $record->update([
                            'title' => $data['title'],
                            'content' => $data['content'],
                            'media_files' => $mediaFiles,
                            'target_url' => $data['target_url'],
                            'is_clickable' => $data['is_clickable'],
                            'display_order' => $data['display_order'],
                            'is_active' => $data['is_active'],
                        ]);
                    }),
                Tables\Actions\DeleteAction::make()
                    ->requiresConfirmation()
                    ->action(function ($record) {
                        $record->delete();
                    }),
                Tables\Actions\Action::make('restore')
                    ->label('Restore')
                    ->icon('heroicon-o-arrow-path')
                    ->color('success')
                    ->visible(fn($record) => $record->trashed())
                    ->action(function ($record) {
                        $highestOrder = CarouselSlide::orderBy('display_order', 'desc')->first()?->display_order ?? 0;

                        $record->display_order = $highestOrder + 1;
                        $record->save();

                        $record->restore();
                    }),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->action(function (array $data, ImageProcessingService $imageProcessingService) {
                        $mediaFiles = [];

                        if (!empty($data['temp_media_files'])) {
                            $file = null;

                            if ($data['temp_media_files'] instanceof TemporaryUploadedFile) {
                                $file = $data['temp_media_files'];
                            } elseif (is_array($data['temp_media_files']) && isset($data['temp_media_files'][0]) && $data['temp_media_files'][0] instanceof TemporaryUploadedFile) {
                                $file = $data['temp_media_files'][0];
                            }

                            if ($file instanceof TemporaryUploadedFile) {
                                try {
                                    $metadata = $imageProcessingService->processImage(
                                        $file,
                                        0,
                                        static::$resourceType
                                    );
                                    $mediaFiles = [$metadata];
                                } catch (\Exception $e) {
                                    Log::error('Failed to process image', ['error' => $e->getMessage()]);
                                    throw new \Exception('Failed to process image: ' . $e->getMessage());
                                }
                            }
                        }

                        CarouselSlide::create([
                            'title' => $data['title'],
                            'content' => $data['content'],
                            'media_files' => $mediaFiles,
                            'target_url' => $data['target_url'],
                            'is_clickable' => $data['is_clickable'],
                            'display_order' => $data['display_order'],
                            'is_active' => $data['is_active'],
                        ]);
                    }),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCarouselSlides::route('/'),
        ];
    }

    // public static function canViewAny(): bool
    // {
    //     return auth()->check() && (
    //         auth()->user()->hasRole('superadmin') || 
    //         auth()->user()->can('view_any_carousel_slide')
    //     );
    // }
}
