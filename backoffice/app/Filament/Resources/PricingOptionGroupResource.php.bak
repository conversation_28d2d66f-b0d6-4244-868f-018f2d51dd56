<?php

namespace App\Filament\Resources;

use App\Filament\Resources\PricingOptionGroupResource\Pages;
use App\Filament\Resources\PricingOptionGroupResource\RelationManagers\ServiceStylesRelationManager;
use App\Filament\Resources\PricingOptionGroupResource\RelationManagers\PricingOptionTypesRelationManager;
use App\Filament\Resources\PricingOptionGroupResource\Widgets\PricingOptionTypesTable;
use App\Models\PricingOptionGroup;
use App\Models\PricingOptionType;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Table;
use Guava\FilamentModalRelationManagers\Actions\Table\RelationManagerAction;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Validation\Rules\Unique;

class PricingOptionGroupResource extends Resource
{
    protected static ?string $model = PricingOptionGroup::class;
    protected static ?string $navigationIcon = 'heroicon-o-tag';
    protected static ?string $navigationLabel = 'Pricing Options';
    protected static ?string $navigationGroup = 'Payment Management';
    protected static ?string $slug = 'pricing-option-groups';

    public static function boot()
    {
        parent::boot();
        logger('PricingOptionGroupResource booted');
        logger('Relations: ' . implode(', ', array_map(fn($class) => class_basename($class), static::getRelations())));
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Basic Information')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\Textarea::make('description')
                            ->maxLength(65535),
                        Forms\Components\Toggle::make('is_active')
                            ->default(true),
                        Forms\Components\TextInput::make('display_order')
                            ->numeric()
                            ->default(function () {
                                $latestRecord = PricingOptionGroup::orderBy('display_order', 'desc')->first();
                                return $latestRecord ? $latestRecord->display_order + 1 : 0;
                            })
                            ->minValue(0)
                            ->unique(
                                ignoreRecord: true,
                                modifyRuleUsing: fn(Unique $rule) => $rule->whereNull('deleted_at')
                            ),
                        Forms\Components\Select::make('serviceStyles')
                            ->label('Service Styles')
                            ->relationship('serviceStyles', 'name')
                            ->multiple()
                            ->preload()
                            ->searchable()
                            ->getOptionLabelFromRecordUsing(fn($record) => "{$record->name} ({$record->serviceType->name})")
                            ->loadingMessage('Loading service styles...')
                            ->optionsLimit(50),
                    ])->columns(2),

                Forms\Components\Section::make('Translations')
                    ->schema([
                        Forms\Components\Tabs::make('Translations')
                            ->tabs([
                                Forms\Components\Tabs\Tab::make('English')
                                    ->schema([
                                        Forms\Components\Hidden::make('translations.en.locale')
                                            ->default('en'),
                                        Forms\Components\TextInput::make('translations.en.name')
                                            ->label('Name (English)')
                                            ->required(),
                                        Forms\Components\Textarea::make('translations.en.description')
                                            ->label('Description (English)'),
                                    ]),
                                Forms\Components\Tabs\Tab::make('Bahasa Melayu')
                                    ->schema([
                                        Forms\Components\Hidden::make('translations.ms.locale')
                                            ->default('ms'),
                                        Forms\Components\TextInput::make('translations.ms.name')
                                            ->label('Name (Bahasa Melayu)')
                                            ->required(),
                                        Forms\Components\Textarea::make('translations.ms.description')
                                            ->label('Description (Bahasa Melayu)'),
                                    ]),
                                Forms\Components\Tabs\Tab::make('Chinese')
                                    ->schema([
                                        Forms\Components\Hidden::make('translations.zh.locale')
                                            ->default('zh'),
                                        Forms\Components\TextInput::make('translations.zh.name')
                                            ->label('Name (Chinese)')
                                            ->required(),
                                        Forms\Components\Textarea::make('translations.zh.description')
                                            ->label('Description (Chinese)'),
                                    ]),
                            ]),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {


        return $table
            //->query(PricingOptionGroup::with('pricingOptionTypes')->withCount('pricingOptionTypes'))
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('description')
                    ->limit(50)
                    ->searchable(),
                Tables\Columns\IconColumn::make('is_active')
                    ->boolean()
                    ->sortable(),
                Tables\Columns\TextColumn::make('display_order')
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('pricingOptionTypes_count')
                    ->label('Types')
                    ->getStateUsing(fn($record) => $record->pricingOptionTypes()->count())
                    ->tooltip(fn($record) => $record->pricingOptionTypes->pluck('name')->join(", "))
                    ->sortable()
                    ->extraAttributes(['class' => 'text-success'])

            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->requiresConfirmation()
                    ->before(function ($record) {
                        $record->update(['is_active' => false]);
                    }),

                RelationManagerAction::make('pricing-types=relation-manager')
                    ->label('Configure Pricing Types')
                    ->icon('heroicon-m-adjustments-horizontal')
                    ->color('info')
                    ->badge(fn($record) => $record->pricingOptionTypes()->count())
                    ->modalDescription('Add the Pricing Types')
                    ->slideOver()
                    ->modalWidth(MaxWidth::FitContent)
                    ->relationManager(PricingOptionTypesRelationManager::make()),
                Tables\Actions\Action::make('restore')
                    ->label('Restore')
                    ->icon('heroicon-o-arrow-path')
                    ->color('success')
                    ->visible(fn($record) => $record->trashed())
                    ->action(function ($record) {
                        $highestOrder = PricingOptionGroup::orderBy('display_order', 'desc')->first()?->display_order ?? 0;

                        $record->display_order = $highestOrder + 1;
                        $record->save();

                        $record->restore();
                    }),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('display_order');
    }

    public static function getRelations(): array
    {
        return [
            ServiceStylesRelationManager::class,
            PricingOptionTypesRelationManager::class,
        ];
    }



    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPricingOptionGroups::route('/'),
            'create' => Pages\CreatePricingOptionGroup::route('/create'),
            'view' => Pages\ViewPricingOptionGroup::route('/{record}'),
            'edit' => Pages\EditPricingOptionGroup::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    public static function canViewAny(): bool
    {
        return auth()->check() && auth()->user()->hasRole('superadmin');
    }
}
