<?php

namespace App\Filament\Resources\SystemAccountResource\Pages;

use App\Filament\Resources\SystemAccountResource;
use App\Filament\Widgets\SystemAccountClickableWidget;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListSystemAccounts extends ListRecords
{
    protected static string $resource = SystemAccountResource::class;

    protected function getHeaderActions(): array
    {
        return [
            //
        ];
    }
    
    protected function getHeaderWidgets(): array
    {
        return [
            //
        ];
    }
}
