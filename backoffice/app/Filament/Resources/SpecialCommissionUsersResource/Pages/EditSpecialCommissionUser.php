<?php

namespace App\Filament\Resources\SpecialCommissionUsersResource\Pages;

use App\Filament\Resources\SpecialCommissionUsersResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;

class EditSpecialCommissionUser extends EditRecord
{
    protected static string $resource = SpecialCommissionUsersResource::class;

    protected function getHeaderActions(): array
    {
        return [
            //
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getSavedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('User commission updated')
            ->body('The user commission settings have been updated successfully.');
    }
}
