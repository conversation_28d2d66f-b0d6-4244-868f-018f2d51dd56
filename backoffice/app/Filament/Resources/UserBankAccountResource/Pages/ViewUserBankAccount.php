<?php

namespace App\Filament\Resources\UserBankAccountResource\Pages;

use App\Filament\Resources\UserBankAccountResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewUserBankAccount extends ViewRecord
{
    protected static string $resource = UserBankAccountResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
