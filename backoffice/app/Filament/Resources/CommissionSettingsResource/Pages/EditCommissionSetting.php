<?php

namespace App\Filament\Resources\CommissionSettingsResource\Pages;

use App\Filament\Resources\CommissionSettingsResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;

class EditCommissionSetting extends EditRecord
{
    protected static string $resource = CommissionSettingsResource::class;

    protected function getHeaderActions(): array
    {
        return [
            //
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getSavedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('Commission setting updated')
            ->body('The commission setting has been updated successfully.');
    }
}
