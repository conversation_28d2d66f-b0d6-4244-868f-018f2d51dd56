<?php

namespace App\Filament\Resources;

use App\Filament\Resources\TopupTransactionResource\Pages;
use App\Models\PaymentTransaction;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Illuminate\Support\Carbon;

class TopupTransactionResource extends Resource
{
    protected static ?string $model = PaymentTransaction::class;
    protected static ?string $navigationIcon = 'heroicon-o-arrow-down-tray';
    protected static ?string $navigationLabel = 'Topup Transactions';
    protected static ?string $navigationGroup = 'Payment Management';
    protected static ?string $modelLabel = 'Topup Transaction';
    protected static ?string $pluralModelLabel = 'Topup Transactions';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('id')
                    ->label('Transaction ID')
                    ->disabled(),
                Forms\Components\Select::make('user_id')
                    ->relationship('user', 'name')
                    ->searchable()
                    ->preload()
                    ->disabled()
                    ->label('User'),
                Forms\Components\TextInput::make('amount')
                    ->disabled()
                    ->numeric()
                    ->label('Amount'),
                Forms\Components\TextInput::make('currency')
                    ->disabled()
                    ->label('Currency'),
                Forms\Components\TextInput::make('gateway')
                    ->disabled()
                    ->label('Payment Gateway'),
                Forms\Components\TextInput::make('gateway_reference')
                    ->disabled()
                    ->label('Gateway Reference'),
                Forms\Components\TextInput::make('status')
                    ->disabled()
                    ->label('Status'),
                Forms\Components\Textarea::make('metadata')
                    ->disabled()
                    ->label('Metadata')
                    ->columnSpan(2),
                Forms\Components\DateTimePicker::make('created_at')
                    ->disabled()
                    ->label('Created At'),
                Forms\Components\DateTimePicker::make('updated_at')
                    ->disabled()
                    ->label('Updated At'),
            ])
            ->columns(2);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('transaction_id')
                    ->sortable()
                    ->searchable()
                    ->label('Transaction ID'),
                Tables\Columns\TextColumn::make('user.name')
                    ->sortable()
                    ->searchable()
                    ->label('User')
                    ->limit(20)
                    ->tooltip(function ($record): string {
                        return $record->user->name ?? '';
                    }),
                Tables\Columns\TextColumn::make('creditpackage.credits')
                    ->sortable()
                    ->searchable()
                    ->label('Credits')
                    ->suffix(' credits')
                    ->extraHeaderAttributes([
                        'class' => 'w-8'
                    ]),
                Tables\Columns\TextColumn::make('amount')
                    ->sortable()
                    ->money(fn($record) => $record->currency_code)
                    ->label('Amount')
                    ->alignRight(),
                Tables\Columns\TextColumn::make('currency_code')
                    ->sortable()
                    ->searchable()
                    ->label('Currency')
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('payment_gateway')
                    ->sortable()
                    ->searchable()
                    ->label('Gateway')
                    ->limit(15)
                    ->tooltip(function ($record): string {
                        return $record->payment_gateway ?? '';
                    }),
                Tables\Columns\TextColumn::make('gateway_reference')
                    ->sortable()
                    ->searchable()
                    ->label('Reference')
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('status')
                    ->sortable()
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'pending' => 'warning',
                        'completed' => 'success',
                        'failed' => 'danger',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(string $state): string => ucfirst($state)),
                Tables\Columns\TextColumn::make('failed_int')
                    ->sortable()
                    ->searchable()
                    ->label('Failed Attempts')
                    ->extraHeaderAttributes([
                        'class' => 'w-8'
                    ])
                    ->alignCenter(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->label('Created At')
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options([
                        'pending' => 'Pending',
                        'completed' => 'Completed',
                        'failed' => 'Failed',
                    ]),
                SelectFilter::make('payment_gateway')
                    ->options([
                        'billplz' => 'Billplz',
                        'stripe' => 'Stripe',
                        'paypal' => 'PayPal',
                    ]),
                Filter::make('created_at')
                    ->form([
                        Forms\Components\DatePicker::make('created_from')
                            ->label('From'),
                        Forms\Components\DatePicker::make('created_until')
                            ->label('Until'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn(Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn(Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];

                        if ($data['created_from'] ?? null) {
                            $indicators['created_from'] = 'From ' . Carbon::parse($data['created_from'])->toFormattedDateString();
                        }

                        if ($data['created_until'] ?? null) {
                            $indicators['created_until'] = 'Until ' . Carbon::parse($data['created_until'])->toFormattedDateString();
                        }

                        return $indicators;
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\BulkAction::make('export')
                        ->label('Export Selected')
                        ->icon('heroicon-o-document-arrow-down')
                        ->action(function ($records) {
                            \Filament\Notifications\Notification::make()
                                ->title('Export')
                                ->body('Export functionality will be implemented in a future update.')
                                ->success()
                                ->send();
                        }),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTopupTransactions::route('/'),
            'view' => Pages\ViewTopupTransaction::route('/{record}'),
        ];
    }

    public static function canDelete($record): bool
    {
        return false; // Transaction records cannot be deleted
    }

    // public static function canViewAny(): bool
    // {
    //     return auth()->check() && (
    //         auth()->user()->hasRole('superadmin') || 
    //         auth()->user()->can('view_any_topup_transaction')
    //     );
    // }
}
