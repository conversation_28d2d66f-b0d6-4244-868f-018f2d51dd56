<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SpecialHoursResource\Pages;
use App\Models\BackendUser;
use App\Models\BackendUserAvailability;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Tables\Filters\SelectFilter;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\Action;
use Carbon\Carbon;

class SpecialHoursResource extends Resource
{
    protected static ?string $model = BackendUserAvailability::class;
    protected static ?string $navigationIcon = 'heroicon-o-clock';
    protected static ?string $navigationLabel = 'Special Hours';

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->where('remarks', 'LIKE', 'Special Hours:%');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('user_id')
                    ->relationship('user', 'name')
                    ->searchable()
                    ->preload()
                    ->required(),

                Forms\Components\Select::make('day_of_week')
                    ->options([
                        'Monday' => 'Monday',
                        'Tuesday' => 'Tuesday',
                        'Wednesday' => 'Wednesday',
                        'Thursday' => 'Thursday',
                        'Friday' => 'Friday',
                        'Saturday' => 'Saturday',
                        'Sunday' => 'Sunday',
                    ])
                    ->hidden(fn(string $operation): bool => $operation === 'edit')
                    ->reactive()
                    ->afterStateUpdated(fn(callable $set) => $set('is_recurring', true)),

                Forms\Components\DatePicker::make('special_date')
                    ->hidden(fn(string $operation): bool => $operation === 'edit')
                    ->reactive()
                    ->afterStateUpdated(fn(callable $set) => $set('is_recurring', false)),

                Forms\Components\TimePicker::make('start_time')
                    ->seconds(false)
                    ->required(),

                Forms\Components\TimePicker::make('end_time')
                    ->seconds(false)
                    ->required()
                    ->after('start_time'),

                Forms\Components\Toggle::make('is_available')
                    ->default(true)
                    ->required(),

                Forms\Components\Textarea::make('remarks')
                    ->columnSpan(2)
                    ->maxLength(255)
                    ->default('Special Hours: ')
                    ->required(),

                Forms\Components\Toggle::make('is_recurring')
                    ->label('Is Recurring (Weekly)')
                    ->hidden()
                    ->default(false),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('user.name')
                    ->label('User')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('day_of_week')
                    ->label('Day')
                    ->sortable()
                    ->formatStateUsing(function ($state, $record) {
                        if ($record->day_of_week) {
                            return $record->day_of_week . ' (Weekly)';
                        } elseif ($record->special_date) {
                            return $record->special_date->format('Y-m-d');
                        }
                        return '-';
                    }),

                Tables\Columns\TextColumn::make('start_time')
                    ->label('Start Time')
                    ->date('h:i A')
                    ->sortable(),

                Tables\Columns\TextColumn::make('end_time')
                    ->label('End Time')
                    ->date('h:i A')
                    ->sortable(),

                Tables\Columns\IconColumn::make('is_available')
                    ->label('Active')
                    ->boolean(),

                Tables\Columns\TextColumn::make('remarks')
                    ->label('Description')
                    ->limit(30)
                    ->formatStateUsing(function ($state) {
                        return str_replace('Special Hours: ', '', $state);
                    })
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        $state = str_replace('Special Hours: ', '', $state);
                        if (strlen($state) <= 30) {
                            return null;
                        }
                        return $state;
                    }),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Last Updated')
                    ->dateTime('Y-m-d H:i')
                    ->sortable(),
            ])
            ->filters([
                SelectFilter::make('user')
                    ->relationship('user', 'name')
                    ->searchable()
                    ->preload(),

                SelectFilter::make('day_of_week')
                    ->options([
                        'Monday' => 'Monday',
                        'Tuesday' => 'Tuesday',
                        'Wednesday' => 'Wednesday',
                        'Thursday' => 'Thursday',
                        'Friday' => 'Friday',
                        'Saturday' => 'Saturday',
                        'Sunday' => 'Sunday',
                    ])
                    ->label('Day of Week'),

                Tables\Filters\Filter::make('weekly')
                    ->query(fn(Builder $query): Builder => $query->whereNotNull('day_of_week')->where('is_recurring', true))
                    ->label('Weekly Special Hours'),

                Tables\Filters\Filter::make('specific_date')
                    ->query(fn(Builder $query): Builder => $query->whereNotNull('special_date'))
                    ->label('Date-Specific Special Hours'),

                Tables\Filters\Filter::make('is_active')
                    ->query(fn(Builder $query): Builder => $query->where('is_available', true))
                    ->label('Active Special Hours'),

                Tables\Filters\Filter::make('not_active')
                    ->query(fn(Builder $query): Builder => $query->where('is_available', false))
                    ->label('Inactive Special Hours'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSpecialHours::route('/'),
            'create' => Pages\CreateSpecialHours::route('/create'),
            'edit' => Pages\EditSpecialHours::route('/{record}/edit'),
        ];
    }

    // public static function canViewAny(): bool
    // {
    //     return auth()->check() && (
    //         auth()->user()->hasAnyRole(['user_manager', 'superadmin']) || 
    //         auth()->user()->can('view_any_special_hours')
    //     );
    // }
}
