<?php

namespace App\Filament\Resources\ServiceCategoryResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use App\Models\BackendUserLevel;
use Illuminate\Validation\Rules\Unique;
use App\Models\ServiceStyle;

class ServiceStylesRelationManager extends RelationManager
{
    protected static string $relationship = 'serviceStyles';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->maxLength(255),
                Forms\Components\Textarea::make('description')
                    ->maxLength(65535),
                Forms\Components\Toggle::make('is_active')
                    ->default(true),
                Forms\Components\TextInput::make('display_order')
                    ->numeric()
                    ->default(function () {
                        $latestRecord = ServiceStyle::orderBy('display_order', 'desc')->first();
                        return $latestRecord ? $latestRecord->display_order + 1 : 0;
                    })
                    ->minValue(0)
                    ->unique(
                        ignoreRecord: true,
                        modifyRuleUsing: fn(Unique $rule) => $rule->whereNull('deleted_at')
                    ),
                Forms\Components\Select::make('min_level_id')
                    ->label('Minimum Level Required')
                    ->options(BackendUserLevel::query()->pluck('name', 'id'))
                    ->nullable(),
                Forms\Components\Toggle::make('can_bypass')
                    ->label('Can be bypassed by specific users')
                    ->default(false),
                Forms\Components\TextInput::make('recommended_price')
                    ->label('Recommended Price (Credits)')
                    ->numeric()
                    ->minValue(0)
                    ->nullable(),
                Forms\Components\TextInput::make('preset_price')
                    ->label('Preset Price (Credits)')
                    ->numeric()
                    ->minValue(0)
                    ->nullable(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('description')
                    ->limit(50),
                Tables\Columns\BooleanColumn::make('is_active')
                    ->sortable(),
                Tables\Columns\TextColumn::make('display_order')
                    ->sortable(),
                Tables\Columns\TextColumn::make('minLevel.name')
                    ->label('Minimum Level')
                    ->sortable(),
                Tables\Columns\BooleanColumn::make('can_bypass')
                    ->label('Bypassable')
                    ->sortable(),
                Tables\Columns\TextColumn::make('recommended_price')
                    ->label('Recommended Price')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('preset_price')
                    ->label('Preset Price')
                    ->numeric()
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
