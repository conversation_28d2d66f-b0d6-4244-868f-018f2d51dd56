<?php

namespace App\Filament\Resources;

use App\Filament\Resources\BackendRaceResource\Pages;
use App\Models\BackendRace;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Validation\Rules\Unique;

class BackendRaceResource extends Resource
{
    protected static ?string $model = BackendRace::class;
    protected static ?string $navigationIcon = 'heroicon-o-user-group';
    protected static ?string $navigationGroup = 'User Management';
    protected static ?string $navigationLabel = 'Races';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Basic Information')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\Textarea::make('description')
                            ->maxLength(65535),
                        Forms\Components\Toggle::make('is_active')
                            ->default(true),
                        Forms\Components\TextInput::make('display_order')
                            ->numeric()
                            ->default(function () {
                                $latestRecord = BackendRace::orderBy('display_order', 'desc')->first();
                                return $latestRecord ? $latestRecord->display_order + 1 : 0;
                            })
                            ->minValue(0),
                    ])->columns(2),

                Forms\Components\Section::make('Translations')
                    ->schema([
                        Forms\Components\Tabs::make('Translations')
                            ->tabs([
                                Forms\Components\Tabs\Tab::make('English')
                                    ->schema([
                                        Forms\Components\Hidden::make('translations.en.locale')
                                            ->default('en'),
                                        Forms\Components\TextInput::make('translations.en.name')
                                            ->label('Name (English)')
                                            ->required(),
                                        Forms\Components\Textarea::make('translations.en.description')
                                            ->label('Description (English)'),
                                    ]),
                                Forms\Components\Tabs\Tab::make('Bahasa Melayu')
                                    ->schema([
                                        Forms\Components\Hidden::make('translations.ms.locale')
                                            ->default('ms'),
                                        Forms\Components\TextInput::make('translations.ms.name')
                                            ->label('Name (Bahasa Melayu)')
                                            ->required(),
                                        Forms\Components\Textarea::make('translations.ms.description')
                                            ->label('Description (Bahasa Melayu)'),
                                    ]),
                                Forms\Components\Tabs\Tab::make('Chinese')
                                    ->schema([
                                        Forms\Components\Hidden::make('translations.zh.locale')
                                            ->default('zh'),
                                        Forms\Components\TextInput::make('translations.zh.name')
                                            ->label('Name (Chinese)')
                                            ->required(),
                                        Forms\Components\Textarea::make('translations.zh.description')
                                            ->label('Description (Chinese)'),
                                    ]),
                            ]),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('description')
                    ->limit(50),
                Tables\Columns\BooleanColumn::make('is_active')
                    ->sortable(),
                Tables\Columns\TextColumn::make('display_order')
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->requiresConfirmation(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBackendRaces::route('/'),
            'create' => Pages\CreateBackendRace::route('/create'),
            'edit' => Pages\EditBackendRace::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    // public static function canViewAny(): bool
    // {
    //     return auth()->check() && (
    //         auth()->user()->hasRole('superadmin') || 
    //         auth()->user()->can('view_any_backend_race')
    //     );
    // }
}
