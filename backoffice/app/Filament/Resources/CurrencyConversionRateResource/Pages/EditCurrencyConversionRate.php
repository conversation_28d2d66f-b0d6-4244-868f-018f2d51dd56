<?php

namespace App\Filament\Resources\CurrencyConversionRateResource\Pages;

use App\Filament\Resources\CurrencyConversionRateResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditCurrencyConversionRate extends EditRecord
{
    protected static string $resource = CurrencyConversionRateResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
    
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
