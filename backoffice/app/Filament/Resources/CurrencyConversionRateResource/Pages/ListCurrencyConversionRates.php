<?php

namespace App\Filament\Resources\CurrencyConversionRateResource\Pages;

use App\Filament\Resources\CurrencyConversionRateResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListCurrencyConversionRates extends ListRecords
{
    protected static string $resource = CurrencyConversionRateResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
