<?php

namespace App\Filament\Resources\CurrencyConversionRateResource\Pages;

use App\Filament\Resources\CurrencyConversionRateResource;
use Filament\Resources\Pages\CreateRecord;

class CreateCurrencyConversionRate extends CreateRecord
{
    protected static string $resource = CurrencyConversionRateResource::class;
    
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
