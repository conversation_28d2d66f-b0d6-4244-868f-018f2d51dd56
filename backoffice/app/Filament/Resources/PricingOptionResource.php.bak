<?php

namespace App\Filament\Resources;

use App\Filament\Resources\PricingOptionResource\Pages;
use App\Models\PricingOption;
use App\Models\PricingOptionType;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Validation\Rules\Unique;

class PricingOptionResource extends Resource
{
    protected static ?string $model = PricingOption::class;
    protected static ?string $navigationIcon = 'heroicon-o-currency-dollar';
    protected static ?string $navigationLabel = 'Pricing Options';
    protected static ?string $navigationGroup = 'Payment Management';
    protected static bool $shouldRegisterNavigation = false;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Basic Information')
                    ->schema([
                        Forms\Components\Select::make('pricing_option_type_id')
                            ->label('Pricing Option Type')
                            ->options(function () {
                                return PricingOptionType::query()
                                    ->where('is_active', true)
                                    ->orderBy('display_order')
                                    ->pluck('name', 'id');
                            })
                            ->required()
                            ->searchable()
                            ->default(fn() => request()->get('pricing_option_type_id')),
                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\Textarea::make('description')
                            ->maxLength(65535),
                        Forms\Components\TextInput::make('credits')
                            ->required()
                            ->numeric()
                            ->minValue(0),
                        Forms\Components\Toggle::make('is_active')
                            ->default(true),
                        Forms\Components\TextInput::make('display_order')
                            ->numeric()
                            ->default(function () {
                                $latestRecord = PricingOption::orderBy('display_order', 'desc')->first();
                                return $latestRecord ? $latestRecord->display_order + 1 : 0;
                            })
                            ->minValue(0)
                            ->unique(
                                ignoreRecord: true,
                                modifyRuleUsing: fn(Unique $rule) => $rule->whereNull('deleted_at')
                            ),
                    ])->columns(2),
                
                Forms\Components\Section::make('Translations')
                    ->schema([
                        Forms\Components\Tabs::make('Translations')
                            ->tabs([
                                Forms\Components\Tabs\Tab::make('English')
                                    ->schema([
                                        Forms\Components\Hidden::make('translations.en.locale')
                                            ->default('en'),
                                        Forms\Components\TextInput::make('translations.en.name')
                                            ->label('Name (English)')
                                            ->required(),
                                        Forms\Components\Textarea::make('translations.en.description')
                                            ->label('Description (English)'),
                                    ]),
                                Forms\Components\Tabs\Tab::make('Bahasa Melayu')
                                    ->schema([
                                        Forms\Components\Hidden::make('translations.ms.locale')
                                            ->default('ms'),
                                        Forms\Components\TextInput::make('translations.ms.name')
                                            ->label('Name (Bahasa Melayu)')
                                            ->required(),
                                        Forms\Components\Textarea::make('translations.ms.description')
                                            ->label('Description (Bahasa Melayu)'),
                                    ]),
                                Forms\Components\Tabs\Tab::make('Chinese')
                                    ->schema([
                                        Forms\Components\Hidden::make('translations.zh.locale')
                                            ->default('zh'),
                                        Forms\Components\TextInput::make('translations.zh.name')
                                            ->label('Name (Chinese)')
                                            ->required(),
                                        Forms\Components\Textarea::make('translations.zh.description')
                                            ->label('Description (Chinese)'),
                                    ]),
                            ]),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('pricingOptionType.name')
                    ->label('Type')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('name')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('description')
                    ->limit(50)
                    ->searchable(),
                Tables\Columns\TextColumn::make('credits')
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_active')
                    ->boolean()
                    ->sortable(),
                Tables\Columns\TextColumn::make('display_order')
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('pricing_option_type_id')
                    ->label('Pricing Option Type')
                    ->options(PricingOptionType::query()->pluck('name', 'id')),
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->requiresConfirmation()
                    ->before(function ($record) {
                        $record->update(['is_active' => false]);
                    }),
                Tables\Actions\Action::make('restore')
                    ->label('Restore')
                    ->icon('heroicon-o-arrow-path')
                    ->color('success')
                    ->visible(fn($record) => $record->trashed())
                    ->action(function ($record) {
                        $highestOrder = PricingOption::orderBy('display_order', 'desc')->first()?->display_order ?? 0;
                        
                        $record->display_order = $highestOrder + 1;
                        $record->save();
                        
                        $record->restore();
                    }),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('display_order');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPricingOptions::route('/'),
            'create' => Pages\CreatePricingOption::route('/create'),
            'edit' => Pages\EditPricingOption::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    public static function canViewAny(): bool
    {
        return auth()->check() && auth()->user()->hasRole('superadmin');
    }
}
