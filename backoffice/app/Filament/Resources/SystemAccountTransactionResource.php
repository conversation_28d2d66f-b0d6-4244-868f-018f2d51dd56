<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SystemAccountTransactionResource\Pages;
use App\Models\BackendSystemAccountTransaction;
use App\Models\BackendUser;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class SystemAccountTransactionResource extends Resource
{
    protected static ?string $model = BackendSystemAccountTransaction::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-library';

    protected static ?string $navigationGroup = 'Commission Module';

    protected static ?int $navigationSort = 18;

    protected static ?string $navigationLabel = 'System Account';

    // public static function canViewAny(): bool
    // {
    //     return auth()->check() && (
    //         auth()->user()->hasAnyRole(['superadmin', 'admin']) || 
    //         auth()->user()->can('view_any_system_account_transaction')
    //     );
    // }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Transaction Information')
                    ->schema([
                        Forms\Components\TextInput::make('amount')
                            ->required()
                            ->disabled()
                            ->suffix('Credits'),
                        Forms\Components\TextInput::make('type')
                            ->required()
                            ->disabled(),
                        Forms\Components\TextInput::make('description')
                            ->required()
                            ->disabled(),
                        Forms\Components\TextInput::make('reference_id')
                            ->label('Order/Scheduled Order ID')
                            ->disabled(),
                        Forms\Components\TextInput::make('reference_type')
                            ->label('Order Type')
                            ->disabled(),
                    ]),
                Forms\Components\Section::make('Metadata')
                    ->schema([
                        Forms\Components\KeyValue::make('metadata')
                            ->disabled(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->sortable(),
                Tables\Columns\TextColumn::make('amount')
                    ->suffix(' Credits')
                    ->sortable(),
                Tables\Columns\TextColumn::make('type')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'credit' => 'success',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('description')
                    ->searchable()
                    ->limit(50),
                Tables\Columns\TextColumn::make('reference_id')
                    ->label('Reference ID')
                    ->searchable(),
                Tables\Columns\TextColumn::make('reference_type')
                    ->label('Reference Type')
                    ->formatStateUsing(fn(string $state): string => ucfirst($state))
                    ->badge(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('reference_type')
                    ->options([
                        'order now' => 'Order Now',
                        'scheduled' => 'Scheduled Order',
                    ]),
                Tables\Filters\Filter::make('created_at')
                    ->form([
                        Forms\Components\DatePicker::make('created_from'),
                        Forms\Components\DatePicker::make('created_until'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn(Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn(Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    }),
                Tables\Filters\Filter::make('talent')
                    ->form([
                        Forms\Components\Select::make('talent_id')
                            ->label('Talent')
                            ->options(BackendUser::where('role', 'talent')->pluck('nickname', 'id'))
                            ->searchable(),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['talent_id'],
                            fn(Builder $query, $talentId): Builder => $query->whereJsonContains('metadata->talent_id', (int) $talentId)
                        );
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                //
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSystemAccountTransactions::route('/'),
            'view' => Pages\ViewSystemAccountTransaction::route('/{record}'),
        ];
    }

    public static function canDelete($record): bool
    {
        return false; // Transaction records cannot be deleted
    }
}
