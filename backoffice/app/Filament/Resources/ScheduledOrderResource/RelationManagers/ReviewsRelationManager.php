<?php

namespace App\Filament\Resources\ScheduledOrderResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ReviewsRelationManager extends RelationManager
{
    protected static string $relationship = 'reviews';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Review Information')
                    ->schema([
                        Forms\Components\Select::make('reviewer_id')
                            ->relationship('reviewer', 'name')
                            ->searchable()
                            ->preload()
                            ->disabled(),
                        Forms\Components\Select::make('reviewee_id')
                            ->relationship('reviewee', 'name')
                            ->searchable()
                            ->preload()
                            ->disabled(),
                        Forms\Components\TextInput::make('rating')
                            ->numeric()
                            ->minValue(1)
                            ->maxValue(5)
                            ->disabled(),
                        Forms\Components\Textarea::make('review_text')
                            ->disabled(),
                        Forms\Components\Toggle::make('is_anonymous')
                            ->disabled(),
                        Forms\Components\Toggle::make('is_hidden')
                            ->label('Hidden (Moderated)')
                            ->helperText('Toggle to hide inappropriate reviews'),
                    ]),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('review_text')
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('Review ID')
                    ->sortable(),
                Tables\Columns\TextColumn::make('reviewer.name')
                    ->label('Reviewer')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('reviewee.name')
                    ->label('Reviewee')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('rating')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('review_text')
                    ->limit(50)
                    ->searchable()
                    ->wrap()
                    ->tooltip(fn($record) => $record->review_text),
                Tables\Columns\IconColumn::make('is_anonymous')
                    ->boolean()
                    ->label('Anonymous')
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_hidden')
                    ->boolean()
                    ->label('Hidden')
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('rating')
                    ->options([
                        '1' => '1 Star',
                        '2' => '2 Stars',
                        '3' => '3 Stars',
                        '4' => '4 Stars',
                        '5' => '5 Stars',
                    ]),
                Tables\Filters\Filter::make('is_anonymous')
                    ->toggle()
                    ->label('Anonymous Reviews')
                    ->query(fn (Builder $query): Builder => $query->where('is_anonymous', true)),
                Tables\Filters\Filter::make('is_hidden')
                    ->toggle()
                    ->label('Hidden Reviews')
                    ->query(fn (Builder $query): Builder => $query->where('is_hidden', true)),
                Tables\Filters\TrashedFilter::make(),
            ])
            ->headerActions([
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->requiresConfirmation()
                    ->successNotificationTitle('Review deleted successfully'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->requiresConfirmation()
                        ->successNotificationTitle('Reviews deleted successfully'),
                    Tables\Actions\BulkAction::make('hideReviews')
                        ->label('Hide Reviews')
                        ->icon('heroicon-o-eye-slash')
                        ->action(function ($records) {
                            foreach ($records as $record) {
                                $record->is_hidden = true;
                                $record->save();
                            }
                        }),
                    Tables\Actions\BulkAction::make('showReviews')
                        ->label('Show Reviews')
                        ->icon('heroicon-o-eye')
                        ->action(function ($records) {
                            foreach ($records as $record) {
                                $record->is_hidden = false;
                                $record->save();
                            }
                        }),
                ]),
            ]);
    }
}
