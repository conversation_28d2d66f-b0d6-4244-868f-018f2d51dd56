<?php

namespace App\Filament\Resources\PricingOptionGroupResource\RelationManagers;

use App\Filament\Resources\PricingOptionTypeResource;
use App\Filament\Resources\PricingOptionTypeResource\RelationManagers\PricingOptionsRelationManager;
use App\Models\PricingOptionType;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Actions\ViewAction;
use Guava\FilamentModalRelationManagers\Actions\Table\RelationManagerAction;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rules\Unique;

class PricingOptionTypesRelationManager extends RelationManager
{
    protected static string $relationship = 'pricingOptionTypes';

    protected static ?string $recordTitleAttribute = 'name';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->maxLength(255),
                Forms\Components\Textarea::make('description')
                    ->maxLength(65535),
                Forms\Components\Toggle::make('has_duration')
                    ->label('Has Duration')
                    ->default(false),
                Forms\Components\Toggle::make('is_active')
                    ->default(true),
                Forms\Components\TextInput::make('display_order')
                    ->numeric()
                    ->default(function () {
                        $latestRecord = PricingOptionType::orderBy('display_order', 'desc')->first();
                        return $latestRecord ? $latestRecord->display_order + 1 : 0;
                    })
                    ->minValue(0)
                    ->unique(
                        ignoreRecord: true,
                        modifyRuleUsing: fn(Unique $rule) => $rule->whereNull('deleted_at')
                    ),
                Forms\Components\Tabs::make('Translations')
                    ->tabs([
                        Forms\Components\Tabs\Tab::make('English')
                            ->schema([
                                Forms\Components\Hidden::make('translations.en.locale')
                                    ->default('en'),
                                Forms\Components\TextInput::make('translations.en.name')
                                    ->label('Name (English)')
                                    ->required(),
                                Forms\Components\Textarea::make('translations.en.description')
                                    ->label('Description (English)'),
                            ]),
                        Forms\Components\Tabs\Tab::make('Bahasa Melayu')
                            ->schema([
                                Forms\Components\Hidden::make('translations.ms.locale')
                                    ->default('ms'),
                                Forms\Components\TextInput::make('translations.ms.name')
                                    ->label('Name (Bahasa Melayu)')
                                    ->required(),
                                Forms\Components\Textarea::make('translations.ms.description')
                                    ->label('Description (Bahasa Melayu)'),
                            ]),
                        Forms\Components\Tabs\Tab::make('Chinese')
                            ->schema([
                                Forms\Components\Hidden::make('translations.zh.locale')
                                    ->default('zh'),
                                Forms\Components\TextInput::make('translations.zh.name')
                                    ->label('Name (Chinese)')
                                    ->required(),
                                Forms\Components\Textarea::make('translations.zh.description')
                                    ->label('Description (Chinese)'),
                            ]),
                    ]),
            ]);
    }

    public function table(Table $table): Table
    {
        $ownerRecord = $this->getOwnerRecord();
        $heading = $ownerRecord ? "Pricing Option Types for {$ownerRecord->name}" : 'Pricing Option Types';
        logger('PricingOptionTypesRelationManager::table called');
        return $table
            ->recordTitleAttribute('name')
            ->heading($heading)
            ->description('List of pricing option types associated with this group.')
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('description')
                    ->limit(50)
                    ->searchable(),
                Tables\Columns\IconColumn::make('has_duration')
                    ->boolean()
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_active')
                    ->boolean()
                    ->sortable(),
                Tables\Columns\TextColumn::make('display_order')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->label('Create Pricing Option Type'),
            ])
            ->actions([



                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->requiresConfirmation()
                    ->before(function ($record) {
                        $record->update(['is_active' => false]);
                    }),

                RelationManagerAction::make('pricing-relation-manager')
                    ->label('Configure Pricings')
                    ->icon('heroicon-s-currency-dollar')
                    ->color('info')
                    ->badge(fn($record) => $record->pricingOptions()->count())
                    ->relationManager(PricingOptionsRelationManager::make()),
                Tables\Actions\Action::make('restore')
                    ->label('Restore')
                    ->icon('heroicon-o-arrow-path')
                    ->color('success')
                    ->visible(fn($record) => $record->trashed())
                    ->action(function ($record) {
                        $highestOrder = PricingOptionType::orderBy('display_order', 'desc')->first()?->display_order ?? 0;

                        $record->display_order = $highestOrder + 1;
                        $record->save();

                        $record->restore();
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->emptyStateHeading('No Pricing Option Types Found')
            ->emptyStateDescription('No pricing option types are associated with this group.')
            ->defaultSort('display_order');
    }


    protected function getTableQuery(): ?Builder
    {
        $query = parent::getTableQuery();

        if ($query === null) {
            Log::error('PricingOptionTypesRelationManager: parent::getTableQuery() returned null', [
                'relationship' => static::$relationship,
                'owner_id' => $this->getOwnerRecord()?->id,
            ]);
            $query = PricingOptionType::query()->where('pricing_option_group_id', $this->getOwnerRecord()->id);
        }

        return $query->withoutGlobalScopes([SoftDeletingScope::class]);
    }


    public function getRelations(): array
    {
        return [
            PricingOptionsRelationManager::class,
        ];
    }

    public function isReadOnly(): bool
    {
        return false;
        //return !Str::of($this->pageClass)->contains('MyViewPageName'); //only on the ViewPage
    }
}
