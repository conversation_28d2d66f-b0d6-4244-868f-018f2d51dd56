<?php

namespace App\Filament\Resources\PricingOptionGroupResource\Pages;

use App\Filament\Resources\PricingOptionGroupResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditPricingOptionGroup extends EditRecord
{
    protected static string $resource = PricingOptionGroupResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
    
    protected function mutateFormDataBeforeFill(array $data): array
    {
        $record = $this->getRecord();

        if ($record) {
            $translations = $record->translations()->get();
            
            foreach ($translations as $translation) {
                $locale = $translation->locale;
                $data['translations'][$locale]['locale'] = $locale;
                $data['translations'][$locale]['name'] = $translation->name;
                $data['translations'][$locale]['description'] = $translation->description;
            }
        }

        return $data;
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        $translationsData = $data['translations'] ?? [];
        unset($data['translations']);
        
        session()->put('pricing_option_group_translations', $translationsData);
        
        return $data;
    }

    protected function afterSave(): void
    {
        $record = $this->getRecord();
        $translationsData = session()->pull('pricing_option_group_translations', []);
        
        foreach ($translationsData as $locale => $translationData) {
            if (empty($translationData['name'])) {
                continue; // Skip empty translations
            }
            
            $record->translations()->updateOrCreate(
                ['locale' => $locale],
                [
                    'name' => $translationData['name'],
                    'description' => $translationData['description'] ?? null,
                ]
            );
        }
    }
}
