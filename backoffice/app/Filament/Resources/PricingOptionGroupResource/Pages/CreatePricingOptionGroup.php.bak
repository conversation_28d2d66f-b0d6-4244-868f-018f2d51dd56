<?php

namespace App\Filament\Resources\PricingOptionGroupResource\Pages;

use App\Filament\Resources\PricingOptionGroupResource;
use Filament\Resources\Pages\CreateRecord;

class CreatePricingOptionGroup extends CreateRecord
{
    protected static string $resource = PricingOptionGroupResource::class;
    
    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $translationsData = $data['translations'] ?? [];
        unset($data['translations']);
        
        session()->put('pricing_option_group_translations', $translationsData);
        
        return $data;
    }
    
    protected function afterCreate(): void
    {
        $record = $this->getRecord();
        $translationsData = session()->pull('pricing_option_group_translations', []);
        
        foreach ($translationsData as $locale => $translationData) {
            if (empty($translationData['name'])) {
                continue; // Skip empty translations
            }
            
            $record->translations()->create([
                'locale' => $locale,
                'name' => $translationData['name'],
                'description' => $translationData['description'] ?? null,
            ]);
        }
    }
}
