<?php

namespace App\Filament\Resources\PricingOptionGroupResource\Pages;

use App\Filament\Resources\PricingOptionGroupResource;
use App\Filament\Resources\PricingOptionGroupResource\Widgets\PricingOptionTypesTable;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\IconEntry;

class ViewPricingOptionGroup extends ViewRecord
{
    protected static string $resource = PricingOptionGroupResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }



    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('Basic Information')
                    ->schema([
                        TextEntry::make('name')
                            ->label('Name'),
                        TextEntry::make('description')
                            ->label('Description'),
                        IconEntry::make('is_active')
                            ->label('Active')
                            ->boolean(),
                        TextEntry::make('display_order')
                            ->label('Display Order'),
                        TextEntry::make('created_at')
                            ->label('Created At')
                            ->dateTime(),
                        TextEntry::make('updated_at')
                            ->label('Updated At')
                            ->dateTime(),
                    ])->columns(2),

                // Section::make('Service Styles')
                //     ->schema([
                //         TextEntry::make('serviceStyles.name')
                //             ->label('Linked Service Styles')
                //             ->listWithLineBreaks()
                //             ->bulleted(),
                //     ]),
            ]);
    }
}
