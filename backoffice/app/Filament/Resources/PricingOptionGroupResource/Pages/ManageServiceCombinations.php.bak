<?php

namespace App\Filament\Resources\PricingOptionGroupResource\Pages;

use App\Filament\Resources\PricingOptionGroupResource;
use App\Models\ServiceCategory;
use App\Models\ServiceLink;
use App\Models\ServiceStyle;
use App\Models\ServiceType;
use App\Models\PricingOptionGroup;
use Filament\Actions;
use Filament\Forms;
use Filament\Resources\Pages\Page;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class ManageServiceCombinations extends Page implements Tables\Contracts\HasTable
{
    use Tables\Concerns\InteractsWithTable;
    
    protected static string $resource = PricingOptionGroupResource::class;
    
    protected static string $view = 'filament.resources.pricing-option-group-resource.pages.manage-service-combinations';
    
    public function table(Table $table): Table
    {
        return $table
            ->query(
                ServiceLink::query()
                    ->with(['serviceCategory', 'serviceType', 'serviceStyle', 'pricingOptionGroup'])
            )
            ->columns([
                Tables\Columns\TextColumn::make('serviceCategory.name')
                    ->label('Service Category')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('serviceType.name')
                    ->label('Service Type')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('serviceStyle.name')
                    ->label('Service Style')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('pricingOptionGroup.name')
                    ->label('Pricing Option Group')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\ToggleColumn::make('is_active')
                    ->label('Active')
                    ->onColor('success')
                    ->offColor('danger')
                    ->afterStateUpdated(function ($record, $state) {
                        $record->is_active = $state;
                        $record->save();
                    }),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('service_category_id')
                    ->label('Service Category')
                    ->options(ServiceCategory::query()->pluck('name', 'id'))
                    ->query(function (Builder $query, array $data) {
                        if (isset($data['value'])) {
                            $query->where('service_category_id', $data['value']);
                        }
                    }),
                Tables\Filters\SelectFilter::make('service_type_id')
                    ->label('Service Type')
                    ->options(ServiceType::query()->pluck('name', 'id'))
                    ->query(function (Builder $query, array $data) {
                        if (isset($data['value'])) {
                            $query->where('service_type_id', $data['value']);
                        }
                    }),
                Tables\Filters\SelectFilter::make('service_style_id')
                    ->label('Service Style')
                    ->options(ServiceStyle::query()->pluck('name', 'id'))
                    ->query(function (Builder $query, array $data) {
                        if (isset($data['value'])) {
                            $query->where('service_style_id', $data['value']);
                        }
                    }),
                Tables\Filters\SelectFilter::make('pricing_option_group_id')
                    ->label('Pricing Option Group')
                    ->options(PricingOptionGroup::query()->pluck('name', 'id'))
                    ->query(function (Builder $query, array $data) {
                        if (isset($data['value'])) {
                            $query->where('pricing_option_group_id', $data['value']);
                        }
                    }),
            ])
            ->actions([
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('activate')
                        ->label('Activate')
                        ->icon('heroicon-o-check')
                        ->color('success')
                        ->action(function ($records) {
                            foreach ($records as $record) {
                                $record->is_active = true;
                                $record->save();
                            }
                        }),
                    Tables\Actions\BulkAction::make('deactivate')
                        ->label('Deactivate')
                        ->icon('heroicon-o-x-mark')
                        ->color('danger')
                        ->action(function ($records) {
                            foreach ($records as $record) {
                                $record->is_active = false;
                                $record->save();
                            }
                        }),
                ]),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->label('Add Combination')
                    ->form([
                        Forms\Components\Wizard::make([
                            Forms\Components\Wizard\Step::make('Select Category')
                                ->schema([
                                    Forms\Components\Select::make('service_category_id')
                                        ->label('Service Category')
                                        ->options(ServiceCategory::query()->where('is_active', true)->pluck('name', 'id'))
                                        ->required()
                                        ->searchable()
                                        ->live(),
                                ]),
                            Forms\Components\Wizard\Step::make('Select Type')
                                ->schema([
                                    Forms\Components\Select::make('service_type_id')
                                        ->label('Service Type')
                                        ->options(function (Forms\Get $get) {
                                            $categoryId = $get('service_category_id');
                                            if (!$categoryId) {
                                                return [];
                                            }
                                            
                                            return ServiceType::query()
                                                ->where('service_category_id', $categoryId)
                                                ->where('is_active', true)
                                                ->pluck('name', 'id');
                                        })
                                        ->required()
                                        ->searchable()
                                        ->live(),
                                ]),
                            Forms\Components\Wizard\Step::make('Select Style')
                                ->schema([
                                    Forms\Components\Select::make('service_style_id')
                                        ->label('Service Style')
                                        ->options(function (Forms\Get $get) {
                                            $typeId = $get('service_type_id');
                                            if (!$typeId) {
                                                return [];
                                            }
                                            
                                            return ServiceStyle::query()
                                                ->where('service_type_id', $typeId)
                                                ->where('is_active', true)
                                                ->pluck('name', 'id');
                                        })
                                        ->required()
                                        ->searchable(),
                                ]),
                            Forms\Components\Wizard\Step::make('Select Pricing Group')
                                ->schema([
                                    Forms\Components\Select::make('pricing_option_group_id')
                                        ->label('Pricing Option Group')
                                        ->options(PricingOptionGroup::query()->where('is_active', true)->pluck('name', 'id'))
                                        ->required()
                                        ->searchable(),
                                    Forms\Components\Toggle::make('is_active')
                                        ->label('Active')
                                        ->default(true),
                                ]),
                        ])->skippable(),
                    ]),
            ]);
    }
    
    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('back_to_list')
                ->label('Back to List')
                ->url(PricingOptionGroupResource::getUrl())
                ->color('secondary'),
        ];
    }
}
