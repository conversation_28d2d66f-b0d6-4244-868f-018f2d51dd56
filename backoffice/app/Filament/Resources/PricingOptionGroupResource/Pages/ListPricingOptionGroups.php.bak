<?php

namespace App\Filament\Resources\PricingOptionGroupResource\Pages;

use App\Filament\Resources\PricingOptionGroupResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListPricingOptionGroups extends ListRecords
{
    protected static string $resource = PricingOptionGroupResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
