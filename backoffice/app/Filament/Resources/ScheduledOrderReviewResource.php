<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ScheduledOrderReviewResource\Pages;
use App\Models\BackendScheduledOrderReview;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Collection;

class ScheduledOrderReviewResource extends Resource
{
    protected static ?string $model = BackendScheduledOrderReview::class;

    protected static ?string $navigationIcon = 'heroicon-o-star';

    protected static ?string $navigationGroup = 'Order Management';

    protected static ?int $navigationSort = 35;

    protected static ?string $navigationLabel = 'Scheduled Order Reviews';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Review Information')
                    ->schema([
                        Forms\Components\TextInput::make('id')
                            ->label('Review ID')
                            ->disabled(),
                        Forms\Components\Select::make('scheduled_order_id')
                            ->relationship('scheduledOrder', 'id')
                            ->searchable()
                            ->preload()
                            ->disabled(),
                        Forms\Components\Select::make('reviewer_id')
                            ->relationship('reviewer', 'name')
                            ->searchable()
                            ->preload()
                            ->disabled(),
                        Forms\Components\Select::make('reviewee_id')
                            ->relationship('reviewee', 'name')
                            ->searchable()
                            ->preload()
                            ->disabled(),
                        Forms\Components\TextInput::make('rating')
                            ->numeric()
                            ->minValue(1)
                            ->maxValue(5)
                            ->disabled(),
                        Forms\Components\Textarea::make('review_text')
                            ->disabled(),
                        Forms\Components\Toggle::make('is_anonymous')
                            ->disabled(),
                        Forms\Components\Toggle::make('is_hidden')
                            ->label('Hidden (Moderated)')
                            ->helperText('Toggle to hide inappropriate reviews'),
                    ]),
                Forms\Components\Section::make('Timestamps')
                    ->schema([
                        Forms\Components\DateTimePicker::make('created_at')
                            ->disabled(),
                        Forms\Components\DateTimePicker::make('updated_at')
                            ->disabled(),
                        Forms\Components\DateTimePicker::make('deleted_at')
                            ->disabled(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('Review ID')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('scheduledOrder.id')
                    ->label('Scheduled Order ID')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('reviewer.name')
                    ->label('Reviewer')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('reviewee.name')
                    ->label('Reviewee')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('rating')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('review_text')
                    ->limit(50)
                    ->searchable(),
                Tables\Columns\IconColumn::make('is_anonymous')
                    ->boolean()
                    ->label('Anonymous')
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_hidden')
                    ->boolean()
                    ->label('Hidden')
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('rating')
                    ->options([
                        '1' => '1 Star',
                        '2' => '2 Stars',
                        '3' => '3 Stars',
                        '4' => '4 Stars',
                        '5' => '5 Stars',
                    ]),
                Tables\Filters\Filter::make('is_anonymous')
                    ->toggle()
                    ->label('Anonymous Reviews')
                    ->query(fn (Builder $query): Builder => $query->where('is_anonymous', true)),
                Tables\Filters\Filter::make('is_hidden')
                    ->toggle()
                    ->label('Hidden Reviews')
                    ->query(fn (Builder $query): Builder => $query->where('is_hidden', true)),
                Tables\Filters\Filter::make('created_at')
                    ->form([
                        Forms\Components\DatePicker::make('created_from'),
                        Forms\Components\DatePicker::make('created_until'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    }),
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    // Tables\Actions\DeleteBulkAction::make(),
                    // Tables\Actions\ForceDeleteBulkAction::make(),
                    // Tables\Actions\RestoreBulkAction::make(),
                    Tables\Actions\BulkAction::make('hideReviews')
                        ->label('Hide Reviews')
                        ->icon('heroicon-o-eye-slash')
                        ->action(function (Collection $records): void {
                            foreach ($records as $record) {
                                $record->is_hidden = true;
                                $record->save();
                            }
                        }),
                    Tables\Actions\BulkAction::make('showReviews')
                        ->label('Show Reviews')
                        ->icon('heroicon-o-eye')
                        ->action(function (Collection $records): void {
                            foreach ($records as $record) {
                                $record->is_hidden = false;
                                $record->save();
                            }
                        }),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListScheduledOrderReviews::route('/'),
            'view' => Pages\ViewScheduledOrderReview::route('/{record}'),
            'edit' => Pages\EditScheduledOrderReview::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
