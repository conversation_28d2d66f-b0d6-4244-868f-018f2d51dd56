<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ServiceLinkResource\Pages;
use App\Models\ServiceLink;
use App\Models\ServiceCategory;
use App\Models\ServiceType;
use App\Models\ServiceStyle;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class ServiceLinkResource extends Resource
{
    protected static ?string $model = ServiceLink::class;
    protected static ?string $navigationIcon = 'heroicon-o-link';
    protected static ?string $navigationLabel = 'Service Links';
    protected static ?string $navigationGroup = 'Service Management';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Service Link')
                    ->schema([
                        Forms\Components\Select::make('service_category_id')
                            ->label('Service Category')
                            ->options(ServiceCategory::query()->where('is_active', true)->pluck('name', 'id'))
                            ->required()
                            ->searchable()
                            ->live()
                            ->afterStateUpdated(fn(Forms\Set $set) => $set('service_type_id', null)),

                        Forms\Components\Select::make('service_type_id')
                            ->label('Service Type')
                            ->options(function (Forms\Get $get) {
                                $categoryId = $get('service_category_id');
                                if (!$categoryId) {
                                    return [];
                                }

                                return ServiceType::query()
                                    ->where('service_category_id', $categoryId)
                                    ->where('is_active', true)
                                    ->pluck('name', 'id');
                            })
                            ->required()
                            ->searchable()
                            ->live()
                            ->afterStateUpdated(fn(Forms\Set $set) => $set('service_style_id', null))
                            ->disabled(fn(Forms\Get $get) => !$get('service_category_id')),

                        Forms\Components\Select::make('service_style_id')
                            ->label('Service Style')
                            ->options(function (Forms\Get $get) {
                                $typeId = $get('service_type_id');
                                if (!$typeId) {
                                    return [];
                                }

                                return ServiceStyle::query()
                                    ->where('service_type_id', $typeId)
                                    ->where('is_active', true)
                                    ->pluck('name', 'id');
                            })
                            ->required()
                            ->searchable()
                            ->disabled(fn(Forms\Get $get) => !$get('service_type_id')),

                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('serviceCategory.name')
                    ->label('Service Category')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('serviceType.name')
                    ->label('Service Type')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('serviceStyle.name')
                    ->label('Service Style')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('service_category_id')
                    ->label('Service Category')
                    ->options(ServiceCategory::query()->pluck('name', 'id'))
                    ->query(function (Builder $query, array $data) {
                        if (isset($data['value'])) {
                            $query->where('service_category_id', $data['value']);
                        }
                    }),
                Tables\Filters\SelectFilter::make('service_type_id')
                    ->label('Service Type')
                    ->options(ServiceType::query()->pluck('name', 'id'))
                    ->query(function (Builder $query, array $data) {
                        if (isset($data['value'])) {
                            $query->where('service_type_id', $data['value']);
                        }
                    }),
                Tables\Filters\SelectFilter::make('service_style_id')
                    ->label('Service Style')
                    ->options(ServiceStyle::query()->pluck('name', 'id'))
                    ->query(function (Builder $query, array $data) {
                        if (isset($data['value'])) {
                            $query->where('service_style_id', $data['value']);
                        }
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListServiceLinks::route('/'),
            'create' => Pages\CreateServiceLink::route('/create'),
            'edit' => Pages\EditServiceLink::route('/{record}/edit'),
        ];
    }

    public static function canViewAny(): bool
    {
        return false;
    }
}
