<?php

namespace App\Filament\Resources\ServiceTypeResource\Pages;

use App\Filament\Resources\ServiceTypeResource;
use App\Models\ServiceType;
use App\Services\ImageProcessingService;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;

class EditServiceType extends EditRecord
{
    protected static string $resource = ServiceTypeResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        $record = $this->getRecord();

        if ($record) {
            $translations = $record->translations()->get();
            
            foreach ($translations as $translation) {
                $locale = $translation->locale;
                $data['translations'][$locale]['locale'] = $locale;
                $data['translations'][$locale]['name'] = $translation->name;
                $data['translations'][$locale]['description'] = $translation->description;
            }
        }

        return $data;
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        if (!empty($data['icon'])) {
            $file = is_array($data['icon']) ? $data['icon'][0] : $data['icon'];
            
            if ($file instanceof TemporaryUploadedFile) {
                try {
                    
                    $imageService = app(ImageProcessingService::class);
                    $metadata = $imageService->processImage(
                        $file,
                        0,
                        ServiceTypeResource::$resourceType
                    );
                    $data['icon_path'] = $metadata['optimized'];
                } catch (\Exception $e) {
                    Log::error('Failed to process image', ['error' => $e->getMessage()]);
                    throw new \Exception('Failed to process image: ' . $e->getMessage());
                }
            }
        }
        
        unset($data['icon']);
        
        $translationsData = $data['translations'] ?? [];
        unset($data['translations']);
        
        session()->put('service_type_translations', $translationsData);
        
        return $data;
    }
    
    protected function afterSave(): void
    {
        $record = $this->getRecord();
        $translationsData = session()->pull('service_type_translations', []);
        
        foreach ($translationsData as $locale => $translationData) {
            if (empty($translationData['name'])) {
                continue; // Skip empty translations
            }
            
            $record->translations()->updateOrCreate(
                ['locale' => $locale],
                [
                    'name' => $translationData['name'],
                    'description' => $translationData['description'] ?? null,
                ]
            );
        }
    }
}
