<?php

namespace App\Filament\Resources\UserServiceResource\Pages;

use App\Filament\Clusters\ServiceManagement;
use App\Filament\Resources\UserServiceResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;


class ApprovedUserServices extends ListRecords
{
    protected static string $resource = UserServiceResource::class;

    protected static ?string $navigationLabel = 'Approved Services';

    protected static ?string $navigationIcon = 'heroicon-o-check';

    //protected static ?string $cluster = ServiceManagement::class;

    protected static ?int $navigationSort = 1;

    protected function getTableQuery(): \Illuminate\Database\Eloquent\Builder
    {
        return parent::getTableQuery()->where('status', 'approved');
    }
}
