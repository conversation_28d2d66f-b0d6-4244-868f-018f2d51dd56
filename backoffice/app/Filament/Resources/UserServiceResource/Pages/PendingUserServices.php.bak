<?php

namespace App\Filament\Resources\UserServiceResource\Pages;

use App\Filament\Clusters\ServiceManagement;
use App\Filament\Resources\UserServiceResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;


class PendingUserServices extends ListRecords
{
    protected static string $resource = UserServiceResource::class;

    protected static ?string $navigationLabel = 'Pending Services';

    //protected static ?string $cluster = ServiceManagement::class;
    protected static ?string $navigationIcon = 'heroicon-o-clock';

    protected static ?int $navigationSort = 2;

    protected function getTableQuery(): \Illuminate\Database\Eloquent\Builder
    {
        return parent::getTableQuery()->where('status', 'pending');
    }
}
