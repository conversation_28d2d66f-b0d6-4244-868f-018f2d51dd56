<?php

namespace App\Filament\Resources;

use App\Filament\Resources\UserBankAccountResource\Pages;
use App\Models\BackendUserBankAccount;
use App\Models\BackendMalaysianBank;
use App\Models\BackendUser;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class UserBankAccountResource extends Resource
{
    protected static ?string $model = BackendUserBankAccount::class;
    protected static ?string $navigationIcon = 'heroicon-o-credit-card';
    protected static ?string $navigationLabel = 'User Bank Accounts';
    protected static ?string $navigationGroup = 'User Management';
    protected static ?string $modelLabel = 'User Bank Account';
    protected static ?string $pluralModelLabel = 'User Bank Accounts';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('user_id')
                    ->label('User')
                    ->relationship('user', 'name')
                    ->searchable()
                    ->required(),
                Forms\Components\Select::make('malaysian_bank_id')
                    ->label('Bank')
                    ->relationship('bank', 'name', fn(Builder $query) => $query->where('is_active', true))
                    ->searchable()
                    ->required(),
                Forms\Components\TextInput::make('account_number')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('account_holder_name')
                    ->required()
                    ->maxLength(255),
                Forms\Components\Toggle::make('is_primary')
                    ->label('Primary Account')
                    ->default(false),
                Forms\Components\Toggle::make('is_verified')
                    ->label('Verified')
                    ->default(false),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->sortable()
                    ->extraHeaderAttributes(['style' => 'width: 80px;']),
                Tables\Columns\TextColumn::make('user.name')
                    ->label('User')
                    ->sortable()
                    ->searchable()
                    ->extraHeaderAttributes(['style' => 'width: 200px;']),
                Tables\Columns\TextColumn::make('bank.name')
                    ->label('Bank')
                    ->sortable()
                    ->searchable()
                    ->extraHeaderAttributes(['style' => 'width: 200px;']),
                Tables\Columns\TextColumn::make('account_number')
                    ->searchable()
                    ->extraHeaderAttributes(['style' => 'width: 180px;']),
                Tables\Columns\TextColumn::make('account_holder_name')
                    ->searchable()
                    ->extraHeaderAttributes(['style' => 'width: 200px;']),
                Tables\Columns\IconColumn::make('is_primary')
                    ->boolean()
                    ->label('Primary')
                    ->extraHeaderAttributes(['style' => 'width: 100px;']),
                Tables\Columns\IconColumn::make('is_verified')
                    ->boolean()
                    ->label('Verified')
                    ->extraHeaderAttributes(['style' => 'width: 100px;']),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('user')
                    ->relationship('user', 'name')
                    ->searchable()
                    ->preload(),
                Tables\Filters\SelectFilter::make('bank')
                    ->relationship('bank', 'name')
                    ->searchable()
                    ->preload(),
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->requiresConfirmation(),
                Tables\Actions\Action::make('setPrimary')
                    ->label('Set as Primary')
                    ->icon('heroicon-o-star')
                    ->color('success')
                    ->visible(fn($record) => !$record->is_primary)
                    ->action(function ($record) {
                        BackendUserBankAccount::where('user_id', $record->user_id)
                            ->where('id', '!=', $record->id)
                            ->update(['is_primary' => false]);

                        $record->update(['is_primary' => true]);
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUserBankAccounts::route('/'),
            'create' => Pages\CreateUserBankAccount::route('/create'),
            'edit' => Pages\EditUserBankAccount::route('/{record}/edit'),
            'view' => Pages\ViewUserBankAccount::route('/{record}'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    // public static function canViewAny(): bool
    // {
    //     return auth()->check() && (
    //         auth()->user()->hasAnyRole(['superadmin', 'admin']) || 
    //         auth()->user()->can('view_any_user_bank_account')
    //     );
    // }
}
