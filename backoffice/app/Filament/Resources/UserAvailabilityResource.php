<?php

namespace App\Filament\Resources;

use App\Filament\Resources\UserAvailabilityResource\Pages;
use App\Models\BackendUser;
use App\Models\BackendUserAvailability;
use App\Models\BackendUserAvailabilityOverride;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Tables\Filters\SelectFilter;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\Action;
use Carbon\Carbon;

class UserAvailabilityResource extends Resource
{
    protected static ?string $model = BackendUserAvailability::class;
    protected static ?string $navigationIcon = 'heroicon-o-calendar';
    protected static ?string $navigationLabel = 'User Availability';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('user_id')
                    ->relationship('user', 'name')
                    ->searchable()
                    ->preload()
                    ->required(),

                Forms\Components\DatePicker::make('special_date')
                    ->hidden(fn(string $operation): bool => $operation === 'edit')
                    ->reactive()
                    ->afterStateUpdated(fn(callable $set) => $set('is_recurring', false)),

                Forms\Components\Repeater::make('availability_data')
                    ->schema([
                        Forms\Components\Select::make('day')
                            ->options([
                                'Monday' => 'Monday',
                                'Tuesday' => 'Tuesday',
                                'Wednesday' => 'Wednesday',
                                'Thursday' => 'Thursday',
                                'Friday' => 'Friday',
                                'Saturday' => 'Saturday',
                                'Sunday' => 'Sunday',
                            ])
                            ->required(fn(callable $get) => !$get('../../special_date'))
                            ->hidden(fn(callable $get) => $get('../../special_date')),

                        Forms\Components\Repeater::make('periods')
                            ->schema([
                                Forms\Components\TimePicker::make('start_time')
                                    ->seconds(false)
                                    ->required(),

                                Forms\Components\TimePicker::make('end_time')
                                    ->seconds(false)
                                    ->required()
                                    ->after('start_time'),
                            ])
                            ->columns(2)
                            ->required()
                            ->minItems(1)
                            ->defaultItems(1)
                            ->label('Time Periods'),
                    ])
                    ->columns(1)
                    ->required(),

                Forms\Components\Toggle::make('is_available')
                    ->default(true)
                    ->required(),

                Forms\Components\Textarea::make('remarks')
                    ->columnSpan(2)
                    ->maxLength(255),

                Forms\Components\Toggle::make('is_recurring')
                    ->label('Is Recurring (Weekly)')
                    ->hidden()
                    ->default(false),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('user.name')
                    ->label('User')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('special_date')
                    ->label('Date')
                    ->date('Y-m-d')
                    ->sortable()
                    ->formatStateUsing(function ($state, $record) {
                        if ($record->special_date) {
                            return $record->special_date->format('Y-m-d');
                        } elseif ($record->is_recurring) {
                            return 'Weekly Recurring';
                        }
                        return '-';
                    }),

                Tables\Columns\ViewColumn::make('availability_data')
                    ->label('Availabilities')
                    ->view('filament.tables.columns.availability-data'),

                Tables\Columns\IconColumn::make('is_available')
                    ->label('Available')
                    ->boolean(),

                Tables\Columns\TextColumn::make('remarks')
                    ->limit(30)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 30) {
                            return null;
                        }
                        return $state;
                    }),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Last Updated')
                    ->dateTime('Y-m-d H:i')
                    ->sortable(),
            ])
            ->filters([
                SelectFilter::make('user')
                    ->relationship('user', 'name')
                    ->searchable()
                    ->preload(),

                Tables\Filters\Filter::make('day_of_week')
                    ->form([
                        Forms\Components\Select::make('day')
                            ->options([
                                'Monday' => 'Monday',
                                'Tuesday' => 'Tuesday',
                                'Wednesday' => 'Wednesday',
                                'Thursday' => 'Thursday',
                                'Friday' => 'Friday',
                                'Saturday' => 'Saturday',
                                'Sunday' => 'Sunday',
                            ])
                            ->label('Day of Week'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['day'],
                                fn(Builder $query, $day): Builder => $query->whereJsonContains('availability_data', ['day' => $day])
                            );
                    }),

                Tables\Filters\Filter::make('recurring')
                    ->query(fn(Builder $query): Builder => $query->where('is_recurring', true))
                    ->label('Weekly Recurring'),

                Tables\Filters\Filter::make('non_recurring')
                    ->query(fn(Builder $query): Builder => $query->where('is_recurring', false))
                    ->label('Special Dates'),

                Tables\Filters\Filter::make('is_available')
                    ->query(fn(Builder $query): Builder => $query->where('is_available', true))
                    ->label('Available Times'),

                Tables\Filters\Filter::make('not_available')
                    ->query(fn(Builder $query): Builder => $query->where('is_available', false))
                    ->label('Unavailable Times'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUserAvailabilities::route('/'),
            'create' => Pages\CreateUserAvailability::route('/create'),
            'edit' => Pages\EditUserAvailability::route('/{record}/edit'),
        ];
    }

    // public static function canViewAny(): bool
    // {
    //     return auth()->check() && (
    //         auth()->user()->hasAnyRole(['user_manager', 'superadmin']) || 
    //         auth()->user()->can('view_any_user_availability')
    //     );
    // }
}
