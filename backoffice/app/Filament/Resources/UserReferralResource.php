<?php

namespace App\Filament\Resources;

use App\Filament\Resources\UserReferralResource\Pages;
use App\Models\UserReferral;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Columns\ViewColumn;
use Illuminate\Database\Eloquent\Builder;

class UserReferralResource extends Resource
{
    protected static ?string $model = UserReferral::class;
    protected static ?string $navigationIcon = 'heroicon-o-user-group';
    protected static ?string $navigationLabel = 'User Referrals';
    protected static ?string $navigationGroup = 'User Management';
    protected static ?string $modelLabel = 'User Referral';
    protected static ?string $pluralModelLabel = 'User Referrals';

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('referrer.name')
                    ->label('Referrer Name')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('referred.name')
                    ->label('Referee Name')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('referred_at')
                    ->label('Referred At')
                    ->dateTime('Y-m-d H:i:s')
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_converted')
                    ->label('Converted')
                    ->boolean()
                    ->sortable(),
                Tables\Columns\TextColumn::make('converted_at')
                    ->label('Converted At')
                    ->dateTime('Y-m-d H:i:s')
                    ->sortable()
                    ->placeholder('Not converted'),
                ViewColumn::make('action_tracker')
                    ->label('Action Tracker')
                    ->view('filament.tables.columns.status-switcher'),
            ])
            ->filters([
                SelectFilter::make('referrer')
                    ->relationship('referrer', 'name')
                    ->searchable()
                    ->preload()
                    ->label('Referrer'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ])
            ->defaultSort('referred_at', 'desc')
            ->paginated([10, 25, 50, 100, 'all']);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUserReferrals::route('/'),
            'view' => Pages\ViewUserReferral::route('/{record}'),
        ];
    }

    // public static function canViewAny(): bool
    // {
    //     return auth()->check() && (
    //         auth()->user()->hasAnyRole(['superadmin', 'user_manager']) || 
    //         auth()->user()->can('view_any_user_referral')
    //     );
    // }
}
