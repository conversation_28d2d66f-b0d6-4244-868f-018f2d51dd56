<?php

namespace App\Filament\Resources\BackendPersonalityResource\Pages;

use App\Filament\Resources\BackendPersonalityResource;
use App\Models\BackendPersonalityTranslation;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;

class CreateBackendPersonality extends CreateRecord
{
    protected static string $resource = BackendPersonalityResource::class;
    
    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $translations = $data['translations'] ?? [];
        unset($data['translations']);
        
        return $data;
    }
    
    protected function handleRecordCreation(array $data): Model
    {
        $record = static::getModel()::create($data);
        
        if (isset($this->data['translations'])) {
            foreach ($this->data['translations'] as $locale => $translation) {
                if (!empty($translation['name'])) {
                    BackendPersonalityTranslation::create([
                        'personality_id' => $record->id,
                        'locale' => $locale,
                        'name' => $translation['name'],
                        'description' => $translation['description'] ?? null,
                    ]);
                }
            }
        }
        
        return $record;
    }
    
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
