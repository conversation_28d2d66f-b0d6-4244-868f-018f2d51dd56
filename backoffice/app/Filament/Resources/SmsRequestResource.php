<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SmsRequestResource\Pages;
use App\Models\BackendOtp;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Illuminate\Database\Eloquent\Builder;

class SmsRequestResource extends Resource
{
    protected static ?string $model = BackendOtp::class;
    protected static ?string $navigationIcon = 'heroicon-o-chat-bubble-left';
    protected static ?string $navigationLabel = 'SMS Requests';
    protected static ?string $navigationGroup = 'Security';

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->sortable(),
                Tables\Columns\TextColumn::make('mobile_number')
                    ->label('Mobile Number')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('otp')
                    ->label('OTP Code')
                    ->searchable(),
                Tables\Columns\TextColumn::make('status_code')
                    ->label('Send Status')
                    ->badge()
                    ->color(
                        fn($state) =>
                        in_array($state, ['200', 'SUCCESS']) ? 'success' : 'danger'
                    ),
                Tables\Columns\TextColumn::make('delivery_status')
                    ->label('Delivery Status')
                    ->badge()
                    ->color(
                        fn($state) =>
                        $state === 'DELIVERED' ? 'success' : ($state === 'UNDELIVERED' ? 'danger' : 'gray')
                    ),
                Tables\Columns\TextColumn::make('delivery_error_code')
                    ->label('Delivery Error')
                    ->tooltip(fn($state) => $state ? "Error code: $state" : null),
                Tables\Columns\TextColumn::make('message_id')
                    ->label('Message ID')
                    ->limit(15)
                    ->tooltip(fn($state) => $state),
                Tables\Columns\TextColumn::make('last_sent_at')
                    ->label('Sent At')
                    ->dateTime('Y-m-d H:i:s')
                    ->sortable(),
                Tables\Columns\TextColumn::make('expires_at')
                    ->label('Expires At')
                    ->dateTime('Y-m-d H:i:s')
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created At')
                    ->dateTime('Y-m-d H:i:s')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Filter::make('created_at')
                    ->form([
                        Forms\Components\DatePicker::make('created_from')
                            ->label('From'),
                        Forms\Components\DatePicker::make('created_until')
                            ->label('Until'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn(Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn(Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    })
                    ->label('Date Range'),
                SelectFilter::make('delivery_status')
                    ->options([
                        'DELIVERED' => 'Delivered',
                        'UNDELIVERED' => 'Undelivered',
                    ])
                    ->label('Delivery Status'),
                Filter::make('mobile_search')
                    ->form([
                        Forms\Components\TextInput::make('mobile_number')
                            ->label('Mobile Number Contains'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['mobile_number'],
                            fn(Builder $query, $number): Builder => $query->where('mobile_number', 'like', "%{$number}%")
                        );
                    })
                    ->label('Mobile Number Filter'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->modalHeading(fn($record) => "SMS Request - {$record->mobile_number}")
            ])
            ->defaultSort('created_at', 'desc')
            ->paginated([10, 25, 50, 100]);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('id')
                    ->label('ID')
                    ->disabled(),
                Forms\Components\TextInput::make('mobile_number')
                    ->label('Mobile Number')
                    ->disabled(),
                Forms\Components\TextInput::make('otp')
                    ->label('OTP Code')
                    ->disabled(),
                Forms\Components\TextInput::make('status_code')
                    ->label('Send Status')
                    ->disabled(),
                Forms\Components\TextInput::make('message_id')
                    ->label('Message ID')
                    ->disabled(),
                Forms\Components\TextInput::make('delivery_status')
                    ->label('Delivery Status')
                    ->disabled(),
                Forms\Components\TextInput::make('delivery_error_code')
                    ->label('Delivery Error Code')
                    ->disabled(),
                Forms\Components\DateTimePicker::make('last_sent_at')
                    ->label('Sent At')
                    ->disabled(),
                Forms\Components\DateTimePicker::make('expires_at')
                    ->label('Expires At')
                    ->disabled(),
                Forms\Components\DateTimePicker::make('created_at')
                    ->label('Created At')
                    ->disabled(),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSmsRequests::route('/'),
        ];
    }

    public static function canCreate(): bool
    {
        return false; // Read-only resource
    }

    public static function canEdit($record): bool
    {
        return false; // Read-only resource
    }

    public static function canDelete($record): bool
    {
        return false; // Read-only resource
    }

    // public static function canViewAny(): bool
    // {
    //     return auth()->check() && (
    //         auth()->user()->hasAnyRole(['superadmin', 'security_admin']) || 
    //         auth()->user()->can('view_any_sms_request')
    //     );
    // }
}
