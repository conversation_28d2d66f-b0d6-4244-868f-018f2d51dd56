<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ScheduledOrderResource\Pages;
use App\Filament\Resources\ScheduledOrderResource\RelationManagers\ReviewsRelationManager;
use App\Models\BackendScheduledOrder;
use App\Models\BackendUser;
use App\Models\UserService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Http;
use Carbon\Carbon;

class ScheduledOrderResource extends Resource
{
    protected static ?string $model = BackendScheduledOrder::class;
    protected static ?string $navigationIcon = 'heroicon-o-clock';
    protected static ?string $navigationGroup = 'Order Management';
    protected static ?string $navigationLabel = 'Scheduled Orders';

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery();
        // ->whereNotNull('scheduled_start_time');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Order Details')
                    ->schema([
                        Forms\Components\Select::make('talent_id')
                            ->label('Talent')
                            ->options(function () {
                                return BackendUser::where('role', 'talent')
                                    ->orderBy('name')
                                    ->pluck('name', 'id')
                                    ->toArray();
                            })
                            ->searchable()
                            ->required()
                            ->reactive()
                            ->afterStateUpdated(fn(callable $set) => $set('user_service_id', null)),

                        Forms\Components\Select::make('user_service_id')
                            ->label('Service')
                            ->options(function (callable $get) {
                                $talentId = $get('talent_id');
                                if (!$talentId) {
                                    return [];
                                }

                                return UserService::where('user_id', $talentId)
                                    ->where('is_active', true)
                                    ->pluck('title', 'id')
                                    ->toArray();
                            })
                            ->searchable()
                            ->required()
                            ->reactive()
                            ->afterStateUpdated(fn(callable $set) => $set('pricing_option_type_id', null)),

                        Forms\Components\Select::make('pricing_option_type_id')
                            ->label('Pricing Option Type')
                            ->options(function (callable $get) {
                                $userServiceId = $get('user_service_id');
                                if (!$userServiceId) {
                                    return [];
                                }

                                $pricingOptionTypes = [];
                                $userService = UserService::find($userServiceId);

                                if ($userService) {
                                    foreach ($userService->pricingOptions as $option) {
                                        if ($option->is_active && $option->pricingOptionType) {
                                            $pricingOptionTypes[$option->pricing_option_type_id] =
                                                $option->pricingOptionType->name . ' (' .
                                                $option->pricingOptionType->unit . ')';
                                        }
                                    }
                                }

                                return $pricingOptionTypes;
                            })
                            ->searchable()
                            ->required(),

                        Forms\Components\TextInput::make('quantity')
                            ->numeric()
                            ->default(1)
                            ->minValue(1)
                            ->required(),

                        Forms\Components\DateTimePicker::make('scheduled_start_time')
                            ->label('Scheduled Start Time')
                            ->required()
                            ->minDate(now())
                            ->seconds(false)
                            ->reactive(),

                        Forms\Components\Textarea::make('remarks')
                            ->maxLength(500),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Order Status')
                    ->schema([
                        Forms\Components\Select::make('status')
                            ->options([
                                'pending' => 'Pending',
                                'accepted' => 'Accepted',
                                'rejected' => 'Rejected',
                                'completed' => 'Completed',
                                'cancelled' => 'Cancelled',
                            ])
                            ->disabled()
                            ->dehydrated(false),

                        Forms\Components\Toggle::make('is_credit_held')
                            ->label('Credit Held')
                            ->disabled()
                            ->dehydrated(false),

                        Forms\Components\DateTimePicker::make('created_at')
                            ->label('Created At')
                            ->disabled()
                            ->dehydrated(false),
                    ])
                    ->columns(3)
                    ->visible(fn($record) => $record !== null),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('Order ID')
                    ->sortable(),

                Tables\Columns\TextColumn::make('customer.name')
                    ->label('Customer')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('talent.name')
                    ->label('Talent')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('userService.title')
                    ->label('Service')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('quantity')
                    ->sortable(),

                Tables\Columns\TextColumn::make('credit_amount')
                    ->label('Credits')
                    ->formatStateUsing(fn($state) => (int)$state)
                    ->sortable(),

                Tables\Columns\TextColumn::make('scheduled_start_time')
                    ->label('Scheduled Time')
                    ->dateTime()
                    ->sortable(),



                Tables\Columns\BadgeColumn::make('status')
                    ->colors([
                        'danger' => 'rejected',
                        'warning' => 'pending',
                        'success' => 'accepted',
                        'primary' => 'completed',
                        'secondary' => 'cancelled',
                    ]),

                Tables\Columns\IconColumn::make('is_credit_held')
                    ->label('Credit Held')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger')
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable(),

                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'pending' => 'Pending',
                        'accepted' => 'Accepted',
                        'rejected' => 'Rejected',
                        'completed' => 'Completed',
                        'cancelled' => 'Cancelled',
                    ]),

                Tables\Filters\Filter::make('scheduled_start_time')
                    ->form([
                        Forms\Components\DatePicker::make('scheduled_from')
                            ->label('From'),
                        Forms\Components\DatePicker::make('scheduled_until')
                            ->label('Until'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['scheduled_from'],
                                fn(Builder $query, $date): Builder => $query->whereDate('scheduled_start_time', '>=', $date),
                            )
                            ->when(
                                $data['scheduled_until'],
                                fn(Builder $query, $date): Builder => $query->whereDate('scheduled_start_time', '<=', $date),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ]);
        // ->bulkActions([
        //     Tables\Actions\BulkActionGroup::make([
        //         Tables\Actions\DeleteBulkAction::make(),
        //     ]),
        // ]);
    }

    public static function getRelations(): array
    {
        return [
            ReviewsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListScheduledOrders::route('/'),
            'create' => Pages\CreateScheduledOrder::route('/create'),
            'view' => Pages\ViewScheduledOrder::route('/{record}'),
        ];
    }

    // public static function canViewAny(): bool
    // {
    //     return auth()->check() && (
    //         auth()->user()->hasAnyRole(['superadmin', 'order_manager']) || 
    //         auth()->user()->can('view_any_scheduled_order')
    //     );
    // }
}
