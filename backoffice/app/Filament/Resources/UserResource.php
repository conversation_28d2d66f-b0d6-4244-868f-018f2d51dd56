<?php

namespace App\Filament\Resources;

use App\Filament\Resources\UserResource\Pages;
use App\Filament\Resources\UserResource\RelationManagers;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Spatie\Permission\Models\Role;

class UserResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationLabel = 'User Management';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('email')
                    ->email()
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('password')
                    ->password()
                    ->required(fn($livewire) => !$livewire->record)
                    ->dehydrated(fn($state) => filled($state))
                    ->dehydrateStateUsing(fn($state) => \Illuminate\Support\Facades\Hash::make($state))
                    ->maxLength(255)
                    ->revealable(),
                Forms\Components\TextInput::make('password_confirmation')
                    ->password()
                    ->required(fn($livewire) => !$livewire->record && filled($livewire->data['password']))
                    ->dehydrated(false)
                    ->same('password')
                    ->maxLength(255)
                    ->revealable(),
                Forms\Components\Select::make('roles')
                    ->multiple()
                    ->relationship('roles', 'name')
                    ->options(function () {
                        $roles = Role::all()->pluck('name', 'id');
                        if (!auth()->user()->hasRole('super_admin')) {
                            $roles = $roles->except(Role::where('name', 'super_admin')->first()?->id);
                        }
                        return $roles;
                    })
                    // ->visible(function () {
                    //     return auth()->check() && auth()->user()->hasAnyRole(['user_manager', 'super_admin']);
                    // })
                    ->preload()
                    ->dehydrateStateUsing(function ($state, $livewire) {
                        $superadminRoleId = Role::where('name', 'super_admin')->first()?->id;
                        $originalRoles = $livewire->record ? $livewire->record->roles->pluck('id')->toArray() : [];
                        $isRemovingSuperadmin = $superadminRoleId && in_array($superadminRoleId, $originalRoles) && !in_array($superadminRoleId, $state ?: []);

                        if (!auth()->user()->hasRole('super_admin') && $superadminRoleId) {
                            if (in_array($superadminRoleId, $state ?: [])) {
                                \Filament\Notifications\Notification::make()
                                    ->title('Permission Denied')
                                    ->body('Only superadmins can assign the superadmin role.')
                                    ->danger()
                                    ->send();
                                return array_filter($state, fn($roleId) => $roleId !== $superadminRoleId);
                            }
                            if ($isRemovingSuperadmin) {
                                \Filament\Notifications\Notification::make()
                                    ->title('Permission Denied')
                                    ->body('Only superadmins can remove the superadmin role.')
                                    ->danger()
                                    ->send();
                                return array_merge($state ?: [], [$superadminRoleId]); // Retain superadmin
                            }
                        }
                        return $state;
                    }),

            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('email')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('roles.name')->label('Role')->badge(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->visible(function ($record) {
                        $currentUser = auth()->user();
                        $isSuperAdmin = $currentUser->hasRole('super_admin');
                        $targetHasSuperAdmin = $record->hasRole('super_admin');
                        return $isSuperAdmin || ($currentUser->hasRole('user_manager') && !$targetHasSuperAdmin);
                    }),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make()
                    ->visible(fn() => auth()->check() && auth()->user()->hasRole('super_admin')),
            ])
            ->recordUrl(function ($record) {
                // Disable row click for user_manager when target is superadmin
                $currentUser = auth()->user();
                if ($currentUser->hasRole('user_manager') && $record->hasRole('super_admin')) {
                    return null; // No URL, disables click
                }
                return static::getUrl('edit', ['record' => $record]);
            });
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUsers::route('/'),
            'create' => Pages\CreateUser::route('/create'),
            'edit' => Pages\EditUser::route('/{record}/edit'),
        ];
    }

    // public static function canViewAny(): bool
    // {
    //     if (auth()->check()) {
    //         \Log::info('User roles: ' . json_encode(auth()->user()->roles->pluck('name')->toArray()));
    //         return auth()->user()->hasAnyRole(['user_manager', 'super_admin'])
    //             || auth()->user()->can('view_user');
    //     }
    //     \Log::info('User not authenticated');
    //     return false;
    // }
}
