<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SocialPostsResource\Pages;
use App\Filament\Resources\SocialPostsResource\RelationManagers;
use App\Filament\Resources\SocialPostsResource\RelationManagers\CommentsRelationManager;
use App\Models\SocialPost;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;

class SocialPostsResource extends Resource
{
    protected static ?string $model = SocialPost::class;

    protected static ?string $navigationIcon = 'heroicon-o-chat-bubble-bottom-center-text';
    protected static ?string $navigationLabel = 'Social Posts';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('user_id')
                    ->relationship('user', 'name') // Adjust to 'nickname' if using BackendUser
                    ->required(),
                Forms\Components\TextInput::make('title')
                    ->required()
                    ->maxLength(45),
                Forms\Components\Textarea::make('description')
                    ->required()
                    ->maxLength(1000),
                Forms\Components\Textarea::make('media_files')
                    ->helperText('Enter as JSON array, e.g., [{"original": "url", "device_info": "agent"}]')
                    ->default('[]'),
                Forms\Components\Textarea::make('location_data')
                    ->helperText('Enter as JSON, e.g., {"lat": 12.34, "lng": 56.78, "place": "City"}')
                    ->default('{}'),
                Forms\Components\Toggle::make('is_hidden')
                    ->default(false),
                Forms\Components\Toggle::make('is_featured')
                    ->default(false),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')->sortable(),
                Tables\Columns\TextColumn::make('user.name')
                    ->label('User')
                    ->sortable()
                    ->wrap()
                    ->searchable(),
                Tables\Columns\TextColumn::make('title')
                    ->sortable()
                    ->wrap()
                    ->searchable(),
                Tables\Columns\TextColumn::make('description')
                    ->limit(50)
                    ->tooltip(fn($record) => $record->description)
                    ->wrap(),
                Tables\Columns\IconColumn::make('is_hidden')
                    ->boolean()
                    ->label('Hidden'),
                Tables\Columns\IconColumn::make('is_featured')
                    ->boolean()
                    ->label('Featured'),
                Tables\Columns\TextColumn::make('likes_count')
                    ->counts('likes')
                    ->wrap()
                    ->sortable(),
                Tables\Columns\TextColumn::make('comments_count')
                    ->counts('comments')
                    ->wrap()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->wrap()
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    //->visible(fn() => auth()->user()->hasRole('superadmin'))
                    ->requiresConfirmation()
                    ->before(function (SocialPost $record) {
                        $record->update(['is_hidden' => true, 'is_featured' => false]); // Set is_active to false before deleting
                        $record->delete();
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkAction::make('delete')
                    ->label('Delete Selected')
                    ->requiresConfirmation()
                    ->color('danger')
                    ->icon('heroicon-o-trash')
                    //->visible(fn() => auth()->user()->hasRole('superadmin')) // Only for superadmin
                    ->action(function ($records) {
                        $records->each(function ($record) {
                            $record->update(['is_hidden' => true, 'is_featured' => false]); // Set is_active to false
                            $record->delete(); // Soft delete the record
                        });
                    }),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            // Add relation managers if needed (e.g., for comments, likes)
            CommentsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSocialPosts::route('/'),
            'create' => Pages\CreateSocialPosts::route('/create'),
            'edit' => Pages\EditSocialPosts::route('/{record}/edit'),
        ];
    }
}
