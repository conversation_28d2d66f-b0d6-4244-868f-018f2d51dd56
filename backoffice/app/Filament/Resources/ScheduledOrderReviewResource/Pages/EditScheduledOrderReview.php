<?php

namespace App\Filament\Resources\ScheduledOrderReviewResource\Pages;

use App\Filament\Resources\ScheduledOrderReviewResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditScheduledOrderReview extends EditRecord
{
    protected static string $resource = ScheduledOrderReviewResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
            Actions\ForceDeleteAction::make(),
            Actions\RestoreAction::make(),
        ];
    }
}
