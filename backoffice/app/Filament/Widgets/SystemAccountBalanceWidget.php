<?php

namespace App\Filament\Widgets;

use App\Filament\Resources\SystemAccountResource;
use App\Models\BackendSystemAccount;
use <PERSON>zhanSalleh\FilamentShield\Traits\HasWidgetShield;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class SystemAccountBalanceWidget extends BaseWidget
{
    use HasWidgetShield;
    protected static ?string $pollingInterval = '60s';

    protected function getStats(): array
    {
        $systemAccount = BackendSystemAccount::first();
        $balance = $systemAccount ? $systemAccount->balance : 0;

        return [
            Stat::make('Total Commission Earned', number_format($balance) . ' Credits')
                ->description('System Account Balance')
                ->descriptionIcon('heroicon-m-building-library')
                ->color('success')
                ->url(SystemAccountResource::getUrl('index')), // <-- make it clickable

            Stat::make('Last Updated', now()->format('d M Y, h:i A'))
                ->description('Data refreshes every 60 seconds')
                ->color('gray'),
        ];

        // return [
        //     Stat::make('System Account Balance', number_format($balance) . ' Credits')
        //         ->description('Click to view details')
        //         ->descriptionIcon('heroicon-m-arrow-right')

        // ];
    }

    // public static function canView(): bool
    // {
    //     return auth()->user()->hasAnyRole(['superadmin', 'admin']);
    // }
}
