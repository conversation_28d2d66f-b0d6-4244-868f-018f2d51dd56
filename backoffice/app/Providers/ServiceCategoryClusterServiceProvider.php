<?php

namespace App\Providers;

use App\Models\ServiceCategory;
use Filament\Clusters\Cluster;
use Filament\Facades\Filament;
use Illuminate\Support\ServiceProvider;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;

class ServiceCategoryClusterServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        $this->registerServiceCategoryClusters();
    }

    protected function registerServiceCategoryClusters(): void
    {
        try {
        } catch (\Exception $e) {
            \Log::error('Failed to register service category clusters: ' . $e->getMessage());
        }
    }
}
