<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class BackendCurrencyConversionRate extends Model
{
    protected $connection = 'backend_db';
    protected $table = 'currency_conversion_rates';
    
    protected $fillable = [
        'from_currency',
        'to_currency',
        'rate',
        'is_active'
    ];
    
    protected $casts = [
        'rate' => 'decimal:6',
        'is_active' => 'boolean'
    ];
}
