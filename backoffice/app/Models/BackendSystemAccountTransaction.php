<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BackendSystemAccountTransaction extends Model
{
    use SoftDeletes;

    protected $connection = 'backend_db';
    protected $table = 'system_account_transactions';

    protected $fillable = [
        'system_account_id',
        'amount',
        'type',
        'description',
        'reference_id',
        'reference_type',
        'metadata',
    ];

    protected $casts = [
        'amount' => 'integer',
        'metadata' => 'array',
    ];

    /**
     * Get the system account that owns the transaction.
     */
    public function systemAccount(): BelongsTo
    {
        return $this->belongsTo(BackendSystemAccount::class, 'system_account_id');
    }

    /**
     * Get the user associated with the transaction through metadata.
     */
    public function user()
    {
        if (isset($this->metadata['talent_id'])) {
            return $this->belongsTo(BackendUser::class, 'metadata->talent_id', 'id');
        }
        
        return $this->belongsTo(BackendUser::class, 'id', 'id')->whereRaw('1 = 0'); // Empty relationship
    }
}
