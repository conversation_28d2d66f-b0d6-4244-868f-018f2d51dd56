<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;

class BackendSystemAccount extends Model
{
    use SoftDeletes;

    protected $connection = 'backend_db';
    protected $table = 'system_accounts';

    protected $fillable = [
        'name',
        'balance',
    ];

    protected $casts = [
        'balance' => 'integer',
    ];

    /**
     * Get the transactions for the system account.
     */
    public function transactions(): HasMany
    {
        return $this->hasMany(BackendSystemAccountTransaction::class, 'system_account_id');
    }
}
