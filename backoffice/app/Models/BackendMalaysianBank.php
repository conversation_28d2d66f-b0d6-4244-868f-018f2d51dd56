<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class BackendMalaysianBank extends Model
{
    use SoftDeletes, HasFactory;

    protected $connection = 'backend_db';
    protected $table = 'malaysian_banks';

    protected $fillable = [
        'code',
        'name',
        'swift_code',
        'logo_url',
        'account_number_format',
        'account_number_regex',
        'description',
        'is_active',
        'display_order',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];
}
