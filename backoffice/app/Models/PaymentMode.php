<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PaymentMode extends Model
{
    protected $connection = 'backend_db';
    protected $table = 'payment_modes';
    
    protected $fillable = [
        'payment_mode_code',
        'payment_mode_name',
    ];
    
    public function withdrawalRequests()
    {
        return $this->hasMany(WithdrawalRequest::class, 'payment_mode_id');
    }
}
