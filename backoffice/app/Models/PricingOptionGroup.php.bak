<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class PricingOptionGroup extends Model
{
    use SoftDeletes;

    protected $connection = 'backend_db';
    protected $table = 'pricing_option_groups';

    protected $fillable = [
        'name',
        'description',
        'is_active',
        'display_order',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'display_order' => 'integer',
    ];

    public function translations(): HasMany
    {
        return $this->hasMany(PricingOptionGroupTranslation::class, 'pricing_option_group_id');
    }

    public function pricingOptionTypes(): HasMany
    {
        return $this->hasMany(PricingOptionType::class, 'pricing_option_group_id');
    }
    
    public function serviceStyles(): BelongsToMany
    {
        return $this->belongsToMany(ServiceStyle::class, 'service_style_pricing_groups', 'pricing_option_group_id', 'service_style_id')
            ->withTimestamps();
    }
    
    public function serviceLinks(): HasMany
    {
        return $this->hasMany(ServiceLink::class, 'pricing_option_group_id');
    }
}
