<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BackendScheduledOrderReview extends Model
{
    use SoftDeletes;

    /**
     * The database connection that should be used by the model.
     *
     * @var string
     */
    protected $connection = 'backend_db';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'scheduled_order_reviews';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'scheduled_order_id',
        'reviewer_id',
        'reviewee_id',
        'rating',
        'review_text',
        'is_anonymous',
        'is_hidden',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'rating' => 'integer',
        'is_anonymous' => 'boolean',
        'is_hidden' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Get the scheduled order that the review is for.
     */
    public function scheduledOrder(): BelongsTo
    {
        return $this->belongsTo(BackendScheduledOrder::class, 'scheduled_order_id');
    }

    /**
     * Get the user that wrote the review.
     */
    public function reviewer(): BelongsTo
    {
        return $this->belongsTo(BackendUser::class, 'reviewer_id');
    }

    /**
     * Get the user that received the review.
     */
    public function reviewee(): BelongsTo
    {
        return $this->belongsTo(BackendUser::class, 'reviewee_id');
    }

    /**
     * Scope a query to only include visible reviews.
     */
    public function scopeVisible($query)
    {
        return $query->where('is_hidden', false);
    }

    /**
     * Scope a query to only include hidden reviews.
     */
    public function scopeHidden($query)
    {
        return $query->where('is_hidden', true);
    }

    /**
     * Scope a query to only include anonymous reviews.
     */
    public function scopeAnonymous($query)
    {
        return $query->where('is_anonymous', true);
    }

    /**
     * Scope a query to only include non-anonymous reviews.
     */
    public function scopeNonAnonymous($query)
    {
        return $query->where('is_anonymous', false);
    }
}
