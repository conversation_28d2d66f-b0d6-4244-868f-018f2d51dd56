<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BackendUserBankAccount extends Model
{
    use SoftDeletes;

    protected $connection = 'backend_db';
    protected $table = 'user_bank_accounts';

    protected $fillable = [
        'user_id',
        'malaysian_bank_id',
        'account_number',
        'account_holder_name',
        'is_primary',
        'is_verified',
    ];

    protected $casts = [
        'is_primary' => 'boolean',
        'is_verified' => 'boolean',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(BackendUser::class, 'user_id');
    }

    public function bank(): BelongsTo
    {
        return $this->belongsTo(BackendMalaysianBank::class, 'malaysian_bank_id');
    }
}
