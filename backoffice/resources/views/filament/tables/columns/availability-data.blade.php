@php
    $availability = $getRecord();
    $availabilityData = $availability->availability_data ?? [];

    if (is_string($availabilityData)) {
        $availabilityData = json_decode($availabilityData, true) ?? [];
    }
@endphp

<div class="px-4 py-3">
    <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Availability Periods</h3>

    @if (empty($availabilityData))
        <div class="text-xs text-gray-500 dark:text-gray-400">No availability data</div>
    @else
        <div class="space-y-4">
            @foreach ($availabilityData as $dayData)
                <div>
                    <div class="text-xs font-medium text-white-900 dark:text-gray-300 mb-1">
                        @if (isset($dayData['day']))
                            {{ $dayData['day'] }}
                        @elseif($availability->special_date)
                            {{ $availability->special_date->format('Y-m-d') }}
                        @else
                            -
                        @endif
                    </div>

                    @if (isset($dayData['periods']) && is_array($dayData['periods']))
                        <div class="space-y-1 pl-3">
                            @foreach ($dayData['periods'] as $period)
                                <div class="flex items-center text-xs text-gray-600 dark:text-gray-600">
                                    <span class="w-4 h-4 mr-1">
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                            stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                    </span>
                                    {{ $period['start_time'] ?? '--:--' }} - {{ $period['end_time'] ?? '--:--' }}
                                </div>
                            @endforeach
                        </div>
                    @endif
                </div>
            @endforeach
        </div>
    @endif

    @if ($availability->is_recurring)
        <div class="mt-2 text-xs text-blue-500 dark:text-blue-400">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline-block mr-1" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            Recurring
        </div>
    @endif

    @if (!$availability->is_available)
        <div class="mt-2 text-xs text-red-500 dark:text-red-400">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline-block mr-1" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
            Not Available
        </div>
    @endif

    @if ($availability->remarks)
        <div class="mt-2 text-xs text-gray-500 dark:text-gray-400">
            <span class="font-medium">Remarks:</span> {{ $availability->remarks }}
        </div>
    @endif
</div>
