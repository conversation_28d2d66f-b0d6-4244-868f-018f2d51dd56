@php
    $referral = $getRecord();
    $actions = [
        'isRegistered' => 'Registration',
        'isTopUp' => 'Top Up',
        'isProfileCompleted' => 'Profile Completion',
        'isOrdersCompleted' => 'Orders Completed',
        'isMissionsCompleted' => 'Missions Completed',
        'isTalentCertified' => 'Talent Certified'
    ];
@endphp

<div class="px-4 py-3">
    <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Referral Actions</h3>
    <div class="space-y-2">
        @foreach($actions as $key => $label)
            <div class="flex items-center justify-between">
                <span class="text-xs text-gray-600 dark:text-gray-400">{{ $label }}</span>
                <span class="ml-2">
                    @if($referral->{$key})
                        <svg class="h-5 w-5 text-green-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                    @else
                        <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                        </svg>
                    @endif
                </span>
            </div>
        @endforeach
    </div>
</div>
