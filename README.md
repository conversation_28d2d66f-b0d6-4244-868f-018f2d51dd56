# Project Status Overview

This document provides a summary of the web application's development status, based on available documentation.

## What is Done

The following features are implemented and considered complete:

### Backend (API - V0.8)
*   **Authentication:** Sign In, Registration, Log out, Reset Password.
*   **Device Information:** Logging history, IP tracking, Device information. (Backoffice management also implemented)
*   **User's Settings:**
    *   Emergency Contacts
    *   Languages (User's Default Language)
    *   e-KYC (Malaysian & Foreigner)
    *   Payment Method (Configure Bank Account)
    *   Allow 3rd Party Access
    *   About us (Configured in Back Office) (Backoffice management also implemented)
    *   Terms & Conditions (Configured in BackOffice) (Backoffice management also implemented)
    *   Privacy Policy (Configured in BackOffice) (Backoffice management also implemented)
    *   Contact Us (Configured in BackOffice) (Backoffice management also implemented)
    *   User update withdrawal bank information
*   **User Role:** Hybrid role (Gamer/Talent & Customer), Customer role.
*   **Complete Profile / Update Profile Module:** (Backoffice management also implemented for most)
    *   Voice note upload
    *   Biography
    *   Personalities (Multi-lang, Pre-set from Back Office)
    *   Personality Translation (Pre-set from Back Office)
    *   Heights and Weights
    *   Date of Birth
    *   Race (Configurable by BackOffice)
    *   Email
    *   Service Category [Gamer, Talent] (Backoffice Admin Approval Required)
    *   Service Type (BackOffice configuration) (Backoffice Admin Approval Required)
    *   Service Style (Preset & Customize, BackOffice configuration) (Backoffice Admin Approval Required)
    *   Pricing Options (Preset Options Configurable in BackOffice) (Backoffice Admin Approval Required)
    *   Pricing (Pre-set Pricing Configurable in BackOffice) (Backoffice Admin Approval Required)
*   **Level & Experience System:** User's Level (Configurable by Back Office), Icons. (Backoffice management also implemented)
    *   Auto Experience Deduction (Configurable in BackOffice)
*   **Email Verification:** Send verification email (click URL method).
*   **E-KYC:** Malaysian (Fullname, IC details, IC photos), Foreigner (Passport photo, Fullname, Passport Number) - via Tencent E-KYC API.
*   **Talent Filtering & Service Type Filtering Module:**
    *   Service Category
    *   Service Style (Dynamic)
    *   Service Type (Dynamic)
    *   Gender
    *   Languages
    *   Apply Filter button showing N numbers of available talents (N number calculation not yet implemented).
*   **Ordering Module (Backend Only):**
    *   Order Now (Check availability, clash detection, remark, 60s talent acceptance, update acceptance rate, notification). (Backoffice management for this is planned, not done)
    *   Order For Later (Pre-Booking, pick start time, validate time, remark, notification).
    *   Reviews (Upon Order Completion for both customer and talent).
*   **Mission Posting Module (Customer find job seeker):** Select Mission's Service Category, Mission Descriptions, Bounty, Pax Required, Min Level Required, Service Start Date.
*   **Mission Applicant (Approve / Reject):** Allow additional applicants before close/full.
*   **Mission Bookmark:** Users can bookmark missions.
*   **Gamer/Talent Availability Setup:** Configure Monday to Sunday (multiple periods), Special Hours (remarks, date, toggle, higher priority). (Backoffice management also implemented)
    *   Special Toggle (Available, Pause, Not Available)
    *   Dynamic Gamer/Talent Status (Green, Orange, Red)
*   **Dispute Module (Depends on Ordering Module):** Requestor role, Dispute Type (Back Office set), Description, Upload Evidence (images).
*   **Social Posting:** Photos and video uploads, Post Title, Post Description, Location (optional). (Backoffice management also implemented)
*   **Chat Module:** (Backoffice management for monitoring also implemented)
    *   Basic Messaging (Texts, Emojis)
    *   Image & Video Transfer
    *   Back Office Monitoring (Voice recording, Text Messaging)
    *   Voice call and Video Call Feature (Include BackOffice Monitoring)
*   **Finance (Platform Credit) Mission X Coin:**
    *   Top up (BillPlz, configurable gateways & rates from backoffice). (Backoffice management also implemented)
    *   Withdrawal (E-KYC mandatory, email verification for invoice/acknowledgment; Platform Credit for Gamers/Talents, Gift to Fiat for all users; rates configured in BackOffice).
    *   User Transactions Ledger.
    *   Export to CSV in BackOffice (Bank Templates).
    *   Approval and Rejection (with reason) For BackOffice.
*   **Gift Shop:**
    *   User Inventory
    *   Buy & Sell (Purchase with Platform Credits, Sell Gift to exchange to credit)
    *   Gift Items (Preset on BackOffice, dynamic pricing)
    *   User's Gift transaction history
    *   Users able to purchase gift with Referral Points
    *   Users able to sell gifts to Platform Credits or fiat
*   **Referral System:**
    *   Track referee list
    *   Referral Point System (Customizable earning rules in BackOffice: e.g., for registration, top-up, profile completion, order completion, mission posting)
    *   Referee action tracking (registration, profile completion, top-up, spending)
    *   Referral Points Redemption History (links to Gift Shop)
*   **Feedback Form:** Topic (Pre-Config at Back Office), Description, File Upload. (Backoffice management also implemented)
*   **Notification:** Push Notification, New Order.

### Backend (API - V1.0)
*   **Voice Note:** Talent able to upload max of 20s.

## What is in Progress

The following features are currently under active development:

### Backend (API - V0.8)
*   **Complete Profile / Update Profile Module:**
    *   Photos and one video upload (max 10 seconds) - `Development Stage: Coding/Implementation`
*   **Talent Filtering & Service Type Filtering Module:**
    *   Level Range (Dynamic) - `Development Stage: Coding/Implementation`
    *   Price (Dynamic) - `Development Stage: Coding/Implementation`
*   **Referral System:**
    *   Send Notification to referrer when referee completed any action - `Development Stage: Coding/Implementation`

## What Has Not Been Implemented (Planned/Future)

The following features are part of the project roadmap but development has not yet commenced or is planned for future versions:

### Backend (API - V0.8)
*   **Talent Filtering & Service Type Filtering Module:**
    *   Dynamic N number of available talents for "Apply Filter (N)" button.
*   **Dispute Module:**
    *   Upload Video (optional, max of 1 video).
*   **Chat Module:**
    *   Flagged text detection.
*   **Notification:**
    *   When fav talent/customer online, send a push notification (toggle on/off).
    *   Order In progress notifications.
    *   Cancel Order notifications.
    *   Reminder notifications.

### Backoffice
*   **Order Module:** Full implementation for backoffice administration including:
    *   OrderResource (List, view, manage, filter orders).
    *   OrderAcceptanceLogResource (Track acceptance patterns).
    *   ReviewResource (Manage and moderate order reviews).
    *   Associated Models (Order, OrderAcceptanceLog, Review) connecting to the backend database.
    *   UI Components for order lists, detail views, and review management.
    *   _Purpose: To provide administrative tools for monitoring and managing the order process._

### Backend (API - V1.0 Features Not Marked as Done)
*   **Clan Module:**
    *   Clan separation by games.
    *   Team assignment to clans (approval required).
    *   Clan creation by any user.
    *   Clan roles (Leader, Manager, Ambassador).
    *   Clan leader actions (kick/add teams).
    *   Clan name and icon.
    *   Clan team management.
    *   Clan announcement feature (push notification required).
    *   Invite-only system for clans.
    *   Team roles (Leader, Members, Captain, Coach, Manager, Analyzer, backup - dynamic settings).
    *   _Purpose: To enable user-created and managed groups for team play and community._
*   **Badges For Every Game (Service Type) Achievement Module:**
    *   Based on completed orders per game.
    *   Based on completed coach training.
    *   _Purpose: To reward user activity and achievements within specific games._
*   **Wishlist:**
    *   Topic, Description.
    *   Wishlist action tracker (frontend progress bar).
    *   Leaderboard of supporters.
    *   Deeplink to talent profile.
    *   Custom Actions (e.g., game-specific achievements, requires game API integration if available).
    *   Category (Esports, Economic benefits, Skill Growth).
    *   _Purpose: To allow users to set and track goals, potentially funded or supported by others._

### Backend (API - V1.1 Features)
*   **OCR / Text Recognition:**
    *   Determine win/loss from uploaded game results.
    *   Update both party points (In Game Points).
    *   OCR detection for tournament stats.
    *   _Purpose: To automate game result processing and stat updates._
*   **Game Experience Module:**
    *   User-specific game points in Mission X.
    *   Experience determined by challenges with higher rank players.
    *   Stats on win/loss against different ranks.
    *   _Purpose: To create an internal ranking and experience system based on game performance._
*   **Clan Tournament Engine:**
    *   Tournament creation by Clan Leader.
    *   Types of Tournaments.
    *   Competitive mode.
    *   Tournament Settings (Random Battle, Custom Battle).
    *   Team WIN/LOSE rate.
    *   Screenshot upload for match results.
    *   Game API or OCR for feedback.
    *   Integrate APIs (Steam, Moontoon, Riot, etc.).
    *   _Purpose: To allow clans to organize and participate in tournaments._
*   **Clan Module Updates:**
    *   Clan battle.
    *   Leaderboard.
    *   _Purpose: To expand clan functionalities with competitive and ranking features._