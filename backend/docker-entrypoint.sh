#!/bin/sh

# Wait for MySQL (backend-db or backoffice-db depending on service)
if [ "$DB_HOST" = "backend-db" ]; then
  echo "Waiting for backend-db..."
  while ! nc -z backend-db 3306; do
    sleep 1
  done
  echo "backend-db is up!"
elif [ "$DB_HOST" = "backoffice-db" ]; then
  echo "Waiting for backoffice-db..."
  while ! nc -z backoffice-db 3306; do
    sleep 1
  done
  echo "backoffice-db is up!"
fi

# Wait for Redis
echo "Waiting for Redis..."
while ! nc -z redis 6379; do
  sleep 1
done
echo "Redis is up!"

# Wait for RabbitMQ
echo "Waiting for RabbitMQ..."
while ! nc -z rabbitmq 5672; do
  sleep 1
done
echo "RabbitMQ is up!"

# Install dependencies (only if not already installed)
if [ ! -d "vendor" ]; then
  echo "Installing Composer dependencies..."
  composer install --optimize-autoloader --no-dev
fi

# Run migrations and start server
echo "Running migrations..."
php artisan migrate --force



echo "Running Optimizations"
php artisan optimize:clear & 
php artisan optimize

echo "Update Search Indices"
php artisan scout:import "App\Models\User"

echo "Starting Laravel server with queue listener..."
php artisan queue:work --tries=3 &
php artisan serve --host=0.0.0.0 --port=8000

