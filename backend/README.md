 # Mission X Backend API

## Symbols Appendix
- ✅ (Implemented in Backend)
- 🔵 (Implemented Backoffice Management)
- ⌚ (Work in Progress)

# V0.8 
## Authentication ✅
- Sign In  ✅
- Registration ✅
- Log out ✅
- Reset Password ✅

## Device Information✅🔵
- Logging history✅
- IP tracking✅
- Device infomration✅
  
## User's Settings
- Emergency Contacts ✅
- Languages (User's Default Language) ✅
- e-KYC (Refer to E-KYC Section)
- Payment Method (Configure Bank Account, Mission X DB) ✅
- Allow 3rd Party Access (Simple Boolean, Mission X DB) ✅
- About us (Configured in Back Office, Misison X DB) ✅🔵
- Terms & Conditoions (Configured in BackOffice, Mission X DB) ✅🔵
- Privacy Policy (Configured in BackOffice, Mission X DB) ✅🔵
- Contact Us (Configured in BackOffice, Mission X DB) ✅🔵
- User update withdrawal bank information ✅
  
## User Role
- Hybrid role (Determined by profile completion[Gamer, Talent] <<<AT LEAST ONE OR MORE SERVICES SET UP>>>) (Able to be Customer role as well)✅
- Customer (No Service Profile Setup) ✅

## Complete Profile / Update Profile Module🔵
- Photos and one video upload(max 10seconds)⌚
- Voice note upload✅
- Biography✅
- Personalities (Multi-lang) (Pre-set from Back Office) ✅
- Personality Translation (Pre-set from Back Office) ✅
- Heights and Weights✅
- Date of Birth ✅
- Race (Configurable by BackOffice)✅
- Email✅

  Skippable Section for Customer, except Talent and Gamers   ( BackOffice Admin Approval Required!!!)✅🔵
    - Service Category [Gamer ,Talent]✅🔵
    - Service Type [MLBB, CS2, Valorant and etc] (BackOffice configuration, Required elements, Icon)✅🔵
    - Service Style [Casual, Competitive, Professional and etc] (Preset Service Style able to configure in the BackOffice, Customize Service Style bind to Service Type) (Some Service type have minimum access level, BackOffice able to manual bypass for certain users)✅🔵
    - Pricing Options [Hourly Rates, Per Service Rate and etc] (Preset Options Configurable in BackOffice)✅🔵
    - Pricing (Pre-set Pricing Configurable in BackOffice)✅🔵

- Required to prompt user to update their Game ID if they are Expert / Professional (Manual verification are required)

## Level & Experience System✅🔵
- User's Level (Configurable by Back Office)✅🔵
  - eg. Level 1 > 0 EXP✅
  - Level 2 > 100 EXP✅
  - Level 3 > 500 EXP✅
- Come with Icon (Configurable at BackOffice)✅🔵

- Auto Experience Deduction (Requires Deduction Engine, Configurable in BackOffice)✅

## Email Verification✅
- Send verification email (click url method)✅

## E-KYC
- Using Tencent E-KYC API
- Malaysian (Fullname, Last 6 Digit of IC, Photo of IC Front and Back)✅
- Foreigner (Passport Photo, Full Name, Passport Number)✅

## Talent Filtering & Service Type Filtering Module ✅
- Service Category✅
- Service Style (Dynamic)✅
- Service Type (Dynamic)✅
- Level Range (Dynamic) ⌚
- Price (Dynamic) ⌚
- Gender ✅
- Languages✅

- Apply Filter button Showing N Numbers of Available Talents (eg. Apply Filter (N)) N < Refers to available number of talents / gamers) <----- Dynamic ✅ (N number of talents not yet implemented)

## Gamer Game ID Setup module
- Gamer input gameID, steam ID , user ID, each games has different input
- Allow users to update their game ID in settings (NEW) (This will be used for OCR in V1.1)
- Users can add more than one game profile, not visible to public

## Ordering Module (This depends on Complete Profile/ Service Setup)
- Order Now✅🔵
    - Check availability each time customer add quantity✅
    - Make sure there are no clashing with pre-booking orders, else queue up, check other suggested talent, or switch to pre-book
    - Remark✅
    - 60 Sec waiting for talent to accept✅
    - Update Acceptance rate of gamer or talent✅
    - Notification required✅

- Order For Later✅
    - Pre-Booking quantity selection
    - Pick Service start time✅
    - Validate Time does not clash with talent/ gamer availability hours ✅
    - Remark✅
    - Notification required  ✅
- Reviews (Upon Order Completion)✅
      - Both customer and talents/gamers are able leave reviews after order completion✅

Ordering Flow
Customer Make Order -> Talent/Gamer Accept Order (Notification) -> Talent/Gamer Accept Order (Auto Accept / Manual Accept) -> Talent/Gamer Complete Order -> Dispute? Yes (Customer Service intervention) -> Order Refund/ Complete/ Partial refund -> Review
                                                                                                                            -> Dispute? No  -> Order Completed -> Review
*** Refer Dipsute Module for More

## Mission Posting Module (Customer find job seeker) (Required Customer to Manually Accept Applicant) ✅
- Select Mission's Service Category ✅
- Mission Descriptions (100 words) ✅
- Bounty (Mission X Coins/ head) ✅
- Pax Required ✅
- Min Level Required ✅
- Service Start Date ✅

## Mission Applicant (Approve / Reject)✅
- Allow additional applicants to apply before application close or full ( Full meaning that customer has approved pax required) ✅
 

## Mission Bookmark✅
- Users are able to bookmark the missions✅

## Gamer/Talent Availability Setup✅🔵
- Allow Talents or Gamers to configure only✅
- Configure Monday to Sunday✅
        - Each day From Time to Time (eg. For Monday, (10am - 6pm), For Tuesday (11am-7pm))✅
        - Able to configure multiple Time Period per day ( eg. Monday (10am-3pm), (5pm-10pm))✅
- Able to Config Special Hours✅
      - Special Hours Remarks✅
      - Select Date✅
      - Toggle On - Set at least one Time Period✅
      - Toggle Off - Not Available, Special Hours has higher priority than availabilty Setup✅

Special Toggle (Available, Pause(30 Min, 1 Hours, 2Hours), Not Avaialable) (This is to temporary pause receiving orders)✅
- Dynamic Gamer/Talent Status (Green, Orange, Red)✅
      - Green (Available)✅
      - Orange (Engaging/ Busy) <<------------ Engaging Order, automatically turn Orange until Order Completed has clicked by talent or gamers✅
      - Red (Not Available)✅

## Dispute Module (Depends on Ordering Module)✅
- Requestor role (Talent/Gamer or Customer)✅
- Dispute Type (Topic Set by Back Office)✅
- Dispute Description✅
- Upload Evidence Photos or Sceenshot ( max 3 Images)✅
- Upload Video (optional, max of 1 videos)

- Result will be decided by backoffice admin✅

## Social Posting✅🔵
- Photos and video uploads (max of 5 photos, 1 videos)✅
- Post Title (45 Chars)✅
- Post Description (100 words)✅
- Location (optional)✅
  
## Chat Module🔵
- Basic Messaging (Support Texts and Emojis)✅
- Image & Video Transfer✅
- Back Office Monitoring (Voice recording, Text Messaging)✅
- Voice call and Video Call Feature (Include BackOffice Monitoring)✅
- Flagged text detection (Implement Later)✅

## Finance (Platform Credit) Mission X Coin✅
- Top up🔵
    - Purchase platform credit with fiat currency (Current Payment Gateway Provider : BillPlz) (Support adding multiple payment gateway channel with custom rate(eg. Fiat currency -> Platform Credit), Configurable from backoffice only)✅
        - DATABASE LEVEL LOCK BEFORE UPDATING USER'S NEW BALANCE (IMPORTANT)✅
- Withdrawal (E-KYC is mandatory!!! Email verification is required -- For Sending Invoice / Acknowledgment)✅
    - For Platform Credit( Only Gamers and Talents able to make Platform Credit Withdrawal to Fiat)✅
    - For Gift (Customers, Gamers, Talents able to sell gift to fiat currency) ( Rates are configured in BackOffice) (Support Multi fiat Currency)✅
- User Transcations Ledger (Top up Platform Credits, Purchases, Ordering, Earning , Withdrawal of Gift to Fiat Currency(All Users),  Platform Credit to Fiat Currency)✅
- Export to csv file in BackOffice (Hong Leong Bank Template and Cimb Bank Template)✅
- Approval and Rejection(Reject reason) For BackOffice✅
  

## Gift Shop 
- User Inventory✅
- Buy & Sell (Purchase with Platform Credits✅, Sell Gift to exchange to credit)✅
- Gift Items (Preset on the BackOffice, Item Purchase and Selling Price, Dynamic)✅
- User's Gift transaction history✅
- Users able to purchase gift with Referral Points (Refer to Referral System)✅
- Users able to sell gifts to Platform Credits✅ or fiat
- Send Notification to Receiver

## Referral System
- Allow Referrer to track referee list✅
- Referral Point System (Customize Referral Point earning at BackOffice)✅
      - Eg. Earn 1 point if referee has registered ✅
      - Eg. Earn 2 points if referee has top up ✅
      - Eg. Earn 3 Points if referee has completed profile✅
      - Eg. Earn 5 Points if referee has completed 5 orders✅
      - Eg. Earn 5 Points if referee has posted 5 Missions✅

For each referee completed action tracking, add columns to user_referrals table for each action with boolean ✅

- Referee action tracking
  - Track Referee registration ✅
  - Track Referee profile completion✅
  - Track Referee Top up✅
  - Track Referee Spending✅
- Referral Points Redeemption History (Refer to Gift Shop)✅
- Send Notification to referrer when referee completed any action ⌚

## Feedback Form  ✅🔵
- Topic (Pre-Config at Back Office)✅
- Description (Fill Up by Users)✅
- File Upload ✅

## Notification
 - Push Notification✅
 - When fav talent/customer online, send a push notification (toggle on/off)
 - New Order ✅
 - Order In progress
 - Cancel Order
 - Reminder

# V1.0

## Voice Note ✅
- Talent able to upload max of 20s✅

## Clan Module
- Each Clans are seperated from each Games 
- Many Teams can be assign to a Clan, approval from Clan are required
- Any user are able to create a clan
- Clan has roles (Clan Leader, Clan Manager, Ambassador)
- Clan leader able to kick or add Teams
- Clan has clan name, and able to set their own clan icon
- Clan Are able to manage Teams
- Clan has annoucement feature (Can select all teams or specify teams) Push notification required
- Clan manage teams
- Invite only (Pull in users, these users will be in pending mode, waiting to be assign to team)

- Team has roles (Team Leader(Max of 1), Team Members(max of 4 --- Depends on games, settings must be dynamic), Captain, Coach(Can have many), Manager, Analyzer， backup)


## Badges For Every Games (Service Type) ACHIEVEMENT MODULE
- Completed numbers of orders per games (10,20,50,100,200,500,1000)
- Completed Coach training

## Wishlist
- Topic
- Description
- Wishlist action tracker (for frontend progress bar) Percentage %
- Leaderboard wish of supporter for particular action
- Deeplink creation, to redirect to talent profile
- Custom Actions (Eg. MLBB solo ranking reaches mythical level, Valorant headshot rate increased to 25%) Required to get stats from game API if available
- Category (Esports, Economic benefits, Skill Growth)


# V1.1

## OCR / Text Regconization (Possibly without using GPU) Depends on Gamer Game ID Setup Module
- Any party that upload Game result can Determine which party win/lose
- Update both party points. In Game Points
- OCR will detect win lose and update the tournament stats

## Game Experience Module
- Each Users will have their own game's point in Mission X
- Game Experience will be determined by challenge with higher rank players
- Gamer's stats on win lose to Higher or lower rank players

## Clan Tournament Engine
- Tournament can be created by Clan Leader
- Types of Tournaments
- Competitive mode
- Tournament Settings ( Random Battle, Custom Battle)
- Each Team will have WIN LOSE rate
- End of each match will required both party to upload screenshot of result
- Each game has its own api or OCR to retrieve feedback
- Integrate APIs (Steam, Moontoon, Riot, etc)

## Clan Module Updates
- Clan battle
- Leaderboard
