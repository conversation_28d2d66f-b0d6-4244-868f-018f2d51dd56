openapi: 3.0.0
info:
  title: Carousel Module API
  description: API for managing carousel slides
  version: 1.0.0

paths:
  /carousel-slides:
    get:
      summary: Get all carousel slides
      tags:
        - Carousel
      security:
        - bearerAuth: []
      responses:
        '200':
          description: A list of carousel slides
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CarouselSlide'
    post:
      summary: Create a new carousel slide
      tags:
        - Carousel
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/CarouselSlideRequest'
      responses:
        '201':
          description: Created carousel slide
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CarouselSlide'
        '422':
          description: Validation errors
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'

  /carousel-slides/{id}:
    get:
      summary: Get a specific carousel slide
      tags:
        - Carousel
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Carousel slide details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CarouselSlide'
        '404':
          description: Carousel slide not found
    put:
      summary: Update a carousel slide
      tags:
        - Carousel
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/CarouselSlideUpdateRequest'
      responses:
        '200':
          description: Updated carousel slide
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CarouselSlide'
        '404':
          description: Carousel slide not found
        '422':
          description: Validation errors
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
    delete:
      summary: Delete a carousel slide
      tags:
        - Carousel
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Carousel slide deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Carousel slide deleted successfully
        '404':
          description: Carousel slide not found

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    MediaFile:
      type: object
      properties:
        original:
          type: string
          example: carousel_slides/original/0/2f81f8a8-3b3e-4f5c-9d7e-1d2c3a4b5c6d.jpg
        optimized:
          type: string
          example: carousel_slides/optimized/0/8f7e6d5c-4b3a-2c1d-0e9f-8a7b6c5d4e3f.jpg
        filename:
          type: string
          example: welcome_banner.jpg
        mime_type:
          type: string
          example: image/jpeg
        size:
          type: integer
          example: 512000
        extension:
          type: string
          example: jpg
        dimensions:
          type: object
          properties:
            width:
              type: integer
              example: 1920
            height:
              type: integer
              example: 1080
        device_info:
          type: string
          example: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36...

    CarouselSlide:
      type: object
      properties:
        id:
          type: integer
          example: 1
        title:
          type: string
          example: Welcome to Mission X
        content:
          type: string
          example: Join our gaming community today
        media_files:
          type: array
          items:
            $ref: '#/components/schemas/MediaFile'
        target_url:
          type: string
          example: https://missionx.com/signup
        is_clickable:
          type: boolean
          example: true
        display_order:
          type: integer
          example: 1
        is_active:
          type: boolean
          example: true
        created_at:
          type: string
          format: date-time
          example: 2023-06-01T12:00:00.000000Z
        updated_at:
          type: string
          format: date-time
          example: 2023-06-01T12:00:00.000000Z

    CarouselSlideRequest:
      type: object
      properties:
        title:
          type: string
          example: Welcome to Mission X
        content:
          type: string
          example: Join our gaming community today
        media_files[]:
          type: array
          items:
            type: string
            format: binary
        target_url:
          type: string
          example: https://missionx.com/signup
        is_clickable:
          type: boolean
          example: true
        display_order:
          type: integer
          example: 1
        is_active:
          type: boolean
          example: true

    CarouselSlideUpdateRequest:
      type: object
      properties:
        title:
          type: string
          example: Updated Title
        content:
          type: string
          example: Updated content for the slide
        media_files[]:
          type: array
          items:
            type: string
            format: binary
        target_url:
          type: string
          example: https://missionx.com/new-promotion
        is_clickable:
          type: boolean
          example: true
        display_order:
          type: integer
          example: 2
        is_active:
          type: boolean
          example: true

    ValidationError:
      type: object
      properties:
        message:
          type: string
          example: The given data was invalid.
        errors:
          type: object
          additionalProperties:
            type: array
            items:
              type: string
          example:
            title: ["The title field is required."]
            media_files: ["The media files field is required."]
