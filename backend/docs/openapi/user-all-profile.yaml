openapi: 3.0.0
info:
  title: Mission X User Complete Profile API
  version: 1.0.0
  description: Complete user profile endpoints for Mission X mobile application

servers:
  - url: http://*************:8000/api
    description: Development server

paths:
  /user/all-profile:
    get:
      tags:
        - User Profile
      summary: Get complete user profile
      description: Retrieves the complete profile of the authenticated user with all available details
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Complete profile retrieved successfully
          headers:
            Content-Type:
              schema:
                type: string
                example: application/json
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompleteUserProfile'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "An error occurred while fetching user profile"
                  errors:
                    type: object
                    properties:
                      server:
                        type: array
                        items:
                          type: string
                        example: ["Detailed error message"]

  /user/all-profile/{id}:
    get:
      tags:
        - User Profile
      summary: Get complete user profile by ID
      description: Retrieves the complete profile of a user by their ID with all available details. Only accessible to authenticated users.
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: ID of the user to retrieve
          schema:
            type: integer
      responses:
        '200':
          description: Complete profile retrieved successfully
          headers:
            Content-Type:
              schema:
                type: string
                example: application/json
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompleteUserProfile'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          description: User not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "User not found"
                  errors:
                    type: object
                    properties:
                      user:
                        type: array
                        items:
                          type: string
                        example: ["User not found"]
        '500':
          description: Server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "An error occurred while fetching user profile"
                  errors:
                    type: object
                    properties:
                      server:
                        type: array
                        items:
                          type: string
                        example: ["Detailed error message"]

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  responses:
    Unauthorized:
      description: Unauthorized
      content:
        application/json:
          schema:
            type: object
            properties:
              message:
                type: string
                example: "Unauthenticated"
  schemas:
    CompleteUserProfile:
      type: object
      properties:
        id:
          type: integer
          example: 9
        mobile_number:
          type: string
          example: "+60125187950"
        email:
          type: string
          nullable: true
          example: "<EMAIL>"
        country_code:
          type: string
          example: "MY"
        nickname:
          type: string
          example: "Qiule"
        gender:
          type: string
          example: "Male"
        date_of_birth:
          type: string
          format: date-time
          example: "2025-05-04T00:00:00.000Z"
        constellation:
          type: string
          example: "Taurus"
          description: "The user's zodiac sign based on date of birth"
        profile_picture:
          type: string
          nullable: true
          example: null
        profile_media:
          type: object
          properties:
            video:
              type: string
              nullable: true
              example: null
            thumbnail:
              type: string
              nullable: true
              example: null
            photos:
              type: array
              items:
                type: object
                properties:
                  path:
                    type: string
                    example: "users/9/profile/photos/1617123456_1.jpg"
                  order:
                    type: integer
                    example: 1
              example: []
        voice_note:
          type: string
          nullable: true
          example: null
        biography:
          type: string
          nullable: true
          example: null
        role:
          type: string
          enum: [customer, talent]
          example: "customer"
        experience:
          type: integer
          example: 0
        height:
          type: number
          format: float
          nullable: true
          example: 180
        weight:
          type: number
          format: float
          nullable: true
          example: 65
        default_language:
          type: array
          items:
            type: object
            properties:
              id:
                type: integer
                example: 1
              slug:
                type: string
                example: "cn"
              name:
                type: string
                example: "Chinese"
        allow_3rd_party_access:
          type: boolean
          example: false
        referral_code:
          type: string
          example: "4382AF0A"
        personalities:
          type: array
          items:
            type: object
            properties:
              id:
                type: integer
                example: 1
              name:
                type: string
                example: "Arrogant"
              description:
                type: string
                nullable: true
                example: "Arrogant"
        level:
          type: object
          nullable: true
          properties:
            id:
              type: integer
              example: 1
            level:
              type: integer
              example: 1
            name:
              type: string
              example: "Beginner"
            description:
              type: string
              example: "Just starting out"
            indicator_image:
              type: string
              nullable: true
              example: null
        is_follow:
          type: boolean
          example: false
        total_followers:
          type: integer
          example: 10
        race:
          type: object
          nullable: true
          properties:
            id:
              type: integer
              example: 2
            name:
              type: string
              example: "Chinese"
            description:
              type: string
              nullable: true
              example: null
        services:
          type: array
          items:
            type: object
            properties:
              id:
                type: integer
                example: 1
              total_person_order:
                type: integer
                example: 20
              created_at:
                type: string
                format: date-time
                example: "2025-04-25T04:11:36.000000Z"
              updated_at:
                type: string
                format: date-time
                example: "2025-04-24T20:11:51.000000Z"
              service_category:
                type: object
                nullable: true
                properties:
                  id:
                    type: integer
                    example: 1
                  name:
                    type: string
                    example: "Gamer"
                  description:
                    type: string
                    example: "Only Gamers"
              service_type:
                type: object
                nullable: true
                properties:
                  id:
                    type: integer
                    example: 4
                  name:
                    type: string
                    example: "LoL"
                  description:
                    type: string
                    example: "League of Legends"
                  icon_path:
                    type: string
                    example: "gift_items/optimized/0/a0295581-438d-4725-85af-6c93af2e6a91.jpg"
                  required_elements:
                    type: string
                    example: "LOL"
                  created_at:
                    type: string
                    format: date-time
                    example: "2025-03-27T00:44:06.000000Z"
                  updated_at:
                    type: string
                    format: date-time
                    example: "2025-03-27T00:44:06.000000Z"
                  deleted_at:
                    type: string
                    format: date-time
                    nullable: true
                    example: null
              service_styles:
                type: array
                items:
                  type: object
                  properties:
                    id:
                      type: integer
                      example: 2
                    name:
                      type: string
                      example: "Casual"
                    description:
                      type: string
                      example: "Casual"
                    min_level_id:
                      type: integer
                      example: 1
                    can_bypass:
                      type: boolean
                      example: false
                    recommended_price:
                      type: number
                      example: 0
                    preset_price:
                      type: number
                      example: 10
                    display_order:
                      type: integer
                      example: 2
                    created_at:
                      type: string
                      format: date-time
                      example: "2025-05-18T18:58:48.000000Z"
                    updated_at:
                      type: string
                      format: date-time
                      example: "2025-05-18T18:58:48.000000Z"
              pricing_options:
                type: object
                nullable: true
                properties:
                  id:
                    type: integer
                    example: 1
                  created_at:
                    type: string
                    format: date-time
                    example: "2025-04-25T04:11:36.000000Z"
                  updated_at:
                    type: string
                    format: date-time
                    example: "2025-04-25T04:11:36.000000Z"
                  pricing_option_type:
                    type: object
                    nullable: true
                    properties:
                      id:
                        type: integer
                        example: 2
                      name:
                        type: string
                        example: "Standard Session"
                      description:
                        type: string
                        example: "Regular Gaming Session"
                      has_duration:
                        type: boolean
                        example: false
                      unit:
                        type: string
                        nullable: true
                        example: null
                      quantity:
                        type: integer
                        nullable: true
                        example: null
                      created_at:
                        type: string
                        format: date-time
                        example: "2025-04-13T23:51:45.000000Z"
                      updated_at:
                        type: string
                        format: date-time
                        example: "2025-04-13T23:51:45.000000Z"
        user_availabilities:
          type: array
          items:
            type: object
          example: []
        reviews:
          type: array
          items:
            type: object
          example: []
        social_posts:
          type: array
          items:
            type: object
          example: []
        missions:
          type: array
          items:
            type: object
          example: []
        game_profiles:
          type: array
          items:
            type: object
            properties:
              id:
                type: integer
                example: 1
              service_type_id:
                type: integer
                example: 4
              identifiers:
                type: object
                example: {"lol_username": "player123"}
              is_hidden:
                type: boolean
                example: false
              status:
                type: string
                enum: [pendingReview, Reviewed]
                example: "Reviewed"
              created_at:
                type: string
                format: date-time
                example: "2025-04-25T04:11:36.000000Z"
              updated_at:
                type: string
                format: date-time
                example: "2025-04-25T04:11:36.000000Z"
              service_type:
                type: object
                nullable: true
                properties:
                  id:
                    type: integer
                    example: 4
                  name:
                    type: string
                    example: "LoL"
                  description:
                    type: string
                    example: "League of Legends"
                  icon_path:
                    type: string
                    example: "service_types/lol_icon.jpg"
                  required_elements:
                    type: string
                    example: "LOL"
