openapi: 3.0.0
info:
  title: Races API
  description: API endpoints for retrieving race data
  version: 1.0.0
servers:
  - url: https://api.missionx.com.my/api
    description: Production server
paths:
  /races:
    get:
      summary: Get all races
      description: Retrieves a list of all active races
      operationId: getRaces
      tags:
        - Races
      responses:
        '200':
          description: A list of races
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Race'
components:
  schemas:
    Race:
      type: object
      properties:
        id:
          type: integer
          format: int64
          example: 1
          description: Unique identifier for the race
        name:
          type: string
          example: "Malay"
          description: Name of the race in the current locale
        description:
          type: string
          example: "Malay race description"
          description: Description of the race in the current locale
      required:
        - id
        - name
