openapi: 3.0.0
info:
  title: User Service API
  description: API for managing user services
  version: 1.0.0
paths:
  /user/services/user/{userId}/approved:
    get:
      tags:
        - User Services
      summary: Get approved services for a user
      description: Returns all approved services offered by a specific user with optional filtering
      security:
        - bearerAuth: []
      parameters:
        - name: userId
          in: path
          required: true
          description: ID of the user to get services for
          schema:
            type: integer
        - name: is_active
          in: query
          required: false
          description: Filter by active status
          schema:
            type: boolean
        - name: service_type_id
          in: query
          required: false
          description: Filter by service type ID
          schema:
            type: integer
        - name: service_style_id
          in: query
          required: false
          description: Filter by service style ID
          schema:
            type: integer
      responses:
        '200':
          description: Successful operation
          headers:
            Content-Type:
              schema:
                type: string
                example: application/json
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserService'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Unauthenticated"
        '404':
          description: User not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "User not found"
