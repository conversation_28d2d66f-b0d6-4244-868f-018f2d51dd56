openapi: 3.0.0
info:
  title: Homepage API
  description: API endpoints for the homepage
  version: 1.0.0

paths:
  /api/homepage:
    get:
      summary: Get all homepage data
      description: Returns all homepage data in a single API call
      parameters:
        - name: per_page
          in: query
          description: Number of talents per page
          required: false
          schema:
            type: integer
            default: 10
        - name: limit
          in: query
          description: Number of service types to return
          required: false
          schema:
            type: integer
            default: 10
      responses:
        '200':
          description: All homepage data
          content:
            application/json:
              schema:
                type: object
                properties:
                  new_talents:
                    $ref: '#/components/schemas/PaginatedTalentResponse'
                  recommended_talents:
                    $ref: '#/components/schemas/PaginatedTalentResponse'
                  online_talents:
                    $ref: '#/components/schemas/PaginatedTalentResponse'
                  available_missions_count:
                    type: integer
                    example: 42
                  popular_service_types:
                    type: array
                    items:
                      $ref: '#/components/schemas/ServiceType'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: "Failed to retrieve homepage data"
                  message:
                    type: string
                    example: "A database error occurred while retrieving homepage data"
                  details:
                    type: string
                    example: "SQLSTATE[42S22]: Column not found: 1054 Unknown column 'reviews.talent_id'"
  /api/homepage/new-talents:
    get:
      summary: Get new registered talents
      description: Returns a list of newly registered talents
      parameters:
        - name: per_page
          in: query
          description: Number of talents per page
          required: false
          schema:
            type: integer
            default: 10
        - name: page
          in: query
          description: Page number
          required: false
          schema:
            type: integer
            default: 1
      responses:
        '200':
          description: A list of newly registered talents
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedTalentResponse'

  /api/homepage/recommended-talents:
    get:
      summary: Get recommended talents
      description: Returns a list of recommended talents based on acceptance rate and reviews
      parameters:
        - name: per_page
          in: query
          description: Number of talents per page
          required: false
          schema:
            type: integer
            default: 10
        - name: page
          in: query
          description: Page number
          required: false
          schema:
            type: integer
            default: 1
      responses:
        '200':
          description: A list of recommended talents
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedTalentResponse'

  /api/homepage/online-talents:
    get:
      summary: Get online talents
      description: Returns a list of currently available talents
      parameters:
        - name: per_page
          in: query
          description: Number of talents per page
          required: false
          schema:
            type: integer
            default: 10
        - name: page
          in: query
          description: Page number
          required: false
          schema:
            type: integer
            default: 1
      responses:
        '200':
          description: A list of online talents
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedTalentResponse'

  /api/homepage/available-missions-count:
    get:
      summary: Get available missions count
      description: Returns the count of available missions
      responses:
        '200':
          description: Count of available missions
          content:
            application/json:
              schema:
                type: object
                properties:
                  available_missions_count:
                    type: integer
                    example: 42

  /api/homepage/popular-service-types:
    get:
      summary: Get popular service types
      description: Returns a list of popular service types with their icons
      parameters:
        - name: limit
          in: query
          description: Number of service types to return
          required: false
          schema:
            type: integer
            default: 10
      responses:
        '200':
          description: A list of popular service types
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ServiceType'

components:
  schemas:
    PaginatedTalentResponse:
      type: object
      properties:
        current_page:
          type: integer
          example: 1
        data:
          type: array
          items:
            $ref: '#/components/schemas/Talent'
        first_page_url:
          type: string
          example: "http://localhost:8000/api/homepage/new-talents?page=1"
        from:
          type: integer
          example: 1
        last_page:
          type: integer
          example: 5
        last_page_url:
          type: string
          example: "http://localhost:8000/api/homepage/new-talents?page=5"
        next_page_url:
          type: string
          nullable: true
          example: "http://localhost:8000/api/homepage/new-talents?page=2"
        path:
          type: string
          example: "http://localhost:8000/api/homepage/new-talents"
        per_page:
          type: integer
          example: 10
        prev_page_url:
          type: string
          nullable: true
          example: null
        to:
          type: integer
          example: 10
        total:
          type: integer
          example: 50
    
    Talent:
      type: object
      properties:
        id:
          type: integer
          example: 1
        nickname:
          type: string
          example: "TalentName"
        profile_picture:
          type: string
          example: "path/to/image.jpg"
        level:
          type: object
          properties:
            id:
              type: integer
              example: 1
            level:
              type: integer
              example: 5
            name:
              type: string
              example: "Professional"
        services:
          type: array
          items:
            type: object
            properties:
              id:
                type: integer
                example: 1
              service_type:
                type: object
                properties:
                  id:
                    type: integer
                    example: 1
                  name:
                    type: string
                    example: "Gaming"
              service_style:
                type: object
                properties:
                  id:
                    type: integer
                    example: 1
                  name:
                    type: string
                    example: "Competitive"
    
    ServiceType:
      type: object
      properties:
        id:
          type: integer
          example: 1
        service_category_id:
          type: integer
          example: 1
        name:
          type: string
          example: "Gaming"
        description:
          type: string
          example: "Gaming services"
        icon_path:
          type: string
          example: "path/to/icon.png"
        is_active:
          type: boolean
          example: true
        display_order:
          type: integer
          example: 1
        required_elements:
          type: array
          items:
            type: string
          example: ["game", "rank"]
        created_at:
          type: string
          format: date-time
          example: "2023-01-01T00:00:00.000000Z"
        updated_at:
          type: string
          format: date-time
          example: "2023-01-01T00:00:00.000000Z"
        deleted_at:
          type: string
          format: date-time
          nullable: true
          example: null
        usage_count:
          type: integer
          example: 42
