openapi: 3.0.0
info:
  title: Mission Disputes API
  description: API for managing disputes related to missions
  version: 1.0.0

paths:
  /missions/{mission}/disputes:
    post:
      summary: Create a new mission dispute
      description: Create a new dispute for a mission
      tags:
        - Mission Disputes
      security:
        - bearerAuth: []
      parameters:
        - name: mission
          in: path
          required: true
          description: ID of the mission to create a dispute for
          schema:
            type: integer
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                dispute_type_id:
                  type: integer
                  description: ID of the dispute type
                description:
                  type: string
                  description: Detailed description of the dispute
                child_id:
                  type: integer
                  description: ID of the applicant being disputed (required only if requestor_type is 'creator')
                media[]:
                  type: array
                  description: Media files to upload (max 3 images and 1 video)
                  items:
                    type: string
                    format: binary
              required:
                - dispute_type_id
                - description
      responses:
        '201':
          description: Dispute created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Dispute created successfully
                  data:
                    type: object
                    properties:
                      id:
                        type: integer
                        example: 1
                      user_id:
                        type: integer
                        example: 456
                      requestor_type:
                        type: string
                        enum: [creator, applicant]
                        example: creator
                      mission_id:
                        type: integer
                        example: 789
                      child_id:
                        type: integer
                        nullable: true
                        example: 123
                      dispute_type_id:
                        type: integer
                        example: 1
                      description:
                        type: string
                        example: The applicant did not complete the mission as agreed
                      status:
                        type: string
                        enum: [submitted, in_review, resolved, rejected]
                        example: submitted
                      resolution_type:
                        type: string
                        enum: [refund, partial_settlement, full_settlement]
                        nullable: true
                        example: null
                      resolution_notes:
                        type: string
                        nullable: true
                        example: null
                      refund_amount:
                        type: string
                        format: decimal
                        example: "0.00"
                      resolved_at:
                        type: string
                        format: date-time
                        nullable: true
                        example: null
                      created_at:
                        type: string
                        format: date-time
                        example: "2025-05-13T03:42:56.000000Z"
                      updated_at:
                        type: string
                        format: date-time
                        example: "2025-05-13T03:42:56.000000Z"
                      media:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: integer
                              example: 1
                            mission_dispute_id:
                              type: integer
                              example: 1
                            media_type:
                              type: string
                              enum: [image, video]
                              example: image
                            original_path:
                              type: string
                              example: "disputes/original/456/abc123.jpg"
                            optimized_path:
                              type: string
                              nullable: true
                              example: "disputes/optimized/456/abc123.jpg"
                            mime_type:
                              type: string
                              example: "image/jpeg"
                            file_size:
                              type: integer
                              example: 1024000
                            created_at:
                              type: string
                              format: date-time
                              example: "2025-05-13T03:42:56.000000Z"
                            updated_at:
                              type: string
                              format: date-time
                              example: "2025-05-13T03:42:56.000000Z"
        '400':
          description: Invalid request parameters
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Only accepted orders can have disputes
        '403':
          description: User is not authorized to create a dispute for this mission
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: You are not authorized to create a dispute for this mission
        '422':
          description: Validation errors
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Validation failed
                  errors:
                    type: object
                    properties:
                      dispute_type_id:
                        type: array
                        items:
                          type: string
                          example: The dispute type id field is required.
                      description:
                        type: array
                        items:
                          type: string
                          example: The description field is required.
                      media:
                        type: array
                        items:
                          type: string
                          example: Maximum 3 images allowed.

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
