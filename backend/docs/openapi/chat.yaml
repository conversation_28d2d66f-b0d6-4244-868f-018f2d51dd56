openapi: 3.0.3
info:
  title: Mission X Chat API
  description: |
    API for managing real-time messaging between users and talents on the Mission X platform.
    Enables users to create conversations, send text messages, share image attachments, and track message delivery status.
  version: 1.0.0
  contact:
    name: Mission X Support
    email: <EMAIL>

servers:
  - url: https://api.missionx.com/api
    description: Production server
  - url: https://staging-api.missionx.com/api
    description: Staging server

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    User:
      type: object
      properties:
        id:
          type: integer
          format: int64
          example: 123
        nickname:
          type: string
          example: "User123"
        profile_image:
          type: string
          format: uri
          example: "https://example.com/profiles/user123.jpg"

    ChatConversation:
      type: object
      properties:
        id:
          type: integer
          format: int64
          example: 1
        user_id:
          type: integer
          format: int64
          example: 123
        talent_id:
          type: integer
          format: int64
          example: 456
        order_id:
          type: integer
          format: int64
          nullable: true
          example: 789
        is_active:
          type: boolean
          example: true
        last_message_at:
          type: string
          format: date-time
          example: "2025-05-01T12:30:45Z"
        created_at:
          type: string
          format: date-time
          example: "2025-04-15T10:20:30Z"
        updated_at:
          type: string
          format: date-time
          example: "2025-05-01T12:30:45Z"
        user:
          $ref: '#/components/schemas/User'
        talent:
          $ref: '#/components/schemas/User'

    ChatMessageStatus:
      type: object
      properties:
        id:
          type: integer
          format: int64
          example: 1
        message_id:
          type: integer
          format: int64
          example: 1
        user_id:
          type: integer
          format: int64
          example: 123
        status:
          type: string
          enum: [sent, delivered, read]
          example: "sent"
        status_timestamp:
          type: string
          format: date-time
          example: "2025-05-01T12:30:45Z"
        created_at:
          type: string
          format: date-time
          example: "2025-05-01T12:30:45Z"
        updated_at:
          type: string
          format: date-time
          example: "2025-05-01T12:30:45Z"

    ChatMessageAttachment:
      type: object
      properties:
        id:
          type: integer
          format: int64
          example: 1
        message_id:
          type: integer
          format: int64
          example: 3
        original_path:
          type: string
          example: "chat_attachments/user_123/original/image_12345.jpg"
        optimized_path:
          type: string
          example: "chat_attachments/user_123/optimized/image_12345.jpg"
        filename:
          type: string
          example: "screenshot.jpg"
        mime_type:
          type: string
          example: "image/jpeg"
        size:
          type: integer
          format: int64
          example: 1024000
        extension:
          type: string
          example: "jpg"
        dimensions:
          type: object
          properties:
            width:
              type: integer
              example: 1280
            height:
              type: integer
              example: 720
        created_at:
          type: string
          format: date-time
          example: "2025-05-01T12:40:45Z"
        updated_at:
          type: string
          format: date-time
          example: "2025-05-01T12:40:45Z"

    ChatMessage:
      type: object
      properties:
        id:
          type: integer
          format: int64
          example: 1
        conversation_id:
          type: integer
          format: int64
          example: 1
        sender_id:
          type: integer
          format: int64
          example: 123
        content:
          type: string
          example: "Hello, are you available for a gaming session?"
        is_flagged:
          type: boolean
          example: false
        flagged_reason:
          type: string
          nullable: true
          example: null
        created_at:
          type: string
          format: date-time
          example: "2025-05-01T12:30:45Z"
        updated_at:
          type: string
          format: date-time
          example: "2025-05-01T12:30:45Z"
        sender:
          $ref: '#/components/schemas/User'
        attachments:
          type: array
          items:
            $ref: '#/components/schemas/ChatMessageAttachment'
        statuses:
          type: array
          items:
            $ref: '#/components/schemas/ChatMessageStatus'

    PaginatedConversations:
      type: object
      properties:
        current_page:
          type: integer
          example: 1
        data:
          type: array
          items:
            $ref: '#/components/schemas/ChatConversation'
        first_page_url:
          type: string
          format: uri
          example: "https://api.missionx.com/api/chat/conversations?page=1"
        from:
          type: integer
          example: 1
        last_page:
          type: integer
          example: 5
        last_page_url:
          type: string
          format: uri
          example: "https://api.missionx.com/api/chat/conversations?page=5"
        next_page_url:
          type: string
          format: uri
          nullable: true
          example: "https://api.missionx.com/api/chat/conversations?page=2"
        path:
          type: string
          format: uri
          example: "https://api.missionx.com/api/chat/conversations"
        per_page:
          type: integer
          example: 15
        prev_page_url:
          type: string
          format: uri
          nullable: true
          example: null
        to:
          type: integer
          example: 15
        total:
          type: integer
          example: 68

    PaginatedMessages:
      type: object
      properties:
        current_page:
          type: integer
          example: 1
        data:
          type: array
          items:
            $ref: '#/components/schemas/ChatMessage'
        first_page_url:
          type: string
          format: uri
          example: "https://api.missionx.com/api/chat/conversations/1/messages?page=1"
        from:
          type: integer
          example: 1
        last_page:
          type: integer
          example: 10
        last_page_url:
          type: string
          format: uri
          example: "https://api.missionx.com/api/chat/conversations/1/messages?page=10"
        next_page_url:
          type: string
          format: uri
          nullable: true
          example: "https://api.missionx.com/api/chat/conversations/1/messages?page=2"
        path:
          type: string
          format: uri
          example: "https://api.missionx.com/api/chat/conversations/1/messages"
        per_page:
          type: integer
          example: 20
        prev_page_url:
          type: string
          format: uri
          nullable: true
          example: null
        to:
          type: integer
          example: 20
        total:
          type: integer
          example: 187

    SuccessResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: object

    ErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          example: "Failed to get conversations"
        error:
          type: string
          example: "Server error"

    ValidationErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          example: "Validation failed"
        errors:
          type: object
          additionalProperties:
            type: array
            items:
              type: string
          example:
            talent_id: ["The talent id field is required."]
            message: ["The message field is required."]

    ConversationsResponse:
      allOf:
        - $ref: '#/components/schemas/SuccessResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/PaginatedConversations'

    MessagesResponse:
      allOf:
        - $ref: '#/components/schemas/SuccessResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/PaginatedMessages'

    StartConversationResponse:
      allOf:
        - $ref: '#/components/schemas/SuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                conversation:
                  $ref: '#/components/schemas/ChatConversation'
                message:
                  $ref: '#/components/schemas/ChatMessage'

    SendMessageResponse:
      allOf:
        - $ref: '#/components/schemas/SuccessResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/ChatMessage'

    UpdateStatusResponse:
      allOf:
        - $ref: '#/components/schemas/SuccessResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/ChatMessageStatus'

security:
  - bearerAuth: []

paths:
  /chat/conversations:
    get:
      summary: Get User Conversations
      description: Retrieves a paginated list of conversations for the authenticated user.
      operationId: getConversations
      parameters:
        - name: page
          in: query
          description: Page number for pagination
          required: false
          schema:
            type: integer
            default: 1
        - name: per_page
          in: query
          description: Number of conversations per page
          required: false
          schema:
            type: integer
            default: 15
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConversationsResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    post:
      summary: Start a New Conversation
      description: Creates a new conversation with a talent and sends an initial message.
      operationId: startConversation
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - talent_id
                - message
              properties:
                talent_id:
                  type: integer
                  format: int64
                  description: ID of the talent to start a conversation with
                  example: 456
                order_id:
                  type: integer
                  format: int64
                  description: ID of an associated order (optional)
                  example: 789
                message:
                  type: string
                  description: Initial message content (max 1000 characters)
                  example: "Hello, I'm interested in your gaming services."
                  maxLength: 1000
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StartConversationResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /chat/conversations/{conversationId}/messages:
    get:
      summary: Get Conversation Messages
      description: Retrieves a paginated list of messages for a specific conversation.
      operationId: getMessages
      parameters:
        - name: conversationId
          in: path
          description: ID of the conversation
          required: true
          schema:
            type: integer
            format: int64
        - name: page
          in: query
          description: Page number for pagination
          required: false
          schema:
            type: integer
            default: 1
        - name: per_page
          in: query
          description: Number of messages per page
          required: false
          schema:
            type: integer
            default: 20
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MessagesResponse'
        '404':
          description: Conversation not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /chat/messages/text:
    post:
      summary: Send a Text Message
      description: Sends a text message to an existing conversation.
      operationId: sendTextMessage
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - conversation_id
                - message
              properties:
                conversation_id:
                  type: integer
                  format: int64
                  description: ID of the conversation
                  example: 1
                message:
                  type: string
                  description: Message content (max 1000 characters)
                  example: "When are you available for a gaming session?"
                  maxLength: 1000
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SendMessageResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /chat/messages/attachment:
    post:
      summary: Send a Message with Attachment
      description: Sends a message with an image attachment to an existing conversation.
      operationId: sendMessageWithAttachment
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              required:
                - conversation_id
                - attachment
              properties:
                conversation_id:
                  type: integer
                  format: int64
                  description: ID of the conversation
                  example: 1
                message:
                  type: string
                  description: Message content (max 1000 characters)
                  example: "Check out this screenshot"
                  maxLength: 1000
                attachment:
                  type: string
                  format: binary
                  description: Image file (JPEG, PNG, GIF, WEBP, HEIC, HEIF, max 10MB)
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SendMessageResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /chat/messages/status:
    post:
      summary: Update Message Status
      description: Updates the status of a message (delivered or read).
      operationId: updateMessageStatus
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - message_id
                - status
              properties:
                message_id:
                  type: integer
                  format: int64
                  description: ID of the message
                  example: 1
                status:
                  type: string
                  description: New status value
                  enum: [delivered, read]
                  example: "read"
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateStatusResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
