openapi: 3.0.0
info:
  title: User Review API
  description: API for retrieving user reviews
  version: 1.0.0
paths:
  /users/{userId}/reviews:
    get:
      tags:
        - User Reviews
      summary: Get reviews for a user
      description: Returns all reviews received by a specific user with filtering options
      security:
        - bearerAuth: []
      parameters:
        - name: userId
          in: path
          required: true
          description: ID of the user to get reviews for
          schema:
            type: integer
        - name: rating
          in: query
          required: false
          description: Filter by rating value (1-5)
          schema:
            type: integer
            minimum: 1
            maximum: 5
        - name: from_date
          in: query
          required: false
          description: Filter by review date (from) - format YYYY-MM-DD
          schema:
            type: string
            format: date
        - name: to_date
          in: query
          required: false
          description: Filter by review date (to) - format YYYY-MM-DD
          schema:
            type: string
            format: date
        - name: is_anonymous
          in: query
          required: false
          description: Filter by anonymous status
          schema:
            type: boolean
      responses:
        '200':
          description: Successful operation
          headers:
            Content-Type:
              schema:
                type: string
                example: application/json
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          example: 1
                        order_type:
                          type: string
                          enum: [regular, scheduled]
                          example: "regular"
                        order_id:
                          type: integer
                          example: 123
                        reviewer:
                          $ref: '#/components/schemas/User'
                        rating:
                          type: integer
                          minimum: 1
                          maximum: 5
                          example: 5
                        review_text:
                          type: string
                          example: "Excellent service!"
                        is_anonymous:
                          type: boolean
                          example: false
                        created_at:
                          type: string
                          format: date-time
                          example: "2023-01-01T12:00:00Z"
                        updated_at:
                          type: string
                          format: date-time
                          example: "2023-01-01T12:00:00Z"
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: "Unauthenticated"
        '404':
          description: User not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: "User not found"
