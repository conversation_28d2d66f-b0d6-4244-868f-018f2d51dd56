openapi: 3.0.0
info:
  title: Mission X User Followers API
  version: 1.0.0
  description: User followers endpoints for Mission X mobile application

servers:
  - url: http://*************:8000/api
    description: Development server

paths:
  /users/{userId}/follow:
    post:
      tags:
        - User Followers
      summary: Toggle follow/unfollow user
      description: Follows or unfollows a user based on current status
      security:
        - bearerAuth: []
      parameters:
        - name: userId
          in: path
          required: true
          description: ID of the user to follow/unfollow
          schema:
            type: integer
      responses:
        '200':
          description: Follow/unfollow action successful
          headers:
            Content-Type:
              schema:
                type: string
                example: application/json
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "You are now following this user"
                  is_following:
                    type: boolean
                    example: true
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: "You cannot follow yourself"
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          description: User not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "User not found"
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: "Validation error"
                  errors:
                    type: object
                    properties:
                      user_id:
                        type: array
                        items:
                          type: string
                        example: ["The selected user id is invalid."]

  /users/{userId}/followers:
    get:
      tags:
        - User Followers
      summary: Get user followers
      description: Retrieves a paginated list of users who follow the specified user
      security:
        - bearerAuth: []
      parameters:
        - name: userId
          in: path
          required: true
          description: ID of the user to get followers for
          schema:
            type: integer
        - name: page
          in: query
          required: false
          description: Page number for pagination
          schema:
            type: integer
            default: 1
        - name: per_page
          in: query
          required: false
          description: Number of items per page
          schema:
            type: integer
            default: 15
            maximum: 100
      responses:
        '200':
          description: Followers retrieved successfully
          headers:
            Content-Type:
              schema:
                type: string
                example: application/json
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      current_page:
                        type: integer
                        example: 1
                      data:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: integer
                              example: 1
                            name:
                              type: string
                              example: "John Doe"
                            nickname:
                              type: string
                              example: "johndoe"
                            profile_picture:
                              type: string
                              nullable: true
                              example: "users/1/profile.jpg"
                            is_following:
                              type: boolean
                              example: true
                      first_page_url:
                        type: string
                        example: "http://example.com/api/users/1/followers?page=1"
                      from:
                        type: integer
                        example: 1
                      last_page:
                        type: integer
                        example: 1
                      last_page_url:
                        type: string
                        example: "http://example.com/api/users/1/followers?page=1"
                      links:
                        type: array
                        items:
                          type: object
                          properties:
                            url:
                              type: string
                              nullable: true
                              example: null
                            label:
                              type: string
                              example: "&laquo; Previous"
                            active:
                              type: boolean
                              example: false
                      next_page_url:
                        type: string
                        nullable: true
                        example: null
                      path:
                        type: string
                        example: "http://example.com/api/users/1/followers"
                      per_page:
                        type: integer
                        example: 15
                      prev_page_url:
                        type: string
                        nullable: true
                        example: null
                      to:
                        type: integer
                        example: 2
                      total:
                        type: integer
                        example: 2
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          description: User not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "User not found"

  /users/{userId}/following:
    get:
      tags:
        - User Followers
      summary: Get user following
      description: Retrieves a paginated list of users that the specified user follows
      security:
        - bearerAuth: []
      parameters:
        - name: userId
          in: path
          required: true
          description: ID of the user to get following for
          schema:
            type: integer
        - name: page
          in: query
          required: false
          description: Page number for pagination
          schema:
            type: integer
            default: 1
        - name: per_page
          in: query
          required: false
          description: Number of items per page
          schema:
            type: integer
            default: 15
            maximum: 100
      responses:
        '200':
          description: Following retrieved successfully
          headers:
            Content-Type:
              schema:
                type: string
                example: application/json
          content:
            application/json:
              schema:
                $ref: '#/paths/~1users~1{userId}~1followers/get/responses/200/content/application~1json/schema'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          description: User not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "User not found"

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  responses:
    Unauthorized:
      description: Unauthorized
      content:
        application/json:
          schema:
            type: object
            properties:
              message:
                type: string
                example: "Unauthenticated"
