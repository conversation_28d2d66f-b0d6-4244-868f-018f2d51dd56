openapi: 3.0.0
info:
  title: Mission Reviews API
  description: API for managing reviews for missions in the Mission X platform
  version: 1.0.0
paths:
  /api/missions/{id}/reviews:
    post:
      summary: Create a review for a mission
      description: Submit a review for a completed mission
      tags:
        - Missions
        - Reviews
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: ID of the mission
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - rating
              properties:
                rating:
                  type: integer
                  minimum: 1
                  maximum: 5
                  description: Rating from 1 to 5 stars
                review_text:
                  type: string
                  description: Review text content
                  maxLength: 1000
                is_anonymous:
                  type: boolean
                  description: Whether the review should be anonymous (only available for mission creators)
                reviewee_id:
                  type: integer
                  description: ID of the user being reviewed (required when mission creator is reviewing an applicant)
      responses:
        201:
          description: Review created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  data:
                    $ref: '#/components/schemas/Review'
        400:
          description: Bad request or already reviewed
        401:
          description: Unauthorized
        404:
          description: Mission not found or not completed
        422:
          description: Validation error
        500:
          description: Server error
    get:
      summary: Get reviews for a mission
      description: Retrieve all reviews for a specific mission
      tags:
        - Missions
        - Reviews
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: ID of the mission
      responses:
        200:
          description: List of reviews
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Review'
        401:
          description: Unauthorized
        404:
          description: Mission not found
        500:
          description: Server error

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  
  schemas:
    Review:
      type: object
      properties:
        id:
          type: integer
        reviewable_type:
          type: string
          description: The type of entity being reviewed (App\\Models\\Mission)
        reviewable_id:
          type: integer
          description: ID of the entity being reviewed
        reviewer_id:
          type: integer
          description: ID of the user writing the review
        reviewee_id:
          type: integer
          description: ID of the user being reviewed
        rating:
          type: integer
          minimum: 1
          maximum: 5
        review_text:
          type: string
        is_anonymous:
          type: boolean
        is_hidden:
          type: boolean
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        reviewer:
          $ref: '#/components/schemas/User'
    
    User:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
        email:
          type: string
        phone:
          type: string
        profile_picture:
          type: string
