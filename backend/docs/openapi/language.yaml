openapi: 3.0.0
info:
  title: Mission X Language API
  version: 1.0.0
  description: Language management endpoints for Mission X mobile application

servers:
  - url: http://*************:8000/api
    description: Development server

paths:
  /languages:
    get:
      tags:
        - Language
      summary: Get all languages
      description: Retrieves all active languages
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Languages retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Language'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error401'

  /languages/{id}:
    get:
      tags:
        - Language
      summary: Get language by ID
      description: Retrieves a specific language by its ID
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: Language ID
      responses:
        '200':
          description: Language retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LanguageDetail'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error401'
        '404':
          description: Language not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error404'
                
  /users/languages:
    get:
      tags:
        - Language
      summary: Get user languages
      description: Retrieves the languages associated with the authenticated user
      security:
        - bearerAuth: []
      responses:
        '200':
          description: User languages retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  languages:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          example: 1
                        name:
                          type: string
                          example: "English"
                        description:
                          type: string
                          nullable: true
                          example: "English language"
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error401'
    post:
      tags:
        - Language
      summary: Update user languages
      description: Updates the languages associated with the authenticated user
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                language_ids:
                  type: array
                  nullable: true
                  items:
                    type: integer
                  example: [1, 2, 3]
      responses:
        '200':
          description: Languages updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Languages updated successfully"
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error401'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error422'

components:
  schemas:
    Language:
      type: object
      properties:
        id:
          type: integer
          example: 1
        name:
          type: string
          example: "English"
        description:
          type: string
          nullable: true
          example: "English language"
        is_active:
          type: boolean
          example: true
        display_order:
          type: integer
          example: 1
    
    LanguageDetail:
      type: object
      properties:
        id:
          type: integer
          example: 1
        name:
          type: string
          example: "English"
        description:
          type: string
          nullable: true
          example: "English language"
        is_active:
          type: boolean
          example: true
        display_order:
          type: integer
          example: 1
        created_at:
          type: string
          format: date-time
          example: "2025-05-20T10:00:00.000000Z"
        updated_at:
          type: string
          format: date-time
          example: "2025-05-20T10:00:00.000000Z"
    
    Error401:
      type: object
      properties:
        message:
          type: string
          example: "Unauthenticated"
    
    Error404:
      type: object
      properties:
        message:
          type: string
          example: "Language not found"
    
    Error422:
      type: object
      properties:
        message:
          type: string
          example: "The given data was invalid."
        errors:
          type: object
          properties:
            language:
              type: array
              items:
                type: string
              example: ["The selected language is invalid."]
  
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
