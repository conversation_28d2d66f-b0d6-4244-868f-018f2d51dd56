openapi: 3.0.0
info:
  title: Game Profiles API
  description: API endpoints for managing user game profiles
  version: 1.0.0

paths:
  /api/user-game-profiles:
    get:
      summary: List user's game profiles
      security:
        - bearerAuth: []
      parameters:
        - name: service_type_id
          in: query
          schema:
            type: integer
          description: Filter by service type ID
        - name: is_hidden
          in: query
          schema:
            type: boolean
          description: Filter by hidden status
      responses:
        200:
          description: A list of user's game profiles
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserGameProfile'
    post:
      summary: Create a new game profile
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - service_type_id
                - identifiers
              properties:
                service_type_id:
                  type: integer
                  description: ID of the service type
                identifiers:
                  type: object
                  description: Game identifiers keyed by game_id.slug
                is_hidden:
                  type: boolean
                  description: Whether this profile is hidden (default: true)
                status:
                  type: string
                  enum: [pendingReview, Reviewed]
                  description: Review status of the profile (default: pendingReview)
      responses:
        201:
          description: Game profile created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserGameProfile'
        422:
          description: Validation errors
          content:
            application/json:
              schema:
                type: object
                properties:
                  errors:
                    type: object

  /api/user-game-profiles/{id}:
    get:
      summary: Get a specific game profile
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        200:
          description: Game profile details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserGameProfile'
        404:
          description: Profile not found
    put:
      summary: Update a game profile
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                identifiers:
                  type: object
                  description: Game identifiers keyed by game_id.slug
                is_hidden:
                  type: boolean
                  description: Whether this profile is hidden (default: true)
                status:
                  type: string
                  enum: [pendingReview, Reviewed]
                  description: Review status of the profile (default: pendingReview)
      responses:
        200:
          description: Game profile updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserGameProfile'
        422:
          description: Validation errors
          content:
            application/json:
              schema:
                type: object
                properties:
                  errors:
                    type: object
    delete:
      summary: Delete a game profile
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        204:
          description: Game profile deleted successfully
        404:
          description: Profile not found

  /api/user-game-profiles/{id}/visible:
    put:
      summary: Set a game profile as visible
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        200:
          description: Game profile set as visible successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                  profile:
                    $ref: '#/components/schemas/UserGameProfile'
        404:
          description: Profile not found
        500:
          description: Failed to set profile as visible

components:
  schemas:
    UserGameProfile:
      type: object
      properties:
        id:
          type: integer
        user_id:
          type: integer
        service_type_id:
          type: integer
        identifiers:
          type: object
          description: Game identifiers keyed by game_id.slug
        is_hidden:
          type: boolean
        status:
          type: string
          enum: [pendingReview, Reviewed]
          description: Review status of the profile
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        service_type:
          type: object
          properties:
            id:
              type: integer
            name:
              type: string
            description:
              type: string
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
