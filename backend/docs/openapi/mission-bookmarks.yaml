openapi: 3.0.0
info:
  title: Mission Bookmarks API
  description: API endpoints for managing mission bookmarks
  version: 1.0.0

paths:
  /api/missions/{mission}/bookmark:
    post:
      summary: Toggle bookmark status for a mission
      tags:
        - Missions
      security:
        - bearerAuth: []
      parameters:
        - name: mission
          in: path
          required: true
          schema:
            type: integer
          description: ID of the mission to bookmark/unbookmark
      responses:
        200:
          description: Bookmark status toggled successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  action:
                    type: string
                    enum: [bookmarked, unbookmarked]
                    description: The action that was performed
        401:
          description: Unauthorized - User is not authenticated
        404:
          description: Mission not found
  
  /api/missions/user/bookmarked-missions:
    get:
      summary: Get all bookmarked missions for the authenticated user
      tags:
        - Missions
      security:
        - bearerAuth: []
      parameters:
        - name: page
          in: query
          required: false
          schema:
            type: integer
            default: 1
          description: Page number for pagination
      responses:
        200:
          description: List of bookmarked missions
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Mission'
                  links:
                    type: object
                    properties:
                      first:
                        type: string
                      last:
                        type: string
                      prev:
                        type: string
                        nullable: true
                      next:
                        type: string
                        nullable: true
                  meta:
                    type: object
                    properties:
                      current_page:
                        type: integer
                      from:
                        type: integer
                      last_page:
                        type: integer
                      path:
                        type: string
                      per_page:
                        type: integer
                      to:
                        type: integer
                      total:
                        type: integer
        401:
          description: Unauthorized - User is not authenticated

components:
  schemas:
    Mission:
      type: object
      properties:
        id:
          type: integer
          description: Mission ID
        user_id:
          type: integer
          description: ID of the user who created the mission
        service_type_id:
          type: integer
          description: ID of the service type
        service_style_id:
          type: integer
          description: ID of the service style
        description:
          type: string
          description: Mission description
        bounty:
          type: integer
          description: Mission bounty amount
        pax_required:
          type: integer
          description: Number of participants required
        min_level_id:
          type: integer
          description: Minimum user level ID required
        max_level_id:
          type: integer
          description: Maximum user level ID allowed
        service_start_date:
          type: string
          format: date-time
          description: Mission start date and time
        service_end_date:
          type: string
          format: date-time
          description: Mission end date and time
        status:
          type: string
          enum: [open, closed, completed, cancelled]
          description: Mission status
        created_at:
          type: string
          format: date-time
          description: Creation timestamp
        updated_at:
          type: string
          format: date-time
          description: Last update timestamp
        is_bookmarked:
          type: boolean
          description: Whether the mission is bookmarked by the authenticated user
        service_type:
          type: object
          properties:
            id:
              type: integer
            name:
              type: string
        service_style:
          type: object
          properties:
            id:
              type: integer
            name:
              type: string
        min_level:
          type: object
          properties:
            id:
              type: integer
            name:
              type: string
        max_level:
          type: object
          properties:
            id:
              type: integer
            name:
              type: string
        user:
          type: object
          properties:
            id:
              type: integer
            name:
              type: string
            profile_picture:
              type: string
              nullable: true
  
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
