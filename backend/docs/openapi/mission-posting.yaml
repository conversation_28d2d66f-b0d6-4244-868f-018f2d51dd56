openapi: 3.0.0
info:
  title: Mission Posting API
  description: API endpoints for creating job missions and managing applicants
  version: 1.0.0

paths:
  /api/missions:
    get:
      summary: List all missions
      tags:
        - Missions
      security:
        - bearerAuth: []
      parameters:
        - name: status
          in: query
          schema:
            type: string
            enum: [open, closed, completed]
          description: Filter by mission status
        - name: type_id
          in: query
          schema:
            type: integer
          description: Filter by service type ID
        - name: style_id
          in: query
          schema:
            type: integer
          description: Filter by service style ID
        - name: min_level_id
          in: query
          schema:
            type: integer
          description: Filter by minimum level ID
      responses:
        200:
          description: Paginated list of missions
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Mission'
                  meta:
                    $ref: '#/components/schemas/PaginationMeta'
    
    post:
      summary: Create a new mission
      tags:
        - Missions
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - service_type_id
                - service_style_id
                - description
                - bounty
                - pax_required
                - min_level_id
                - service_start_date
              properties:
                service_type_id:
                  type: integer
                  description: ID of the service type
                service_style_id:
                  type: integer
                  description: ID of the service style
                description:
                  type: string
                  description: Description of the mission (max 100 words)
                bounty:
                  type: integer
                  description: Bounty amount in Mission X Coins per person
                pax_required:
                  type: integer
                  description: Number of people required
                min_level_id:
                  type: integer
                  description: Minimum level requirement for applicants
                service_start_date:
                  type: string
                  format: date-time
                  description: Service start date and time
                is_anonymous:
                  type: boolean
                  description: Whether the mission creator's identity should be anonymous
                  default: false
      responses:
        201:
          description: Mission created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                  mission:
                    $ref: '#/components/schemas/Mission'
        422:
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
  
  /api/missions/{id}:
    get:
      summary: Get a specific mission
      tags:
        - Missions
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: Mission ID
      responses:
        200:
          description: Mission details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MissionWithApplicants'
        403:
          description: Forbidden
        404:
          description: Mission not found
    
    put:
      summary: Update a mission
      tags:
        - Missions
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: Mission ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                service_type_id:
                  type: integer
                  description: ID of the service type
                service_style_id:
                  type: integer
                  description: ID of the service style
                description:
                  type: string
                  description: Description of the mission (max 100 words)
                bounty:
                  type: integer
                  description: Bounty amount in Mission X Coins per person
                pax_required:
                  type: integer
                  description: Number of people required
                min_level_id:
                  type: integer
                  description: Minimum level requirement for applicants
                service_start_date:
                  type: string
                  format: date-time
                  description: Service start date and time
                is_anonymous:
                  type: boolean
                  description: Whether the mission creator's identity should be anonymous
                  default: false
                status:
                  type: string
                  enum: [open, closed, completed]
                  description: Mission status
                is_anonymous:
                  type: boolean
                  description: Whether the mission creator's identity should be anonymous
      responses:
        200:
          description: Mission updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                  mission:
                    $ref: '#/components/schemas/Mission'
        403:
          description: Forbidden
        404:
          description: Mission not found
        422:
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
    
    delete:
      summary: Delete a mission
      tags:
        - Missions
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: Mission ID
      responses:
        200:
          description: Mission deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
        403:
          description: Forbidden
        404:
          description: Mission not found
        422:
          description: Cannot delete mission with applicants
          content:
            application/json:
              schema:
  /api/missions/{mission_id}/images/{image_id}:
    delete:
      summary: Delete a mission image
      tags:
        - Missions
      security:
        - bearerAuth: []
      parameters:
        - name: mission_id
          in: path
          required: true
          schema:
            type: integer
          description: Mission ID
        - name: image_id
          in: path
          required: true
          schema:
            type: integer
          description: Image ID
      responses:
        200:
          description: Mission image deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
        403:
          description: Forbidden
        404:
          description: Mission or image not found
        422:
          description: Cannot delete image for a closed or completed mission
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string

                type: object
                properties:
                  message:
                    type: string
  
  /api/missions/{mission_id}/apply:
    post:
      summary: Apply for a mission
      tags:
        - Mission Applicants
      security:
        - bearerAuth: []
      parameters:
        - name: mission_id
          in: path
          required: true
          schema:
            type: integer
          description: Mission ID
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                notes:
                  type: string
                  description: Application notes
      responses:
        201:
          description: Application submitted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                  application:
                    $ref: '#/components/schemas/MissionApplicant'
        403:
          description: Forbidden
        404:
          description: Mission not found
        422:
          description: Cannot apply for this mission
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
  
  /api/missions/{mission_id}/applicants:
    get:
      summary: Get applicants for a mission
      tags:
        - Mission Applicants
      security:
        - bearerAuth: []
      parameters:
        - name: mission_id
          in: path
          required: true
          schema:
            type: integer
          description: Mission ID
      responses:
        200:
          description: List of applicants for the mission
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/MissionApplicantWithUser'
        403:
          description: Forbidden
        404:
          description: Mission not found
  
  /api/missions/{mission_id}/applicants/{applicant_id}/approve:
    post:
      summary: Approve an applicant
      tags:
        - Mission Applicants
      security:
        - bearerAuth: []
      parameters:
        - name: mission_id
          in: path
          required: true
          schema:
            type: integer
          description: Mission ID
        - name: applicant_id
          in: path
          required: true
          schema:
            type: integer
          description: Applicant ID
      responses:
        200:
          description: Applicant approved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                  mission_status:
                    type: string
                  approved_count:
                    type: integer
                  required_count:
                    type: integer
        403:
          description: Forbidden
        404:
          description: Mission or applicant not found
        422:
          description: Cannot approve this applicant
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
  
  /api/missions/{mission_id}/applicants/{applicant_id}/reject:
    post:
      summary: Reject an applicant
      tags:
        - Mission Applicants
      security:
        - bearerAuth: []
      parameters:
        - name: mission_id
          in: path
          required: true
          schema:
            type: integer
          description: Mission ID
        - name: applicant_id
          in: path
          required: true
          schema:
            type: integer
          description: Applicant ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - rejection_reason
              properties:
                rejection_reason:
                  type: string
                  description: Reason for rejection
      responses:
        200:
          description: Applicant rejected successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
        403:
          description: Forbidden
        404:
          description: Mission or applicant not found
        422:
          description: Cannot reject this applicant
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  
  schemas:
    MissionImage:
      type: object
      properties:
        id:
          type: integer
          format: int64
          example: 1
        mission_id:
          type: integer
          format: int64
          example: 123
        original_path:
          type: string
          example: "missions/original/1/abc123.jpg"
        optimized_path:
          type: string
          example: "missions/optimized/1/def456.jpg"
        file_name:
          type: string
          example: "house.jpg"
        mime_type:
          type: string
          example: "image/jpeg"
        size:
          type: integer
          example: 2048000
        extension:
          type: string
          example: "jpg"
        dimensions:
          type: object
          properties:
            width:
              type: integer
              example: 1920
            height:
              type: integer
              example: 1080
        converted:
          type: boolean
          example: false
        created_at:
          type: string
          format: date-time
          example: "2025-03-25T05:00:00.000000Z"
        updated_at:
          type: string
          format: date-time
          example: "2025-03-25T05:00:00.000000Z"
    Mission:
      type: object
      properties:
        id:
          type: integer
        user_id:
          type: integer
        service_type_id:
          type: integer
        service_style_id:
          type: integer
        description:
          type: string
        bounty:
          type: integer
        pax_required:
          type: integer
        min_level_id:
          type: integer
        service_start_date:
          type: string
          format: date-time
        status:
          type: string
          enum: [open, closed, completed]
        is_anonymous:
          type: boolean
          description: Whether the mission creator's identity is anonymous
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        serviceType:
          $ref: '#/components/schemas/ServiceType'
        serviceStyle:
          $ref: '#/components/schemas/ServiceStyle'
        minLevel:
          $ref: '#/components/schemas/UserLevel'
    
    MissionWithApplicants:
      allOf:
        - $ref: '#/components/schemas/Mission'
        - type: object
          properties:
            applicants:
              type: array
              items:
                $ref: '#/components/schemas/MissionApplicantWithUser'
    
    MissionApplicant:
      type: object
      properties:
        id:
          type: integer
        mission_id:
          type: integer
        user_id:
          type: integer
        status:
          type: string
          enum: [pending, approved, rejected]
        notes:
          type: string
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
    
    MissionApplicantWithUser:
      allOf:
        - $ref: '#/components/schemas/MissionApplicant'
        - type: object
          properties:
            user:
              $ref: '#/components/schemas/UserBasic'
    
    ServiceType:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
        description:
          type: string
    
    ServiceStyle:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
        description:
          type: string
    
    UserLevel:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
        level:
          type: integer
        min_experience:
          type: integer
        max_experience:
          type: integer
    
    UserBasic:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
        profile_picture:
          type: string
    
    ValidationError:
      type: object
      properties:
        message:
          type: string
        errors:
          type: object
          additionalProperties:
            type: array
            items:
              type: string
    
    PaginationMeta:
      type: object
      properties:
        current_page:
          type: integer
        from:
          type: integer
        last_page:
          type: integer
        path:
          type: string
        per_page:
          type: integer
        to:
          type: integer
        total:
          type: integer
