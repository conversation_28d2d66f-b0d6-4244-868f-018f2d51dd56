openapi: 3.0.0
info:
  title: Scheduled Order Disputes API
  description: API for creating and managing disputes related to scheduled orders
  version: 1.0.0
paths:
  /scheduled-orders/{orderId}/disputes:
    post:
      summary: Create a new dispute for a scheduled order
      tags:
        - Scheduled Order Disputes
      parameters:
        - name: orderId
          in: path
          required: true
          description: ID of the scheduled order to dispute
          schema:
            type: integer
        - name: Authorization
          in: header
          required: true
          description: Bearer token
          schema:
            type: string
        - name: Accept
          in: header
          required: true
          description: Application type
          schema:
            type: string
            default: application/json
        - name: Content-Type
          in: header
          required: true
          description: Content type
          schema:
            type: string
            default: multipart/form-data
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              required:
                - dispute_type_id
                - description
              properties:
                dispute_type_id:
                  type: integer
                  description: ID of the dispute type
                description:
                  type: string
                  description: Description of the dispute issue
                images[]:
                  type: array
                  items:
                    type: string
                    format: binary
                  description: Up to 3 image files (max 10MB each)
                video:
                  type: string
                  format: binary
                  description: Single video file (max 20MB)
      responses:
        '201':
          description: Dispute created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Dispute created successfully"
                  data:
                    type: object
                    properties:
                      id:
                        type: integer
                      user_id:
                        type: integer
                      requestor_type:
                        type: string
                        enum: [client, talent]
                      order_id:
                        type: integer
                      order_type:
                        type: string
                        enum: [order_now, scheduled_order]
                      dispute_type_id:
                        type: integer
                      description:
                        type: string
                      status:
                        type: string
                        enum: [submitted, in_review, resolved, rejected]
                      created_at:
                        type: string
                        format: date-time
                      updated_at:
                        type: string
                        format: date-time
                      disputeType:
                        type: object
                        properties:
                          id:
                            type: integer
                          name:
                            type: string
                          is_active:
                            type: boolean
                      media:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: integer
                            dispute_id:
                              type: integer
                            media_type:
                              type: string
                              enum: [image, video]
                            original_path:
                              type: string
                            optimized_path:
                              type: string
                            mime_type:
                              type: string
                            file_size:
                              type: integer
        '400':
          description: Bad request
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Order not found
        '422':
          description: Validation failed
      security:
        - bearerAuth: []
