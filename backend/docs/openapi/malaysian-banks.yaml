openapi: 3.0.0
info:
  title: Malaysian Banks API
  version: 1.0.0
  description: API endpoints for retrieving Malaysian bank data
servers:
  - url: https://api.missionx.com.my/api
    description: Production server
paths:
  /malaysian-banks:
    get:
      summary: Get Malaysian banks
      description: Retrieves a list of all active Malaysian banks
      tags:
        - Malaysian Banks
      security:
        - bearerAuth: []
      responses:
        '200':
          description: A list of Malaysian banks
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/MalaysianBank'
        '401':
          $ref: '#/components/responses/Unauthorized'
components:
  schemas:
    MalaysianBank:
      type: object
      properties:
        id:
          type: integer
          example: 1
        code:
          type: string
          example: "MBB"
        name:
          type: string
          example: "Maybank"
        swift_code:
          type: string
          nullable: true
          example: "MBBEMYKL"
        logo_url:
          type: string
          nullable: true
          example: "https://example.com/logos/maybank.png"
        account_number_format:
          type: string
          nullable: true
          example: "12 digits"
        description:
          type: string
          nullable: true
          example: "Malayan Banking Berhad"
  responses:
    Unauthorized:
      description: Unauthorized
      content:
        application/json:
          schema:
            type: object
            properties:
              message:
                type: string
                example: "Unauthenticated"
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
