# User Followers API Documentation

## Overview
API endpoints for managing user follow relationships.

## Base URL
`/api`

## Authentication
- JWT Bearer token required for all endpoints

## Endpoints

### Toggle Follow/Unfollow User
```http
POST /users/{userId}/follow
```

**Headers:**
```
Authorization: Bearer {token}
Content-Type: application/json
```

**Response Headers:**
```
Content-Type: application/json
```

**Response (200):**
```json
{
    "success": true,
    "message": "You are now following this user",
    "is_following": true
}
```

**Response (400):**
```json
{
    "success": false,
    "message": "You cannot follow yourself"
}
```

**Response (422):**
```json
{
    "success": false,
    "message": "Validation error",
    "errors": {
        "user_id": ["The selected user id is invalid."]
    }
}
```

### Get User Followers
```http
GET /users/{userId}/followers
```

**Headers:**
```
Authorization: Bearer {token}
```

**Response Headers:**
```
Content-Type: application/json
```

**Query Parameters:**
```
page: integer (optional, default: 1)
per_page: integer (optional, default: 15, max: 100)
```

**Response (200):**
```json
{
    "success": true,
    "data": {
        "current_page": 1,
        "data": [
            {
                "id": 1,
                "name": "John Doe",
                "nickname": "johndoe",
                "profile_picture": "users/1/profile.jpg",
                "is_following": true
            },
            {
                "id": 2,
                "name": "Jane Smith",
                "nickname": "janesmith",
                "profile_picture": "users/2/profile.jpg",
                "is_following": false
            }
        ],
        "first_page_url": "http://example.com/api/users/1/followers?page=1",
        "from": 1,
        "last_page": 1,
        "last_page_url": "http://example.com/api/users/1/followers?page=1",
        "links": [
            {
                "url": null,
                "label": "&laquo; Previous",
                "active": false
            },
            {
                "url": "http://example.com/api/users/1/followers?page=1",
                "label": "1",
                "active": true
            },
            {
                "url": null,
                "label": "Next &raquo;",
                "active": false
            }
        ],
        "next_page_url": null,
        "path": "http://example.com/api/users/1/followers",
        "per_page": 15,
        "prev_page_url": null,
        "to": 2,
        "total": 2
    }
}
```

### Get User Following
```http
GET /users/{userId}/following
```

**Headers:**
```
Authorization: Bearer {token}
```

**Response Headers:**
```
Content-Type: application/json
```

**Query Parameters:**
```
page: integer (optional, default: 1)
per_page: integer (optional, default: 15, max: 100)
```

**Response (200):**
```json
{
    "success": true,
    "data": {
        "current_page": 1,
        "data": [
            {
                "id": 3,
                "name": "Alice Johnson",
                "nickname": "alicejohnson",
                "profile_picture": "users/3/profile.jpg",
                "is_following": true
            },
            {
                "id": 4,
                "name": "Bob Williams",
                "nickname": "bobwilliams",
                "profile_picture": "users/4/profile.jpg",
                "is_following": true
            }
        ],
        "first_page_url": "http://example.com/api/users/1/following?page=1",
        "from": 1,
        "last_page": 1,
        "last_page_url": "http://example.com/api/users/1/following?page=1",
        "links": [
            {
                "url": null,
                "label": "&laquo; Previous",
                "active": false
            },
            {
                "url": "http://example.com/api/users/1/following?page=1",
                "label": "1",
                "active": true
            },
            {
                "url": null,
                "label": "Next &raquo;",
                "active": false
            }
        ],
        "next_page_url": null,
        "path": "http://example.com/api/users/1/following",
        "per_page": 15,
        "prev_page_url": null,
        "to": 2,
        "total": 2
    }
}
```

## User Profile Updates

The user profile endpoints now include follower and following counts, as well as follow status information:

### Get User Profile
```http
GET /user/profile
```

**Response (200):**
```json
{
    // ... existing profile fields ...
    "total_followers": 10,
    "following_count": 5,
    "is_following": false // Always false for own profile
}
```

### Get User Profile By ID
```http
GET /users/{userId}/profile
```

**Response (200):**
```json
{
    // ... existing profile fields ...
    "total_followers": 10,
    "following_count": 5,
    "is_following": true // Whether the authenticated user is following this user
}
```

## Error Responses

**Response Headers for all error responses:**
```
Content-Type: application/json
```

### 401 Unauthorized
```json
{
    "message": "Unauthenticated"
}
```

### 404 Not Found
```json
{
    "message": "User not found"
}
```

### 500 Server Error
```json
{
    "success": false,
    "message": "An error occurred while processing your request"
}
```
