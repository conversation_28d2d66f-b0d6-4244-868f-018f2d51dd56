# User Complete Profile API Documentation

## Overview
API endpoints for retrieving complete user profile information with all available details.

## Base URL
`/api`

## Authentication
- JWT Bearer token required for all endpoints

## Endpoints

### Get Complete User Profile
```http
GET /user/all-profile
```

**Headers:**
```
Authorization: Bearer {token}
```

**Response Headers:**
```
Content-Type: application/json
```

**Response (200):**
```json
{
  "id": 9,
  "mobile_number": "+60125187950",
  "email": "<EMAIL>",
  "country_code": "M<PERSON>",
  "nickname": "<PERSON><PERSON>",
  "gender": "Male",
  "date_of_birth": "2025-05-04T00:00:00.000Z",
  "constellation": "Taurus",
  "profile_picture": null,
  "profile_media": {
    "video": null,
    "thumbnail": null,
    "photos": []
  },
  "voice_note": null,
  "biography": null,
  "role": "customer",
  "experience": 0,
  "height": 180,
  "weight": 65,
  "default_language": [
    {
      "id": 1,
      "slug": "cn",
      "name": "Chinese"
    },
    {
      "id": 2,
      "slug": "en",
      "name": "English"
    }
  ],
  "allow_3rd_party_access": false,
  "referral_code": "4382AF0A",
  "personalities": [
    {
      "id": 1,
      "name": "Arrogant",
      "description": "Arrogant"
    },
    {
      "id": 2,
      "name": "Adventurous",
      "description": null
    }
  ],
  "level": {
    "id": 1,
    "level": 1,
    "name": "Beginner",
    "description": "Just starting out",
    "indicator_image": null
  },
  "is_follow": false,
  "total_followers": 10,
  "race": {
    "id": 2,
    "name": "Chinese",
    "description": null
  },
  "services": [
    {
      "id": 1,
      "total_person_order": 20,
      "created_at": "2025-04-25T04:11:36.000000Z",
      "updated_at": "2025-04-24T20:11:51.000000Z",
      "service_category": {
        "id": 1,
        "name": "Gamer",
        "description": "Only Gamers"
      },
      "service_type": {
        "id": 4,
        "name": "LoL",
        "description": "League of Legends",
        "icon_path": "gift_items/optimized/0/a0295581-438d-4725-85af-6c93af2e6a91.jpg",
        "required_elements": "LOL",
        "created_at": "2025-03-27T00:44:06.000000Z",
        "updated_at": "2025-03-27T00:44:06.000000Z",
        "deleted_at": null
      },
      "service_styles": [
        {
          "id": 2,
          "name": "Casual",
          "description": "Casual",
          "min_level_id": 1,
          "can_bypass": false,
          "recommended_price": 0,
          "preset_price": 10,
          "display_order": 2,
          "created_at": "2025-05-18T18:58:48.000000Z",
          "updated_at": "2025-05-18T18:58:48.000000Z"
        }
      ],
      "pricing_options": {
        "id": 1,
        "created_at": "2025-04-25T04:11:36.000000Z",
        "updated_at": "2025-04-25T04:11:36.000000Z",
        "pricing_option_type": {
          "id": 2,
          "name": "Standard Session",
          "description": "Regular Gaming Session",
          "has_duration": false,
          "unit": null,
          "quantity": null,
          "created_at": "2025-04-13T23:51:45.000000Z",
          "updated_at": "2025-04-13T23:51:45.000000Z"
        }
      }
    }
  ],
  "user_availabilities": [],
  "reviews": [],
  "social_posts": [],
  "missions": [],
  "game_profiles": [
    {
      "id": 1,
      "service_type_id": 4,
      "identifiers": {
        "lol_username": "player123"
      },
      "is_hidden": false,
      "status": "Reviewed",
      "created_at": "2025-04-25T04:11:36.000000Z",
      "updated_at": "2025-04-25T04:11:36.000000Z",
      "service_type": {
        "id": 4,
        "name": "LoL",
        "description": "League of Legends",
        "icon_path": "service_types/lol_icon.jpg",
        "required_elements": "LOL"
      }
    }
  ]
}
```

**Response (401):**
```json
{
  "message": "Unauthenticated"
}
```

**Response (500):**
```json
{
  "message": "An error occurred while fetching user profile",
  "errors": {
    "server": ["Detailed error message"]
  }
}
```

### Get Complete User Profile by ID
```http
GET /user/all-profile/{id}
```

**Parameters:**
- `id` (path, required): The ID of the user to retrieve

**Headers:**
```
Authorization: Bearer {token}
```

**Response Headers:**
```
Content-Type: application/json
```

**Response (200):**
```json
{
  "id": 9,
  "mobile_number": "+60125187950",
  "email": "<EMAIL>",
  "country_code": "MY",
  "nickname": "Qiule",
  "gender": "Male",
  "date_of_birth": "2025-05-04T00:00:00.000Z",
  "constellation": "Taurus",
  "profile_picture": null,
  "profile_media": {
    "video": null,
    "thumbnail": null,
    "photos": []
  },
  "voice_note": null,
  "biography": null,
  "role": "customer",
  "experience": 0,
  "height": 180,
  "weight": 65,
  "default_language": [
    {
      "id": 1,
      "slug": "cn",
      "name": "Chinese"
    },
    {
      "id": 2,
      "slug": "en",
      "name": "English"
    }
  ],
  "allow_3rd_party_access": false,
  "referral_code": "4382AF0A",
  "personalities": [
    {
      "id": 1,
      "name": "Arrogant",
      "description": "Arrogant"
    },
    {
      "id": 2,
      "name": "Adventurous",
      "description": null
    }
  ],
  "level": {
    "id": 1,
    "level": 1,
    "name": "Beginner",
    "description": "Just starting out",
    "indicator_image": null
  },
  "is_follow": false,
  "total_followers": 10,
  "race": {
    "id": 2,
    "name": "Chinese",
    "description": null
  },
  "services": [
    {
      "id": 1,
      "total_person_order": 20,
      "created_at": "2025-04-25T04:11:36.000000Z",
      "updated_at": "2025-04-24T20:11:51.000000Z",
      "service_category": {
        "id": 1,
        "name": "Gamer",
        "description": "Only Gamers"
      },
      "service_type": {
        "id": 4,
        "name": "LoL",
        "description": "League of Legends",
        "icon_path": "gift_items/optimized/0/a0295581-438d-4725-85af-6c93af2e6a91.jpg",
        "required_elements": "LOL",
        "created_at": "2025-03-27T00:44:06.000000Z",
        "updated_at": "2025-03-27T00:44:06.000000Z",
        "deleted_at": null
      },
      "service_styles": [
        {
          "id": 2,
          "name": "Casual",
          "description": "Casual",
          "min_level_id": 1,
          "can_bypass": false,
          "recommended_price": 0,
          "preset_price": 10,
          "display_order": 2,
          "created_at": "2025-05-18T18:58:48.000000Z",
          "updated_at": "2025-05-18T18:58:48.000000Z"
        }
      ],
      "pricing_options": {
        "id": 1,
        "pricing_option_type": {
          "id": 2,
          "name": "Standard Session",
          "description": "Regular Gaming Session",
          "has_duration": false,
          "unit": null,
          "quantity": null
        }
      }
    }
  ],
  "user_availabilities": [],
  "reviews": [],
  "social_posts": [],
  "missions": [],
  "game_profiles": [
    {
      "id": 1,
      "service_type_id": 4,
      "identifiers": {
        "lol_username": "player123"
      },
      "is_hidden": false,
      "status": "Reviewed",
      "created_at": "2025-04-25T04:11:36.000000Z",
      "updated_at": "2025-04-25T04:11:36.000000Z",
      "service_type": {
        "id": 4,
        "name": "LoL",
        "description": "League of Legends",
        "icon_path": "service_types/lol_icon.jpg",
        "required_elements": "LOL"
      }
    }
  ]
}
```

**Response (401):**
```json
{
  "message": "Unauthenticated"
}
```

**Response (404):**
```json
{
  "message": "User not found",
  "errors": {
    "user": ["User not found"]
  }
}
```

**Response (500):**
```json
{
  "message": "An error occurred while fetching user profile",
  "errors": {
    "server": ["Detailed error message"]
  }
}
```

## Error Responses

**Response Headers for all error responses:**
```
Content-Type: application/json
```

### 401 Unauthorized
```json
{
  "message": "Unauthenticated"
}
```

### 404 Not Found
```json
{
  "message": "User not found",
  "errors": {
    "user": ["User not found"]
  }
}
```

### 500 Server Error
```json
{
  "message": "An error occurred while fetching user profile",
  "errors": {
    "server": ["Detailed error message"]
  }
}
```
