# Chat API Documentation

## Overview

The Chat API provides endpoints for managing real-time messaging between users and talents on the Mission X platform. It enables users to create conversations, send text messages, share image attachments, and track message delivery status.

## Base URL

```
https://api.missionx.com/api
```

## Authentication

All chat endpoints require authentication using a valid JWT token. Include the token in the `Authorization` header:

```
Authorization: Bearer {your_jwt_token}
```

## Endpoints

### Get User Conversations

Retrieves a paginated list of conversations for the authenticated user.

**URL**: `/chat/conversations`

**Method**: `GET`

**Headers**:
- `Content-Type: application/json`
- `Accept: application/json`
- `Authorization: Bearer {token}`

**Query Parameters**:
- `page` (optional): Page number for pagination (default: 1)
- `per_page` (optional): Number of conversations per page (default: 15)

**Success Response**:
- **Code**: 200 OK
- **Content**:
```json
{
  "success": true,
  "data": {
    "current_page": 1,
    "data": [
      {
        "id": 1,
        "user_id": 123,
        "talent_id": 456,
        "order_id": 789,
        "is_active": true,
        "last_message_at": "2025-05-01T12:30:45Z",
        "created_at": "2025-04-15T10:20:30Z",
        "updated_at": "2025-05-01T12:30:45Z",
        "user": {
          "id": 123,
          "nickname": "User123",
          "profile_image": "https://example.com/profiles/user123.jpg"
        },
        "talent": {
          "id": 456,
          "nickname": "Talent456",
          "profile_image": "https://example.com/profiles/talent456.jpg"
        }
      }
    ],
    "first_page_url": "https://api.missionx.com/api/chat/conversations?page=1",
    "from": 1,
    "last_page": 5,
    "last_page_url": "https://api.missionx.com/api/chat/conversations?page=5",
    "next_page_url": "https://api.missionx.com/api/chat/conversations?page=2",
    "path": "https://api.missionx.com/api/chat/conversations",
    "per_page": 15,
    "prev_page_url": null,
    "to": 15,
    "total": 68
  }
}
```

**Error Response**:
- **Code**: 500 Internal Server Error
- **Content**:
```json
{
  "success": false,
  "message": "Failed to get conversations",
  "error": "Server error"
}
```

### Get Conversation Messages

Retrieves a paginated list of messages for a specific conversation.

**URL**: `/chat/conversations/{conversationId}/messages`

**Method**: `GET`

**Headers**:
- `Content-Type: application/json`
- `Accept: application/json`
- `Authorization: Bearer {token}`

**URL Parameters**:
- `conversationId`: ID of the conversation

**Query Parameters**:
- `page` (optional): Page number for pagination (default: 1)
- `per_page` (optional): Number of messages per page (default: 20)

**Success Response**:
- **Code**: 200 OK
- **Content**:
```json
{
  "success": true,
  "data": {
    "current_page": 1,
    "data": [
      {
        "id": 1,
        "conversation_id": 1,
        "sender_id": 123,
        "content": "Hello, are you available for a gaming session?",
        "is_flagged": false,
        "flagged_reason": null,
        "created_at": "2025-05-01T12:30:45Z",
        "updated_at": "2025-05-01T12:30:45Z",
        "sender": {
          "id": 123,
          "nickname": "User123",
          "profile_image": "https://example.com/profiles/user123.jpg"
        },
        "attachments": [],
        "statuses": [
          {
            "id": 1,
            "message_id": 1,
            "user_id": 123,
            "status": "sent",
            "status_timestamp": "2025-05-01T12:30:45Z"
          },
          {
            "id": 2,
            "message_id": 1,
            "user_id": 456,
            "status": "read",
            "status_timestamp": "2025-05-01T12:31:15Z"
          }
        ]
      }
    ],
    "first_page_url": "https://api.missionx.com/api/chat/conversations/1/messages?page=1",
    "from": 1,
    "last_page": 10,
    "last_page_url": "https://api.missionx.com/api/chat/conversations/1/messages?page=10",
    "next_page_url": "https://api.missionx.com/api/chat/conversations/1/messages?page=2",
    "path": "https://api.missionx.com/api/chat/conversations/1/messages",
    "per_page": 20,
    "prev_page_url": null,
    "to": 20,
    "total": 187
  }
}
```

**Error Response**:
- **Code**: 404 Not Found
- **Content**:
```json
{
  "success": false,
  "message": "Failed to get messages",
  "error": "Not found"
}
```

### Start a New Conversation

Creates a new conversation with a talent and sends an initial message.

**URL**: `/chat/conversations`

**Method**: `POST`

**Headers**:
- `Content-Type: application/json`
- `Accept: application/json`
- `Authorization: Bearer {token}`

**Request Body**:
```json
{
  "talent_id": 456,
  "order_id": 789,
  "message": "Hello, I'm interested in your gaming services."
}
```

**Parameters**:
- `talent_id` (required): ID of the talent to start a conversation with
- `order_id` (optional): ID of an associated order
- `message` (required): Initial message content (max 1000 characters)

**Success Response**:
- **Code**: 200 OK
- **Content**:
```json
{
  "success": true,
  "data": {
    "conversation": {
      "id": 1,
      "user_id": 123,
      "talent_id": 456,
      "order_id": 789,
      "is_active": true,
      "last_message_at": "2025-05-01T12:30:45Z",
      "created_at": "2025-05-01T12:30:45Z",
      "updated_at": "2025-05-01T12:30:45Z"
    },
    "message": {
      "id": 1,
      "conversation_id": 1,
      "sender_id": 123,
      "content": "Hello, I'm interested in your gaming services.",
      "is_flagged": false,
      "flagged_reason": null,
      "created_at": "2025-05-01T12:30:45Z",
      "updated_at": "2025-05-01T12:30:45Z"
    }
  }
}
```

**Error Responses**:
- **Code**: 422 Unprocessable Entity
- **Content**:
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": {
    "talent_id": ["The talent id field is required."],
    "message": ["The message field is required."]
  }
}
```

- **Code**: 500 Internal Server Error
- **Content**:
```json
{
  "success": false,
  "message": "Failed to start conversation",
  "error": "Server error"
}
```

### Send a Text Message

Sends a text message to an existing conversation.

**URL**: `/chat/messages/text`

**Method**: `POST`

**Headers**:
- `Content-Type: application/json`
- `Accept: application/json`
- `Authorization: Bearer {token}`

**Request Body**:
```json
{
  "conversation_id": 1,
  "message": "When are you available for a gaming session?"
}
```

**Parameters**:
- `conversation_id` (required): ID of the conversation
- `message` (required): Message content (max 1000 characters)

**Success Response**:
- **Code**: 200 OK
- **Content**:
```json
{
  "success": true,
  "data": {
    "id": 2,
    "conversation_id": 1,
    "sender_id": 123,
    "content": "When are you available for a gaming session?",
    "is_flagged": false,
    "flagged_reason": null,
    "created_at": "2025-05-01T12:35:45Z",
    "updated_at": "2025-05-01T12:35:45Z"
  }
}
```

**Error Responses**:
- **Code**: 422 Unprocessable Entity
- **Content**:
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": {
    "conversation_id": ["The conversation id field is required."],
    "message": ["The message field is required."]
  }
}
```

- **Code**: 500 Internal Server Error
- **Content**:
```json
{
  "success": false,
  "message": "Failed to send message",
  "error": "Server error"
}
```

### Send a Message with Attachment

Sends a message with an image attachment to an existing conversation.

**URL**: `/chat/messages/attachment`

**Method**: `POST`

**Headers**:
- `Content-Type: multipart/form-data`
- `Accept: application/json`
- `Authorization: Bearer {token}`

**Form Data**:
- `conversation_id` (required): ID of the conversation
- `message` (optional): Message content (max 1000 characters)
- `attachment` (required): Image file (JPEG, PNG, GIF, WEBP, HEIC, HEIF, max 10MB)

**Success Response**:
- **Code**: 200 OK
- **Content**:
```json
{
  "success": true,
  "data": {
    "id": 3,
    "conversation_id": 1,
    "sender_id": 123,
    "content": "Check out this screenshot",
    "is_flagged": false,
    "flagged_reason": null,
    "created_at": "2025-05-01T12:40:45Z",
    "updated_at": "2025-05-01T12:40:45Z",
    "attachments": [
      {
        "id": 1,
        "message_id": 3,
        "original_path": "chat_attachments/user_123/original/image_12345.jpg",
        "optimized_path": "chat_attachments/user_123/optimized/image_12345.jpg",
        "filename": "screenshot.jpg",
        "mime_type": "image/jpeg",
        "size": 1024000,
        "extension": "jpg",
        "dimensions": {
          "width": 1280,
          "height": 720
        },
        "created_at": "2025-05-01T12:40:45Z",
        "updated_at": "2025-05-01T12:40:45Z"
      }
    ]
  }
}
```

**Error Responses**:
- **Code**: 422 Unprocessable Entity
- **Content**:
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": {
    "conversation_id": ["The conversation id field is required."],
    "attachment": ["The attachment field is required."]
  }
}
```

- **Code**: 500 Internal Server Error
- **Content**:
```json
{
  "success": false,
  "message": "Failed to send message with attachment",
  "error": "Server error"
}
```

### Update Message Status

Updates the status of a message (delivered or read).

**URL**: `/chat/messages/status`

**Method**: `POST`

**Headers**:
- `Content-Type: application/json`
- `Accept: application/json`
- `Authorization: Bearer {token}`

**Request Body**:
```json
{
  "message_id": 1,
  "status": "read"
}
```

**Parameters**:
- `message_id` (required): ID of the message
- `status` (required): New status value (must be "delivered" or "read")

**Success Response**:
- **Code**: 200 OK
- **Content**:
```json
{
  "success": true,
  "data": {
    "id": 2,
    "message_id": 1,
    "user_id": 456,
    "status": "read",
    "status_timestamp": "2025-05-01T12:45:45Z",
    "created_at": "2025-05-01T12:31:15Z",
    "updated_at": "2025-05-01T12:45:45Z"
  }
}
```

**Error Responses**:
- **Code**: 422 Unprocessable Entity
- **Content**:
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": {
    "message_id": ["The message id field is required."],
    "status": ["The status must be one of: delivered, read."]
  }
}
```

- **Code**: 500 Internal Server Error
- **Content**:
```json
{
  "success": false,
  "message": "Failed to update message status",
  "error": "Server error"
}
```

## Models

### ChatConversation

Represents a conversation between a user and a talent.

**Attributes**:
- `id`: Unique identifier
- `user_id`: ID of the user
- `talent_id`: ID of the talent
- `order_id`: Optional ID of an associated order
- `is_active`: Whether the conversation is active
- `last_message_at`: Timestamp of the last message
- `created_at`: Creation timestamp
- `updated_at`: Last update timestamp
- `deleted_at`: Soft delete timestamp (if deleted)

**Relationships**:
- `user`: The user in the conversation
- `talent`: The talent in the conversation
- `order`: The associated order (if any)
- `messages`: All messages in the conversation

### ChatMessage

Represents a message within a conversation.

**Attributes**:
- `id`: Unique identifier
- `conversation_id`: ID of the conversation
- `sender_id`: ID of the user who sent the message
- `content`: Text content of the message
- `is_flagged`: Whether the message has been flagged for inappropriate content
- `flagged_reason`: Reason for flagging (if flagged)
- `created_at`: Creation timestamp
- `updated_at`: Last update timestamp
- `deleted_at`: Soft delete timestamp (if deleted)

**Relationships**:
- `conversation`: The conversation the message belongs to
- `sender`: The user who sent the message
- `attachments`: Any attachments to the message
- `statuses`: Status updates for the message

### ChatMessageAttachment

Represents an image attachment to a message.

**Attributes**:
- `id`: Unique identifier
- `message_id`: ID of the message
- `original_path`: Path to the original image file
- `optimized_path`: Path to the optimized image file
- `filename`: Original filename
- `mime_type`: MIME type of the file
- `size`: File size in bytes
- `extension`: File extension
- `dimensions`: Image dimensions (width and height)
- `created_at`: Creation timestamp
- `updated_at`: Last update timestamp

**Relationships**:
- `message`: The message the attachment belongs to

### ChatMessageStatus

Represents the delivery status of a message for a specific user.

**Attributes**:
- `id`: Unique identifier
- `message_id`: ID of the message
- `user_id`: ID of the user
- `status`: Status value (sent, delivered, read)
- `status_timestamp`: Timestamp when the status was updated
- `created_at`: Creation timestamp
- `updated_at`: Last update timestamp

**Relationships**:
- `message`: The message the status belongs to
- `user`: The user the status is for

## Message Processing

Messages are processed asynchronously using a queue system:

1. When a message is sent, it is stored in the database with a "sent" status for the sender
2. The message is queued for delivery using RabbitMQ
3. The `ProcessChatMessage` job processes the message:
   - Updates the message status to "delivered" for the recipient
   - Sends a push notification to the recipient's devices using Firebase
4. When the recipient reads the message, the status is updated to "read"

## Content Moderation

All message content is processed for moderation:

1. Text content is sanitized to remove any HTML or potentially harmful content
2. Text is filtered for inappropriate language using the BadWordService
3. If inappropriate content is detected, the message is flagged
4. Image attachments are validated and processed:
   - Metadata is stripped
   - Images are optimized and resized
   - Multiple formats are supported (JPEG, PNG, GIF, WEBP, HEIC, HEIF)

## Notifications

Push notifications are sent to recipients when new messages are received:

1. Device tokens are retrieved for the recipient
2. Firebase Cloud Messaging is used to deliver notifications
3. Notifications include:
   - Sender name
   - Message preview (text or image indicator)
   - Deep link data to open the conversation
