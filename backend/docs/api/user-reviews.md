# User Reviews API

## Overview
This API endpoint allows users to retrieve reviews for a specific user with filtering options.

## Base URL
```
http://103.233.0.105:8000/api
```

## Authentication
This endpoint requires authentication. Include a valid JWT token in the Authorization header:
```
Authorization: Bearer {your_token}
```

## Endpoints

### Get Reviews for a User
Retrieves all reviews received by a specific user with filtering options.

**URL**: `/users/{userId}/reviews`

**Method**: `GET`

**Auth required**: Yes

**Parameters**:
- `userId` (path parameter): The ID of the user to get reviews for
- `rating` (query parameter, optional): Filter by rating value (1-5)
- `from_date` (query parameter, optional): Filter by review date (from) - format YYYY-MM-DD
- `to_date` (query parameter, optional): Filter by review date (to) - format YYYY-MM-DD
- `is_anonymous` (query parameter, optional): Filter by anonymous status (true/false)

**Success Response**:
- **Code**: 200 OK
- **Content-Type**: application/json
- **Content Example**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "order_type": "regular",
      "order_id": 123,
      "reviewer": {
        "id": 5,
        "nickname": "John",
        "profile_picture": "https://example.com/profile.jpg"
      },
      "rating": 5,
      "review_text": "Excellent service!",
      "is_anonymous": false,
      "created_at": "2023-01-01T12:00:00.000Z",
      "updated_at": "2023-01-01T12:00:00.000Z"
    },
    {
      "id": 2,
      "order_type": "scheduled",
      "order_id": 456,
      "reviewer": {
        "id": 8,
        "nickname": "Anonymous",
        "profile_picture": null
      },
      "rating": 4,
      "review_text": "Great work, would recommend!",
      "is_anonymous": true,
      "created_at": "2023-01-02T14:30:00.000Z",
      "updated_at": "2023-01-02T14:30:00.000Z"
    }
  ]
}
```

**Error Responses**:

- **Code**: 401 Unauthorized
- **Content-Type**: application/json
- **Content Example**:
```json
{
  "success": false,
  "message": "Unauthenticated"
}
```

- **Code**: 404 Not Found
- **Content-Type**: application/json
- **Content Example**:
```json
{
  "success": false,
  "message": "User not found"
}
```

- **Code**: 500 Internal Server Error
- **Content-Type**: application/json
- **Content Example**:
```json
{
  "success": false,
  "message": "Failed to get user reviews",
  "error": "Server error"
}
```
