# Language API Documentation

## Overview
API endpoints for managing languages and retrieving language information.

## Base URL
`/api`

## Authentication
- JWT Bearer token required for all endpoints

## Endpoints

### Get All Languages
```http
GET /languages
```

**Headers:**
```
Authorization: Bearer {token}
```

**Response Headers:**
```
Content-Type: application/json
```

**Response (200):**
```json
[
    {
        "id": "integer",
        "name": "string",
        "description": "string|null",
        "is_active": "boolean",
        "display_order": "integer"
    }
]
```

### Get Language by ID
```http
GET /languages/{id}
```

**Headers:**
```
Authorization: Bearer {token}
```

**Response Headers:**
```
Content-Type: application/json
```

**Response (200):**
```json
{
    "id": "integer",
    "name": "string",
    "description": "string|null",
    "is_active": "boolean",
    "display_order": "integer",
    "created_at": "datetime",
    "updated_at": "datetime"
}
```

### Get User Languages
```http
GET /users/languages
```

**Headers:**
```
Authorization: Bearer {token}
```

**Response Headers:**
```
Content-Type: application/json
```

**Response (200):**
```json
{
    "languages": [
        {
            "id": "integer",
            "name": "string",
            "description": "string|null"
        }
    ]
}
```

### Update User Languages
```http
POST /users/languages
```

**Headers:**
```
Authorization: Bearer {token}
Content-Type: application/json
```

**Request Body:**
```json
{
    "language_ids": [1, 2, 3]
}
```

**Response Headers:**
```
Content-Type: application/json
```

**Response (200):**
```json
{
    "message": "Languages updated successfully"
}
```

### User Profile Languages
When retrieving a user profile, the languages associated with the user are included in the response:

```http
GET /users/profile
```

**Response (partial):**
```json
{
    "languages": [
        {
            "id": "integer",
            "name": "string"
        }
    ]
}
```

### Filtering Talents by Language
Languages can be used as a filter parameter when retrieving talents:

```http
GET /talents
```

**Query Parameters:**
```
language: integer or array of integers
```

The `language` parameter accepts either a single language ID or an array of language IDs. When providing multiple IDs, they can be comma-separated or provided as an array parameter.

## Error Responses

**Response Headers for all error responses:**
```
Content-Type: application/json
```

### 401 Unauthorized
```json
{
    "message": "Unauthenticated"
}
```

### 404 Not Found
```json
{
    "message": "Language not found"
}
```

### 422 Validation Error
```json
{
    "message": "The given data was invalid",
    "errors": {
        "language": ["The selected language is invalid."]
    }
}
```
