# Scheduled Order Disputes API

## Overview
This API allows users to create and manage disputes for scheduled orders.

## Base URL
`/api/scheduled-orders/{orderId}/disputes`

## Authentication
All endpoints require JWT authentication.

## Endpoints

### Create a Dispute for a Scheduled Order
Create a new dispute for a specific scheduled order.

**URL**: `POST /api/scheduled-orders/{orderId}/disputes`

**Auth required**: Yes

**Headers**:
- Content-Type: multipart/form-data
- Accept: application/json
- Authorization: Bearer {token}

**Path Parameters**:
- `orderId`: ID of the scheduled order to dispute

**Request Body**:
- `dispute_type_id`: (required) ID of the dispute type
- `description`: (required) Description of the dispute
- `images[]`: (optional) Up to 3 image files (jpeg, png, jpg, heic, heif; max 10MB each)
- `video`: (optional) Single video file (mp4, mov, avi, flv; max 20MB)

**Success Response**:
- Code: 201 CREATED
- Content:
```json
{
  "success": true,
  "message": "Dispute created successfully",
  "data": {
    "id": 1,
    "user_id": 123,
    "requestor_type": "client",
    "order_id": 456,
    "order_type": "scheduled_order",
    "dispute_type_id": 2,
    "description": "Service not as described",
    "status": "submitted",
    "created_at": "2023-05-12T10:00:00.000000Z",
    "updated_at": "2023-05-12T10:00:00.000000Z",
    "disputeType": {
      "id": 2,
      "name": "Service not as described",
      "is_active": true
    },
    "media": [
      {
        "id": 1,
        "dispute_id": 1,
        "media_type": "image",
        "original_path": "disputes/original/123/abc123.jpg",
        "optimized_path": "disputes/optimized/123/abc123.jpg",
        "mime_type": "image/jpeg",
        "file_size": 153600
      }
    ]
  }
}
```

**Error Responses**:
- Code: 400 BAD REQUEST (If order is not in an acceptable state for disputes)
- Code: 403 FORBIDDEN (If user does not have permission)
- Code: 422 UNPROCESSABLE ENTITY (If validation fails)
