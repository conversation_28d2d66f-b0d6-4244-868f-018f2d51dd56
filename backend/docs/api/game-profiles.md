# Game Profiles API

## Overview

The Game Profiles API allows users to manage their game profiles for different service types. Users can create multiple profiles for each service type, which can be either hidden or visible.

## Base URL

```
https://api.missionx.com/api
```

## Authentication

All endpoints require authentication using a Bearer token.

```
Authorization: Bearer {token}
```

## Endpoints

### List User Game Profiles

```
GET /api/user-game-profiles
```

Returns a list of the authenticated user's game profiles.

#### Query Parameters

| Parameter       | Type    | Description                     | Required |
|-----------------|---------|--------------------------------|----------|
| service_type_id | integer | Filter by service type ID      | No       |
| is_hidden       | boolean | Filter by hidden status        | No       |

#### Response

```json
[
  {
    "id": 1,
    "user_id": 42,
    "service_type_id": 5,
    "identifiers": {
      "ign": "<PERSON>One",
      "moontoon_id": "123456789"
    },
    "is_hidden": false,
    "created_at": "2025-05-22T10:30:00Z",
    "updated_at": "2025-05-22T10:30:00Z",
    "service_type": {
      "id": 5,
      "name": "Mobile Legends",
      "description": "Mobile MOBA game"
    }
  }
]
```

### Create Game Profile

```
POST /api/user-game-profiles
```

Creates a new game profile for the authenticated user.

#### Request Body

| Parameter       | Type    | Description                          | Required |
|-----------------|---------|--------------------------------------|----------|
| service_type_id | integer | ID of the service type               | Yes      |
| identifiers     | object  | Game identifiers keyed by game_id.slug | Yes      |
| is_hidden       | boolean | Whether this profile is hidden (default: true) | No       |
| status          | string  | Review status (pendingReview or Reviewed, default: pendingReview) | No       |

Example:

```json
{
  "service_type_id": 5,
  "identifiers": {
    "ign": "PlayerOne",
    "moontoon_id": "123456789"
  },
  "is_hidden": false,
  "status": "pendingReview"
}
```

#### Response

```json
{
  "id": 1,
  "user_id": 42,
  "service_type_id": 5,
  "identifiers": {
    "ign": "PlayerOne",
    "moontoon_id": "123456789"
  },
  "is_hidden": false,
  "status": "pendingReview",
  "created_at": "2025-05-22T10:30:00Z",
  "updated_at": "2025-05-22T10:30:00Z",
  "service_type": {
    "id": 5,
    "name": "Mobile Legends",
    "description": "Mobile MOBA game"
  }
}
```

### Get Game Profile

```
GET /api/user-game-profiles/{id}
```

Returns details of a specific game profile.

#### Path Parameters

| Parameter | Type    | Description        | Required |
|-----------|---------|-------------------|----------|
| id        | integer | Game profile ID   | Yes      |

#### Response

```json
{
  "id": 1,
  "user_id": 42,
  "service_type_id": 5,
  "identifiers": {
    "ign": "PlayerOne",
    "moontoon_id": "123456789"
  },
  "is_hidden": false,
  "status": "pendingReview",
  "created_at": "2025-05-22T10:30:00Z",
  "updated_at": "2025-05-22T10:30:00Z",
  "service_type": {
    "id": 5,
    "name": "Mobile Legends",
    "description": "Mobile MOBA game"
  }
}
```

### Update Game Profile

```
PUT /api/user-game-profiles/{id}
```

Updates an existing game profile.

#### Path Parameters

| Parameter | Type    | Description        | Required |
|-----------|---------|-------------------|----------|
| id        | integer | Game profile ID   | Yes      |

#### Request Body

| Parameter   | Type    | Description                          | Required |
|-------------|---------|--------------------------------------|----------|
| identifiers | object  | Game identifiers keyed by game_id.slug | No       |
| is_hidden   | boolean | Whether this profile is hidden       | No       |
| status      | string  | Review status (pendingReview or Reviewed) | No       |

Example:

```json
{
  "identifiers": {
    "ign": "PlayerTwo",
    "moontoon_id": "987654321"
  },
  "is_hidden": false,
  "status": "Reviewed"
}
```

#### Response

```json
{
  "id": 1,
  "user_id": 42,
  "service_type_id": 5,
  "identifiers": {
    "ign": "PlayerTwo",
    "moontoon_id": "987654321"
  },
  "is_hidden": false,
  "created_at": "2025-05-22T10:30:00Z",
  "updated_at": "2025-05-22T11:15:00Z",
  "service_type": {
    "id": 5,
    "name": "Mobile Legends",
    "description": "Mobile MOBA game"
  }
}
```

### Delete Game Profile

```
DELETE /api/user-game-profiles/{id}
```

Deletes a game profile.

#### Path Parameters

| Parameter | Type    | Description        | Required |
|-----------|---------|-------------------|----------|
| id        | integer | Game profile ID   | Yes      |

#### Response

Returns a 204 No Content response on success.

### Set Game Profile as Visible

```
PUT /api/user-game-profiles/{id}/visible
```

Sets a game profile as visible (unhidden).

#### Path Parameters

| Parameter | Type    | Description        | Required |
|-----------|---------|-------------------|----------|
| id        | integer | Game profile ID   | Yes      |

#### Response

```json
{
  "message": "Game profile set as visible successfully",
  "profile": {
    "id": 1,
    "user_id": 42,
    "service_type_id": 5,
    "identifiers": {
      "ign": "PlayerTwo",
      "moontoon_id": "987654321"
    },
    "is_hidden": false,
    "created_at": "2025-05-22T10:30:00Z",
    "updated_at": "2025-05-22T11:30:00Z",
    "service_type": {
      "id": 5,
      "name": "Mobile Legends",
      "description": "Mobile MOBA game"
    }
  }
}
```
