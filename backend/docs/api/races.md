# Races API

## Overview

The Races API provides endpoints for retrieving race data. Races are categories like Malay, Indian, Chinese, and Arab that users can select when setting up their profile.

## Base URL

All URLs referenced in this documentation have the following base:

```
https://api.missionx.com.my/api
```

## Authentication

The Races API endpoints are public and do not require authentication.

## Endpoints

### Get All Races

Retrieves a list of all active races.

**URL**: `/races`

**Method**: `GET`

**Auth required**: No

**Permissions required**: None

#### Success Response

**Code**: `200 OK`

**Content example**:

```json
[
  {
    "id": 1,
    "name": "Malay",
    "description": "Malay race description"
  },
  {
    "id": 2,
    "name": "Chinese",
    "description": "Chinese race description"
  },
  {
    "id": 3,
    "name": "Indian",
    "description": "Indian race description"
  },
  {
    "id": 4,
    "name": "Arab",
    "description": "Arab race description"
  }
]
```

#### Notes

- The response includes all active races ordered by their display order.
- Race names and descriptions are returned in the current locale.
- If a translation is not available for the current locale, the default name and description are returned.
