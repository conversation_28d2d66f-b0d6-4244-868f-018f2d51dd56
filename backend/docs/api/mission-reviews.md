# Mission Reviews API

This API allows users to create and retrieve reviews for missions.

## Base URL

```
/api/missions/{id}/reviews
```

## Authentication

All endpoints require authentication using a Bear<PERSON> token.

## Endpoints

### Create a Review for a Mission

```
POST /api/missions/{id}/reviews
```

Create a review for a completed mission. The user must be either the mission creator or an approved applicant for the mission.

#### Request Headers

| Header          | Value              | Required |
|-----------------|-------------------|----------|
| Content-Type    | application/json  | Yes      |
| Accept          | application/json  | Yes      |
| Authorization   | Bearer {token}    | Yes      |

#### Path Parameters

| Parameter | Type    | Description     |
|-----------|---------|-----------------|
| id        | integer | ID of the mission |

#### Request Body

| Field       | Type    | Description                                                | Required |
|-------------|---------|------------------------------------------------------------|---------| 
| rating      | integer | Rating from 1 to 5 stars                                   | Yes     |
| review_text | string  | Review text content (max 1000 characters)                  | No      |
| is_anonymous| boolean | Whether the review should be anonymous                     | No      |
| reviewee_id | integer | ID of the user being reviewed (required for mission creators) | Conditional |

> Note: `reviewee_id` is required when the mission creator is reviewing an applicant.

#### Example Request

```json
{
  "rating": 5,
  "review_text": "Great experience working on this mission!",
  "is_anonymous": false
}
```

#### Example Response (201 Created)

```json
{
  "success": true,
  "message": "Review submitted successfully",
  "data": {
    "id": 1,
    "reviewable_type": "App\\Models\\Mission",
    "reviewable_id": 123,
    "reviewer_id": 456,
    "reviewee_id": 789,
    "rating": 5,
    "review_text": "Great experience working on this mission!",
    "is_anonymous": false,
    "is_hidden": false,
    "created_at": "2025-05-27T12:00:00.000000Z",
    "updated_at": "2025-05-27T12:00:00.000000Z"
  }
}
```

#### Error Responses

| Status | Description                                                |
|--------|------------------------------------------------------------|
| 400    | Bad request or user has already reviewed this mission      |
| 401    | Unauthorized                                               |
| 404    | Mission not found or not completed                         |
| 422    | Validation error                                           |
| 500    | Server error                                               |

### Get Reviews for a Mission

```
GET /api/missions/{id}/reviews
```

Retrieve all reviews for a specific mission. The user must be either the mission creator or an applicant for the mission.

#### Request Headers

| Header          | Value              | Required |
|-----------------|-------------------|----------|
| Accept          | application/json  | Yes      |
| Authorization   | Bearer {token}    | Yes      |

#### Path Parameters

| Parameter | Type    | Description     |
|-----------|---------|-----------------|
| id        | integer | ID of the mission |

#### Example Response (200 OK)

```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "reviewable_type": "App\\Models\\Mission",
      "reviewable_id": 123,
      "reviewer_id": 456,
      "reviewee_id": 789,
      "rating": 5,
      "review_text": "Great experience working on this mission!",
      "is_anonymous": false,
      "is_hidden": false,
      "created_at": "2025-05-27T12:00:00.000000Z",
      "updated_at": "2025-05-27T12:00:00.000000Z",
      "reviewer": {
        "id": 456,
        "name": "John Doe",
        "profile_picture": "https://example.com/profile.jpg"
      }
    }
  ]
}
```

#### Error Responses

| Status | Description                                                |
|--------|------------------------------------------------------------|
| 401    | Unauthorized                                               |
| 404    | Mission not found or user not authorized to view reviews   |
| 500    | Server error                                               |
