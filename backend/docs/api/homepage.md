# Homepage API

## Overview
This API provides access to homepage data including new talents, recommended talents, online talents, available missions count, and popular service types.

## Base URL
`/api/homepage`

## Authentication
These endpoints do not require authentication.

## Endpoints

### Get All Homepage Data
Returns all homepage data in a single API call.

- **URL**: `/`
- **Method**: `GET`
- **URL Parameters**:
  - `per_page` (optional): Number of talents per page (default: 10)
  - `limit` (optional): Number of service types to return (default: 10)
- **Headers**:
  - Content-Type: application/json

**Response**:
```json
{
  "new_talents": {
    "current_page": 1,
    "data": [
      {
        "id": 1,
        "nickname": "TalentName",
        "profile_picture": "path/to/image.jpg",
        "level": {
          "id": 1,
          "level": 5,
          "name": "Professional"
        },
        "services": [
          {
            "id": 1,
            "service_type": {
              "id": 1,
              "name": "Gaming"
            },
            "service_style": {
              "id": 1,
              "name": "Competitive"
            }
          }
        ]
      }
    ],
    "first_page_url": "http://localhost:8000/api/homepage/new-talents?page=1",
    "from": 1,
    "last_page": 5,
    "last_page_url": "http://localhost:8000/api/homepage/new-talents?page=5",
    "links": [
      // pagination links
    ],
    "next_page_url": "http://localhost:8000/api/homepage/new-talents?page=2",
    "path": "http://localhost:8000/api/homepage/new-talents",
    "per_page": 10,
    "prev_page_url": null,
    "to": 10,
    "total": 50
  },
  "recommended_talents": {
    // Same format as new_talents
  },
  "online_talents": {
    // Same format as new_talents
  },
  "available_missions_count": 42,
  "popular_service_types": [
    {
      "id": 1,
      "service_category_id": 1,
      "name": "Gaming",
      "description": "Gaming services",
      "icon_path": "path/to/icon.png",
      "is_active": true,
      "display_order": 1,
      "required_elements": ["game", "rank"],
      "created_at": "2023-01-01T00:00:00.000000Z",
      "updated_at": "2023-01-01T00:00:00.000000Z",
      "deleted_at": null,
      "usage_count": 42
    }
  ]
}
```

## Individual Endpoints

### Get New Registered Talents
Returns a list of newly registered talents.

- **URL**: `/new-talents`
- **Method**: `GET`
- **URL Parameters**:
  - `per_page` (optional): Number of talents per page (default: 10)
  - `page` (optional): Page number (default: 1)
- **Headers**:
  - Content-Type: application/json

**Response**:
```json
{
  "current_page": 1,
  "data": [
    {
      "id": 1,
      "nickname": "TalentName",
      "profile_picture": "path/to/image.jpg",
      "level": {
        "id": 1,
        "level": 5,
        "name": "Professional"
      },
      "services": [
        {
          "id": 1,
          "service_type": {
            "id": 1,
            "name": "Gaming"
          },
          "service_style": {
            "id": 1,
            "name": "Competitive"
          }
        }
      ]
    }
  ],
  "first_page_url": "http://localhost:8000/api/homepage/new-talents?page=1",
  "from": 1,
  "last_page": 5,
  "last_page_url": "http://localhost:8000/api/homepage/new-talents?page=5",
  "links": [
    // pagination links
  ],
  "next_page_url": "http://localhost:8000/api/homepage/new-talents?page=2",
  "path": "http://localhost:8000/api/homepage/new-talents",
  "per_page": 10,
  "prev_page_url": null,
  "to": 10,
  "total": 50
}
```

### Get Recommended Talents
Returns a list of recommended talents based on acceptance rate and reviews.

- **URL**: `/recommended-talents`
- **Method**: `GET`
- **URL Parameters**:
  - `per_page` (optional): Number of talents per page (default: 10)
  - `page` (optional): Page number (default: 1)
- **Headers**:
  - Content-Type: application/json

**Response**: Same format as New Registered Talents.

### Get Online Talents
Returns a list of currently available talents.

- **URL**: `/online-talents`
- **Method**: `GET`
- **URL Parameters**:
  - `per_page` (optional): Number of talents per page (default: 10)
  - `page` (optional): Page number (default: 1)
- **Headers**:
  - Content-Type: application/json

**Response**: Same format as New Registered Talents.

### Get Available Missions Count
Returns the count of available missions.

- **URL**: `/available-missions-count`
- **Method**: `GET`
- **Headers**:
  - Content-Type: application/json

**Response**:
```json
{
  "available_missions_count": 42
}
```

### Get Popular Service Types
Returns a list of popular service types with their icons.

- **URL**: `/popular-service-types`
- **Method**: `GET`
- **URL Parameters**:
  - `limit` (optional): Number of service types to return (default: 10)
- **Headers**:
  - Content-Type: application/json

**Response**:
```json
[
  {
    "id": 1,
    "service_category_id": 1,
    "name": "Gaming",
    "description": "Gaming services",
    "icon_path": "path/to/icon.png",
    "is_active": true,
    "display_order": 1,
    "required_elements": ["game", "rank"],
    "created_at": "2023-01-01T00:00:00.000000Z",
    "updated_at": "2023-01-01T00:00:00.000000Z",
    "deleted_at": null,
    "usage_count": 42
  }
]
```
