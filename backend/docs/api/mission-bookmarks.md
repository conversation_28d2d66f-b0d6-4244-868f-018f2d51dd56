# Mission Bookmarks API

## Overview
The Mission Bookmarks API allows users to bookmark missions and retrieve their bookmarked missions.

## Base URL
`/api`

## Authentication
All endpoints require authentication using JW<PERSON> token.

## Endpoints

### Toggle Mission Bookmark
**POST** `/missions/{mission}/bookmark`

Toggle the bookmark status of a mission for the authenticated user.

#### Parameters
- `mission` (path parameter): ID of the mission to bookmark/unbookmark

#### Headers
- `Content-Type: application/json`
- `Accept: application/json`
- `Authorization: Bearer {token}`

#### Response
```json
{
  "action": "bookmarked" // or "unbookmarked"
}
```

#### Status Codes
- `200 OK`: Bookmark status toggled successfully
- `401 Unauthorized`: User is not authenticated
- `404 Not Found`: Mission not found

### Get Bookmarked Missions
**GET** `/missions/user/bookmarked-missions`

Get all missions bookmarked by the authenticated user.

#### Parameters
- `page` (query parameter, optional): Page number for pagination (default: 1)

#### Headers
- `Content-Type: application/json`
- `Accept: application/json`
- `Authorization: Bearer {token}`

#### Response
```json
{
  "data": [
    {
      "id": 1,
      "user_id": 1,
      "service_type_id": 1,
      "service_style_id": 2,
      "description": "Mission description",
      "bounty": 100,
      "pax_required": 5,
      "min_level_id": 2,
      "max_level_id": 5,
      "service_start_date": "2025-06-01T10:00:00Z",
      "service_end_date": "2025-06-01T12:00:00Z",
      "status": "open",
      "created_at": "2025-05-01T12:00:00Z",
      "updated_at": "2025-05-01T12:00:00Z",
      "is_bookmarked": true,
      "service_type": {
        "id": 1,
        "name": "Gaming"
      },
      "service_style": {
        "id": 2,
        "name": "Coaching"
      },
      "min_level": {
        "id": 2,
        "name": "Intermediate"
      },
      "max_level": {
        "id": 5,
        "name": "Expert"
      },
      "user": {
        "id": 1,
        "name": "John Doe",
        "profile_picture": "https://example.com/profile.jpg"
      }
    }
  ],
  "links": {
    "first": "http://example.com/api/user/bookmarked-missions?page=1",
    "last": "http://example.com/api/user/bookmarked-missions?page=1",
    "prev": null,
    "next": null
  },
  "meta": {
    "current_page": 1,
    "from": 1,
    "last_page": 1,
    "path": "http://example.com/api/user/bookmarked-missions",
    "per_page": 10,
    "to": 1,
    "total": 1
  }
}
```

#### Status Codes
- `200 OK`: Request successful
- `401 Unauthorized`: User is not authenticated
