# Mission Posting API

## Overview

The Mission Posting API provides endpoints for users to create job missions and manually approve applicants. This API enables customers to post missions with specific requirements and manage the applicants who wish to participate in these missions.

## Base URL

```
https://api.mission-x.com/api
```

## Authentication

All endpoints require authentication using a JW<PERSON> token in the Authorization header:

```
Authorization: Bearer {your_token}
```

## Image Upload Guidelines

* Maximum 5 images per mission
* Maximum file size: 20MB per image
* Supported formats: JPEG, PNG, HEIC/HEIF (iOS), WebP (Android)
* Images will be automatically optimized while preserving quality
* Original and optimized versions are stored in CDN

## Endpoints

### Missions

#### List Missions
```
GET /missions
```

**Query Parameters:**
- `status` (string, optional): Filter by mission status (open, closed, completed)
- `type_id` (integer, optional): Filter by service type ID
- `style_id` (integer, optional): Filter by service style ID
- `min_level_id` (integer, optional): Filter by minimum level ID

**Response:**
```json
{
  "current_page": 1,
  "data": [
    {
      "id": 1,
      "user_id": 123,
      "service_type_id": 1,
      "service_style_id": 2,
      "description": "Looking for experienced Valorant players to help with team practice",
      "bounty": 500,
      "pax_required": 5,
      "min_level_id": 3,
      "service_start_date": "2025-04-15T14:00:00.000000Z",
      "status": "open",
      "created_at": "2025-03-25T12:00:00.000000Z",
      "updated_at": "2025-03-25T12:00:00.000000Z",
      "serviceCategory": {
        "id": 1,
        "name": "Gamer",
        "description": "Services provided by gamers"
      },
      "minLevel": {
        "id": 3,
        "name": "Intermediate",
        "level": 3,
        "min_experience": 1000,
        "max_experience": 2999
      }
    }
  ],
  "first_page_url": "https://api.mission-x.com/api/missions?page=1",
  "from": 1,
  "last_page": 1,
  "last_page_url": "https://api.mission-x.com/api/missions?page=1",
  "next_page_url": null,
  "path": "https://api.mission-x.com/api/missions",
  "per_page": 15,
  "prev_page_url": null,
  "to": 1,
  "total": 1
}
```

#### Create Mission
```
POST /missions
```

**Headers:**
```
Content-Type: application/json
```

**Request Body:**
```json
{
  "service_type_id": 1,
  "service_style_id": 2,
  "description": "Looking for experienced Valorant players to help with team practice",
  "bounty": 500,
  "pax_required": 5,
  "min_level_id": 3,
  "service_start_date": "2025-04-15T14:00:00.000000Z",
  "service_end_date": "2025-04-30T14:00:00.000000Z",
  "is_anonymous": false,
  "images": [
    {
      "id": 1,
      "mission_id": 1,
      "original_path": "missions/original/1/abc123.jpg",
      "optimized_path": "missions/optimized/1/def456.jpg",
      "file_name": "house.jpg",
      "mime_type": "image/jpeg",
      "size": 2048000,
      "extension": "jpg",
      "dimensions": {
        "width": 1920,
        "height": 1080
      },
      "converted": false,
      "created_at": "2025-03-25T05:00:00.000000Z",
      "updated_at": "2025-03-25T05:00:00.000000Z"
    }
  ]
}
```

**Response:**
```json
{
  "message": "Mission created successfully",
  "mission": {
    "id": 1,
    "user_id": 123,
    "service_type_id": 1,
  "service_style_id": 2,
    "description": "Looking for experienced Valorant players to help with team practice",
    "bounty": 500,
    "pax_required": 5,
    "min_level_id": 3,
    "service_start_date": "2025-04-15T14:00:00.000000Z",
    "service_end_date": "2025-04-30T14:00:00.000000Z",
    "status": "open",
    "created_at": "2025-03-25T12:00:00.000000Z",
    "updated_at": "2025-03-25T12:00:00.000000Z",
    "serviceType": {
      "id": 1,
      "name": "Gamer",
      "description": "Services provided by gamers"
    },
    "serviceStyle": {
      "id": 2,
      "name": "Competitive",
      "description": "Competitive gaming services"
    },
    "minLevel": {
      "id": 3,
      "name": "Intermediate",
      "level": 3,
      "min_experience": 1000,
      "max_experience": 2999
    }
  }
}
```

#### Get Mission Details
```
GET /missions/{id}
```

**Path Parameters:**
- `id` (integer, required): Mission ID

**Response:**
```json
{
  "id": 1,
  "user_id": 123,
  "service_type_id": 1,
  "service_style_id": 2,
  "description": "Looking for experienced Valorant players to help with team practice",
  "bounty": 500,
  "pax_required": 5,
  "min_level_id": 3,
  "service_start_date": "2025-04-15T14:00:00.000000Z",
  "service_end_date": "2025-04-30T14:00:00.000000Z",
  "is_anonymous": false,
  "status": "open",
  "created_at": "2025-03-25T12:00:00.000000Z",
  "updated_at": "2025-03-25T12:00:00.000000Z",
  "serviceType": {
    "id": 1,
    "name": "Gamer",
    "description": "Services provided by gamers"
  },
  "serviceStyle": {
    "id": 2,
    "name": "Competitive",
    "description": "Competitive gaming services"
  },
  "minLevel": {
    "id": 3,
    "name": "Intermediate",
    "level": 3,
    "min_experience": 1000,
    "max_experience": 2999
  },
  "applicants": [
    {
      "id": 1,
      "mission_id": 1,
      "user_id": 456,
      "status": "pending",
      "notes": "I'm interested in joining your team practice",
      "created_at": "2025-03-25T12:30:00.000000Z",
      "updated_at": "2025-03-25T12:30:00.000000Z",
      "user": {
        "id": 456,
        "name": "John Doe",
        "profile_picture": "https://example.com/profiles/johndoe.jpg"
      }
    }
  ]
}
```

#### Update Mission
```
PUT /missions/{id}
```

**Path Parameters:**
- `id` (integer, required): Mission ID

**Headers:**
```
Content-Type: application/json
```

**Request Body:**
```json
{
  "service_type_id": 1,
  "service_style_id": 2,
  "description": "Looking for experienced Valorant players to help with team practice. Must be at least Gold rank.",
  "bounty": 600,
  "pax_required": 4,
  "min_level_id": 3,
  "service_start_date": "2025-04-16T15:00:00.000000Z",
  "service_end_date": "2025-05-01T15:00:00.000000Z",
  "is_anonymous": true,
  "status": "open"
}
```

**Response:**
```json
{
  "message": "Mission updated successfully",
  "mission": {
    "id": 1,
    "user_id": 123,
    "service_type_id": 1,
  "service_style_id": 2,
    "description": "Looking for experienced Valorant players to help with team practice. Must be at least Gold rank.",
    "bounty": 600,
    "pax_required": 4,
    "min_level_id": 3,
    "service_start_date": "2025-04-16T15:00:00.000000Z",
    "status": "open",
    "created_at": "2025-03-25T12:00:00.000000Z",
    "updated_at": "2025-03-25T13:00:00.000000Z",
    "serviceType": {
      "id": 1,
      "name": "Gamer",
      "description": "Services provided by gamers"
    },
    "serviceStyle": {
      "id": 2,
      "name": "Competitive",
      "description": "Competitive gaming services"
    },
### Delete Mission Image
```
DELETE /missions/{mission_id}/images/{image_id}
```

**Path Parameters:**
- `mission_id` (integer, required): Mission ID
- `image_id` (integer, required): Image ID

**Response:**
```json
{
  "message": "Mission image deleted successfully"
}
```

      "name": "Gamer",
      "description": "Services provided by gamers"
    },
    "minLevel": {
      "id": 3,
      "name": "Intermediate",
      "level": 3,
      "min_experience": 1000,
      "max_experience": 2999
    }
  }
}
```

#### Delete Mission
```
DELETE /missions/{id}
```

**Path Parameters:**
- `id` (integer, required): Mission ID

**Response:**
```json
{
  "message": "Mission deleted successfully"
}
```

### Mission Applicants

#### Apply for Mission
```
POST /missions/{mission_id}/apply
```

**Path Parameters:**
- `mission_id` (integer, required): Mission ID

**Headers:**
```
Content-Type: application/json
```

**Request Body:**
```json
{
  "notes": "I'm interested in joining your team practice"
}
```

**Response:**
```json
{
  "message": "Application submitted successfully",
  "application": {
    "id": 1,
    "mission_id": 1,
    "user_id": 456,
    "status": "pending",
    "notes": "I'm interested in joining your team practice",
    "created_at": "2025-03-25T12:30:00.000000Z",
    "updated_at": "2025-03-25T12:30:00.000000Z"
  }
}
```

#### Get Mission Applicants
```
GET /missions/{mission_id}/applicants
```

**Path Parameters:**
- `mission_id` (integer, required): Mission ID

**Response:**
```json
[
  {
    "id": 1,
    "mission_id": 1,
    "user_id": 456,
    "status": "pending",
    "notes": "I'm interested in joining your team practice",
    "created_at": "2025-03-25T12:30:00.000000Z",
    "updated_at": "2025-03-25T12:30:00.000000Z",
    "user": {
      "id": 456,
      "name": "John Doe",
      "profile_picture": "https://example.com/profiles/johndoe.jpg"
    }
  },
  {
    "id": 2,
    "mission_id": 1,
    "user_id": 789,
    "status": "pending",
    "notes": "I have experience in competitive Valorant",
    "created_at": "2025-03-25T12:45:00.000000Z",
    "updated_at": "2025-03-25T12:45:00.000000Z",
    "user": {
      "id": 789,
      "name": "Jane Smith",
      "profile_picture": "https://example.com/profiles/janesmith.jpg"
    }
  }
]
```

#### Approve Applicant
```
POST /missions/{mission_id}/applicants/{applicant_id}/approve
```

**Path Parameters:**
- `mission_id` (integer, required): Mission ID
- `applicant_id` (integer, required): Applicant ID

**Response:**
```json
{
  "message": "Applicant approved successfully",
  "mission_status": "open",
  "approved_count": 1,
  "required_count": 5
}
```

#### Reject Applicant
```
POST /missions/{mission_id}/applicants/{applicant_id}/reject
```

**Path Parameters:**
- `mission_id` (integer, required): Mission ID
- `applicant_id` (integer, required): Applicant ID

**Headers:**
```
Content-Type: application/json
```

**Request Body:**
```json
{
  "rejection_reason": "Looking for applicants with more experience"
}
```

**Response:**
```json
{
  "message": "Applicant rejected successfully"
}
```

## Error Responses

### Validation Error
```json
{
  "message": "The given data was invalid.",
  "errors": {
    "description": [
      "The description field is required."
    ],
    "bounty": [
      "The bounty must be at least 1."
    ]
  }
}
```

### Authorization Error
```json
{
  "message": "You are not authorized to perform this action."
}
```

### Resource Not Found
```json
{
  "message": "The requested resource was not found."
}
```

### Business Logic Error
```json
{
  "message": "Cannot approve applicants for a closed or completed mission"
}
```
