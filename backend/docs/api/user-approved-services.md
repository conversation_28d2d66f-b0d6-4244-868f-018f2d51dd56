# User Approved Services API

## Overview
This API endpoint allows users to retrieve approved services offered by a specific user.

## Base URL
```
http://103.233.0.105:8000/api
```

## Authentication
This endpoint requires authentication. Include a valid JWT token in the Authorization header:
```
Authorization: Bearer {your_token}
```

## Endpoints

### Get Approved Services for a User
Retrieves all approved services offered by a specific user with optional filtering.

**URL**: `/user/services/user/{userId}/approved`

**Method**: `GET`

**Auth required**: Yes

**Parameters**:
- `userId` (path parameter): The ID of the user to get services for
- `is_active` (query parameter, optional): Filter by active status (true/false)
- `service_type_id` (query parameter, optional): Filter by service type ID
- `service_style_id` (query parameter, optional): Filter by service style ID

**Success Response**:
- **Code**: 200 OK
- **Content-Type**: application/json
- **Content Example**:
```json
[
  {
    "id": 1,
    "user_id": 42,
    "service_type_id": 3,
    "service_style_id": 7,
    "title": "Professional Software Development",
    "description": "Expert software development services.",
    "service_elements": ["code review", "feature development"],
    "status": "approved",
    "is_active": true,
    "created_at": "2023-01-01T12:00:00.000Z",
    "updated_at": "2023-01-01T12:30:00.000Z",
    "serviceType": {
      "id": 3,
      "name": "Software Development"
    },
    "serviceStyle": {
      "id": 7,
      "name": "Professional"
    },
    "pricingOptions": [
      {
        "id": 5,
        "user_service_id": 1,
        "pricing_option_id": 2,
        "is_active": true,
        "pricingOption": {
          "id": 2,
          "name": "Hourly Rate"
        }
      }
    ]
  }
]
```

**Error Responses**:

- **Code**: 401 Unauthorized
- **Content-Type**: application/json
- **Content Example**:
```json
{
  "message": "Unauthenticated"
}
```

- **Code**: 404 Not Found
- **Content-Type**: application/json
- **Content Example**:
```json
{
  "message": "User not found"
}
```
