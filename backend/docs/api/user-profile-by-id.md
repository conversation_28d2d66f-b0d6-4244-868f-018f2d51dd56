# User Profile by ID API

## Overview
This API endpoint allows authenticated users to retrieve the profile of any user by their ID.

## Base URL
```
http://103.233.0.105:8000/api
```

## Authentication
This endpoint requires authentication. Include a valid JWT token in the Authorization header:
```
Authorization: Bearer {your_token}
```

## Endpoints

### Get User Profile by ID
Retrieves the complete profile of a user by their ID.

**URL**: `/user/profile/{id}`

**Method**: `GET`

**Auth required**: Yes

**Parameters**:
- `id` (path parameter): The ID of the user to retrieve

**Headers:**
```
Authorization: Bearer {token}
```

**Response Headers:**
```
Content-Type: application/json
```

**Success Response**:
- **Code**: 200 OK
- **Content-Type**: application/json
- **Content Example**:
```json
{
  "id": 1,
  "mobile_number": "+601122220000",
  "email": "<EMAIL>",
  "country_code": "M<PERSON>",
  "nickname": "<PERSON> 👨‍💻",
  "gender": "Male",
  "date_of_birth": "1990-01-01T00:00:00.000Z",
  "constellation": "Capricorn",
  "profile_picture": null,
  "voice_note": "users/1/1617123456.aac",
  "biography": "I am a software developer with 5 years of experience.",
  "role": "customer",
  "experience": 5,
  "race": {
    "id": 1,
    "name": "Asian",
    "description": "Asian ethnicity"
  },
  "height": 175.5,
  "weight": 70.2,
  "default_language": "en",
  "allow_3rd_party_access": true,
  "referral_code": "XYZ789AB",
  "personalities": [
    {
      "id": 1,
      "name": "Friendly",
      "description": "Friendly personality trait"
    }
  ],
  "level": {
    "id": 1,
    "level": 1,
    "name": "Beginner",
    "description": "Just starting out",
    "indicator_image": "levels/beginner.png"
  }
}
```

**Error Responses**:

- **Code**: 401 Unauthorized
- **Content-Type**: application/json
- **Content Example**:
```json
{
  "message": "Unauthenticated"
}
```

- **Code**: 404 Not Found
- **Content-Type**: application/json
- **Content Example**:
```json
{
  "message": "User not found"
}
```

- **Code**: 422 Validation Error
- **Content-Type**: application/json
- **Content Example**:
```json
{
  "message": "The given data was invalid",
  "errors": {
    "field": ["error message"]
  }
}
```

## Notes
- All date fields are returned in DateTime format (ISO 8601)
- The response structure is identical to the current user profile endpoint
- Only authenticated users can access this endpoint
- The constellation field is calculated based on the user's date of birth
