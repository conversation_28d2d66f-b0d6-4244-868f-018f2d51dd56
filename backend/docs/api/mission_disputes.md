# Mission Disputes API

## Overview

The Mission Disputes API allows users to create and manage disputes related to missions. This API is used when there are issues with mission applications or completion that need to be resolved by the platform administrators.

## Base URL

```
/api/missions/{mission}/disputes
```

## Authentication

All endpoints require authentication using a valid JW<PERSON> token in the Authorization header.

## Endpoints

### Create Mission Dispute

Create a new dispute for a mission.

**URL**: `/api/missions/{mission}/disputes`

**Method**: `POST`

**Auth required**: Yes

**Headers**:
- `Content-Type: application/json`
- `Accept: application/json`
- `Authorization: Bearer {token}`

**URL Parameters**:
- `mission` - ID of the mission to create a dispute for

**Request Body**:

```json
{
  "dispute_type_id": 1,
  "description": "The applicant did not complete the mission as agreed",
  "child_id": 123,  // Required only if requestor_type is 'creator'
  "media": [
    // Optional: Up to 3 images and 1 video can be uploaded
  ]
}
```

**Response**:

```json
{
  "message": "Dispute created successfully",
  "data": {
    "id": 1,
    "user_id": 456,
    "requestor_type": "creator",
    "mission_id": 789,
    "child_id": 123,
    "dispute_type_id": 1,
    "description": "The applicant did not complete the mission as agreed",
    "status": "submitted",
    "resolution_type": null,
    "resolution_notes": null,
    "refund_amount": "0.00",
    "resolved_at": null,
    "created_at": "2025-05-13T03:42:56.000000Z",
    "updated_at": "2025-05-13T03:42:56.000000Z",
    "media": []
  }
}
```

**Status Codes**:
- `201 Created` - Dispute created successfully
- `400 Bad Request` - Invalid request parameters
- `403 Forbidden` - User is not authorized to create a dispute for this mission
- `422 Unprocessable Entity` - Validation errors

**Notes**:
- If the user is the mission creator, they must specify which applicant they are disputing against using the `child_id` parameter.
- If the user is an applicant, they do not need to specify a `child_id`.
- Media files can be uploaded as form-data with the key `media[]`.
- Maximum 3 images and 1 video can be uploaded.
- Supported image formats: JPEG, PNG, HEIC, HEIF.
- Supported video formats: MP4, MOV, AVI, FLV.
- Maximum file size: 20MB.

## Using Existing Dispute Endpoints

For other dispute operations (viewing disputes, getting dispute types, etc.), use the existing dispute endpoints:

- `GET /api/disputes/types` - Get all dispute types
- `GET /api/disputes` - Get all disputes for the authenticated user
- `GET /api/disputes/{id}` - Get a specific dispute

These endpoints will include mission disputes in their responses.
