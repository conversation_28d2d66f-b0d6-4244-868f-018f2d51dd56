# Carousel Module API

## Overview
The Carousel Module API provides endpoints for managing carousel slides in the application. These slides can be displayed on the frontend and can be configured to redirect users to other parts of the application or external websites when clicked.

## Base URL
`https://api.example.com/api`

## Authentication
All carousel management endpoints require authentication. Include the bearer token in the Authorization header:

```
Authorization: Bearer <token>
```

## Endpoints

### Get All Carousel Slides

Retrieves all active carousel slides ordered by display_order.

**URL**: `/carousel-slides`  
**Method**: `GET`  
**Auth required**: Yes  
**Headers**:
- Content-Type: application/json
- Accept: application/json
- Authorization: Bearer <token>

#### Success Response

**Code**: `200 OK`  
**Content example**:

```json
[
  {
    "id": 1,
    "title": "Welcome to Mission X",
    "content": "Join our gaming community today",
    "media_files": [
      {
        "original": "carousel_slides/original/0/2f81f8a8-3b3e-4f5c-9d7e-1d2c3a4b5c6d.jpg",
        "optimized": "carousel_slides/optimized/0/8f7e6d5c-4b3a-2c1d-0e9f-8a7b6c5d4e3f.jpg",
        "filename": "welcome_banner.jpg",
        "mime_type": "image/jpeg",
        "size": 512000,
        "extension": "jpg",
        "dimensions": {
          "width": 1920,
          "height": 1080
        }
      }
    ],
    "target_url": "https://missionx.com/signup",
    "is_clickable": true,
    "display_order": 1,
    "is_active": true,
    "created_at": "2023-06-01T12:00:00.000000Z",
    "updated_at": "2023-06-01T12:00:00.000000Z"
  }
]
```

### Get Specific Carousel Slide

Retrieves a specific carousel slide by ID.

**URL**: `/carousel-slides/{id}`  
**Method**: `GET`  
**Auth required**: Yes  
**Headers**:
- Content-Type: application/json
- Accept: application/json
- Authorization: Bearer <token>

#### Success Response

**Code**: `200 OK`  
**Content example**:

```json
{
  "id": 1,
  "title": "Welcome to Mission X",
  "content": "Join our gaming community today",
  "media_files": [
    {
      "original": "carousel_slides/original/0/2f81f8a8-3b3e-4f5c-9d7e-1d2c3a4b5c6d.jpg",
      "optimized": "carousel_slides/optimized/0/8f7e6d5c-4b3a-2c1d-0e9f-8a7b6c5d4e3f.jpg",
      "filename": "welcome_banner.jpg",
      "mime_type": "image/jpeg",
      "size": 512000,
      "extension": "jpg",
      "dimensions": {
        "width": 1920,
        "height": 1080
      }
    }
  ],
  "target_url": "https://missionx.com/signup",
  "is_clickable": true,
  "display_order": 1,
  "is_active": true,
  "created_at": "2023-06-01T12:00:00.000000Z",
  "updated_at": "2023-06-01T12:00:00.000000Z"
}
```

### Create Carousel Slide

Creates a new carousel slide.

**URL**: `/carousel-slides`  
**Method**: `POST`  
**Auth required**: Yes  
**Headers**:
- Accept: application/json
- Authorization: Bearer <token>
- Content-Type: multipart/form-data (for file upload)

#### Request Parameters

| Parameter     | Type    | Required | Description                                      |
|---------------|---------|----------|--------------------------------------------------|
| title         | string  | No       | Title of the slide (max 100 characters)          |
| content       | string  | No       | Content/description of the slide (max 500 chars) |
| media_files[] | file    | Yes      | Image file (jpeg, png, jpg, heic, heif, webp)    |
| target_url    | string  | No       | URL to redirect when slide is clicked            |
| is_clickable  | boolean | No       | Whether the slide is clickable (default: false)  |
| display_order | integer | No       | Order to display the slide (default: 0)          |
| is_active     | boolean | No       | Whether the slide is active (default: true)      |

#### Success Response

**Code**: `201 Created`  
**Content example**:

```json
{
  "id": 1,
  "title": "Welcome to Mission X",
  "content": "Join our gaming community today",
  "media_files": [
    {
      "original": "carousel_slides/original/0/2f81f8a8-3b3e-4f5c-9d7e-1d2c3a4b5c6d.jpg",
      "optimized": "carousel_slides/optimized/0/8f7e6d5c-4b3a-2c1d-0e9f-8a7b6c5d4e3f.jpg",
      "filename": "welcome_banner.jpg",
      "mime_type": "image/jpeg",
      "size": 512000,
      "extension": "jpg",
      "dimensions": {
        "width": 1920,
        "height": 1080
      },
      "device_info": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36..."
    }
  ],
  "target_url": "https://missionx.com/signup",
  "is_clickable": true,
  "display_order": 1,
  "is_active": true,
  "created_at": "2023-06-01T12:00:00.000000Z",
  "updated_at": "2023-06-01T12:00:00.000000Z"
}
```

### Update Carousel Slide

Updates an existing carousel slide.

**URL**: `/carousel-slides/{id}`  
**Method**: `PUT` or `PATCH`  
**Auth required**: Yes  
**Headers**:
- Accept: application/json
- Authorization: Bearer <token>
- Content-Type: multipart/form-data (for file upload)

#### Request Parameters

| Parameter     | Type    | Required | Description                                      |
|---------------|---------|----------|--------------------------------------------------|
| title         | string  | No       | Title of the slide (max 100 characters)          |
| content       | string  | No       | Content/description of the slide (max 500 chars) |
| media_files[] | file    | No       | Image file (jpeg, png, jpg, heic, heif, webp)    |
| target_url    | string  | No       | URL to redirect when slide is clicked            |
| is_clickable  | boolean | No       | Whether the slide is clickable                   |
| display_order | integer | No       | Order to display the slide                       |
| is_active     | boolean | No       | Whether the slide is active                      |

#### Success Response

**Code**: `200 OK`  
**Content example**:

```json
{
  "id": 1,
  "title": "Updated Title",
  "content": "Updated content for the slide",
  "media_files": [
    {
      "original": "carousel_slides/original/0/2f81f8a8-3b3e-4f5c-9d7e-1d2c3a4b5c6d.jpg",
      "optimized": "carousel_slides/optimized/0/8f7e6d5c-4b3a-2c1d-0e9f-8a7b6c5d4e3f.jpg",
      "filename": "welcome_banner.jpg",
      "mime_type": "image/jpeg",
      "size": 512000,
      "extension": "jpg",
      "dimensions": {
        "width": 1920,
        "height": 1080
      },
      "device_info": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36..."
    }
  ],
  "target_url": "https://missionx.com/new-promotion",
  "is_clickable": true,
  "display_order": 2,
  "is_active": true,
  "created_at": "2023-06-01T12:00:00.000000Z",
  "updated_at": "2023-06-05T15:30:00.000000Z"
}
```

### Delete Carousel Slide

Deletes a carousel slide (soft delete).

**URL**: `/carousel-slides/{id}`  
**Method**: `DELETE`  
**Auth required**: Yes  
**Headers**:
- Content-Type: application/json
- Accept: application/json
- Authorization: Bearer <token>

#### Success Response

**Code**: `200 OK`  
**Content example**:

```json
{
  "message": "Carousel slide deleted successfully"
}
```
