<?php

namespace App\Providers;

use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        'App\Models\UserService' => 'App\Policies\UserServicePolicy',
        'App\Models\EmergencyContact' => 'App\Policies\EmergencyContactPolicy',
        'App\Models\Mission' => 'App\Policies\MissionPolicy',
        'App\Models\MissionApplicant' => 'App\Policies\MissionApplicantPolicy',
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        $this->registerPolicies();
    }
}
