<?php

namespace App\Services;

use App\Models\Mission;
use App\Models\MissionApplicant;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use App\Services\MissionCommissionService;

class MissionCreditService
{
    protected $creditService;
    protected $missionCommissionService;

    public function __construct(
        CreditService $creditService,
        MissionCommissionService $missionCommissionService = null
    ) {
        $this->creditService = $creditService;
        $this->missionCommissionService = $missionCommissionService;
    }

    /**
     * Hold credits for a mission applicant
     *
     * @param Mission $mission
     * @param MissionApplicant $applicant
     * @return void
     */
    public function holdCreditsForMission(Mission $mission, MissionApplicant $applicant)
    {
        $bounty = $mission->bounty;
        
        DB::transaction(function () use ($bounty, $mission, $applicant) {
            $applicant->update([
                'credit_amount' => $bounty,
                'is_credit_held' => true,
                'credit_held_at' => now(),
            ]);
        });
    }

    /**
     * Release credits to an applicant when mission is completed
     *
     * @param Mission $mission
     * @param MissionApplicant $applicant
     * @return void
     */
    public function releaseCreditsToApplicant(Mission $mission, MissionApplicant $applicant)
    {
        $applicant->load('user');
        $user = $applicant->user;
        
        DB::transaction(function () use ($mission, $applicant, $user) {
            $this->creditService->addCredits(
                $user,
                $applicant->credit_amount,
                "Payment for Mission #{$mission->id}",
                [
                    'mission_id' => $mission->id,
                    'applicant_id' => $applicant->id,
                    'transaction_type' => 'mission_payment'
                ]
            );
            
            $applicant->update([
                'is_credit_held' => false,
                'credit_release_at' => now(),
            ]);
        });
    }

    /**
     * Refund credits to talent when mission is cancelled
     *
     * @param Mission $mission
     * @param MissionApplicant $applicant
     * @return void
     */
    public function refundCreditsToTalent(Mission $mission, MissionApplicant $applicant)
    {
        $talent = $mission->user;
        
        DB::transaction(function () use ($mission, $applicant, $talent) {
            $this->creditService->addCredits(
                $talent,
                $applicant->credit_amount,
                "Refund for cancelled Mission #{$mission->id} - Applicant #{$applicant->id}",
                [
                    'mission_id' => $mission->id,
                    'applicant_id' => $applicant->id,
                    'transaction_type' => 'mission_refund'
                ]
            );
            
            $applicant->update([
                'is_credit_held' => false,
                'credit_release_at' => now(),
            ]);
        });
    }
    
    /**
     * Hold credits for mission creation instead of deducting immediately
     *
     * @param Mission $mission
     * @return void
     */
    public function deductCreditsForMissionCreation(Mission $mission)
    {
        $user = $mission->user;
        $bounty = $mission->bounty;
        $paxRequired = $mission->pax_required;
        $totalAmount = $bounty * $paxRequired;
        $commissionAmount = 0;
        
        if ($this->missionCommissionService) {
            $commissionAmount = $this->missionCommissionService->calculateCommission($mission);
        }
        
        $totalWithCommission = $totalAmount + $commissionAmount;
        
        if ($user->credits_balance < $totalWithCommission) {
            throw new \Exception('Insufficient credits to create this mission (including commission)');
        }
        
        DB::transaction(function () use ($user, $totalWithCommission, $mission, $commissionAmount, $totalAmount) {
            $this->creditService->deductCredits(
                $user,
                $totalWithCommission,
                "Credit hold for creating Mission #{$mission->id} (including commission)",
                [
                    'mission_id' => $mission->id,
                    'transaction_type' => 'mission_hold',
                    'commission_amount' => $commissionAmount
                ]
            );
            
            $mission->update([
                'is_credit_held' => true,
                'credit_held_at' => now(),
                'credit_amount' => $totalAmount,
                'commission_amount' => $commissionAmount
            ]);
        });
    }
    
    /**
     * Check if a mission can be cancelled based on time restriction
     * 
     * @param Mission $mission
     * @return array [bool $canCancel, string $reason]
     */
    public function canCancelMission(Mission $mission): array
    {
        // Check if mission is already completed or cancelled
        if ($mission->isCompleted()) {
            return [false, 'Mission is already completed'];
        }
        
        if ($mission->isCancelled()) {
            return [false, 'Mission is already cancelled'];
        }
        
        $now = now();
        $startTime = $mission->service_start_date;
        $oneHourBefore = $startTime->copy()->subHour();
        
        if ($now->isAfter($oneHourBefore)) {
            $minutesLeft = $now->diffInMinutes($oneHourBefore, false);
            return [false, "Mission cannot be cancelled less than 1 hour before start time. You are {$minutesLeft} minutes past the cancellation deadline."];
        }
        
        return [true, ''];
    }
    
    /**
     * Refund credits to mission creator when mission is cancelled
     *
     * @param Mission $mission
     * @param bool $skipCancellationCheck Skip the cancellation check (used when mission is already marked as cancelled)
     * @return array [bool $success, string $message]
     */
    public function refundCreditsToCreator(Mission $mission, bool $skipCancellationCheck = false): array
    {
        \Illuminate\Support\Facades\Log::info('Refunding credits for mission', [
            'mission_id' => $mission->id,
            'status' => $mission->status,
            'is_cancelled' => $mission->isCancelled(),
            'skip_check' => $skipCancellationCheck,
            'is_credit_held' => $mission->is_credit_held,
            'credit_amount' => $mission->credit_amount,
            'commission_amount' => $mission->commission_amount
        ]);
        
        // Skip cancellation check if mission is already marked as cancelled
        if (!$skipCancellationCheck && !$mission->isCancelled()) {
            // Only check time restriction if mission is not already cancelled
            [$canCancel, $reason] = $this->canCancelMission($mission);
            
            if (!$canCancel) {
                return [false, $reason];
            }
        }
        
        $creator = $mission->user;
        
        $creditAmount = $mission->credit_amount ?? 0;
        $commissionAmount = $mission->commission_amount ?? 0;
        $totalWithCommission = $creditAmount + $commissionAmount;
        
        if ($totalWithCommission <= 0) {
            return [false, 'No credits to refund'];
        }
        
        DB::transaction(function () use ($creator, $totalWithCommission, $mission, $commissionAmount) {
            $this->creditService->addCredits(
                $creator,
                $totalWithCommission,
                "Refund for cancelled Mission #{$mission->id} (including commission)",
                [
                    'mission_id' => $mission->id,
                    'transaction_type' => 'mission_creation_refund',
                    'commission_amount' => $commissionAmount
                ]
            );
            
            $mission->update([
                'is_credit_held' => false,
            ]);
        });
        
        return [true, 'Credits refunded successfully'];
    }
}
