<?php

namespace App\Services;

use App\Models\User;
use App\Models\CommissionSetting;
use Illuminate\Support\Facades\Log;

class CommissionService
{
    protected $creditService;
    protected $systemAccountService;
    
    public function __construct(CreditService $creditService, SystemAccountService $systemAccountService)
    {
        $this->creditService = $creditService;
        $this->systemAccountService = $systemAccountService;
    }
    
    /**
     * Calculate the commission amount from an order credit amount
     *
     * @param User $talent
     * @param int $creditAmount
     * @return int
     */
    public function calculateCommission(User $talent, int $creditAmount): int
    {
        $commissionPercentage = $this->getCommissionPercentage($talent);
        
        $commissionAmount = ($creditAmount * $commissionPercentage) / 100;
        
        return (int) round($commissionAmount);
    }
    
    /**
     * Apply commission to a completed order
     *
     * @param User $talent
     * @param int $orderId
     * @param int $creditAmount
     * @param string $orderType
     * @return array
     */
    public function applyCommission(User $talent, int $orderId, int $creditAmount, string $orderType = 'order now'): array
    {
        $commissionAmount = $this->calculateCommission($talent, $creditAmount);
        $talentCreditAmount = $creditAmount - $commissionAmount;
        
        $result = [
            'original_amount' => $creditAmount,
            'commission_amount' => $commissionAmount,
            'talent_amount' => $talentCreditAmount,
        ];
        
        if ($talentCreditAmount > 0) {
            $orderTypeText = $orderType === 'scheduled' ? 'Scheduled Order' : 'Order';
            $this->creditService->addCredits(
                $talent,
                $talentCreditAmount,
                "Payment for {$orderTypeText} #{$orderId} (after commission)",
                [
                    'order_id' => $orderId,
                    'order_type' => $orderType,
                    'original_amount' => $creditAmount,
                    'commission_amount' => $commissionAmount
                ]
            );
            
            if ($commissionAmount > 0) {
                $this->systemAccountService->addCommission(
                    $commissionAmount,
                    "Commission from {$orderTypeText} #{$orderId}",
                    $orderId,
                    $orderType,
                    [
                        'talent_id' => $talent->id,
                        'talent_nickname' => $talent->nickname,
                        'original_amount' => $creditAmount,
                        'commission_percentage' => $this->getCommissionPercentage($talent)
                    ]
                );
            }
        }
        
        return $result;
    }
    
    /**
     * Get the commission percentage for a talent
     *
     * @param User $talent
     * @return int
     */
    public function getCommissionPercentage(User $talent): int
    {
        if ($talent->special_commission && $talent->special_commission_percentage !== null) {
            return $talent->special_commission_percentage;
        }
        
        return $this->getDefaultCommission();
    }
    
    /**
     * Get the default commission percentage
     *
     * @return int Percentage value (0-100)
     */
    public function getDefaultCommission(): int
    {
        $setting = CommissionSetting::where('key', 'default_commission_percentage')->first();
        
        return $setting ? $setting->value : 10;
    }
}
