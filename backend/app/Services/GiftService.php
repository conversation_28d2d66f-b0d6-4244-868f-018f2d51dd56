<?php

namespace App\Services;

use App\Models\GiftItem;
use App\Models\User;
use App\Models\UserGift;
use App\Models\GiftTransaction;
use Illuminate\Support\Facades\DB;
use Exception;

class GiftService
{
    protected $creditService;
    protected $pointService;
    
    public function __construct(CreditService $creditService, PointService $pointService)
    {
        $this->creditService = $creditService;
        $this->pointService = $pointService;
    }
    
    /**
     * Purchase a gift item for a user.
     *
     * @param User $user
     * @param GiftItem $giftItem
     * @param int $quantity
     * @return UserGift
     * @throws Exception
     */
    public function purchaseGift(User $user, GiftItem $giftItem, int $quantity = 1): UserGift
    {
        if (!$giftItem->is_active) {
            throw new Exception('Gift item is not available for purchase');
        }
        
        if ($quantity <= 0) {
            throw new Exception('Quantity must be greater than zero');
        }
        
        // Calculate total price
        $totalPrice = $giftItem->credit_price * $quantity;
        
        return DB::transaction(function () use ($user, $giftItem, $quantity, $totalPrice) {
            // Use the credit service to deduct credits (this handles insufficient balance checks)
            $this->creditService->deductCredits(
                $user,
                $totalPrice,
                "Purchase of {$quantity} {$giftItem->name}",
                [
                    'gift_item_id' => $giftItem->id,
                    'quantity' => $quantity,
                    'transaction_type' => 'gift_purchase'
                ]
            );
            
            // Check if user already has this gift
            $existingGift = UserGift::where('user_id', $user->id)
                ->where('gift_item_id', $giftItem->id)
                ->first();
                
            if ($existingGift) {
                // Update quantity
                $existingGift->quantity += $quantity;
                $existingGift->save();
                $userGift = $existingGift;
            } else {
                // Create the user gift record
                $userGift = UserGift::create([
                    'user_id' => $user->id,
                    'gift_item_id' => $giftItem->id,
                    'acquired_at' => now(),
                    'can_sell' => true,
                    'quantity' => $quantity
                ]);
            }
            
            // Record the transaction
            GiftTransaction::create([
                'user_id' => $user->id,
                'gift_item_id' => $giftItem->id,
                'transaction_type' => 'purchase',
                'quantity' => $quantity,
                'payment_method' => 'credits',
                'points_spent' => null,
                'credits_spent' => $totalPrice,
                'metadata' => [
                    'gift_name' => $giftItem->name,
                    'credits_balance_after' => $user->credits_balance
                ]
            ]);
            
            return $userGift;
        });
    }
    
    /**
     * Sell a gift back to the platform for credits.
     *
     * @param UserGift $userGift
     * @param int $quantity
     * @return bool
     * @throws Exception
     */
    public function sellGift(UserGift $userGift, int $quantity = 1): bool
    {
        if (!$userGift->can_sell) {
            throw new Exception('This gift cannot be sold');
        }
        
        if ($quantity <= 0) {
            throw new Exception('Quantity must be greater than zero');
        }
        
        if ($userGift->quantity < $quantity) {
            throw new Exception('You do not have enough of this gift to sell');
        }
        
        return DB::transaction(function () use ($userGift, $quantity) {
            $giftItem = $userGift->giftItem;
            $user = $userGift->user;
            
            // Calculate total sell back price
            $totalSellBackPrice = $giftItem->sell_back_price * $quantity;
            
            // Add credits to the user's balance
            $this->creditService->addCredits(
                $user,
                $totalSellBackPrice,
                "Sell back of {$quantity} {$giftItem->name}",
                [
                    'gift_item_id' => $giftItem->id,
                    'user_gift_id' => $userGift->id,
                    'quantity' => $quantity,
                    'transaction_type' => 'gift_sell_back'
                ]
            );
            
            // Update or delete the user gift
            if ($userGift->quantity == $quantity) {
                // Delete if all are sold
                $userGift->delete();
            } else {
                // Decrement quantity
                $userGift->quantity -= $quantity;
                $userGift->save();
            }
            
            // Record the transaction
            GiftTransaction::create([
                'user_id' => $user->id,
                'gift_item_id' => $giftItem->id,
                'transaction_type' => 'sell',
                'quantity' => $quantity,
                'payment_method' => 'credits',
                'points_spent' => null,
                'credits_spent' => -$totalSellBackPrice, // Negative to indicate credits received
                'metadata' => [
                    'gift_name' => $giftItem->name,
                    'credits_balance_after' => $user->credits_balance
                ]
            ]);
            
            return true;
        });
    }
    
    /**
     * Get available gift items for a user.
     *
     * @param User $user
     * @param string $locale
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getAvailableGiftItems(User $user, string $locale)
    {
        return GiftItem::with(['translations' => function ($query) use ($locale) {
            $query->where('locale', $locale);
        }])
        ->where('is_active', true)
        ->orderBy('display_order')
        ->orderBy('credit_price')
        ->get()
        ->map(function ($giftItem) {
            $translation = $giftItem->translations->first();
            return [
                'id' => $giftItem->id,
                'name' => $translation?->name ?? $giftItem->name,
                'description' => $translation?->description ?? $giftItem->description,
                'icon_path' => $giftItem->icon_path,
                'credit_price' => $giftItem->credit_price,
                'sell_back_price' => $giftItem->sell_back_price
            ];
        });
    }
    
    /**
     * Gift an item from one user to another.
     *
     * @param User $fromUser
     * @param User $toUser
     * @param UserGift $userGift
     * @param int $quantity
     * @return UserGift
     * @throws Exception
     */
    public function giftToUser(User $fromUser, User $toUser, UserGift $userGift, int $quantity): UserGift
    {
        if ($fromUser->id !== $userGift->user_id) {
            throw new Exception('You can only gift items that you own');
        }
        
        if ($quantity <= 0) {
            throw new Exception('Quantity must be greater than zero');
        }
        
        if ($userGift->quantity < $quantity) {
            throw new Exception('You do not have enough of this gift to transfer');
        }
        
        return DB::transaction(function () use ($fromUser, $toUser, $userGift, $quantity) {
            $giftItem = $userGift->giftItem;
            
            // Create or update recipient's gift record
            $recipientGift = UserGift::where('user_id', $toUser->id)
                ->where('gift_item_id', $giftItem->id)
                ->first();
                
            if ($recipientGift) {
                // Update existing gift record
                $recipientGift->quantity += $quantity;
                $recipientGift->save();
            } else {
                // Create new gift record
                $recipientGift = UserGift::create([
                    'user_id' => $toUser->id,
                    'gift_item_id' => $giftItem->id,
                    'gifted_by_user_id' => $fromUser->id,
                    'acquired_at' => now(),
                    'can_sell' => true,
                    'quantity' => $quantity
                ]);
            }
            
            // Update sender's gift record
            if ($userGift->quantity == $quantity) {
                // Delete if all are gifted
                $userGift->delete();
            } else {
                // Decrement quantity
                $userGift->quantity -= $quantity;
                $userGift->save();
            }
            
            // Record the transaction for sender
            GiftTransaction::create([
                'user_id' => $fromUser->id,
                'gift_item_id' => $giftItem->id,
                'transaction_type' => 'gift_sent',
                'quantity' => $quantity,
                'payment_method' => 'none',
                'points_spent' => null,
                'credits_spent' => null,
                'metadata' => [
                    'gift_name' => $giftItem->name,
                    'recipient_id' => $toUser->id,
                    'recipient_nickname' => $toUser->nickname
                ]
            ]);
            
            // Record the transaction for recipient
            GiftTransaction::create([
                'user_id' => $toUser->id,
                'gift_item_id' => $giftItem->id,
                'transaction_type' => 'gift_received',
                'quantity' => $quantity,
                'payment_method' => 'none',
                'points_spent' => null,
                'credits_spent' => null,
                'metadata' => [
                    'gift_name' => $giftItem->name,
                    'sender_id' => $fromUser->id,
                    'sender_nickname' => $fromUser->nickname
                ]
            ]);
            
            return $recipientGift;
        });
    }
    
    /**
     * Get total gifts received by a user.
     *
     * @param User $user
     * @return array
     */
    public function getTotalGiftsReceived(User $user): array
    {
        $totalGifts = UserGift::where('user_id', $user->id)
            ->whereNotNull('gifted_by_user_id')
            ->sum('quantity');
            
        $uniqueGiftItems = UserGift::where('user_id', $user->id)
            ->whereNotNull('gifted_by_user_id')
            ->distinct('gift_item_id')
            ->count('gift_item_id');
            
        return [
            'total_gifts_received' => (int)$totalGifts,
            'unique_gift_items_received' => $uniqueGiftItems
        ];
    }
    
    /**
     * Get gift contributors for a user.
     *
     * @param User $user
     * @return array
     */
    public function getGiftContributors(User $user): array
    {
        $contributors = UserGift::where('user_id', $user->id)
            ->whereNotNull('gifted_by_user_id')
            ->with('giftedBy')
            ->get()
            ->groupBy('gifted_by_user_id')
            ->map(function ($gifts, $contributorId) {
                $contributor = $gifts->first()->giftedBy;
                $totalGifts = $gifts->sum('quantity');
                $uniqueGiftItems = $gifts->pluck('gift_item_id')->unique()->count();
                
                return [
                    'contributor_id' => $contributor->id,
                    'contributor_nickname' => $contributor->nickname,
                    'total_gifts' => $totalGifts,
                    'unique_gift_items' => $uniqueGiftItems
                ];
            })
            ->values()
            ->toArray();
            
        return $contributors;
    }
    
    /**
     * Get gift inventory statistics for a user.
     *
     * @param User $user
     * @return array
     */
    public function getGiftInventoryStatistics(User $user): array
    {
        // Get total giftable items (purchased by user)
        $giftableItems = UserGift::where('user_id', $user->id)
            ->whereNull('gifted_by_user_id')
            ->sum('quantity');
            
        $uniqueGiftableItems = UserGift::where('user_id', $user->id)
            ->whereNull('gifted_by_user_id')
            ->distinct('gift_item_id')
            ->count('gift_item_id');
            
        // Get total non-giftable items (received from others)
        $nonGiftableItems = UserGift::where('user_id', $user->id)
            ->whereNotNull('gifted_by_user_id')
            ->sum('quantity');
            
        $uniqueNonGiftableItems = UserGift::where('user_id', $user->id)
            ->whereNotNull('gifted_by_user_id')
            ->distinct('gift_item_id')
            ->count('gift_item_id');
            
        return [
            'giftable_items' => [
                'total' => (int)$giftableItems,
                'unique' => $uniqueGiftableItems
            ],
            'non_giftable_items' => [
                'total' => (int)$nonGiftableItems,
                'unique' => $uniqueNonGiftableItems
            ],
            'total_items' => (int)$giftableItems + (int)$nonGiftableItems,
            'total_unique_items' => $uniqueGiftableItems + $uniqueNonGiftableItems
        ];
    }
    
    /**
     * Get gift transaction history for a user.
     *
     * @param User $user
     * @param int $limit
     * @param int $offset
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getTransactionHistory(User $user, int $limit = 10, int $offset = 0)
    {
        return GiftTransaction::where('user_id', $user->id)
            ->with('giftItem')
            ->orderBy('created_at', 'desc')
            ->skip($offset)
            ->take($limit)
            ->get();
    }
    
    /**
     * Redeem a gift item using referral points.
     *
     * @param User $user
     * @param GiftItem $giftItem
     * @param int $quantity
     * @return UserGift
     * @throws Exception
     */
    public function redeemGiftWithPoints(User $user, GiftItem $giftItem, int $quantity = 1): UserGift
    {
        if (!$giftItem->is_active) {
            throw new Exception('Gift item is not available for redemption');
        }
        
        if ($quantity <= 0) {
            throw new Exception('Quantity must be greater than zero');
        }
        
        // Calculate total point cost (using credit price as point cost)
        $totalPointCost = $giftItem->credit_price * $quantity;
        
        return DB::transaction(function () use ($user, $giftItem, $quantity, $totalPointCost) {
            // Use the point service to deduct points
            $this->pointService->redeemPoints(
                $user,
                $totalPointCost,
                "Redemption of {$quantity} {$giftItem->name}",
                [
                    'gift_item_id' => $giftItem->id,
                    'quantity' => $quantity,
                    'transaction_type' => 'gift_redemption'
                ]
            );
            
            // Check if user already has this gift
            $existingGift = UserGift::where('user_id', $user->id)
                ->where('gift_item_id', $giftItem->id)
                ->first();
                
            if ($existingGift) {
                // Update quantity
                $existingGift->quantity += $quantity;
                $existingGift->save();
                $userGift = $existingGift;
            } else {
                // Create the user gift record
                $userGift = UserGift::create([
                    'user_id' => $user->id,
                    'gift_item_id' => $giftItem->id,
                    'acquired_at' => now(),
                    'can_sell' => true,
                    'quantity' => $quantity
                ]);
            }
            
            // Record the transaction
            GiftTransaction::create([
                'user_id' => $user->id,
                'gift_item_id' => $giftItem->id,
                'transaction_type' => 'redeem',
                'quantity' => $quantity,
                'payment_method' => 'points',
                'points_spent' => $totalPointCost,
                'credits_spent' => null,
                'metadata' => [
                    'gift_name' => $giftItem->name,
                    'points_balance_after' => $user->points_balance
                ]
            ]);
            
            return $userGift;
        });
    }
}
