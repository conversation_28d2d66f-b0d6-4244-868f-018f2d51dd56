<?php

namespace App\Services;

use App\Models\EmailVerificationToken;
use App\Models\User;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use Illuminate\Mail\Message;

class EmailVerificationService
{
    /**
     * Send a verification email to the user.
     *
     * @param User $user
     * @param string $email
     * @return array
     */
    public function sendVerificationEmail(User $user, string $email): array
    {
        $token = Str::random(64);
        $expiresAt = now()->addHours(24);

        EmailVerificationToken::where('user_id', $user->id)
            ->where('email', $email)
            ->delete();

        EmailVerificationToken::create([
            'user_id' => $user->id,
            'email' => $email,
            'token' => $token,
            'expires_at' => $expiresAt,
        ]);

        $verificationUrl = route('email.verify', ['token' => $token]);

        try {
            Mail::send(
                'emails.verify', 
                ['user' => $user, 'url' => $verificationUrl], 
                function (Message $message) use ($user, $email) {
                    $message->to($email);
                    $message->subject('Email Verification - Mission X');
                }
            );

            Log::info('Verification email sent', [
                'user_id' => $user->id,
                'email' => $email,
            ]);

            return [
                'success' => true,
                'message' => 'Verification email sent successfully',
            ];
        } catch (\Exception $e) {
            Log::error('Failed to send verification email', [
                'user_id' => $user->id,
                'email' => $email,
                'error' => $e->getMessage(),
                'error_code' => $e->getCode(),
                'error_trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'message' => 'Failed to send verification email',
                'error' => $e->getMessage(),
                'error_code' => $e->getCode(),
            ];
        }
    }

    /**
     * Verify the user's email using the provided token.
     *
     * @param string $token
     * @return array
     */
    public function verifyEmail(string $token): array
    {
        $verificationToken = EmailVerificationToken::where('token', $token)->first();

        if (!$verificationToken) {
            return [
                'success' => false,
                'message' => 'Invalid verification token',
            ];
        }

        if ($verificationToken->isExpired()) {
            return [
                'success' => false,
                'message' => 'Verification token has expired',
            ];
        }

        $user = $verificationToken->user;

        if (!$user) {
            return [
                'success' => false,
                'message' => 'User not found',
            ];
        }

        // Check if the email is already in use by another user
        $existingUser = User::where('email', $verificationToken->email)
            ->where('id', '!=', $user->id)
            ->first();

        if ($existingUser) {
            Log::error('Email verification failed: Email already in use', [
                'user_id' => $user->id,
                'email' => $verificationToken->email,
                'existing_user_id' => $existingUser->id,
            ]);

            $verificationToken->delete();

            return [
                'success' => false,
                'message' => 'This email address is already associated with another account.',
            ];
        }

        // If the email is the same as the user's current email, just mark it as verified
        if ($user->email === $verificationToken->email) {
            $user->email_verified_at = now();
            $user->save();
        } else {
            // Otherwise update both the email and verification status
            $user->email = $verificationToken->email;
            $user->email_verified_at = now();
            $user->save();
        }

        $verificationToken->delete();

        Log::info('Email verified successfully', [
            'user_id' => $user->id,
            'email' => $user->email,
        ]);

        return [
            'success' => true,
            'message' => 'Email verified successfully',
        ];
    }
}