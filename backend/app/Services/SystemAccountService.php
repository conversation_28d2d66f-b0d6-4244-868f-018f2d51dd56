<?php

namespace App\Services;

use App\Models\SystemAccount;
use App\Models\SystemAccountTransaction;
use Illuminate\Support\Facades\DB;
use Exception;

class SystemAccountService
{
    /**
     * Get or create the system account.
     *
     * @return SystemAccount
     */
    public function getSystemAccount(): SystemAccount
    {
        $systemAccount = SystemAccount::first();
        
        if (!$systemAccount) {
            $systemAccount = SystemAccount::create([
                'name' => 'System Account',
                'balance' => 0,
            ]);
        }
        
        return $systemAccount;
    }
    
    /**
     * Add commission to the system account.
     *
     * @param int $amount
     * @param string $description
     * @param int|null $referenceId
     * @param string|null $referenceType
     * @param array $metadata
     * @return SystemAccountTransaction
     * @throws Exception
     */
    public function addCommission(
        int $amount,
        string $description,
        ?int $referenceId = null,
        ?string $referenceType = null,
        array $metadata = []
    ): SystemAccountTransaction {
        if ($amount <= 0) {
            throw new Exception('Commission amount must be greater than zero');
        }
        
        return $this->createTransaction(
            'credit',
            $amount,
            $description,
            $referenceId,
            $referenceType,
            $metadata
        );
    }
    
    /**
     * Create a system account transaction with database locking for thread safety.
     *
     * @param string $type
     * @param int $amount
     * @param string $description
     * @param int|null $referenceId
     * @param string|null $referenceType
     * @param array $metadata
     * @return SystemAccountTransaction
     */
    private function createTransaction(
        string $type,
        int $amount,
        string $description,
        ?int $referenceId = null,
        ?string $referenceType = null,
        array $metadata = []
    ): SystemAccountTransaction {
        return DB::transaction(function () use ($type, $amount, $description, $referenceId, $referenceType, $metadata) {
            $systemAccount = SystemAccount::lockForUpdate()->first();
            if (!$systemAccount) {
                $systemAccount = SystemAccount::create([
                    'name' => 'System Account',
                    'balance' => 0,
                ]);
            }
            
            $newBalance = $systemAccount->balance + $amount;
            
            $systemAccount->update(['balance' => $newBalance]);
            
            return SystemAccountTransaction::create([
                'system_account_id' => $systemAccount->id,
                'amount' => $amount,
                'type' => $type,
                'description' => $description,
                'reference_id' => $referenceId,
                'reference_type' => $referenceType,
                'metadata' => $metadata
            ]);
        });
    }
    
    /**
     * Get the system account balance.
     *
     * @return int
     */
    public function getBalance(): int
    {
        $systemAccount = $this->getSystemAccount();
        return $systemAccount->balance;
    }
    
    /**
     * Get the system account transaction history.
     *
     * @param int $limit
     * @param int $offset
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getTransactionHistory(int $limit = 10, int $offset = 0)
    {
        return SystemAccountTransaction::orderBy('created_at', 'desc')
            ->skip($offset)
            ->take($limit)
            ->get();
    }
}
