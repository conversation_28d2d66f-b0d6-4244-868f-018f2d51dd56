<?php

namespace App\Services;

use App\Models\MissionCommission;
use App\Models\Mission;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class MissionCommissionService
{
    protected $creditService;
    protected $systemAccountService;
    
    public function __construct(CreditService $creditService, SystemAccountService $systemAccountService)
    {
        $this->creditService = $creditService;
        $this->systemAccountService = $systemAccountService;
    }
    
    /**
     * Calculate the commission amount from a mission
     *
     * @param Mission $mission
     * @return int
     */
    public function calculateCommission(Mission $mission): int
    {
        $commissionPercentage = $this->getDefaultCommission();
        $totalAmount = $mission->bounty * $mission->pax_required;
        
        $commissionAmount = ($totalAmount * $commissionPercentage) / 100;
        
        return (int) round($commissionAmount);
    }
    
    /**
     * Apply commission to a mission
     *
     * @param Mission $mission
     * @return array
     */
    public function applyCommission(Mission $mission): array
    {
        $totalAmount = $mission->bounty * $mission->pax_required;
        $commissionAmount = $this->calculateCommission($mission);
        
        $result = [
            'original_amount' => $totalAmount,
            'commission_amount' => $commissionAmount,
            'total_amount' => $totalAmount + $commissionAmount,
        ];
        
        if ($commissionAmount > 0) {
            try {
                $transaction = $this->systemAccountService->addCommission(
                    $commissionAmount,
                    "Commission from Mission #{$mission->id}",
                    $mission->id,
                    'mission',
                    [
                        'creator_id' => $mission->user_id,
                        'creator_name' => $mission->user->name ?? 'Unknown',
                        'original_amount' => $totalAmount,
                        'commission_percentage' => $this->getDefaultCommission(),
                        'transaction_type' => 'mission_commission'
                    ]
                );
                
                if (!$transaction) {
                    \Illuminate\Support\Facades\Log::error('Failed to create system account transaction for mission commission', [
                        'mission_id' => $mission->id,
                        'commission_amount' => $commissionAmount
                    ]);
                }
            } catch (\Exception $e) {
                \Illuminate\Support\Facades\Log::error('Exception creating system account transaction for mission commission', [
                    'mission_id' => $mission->id,
                    'commission_amount' => $commissionAmount,
                    'error' => $e->getMessage()
                ]);
            }
        }
        
        return $result;
    }
    
    /**
     * Get the default commission percentage for missions
     *
     * @return int Percentage value (0-100)
     */
    public function getDefaultCommission(): int
    {
        $setting = MissionCommission::where('key', 'default_mission_commission_percentage')->first();
        
        return $setting ? $setting->value : 10;
    }
}
