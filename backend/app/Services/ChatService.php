<?php

namespace App\Services;

use App\Models\ChatConversation;
use App\Models\ChatMessage;
use App\Models\ChatMessageAttachment;
use App\Models\ChatMessageStatus;
use App\Models\DeviceToken;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Queue;

class ChatService
{
    protected $badWordService;
    protected $imageProcessingService;
    protected $firebaseNotificationService;
    protected $inputSanitizationService;
    protected $chatQueueConnection;
    
    public function __construct(
        BadWordService $badWordService,
        ImageProcessingService $imageProcessingService,
        FirebaseNotificationService $firebaseNotificationService,
        InputSanitizationService $inputSanitizationService
    ) {
        $this->badWordService = $badWordService;
        $this->imageProcessingService = $imageProcessingService;
        $this->firebaseNotificationService = $firebaseNotificationService;
        $this->inputSanitizationService = $inputSanitizationService;
        $this->chatQueueConnection = env('CHAT_QUEUE_CONNECTION', env('QUEUE_CONNECTION', 'rabbitmq'));
    }
    
    /**
     * Get or create a conversation between a user and talent
     */
    public function getOrCreateConversation(int $userId, int $talentId, ?int $orderId = null): ChatConversation
    {
        $conversation = ChatConversation::where(function ($query) use ($userId, $talentId) {
            $query->where('user_id', $userId)
                ->where('talent_id', $talentId);
        })->first();
        
        if (!$conversation) {
            $conversation = ChatConversation::create([
                'user_id' => $userId,
                'talent_id' => $talentId,
                'order_id' => $orderId,
                'is_active' => true,
            ]);
        } elseif ($orderId && !$conversation->order_id) {
            $conversation->update(['order_id' => $orderId]);
        }
        
        return $conversation;
    }
    
    /**
     * Send a text message
     */
    public function sendTextMessage(int $conversationId, int $senderId, string $content): ChatMessage
    {
        $sanitizedContent = $this->inputSanitizationService->sanitize($content);
        $flagged = false;
        $flaggedReason = null;
        
        $filteredContent = $this->badWordService->filterContent($sanitizedContent);
        if ($filteredContent !== $sanitizedContent) {
            $flagged = true;
            $flaggedReason = 'Contains inappropriate language';
        }
        
        $conversation = ChatConversation::findOrFail($conversationId);
        
        DB::beginTransaction();
        try {
            $message = ChatMessage::create([
                'conversation_id' => $conversationId,
                'sender_id' => $senderId,
                'content' => $filteredContent,
                'is_flagged' => $flagged,
                'flagged_reason' => $flaggedReason,
            ]);
            
            ChatMessageStatus::create([
                'message_id' => $message->id,
                'user_id' => $senderId,
                'status' => 'sent',
                'status_timestamp' => Carbon::now(),
            ]);
            
            $conversation->update(['last_message_at' => Carbon::now()]);
            
            DB::commit();
            
            $this->queueMessageDelivery($message);
            
            return $message;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to send text message', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            throw $e;
        }
    }
    
    /**
     * Send a message with attachment
     */
    public function sendMessageWithAttachment(
        int $conversationId, 
        int $senderId, 
        string $content, 
        UploadedFile $file
    ): ChatMessage {
        $sanitizedContent = $this->inputSanitizationService->sanitize($content);
        $flagged = false;
        $flaggedReason = null;
        
        $filteredContent = $this->badWordService->filterContent($sanitizedContent);
        if ($filteredContent !== $sanitizedContent) {
            $flagged = true;
            $flaggedReason = 'Contains inappropriate language';
        }
        
        $conversation = ChatConversation::findOrFail($conversationId);
        
        DB::beginTransaction();
        try {
            $message = ChatMessage::create([
                'conversation_id' => $conversationId,
                'sender_id' => $senderId,
                'content' => $filteredContent,
                'is_flagged' => $flagged,
                'flagged_reason' => $flaggedReason,
            ]);
            
            $imageMetadata = $this->processAttachment($message->id, $senderId, $file);
            
            ChatMessageStatus::create([
                'message_id' => $message->id,
                'user_id' => $senderId,
                'status' => 'sent',
                'status_timestamp' => Carbon::now(),
            ]);
            
            $conversation->update(['last_message_at' => Carbon::now()]);
            
            DB::commit();
            
            $this->queueMessageDelivery($message);
            
            return $message;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to send message with attachment', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            throw $e;
        }
    }
    
    /**
     * Process attachment file
     */
    protected function processAttachment(int $messageId, int $userId, UploadedFile $file): ChatMessageAttachment
    {
        if (!$this->imageProcessingService->validateImageContent($file)) {
            throw new \Exception('Invalid image content');
        }
        
        if ($this->imageProcessingService->isMobileImageFormat($file)) {
            $imageMetadata = $this->imageProcessingService->processMobileImage($file, $userId, 'chat_attachments');
        } elseif (strtolower($file->getClientOriginalExtension()) === 'webp') {
            $imageMetadata = $this->imageProcessingService->processWebpImage($file, $userId, 'chat_attachments');
        } else {
            $imageMetadata = $this->imageProcessingService->processImage($file, $userId, 'chat_attachments');
        }
        
        return ChatMessageAttachment::create([
            'message_id' => $messageId,
            'original_path' => $imageMetadata['original'],
            'optimized_path' => $imageMetadata['optimized'],
            'filename' => $imageMetadata['filename'],
            'mime_type' => $imageMetadata['mime_type'],
            'size' => $imageMetadata['size'],
            'extension' => $imageMetadata['extension'],
            'dimensions' => $imageMetadata['dimensions'] ?? null,
        ]);
    }
    
    /**
     * Queue message for delivery
     */
    protected function queueMessageDelivery(ChatMessage $message): void
    {
        try {
            Queue::connection($this->chatQueueConnection)->push('chat.delivery', json_encode([
                'message_id' => $message->id,
                'conversation_id' => $message->conversation_id,
                'timestamp' => Carbon::now()->toIso8601String(),
            ]));
            
            Log::info('Chat message queued for delivery', [
                'message_id' => $message->id,
                'queue_connection' => $this->chatQueueConnection,
                'timestamp' => Carbon::now()->toIso8601String(),
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to queue chat message for delivery', [
                'message_id' => $message->id,
                'queue_connection' => $this->chatQueueConnection,
                'error_code' => $e->getCode(),
                'error_message' => $e->getMessage(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'timestamp' => Carbon::now()->toIso8601String(),
            ]);
            
            if ($this->chatQueueConnection !== config('queue.default')) {
                try {
                    Queue::connection(config('queue.default'))->push('chat.delivery', json_encode([
                        'message_id' => $message->id,
                        'conversation_id' => $message->conversation_id,
                        'timestamp' => Carbon::now()->toIso8601String(),
                    ]));
                    
                    Log::info('Chat message queued for delivery using fallback connection', [
                        'message_id' => $message->id,
                        'fallback_queue_connection' => config('queue.default'),
                        'timestamp' => Carbon::now()->toIso8601String(),
                    ]);
                } catch (\Exception $fallbackException) {
                    Log::error('Failed to queue chat message for delivery using fallback connection', [
                        'message_id' => $message->id,
                        'fallback_queue_connection' => config('queue.default'),
                        'error_code' => $fallbackException->getCode(),
                        'error_message' => $fallbackException->getMessage(),
                        'error_file' => $fallbackException->getFile(),
                        'error_line' => $fallbackException->getLine(),
                        'timestamp' => Carbon::now()->toIso8601String(),
                    ]);
                }
            }
        }
        
        $conversation = $message->conversation;
        $recipientId = $message->sender_id === $conversation->user_id 
            ? $conversation->talent_id 
            : $conversation->user_id;
        
        $sender = User::find($message->sender_id);
        $senderName = $sender ? $sender->nickname : 'Someone';
        
        $deviceTokens = DeviceToken::where('user_id', $recipientId)->pluck('token')->toArray();
        if (count($deviceTokens) > 0) {
            foreach ($deviceTokens as $token) {
                $this->firebaseNotificationService->sendToDevice(
                    $token,
                    "New message from {$senderName}",
                    $message->attachments()->exists() 
                        ? "📷 {$senderName} sent you an image" 
                        : substr($message->content, 0, 100),
                    [
                        'type' => 'chat_message',
                        'conversation_id' => $conversation->id,
                        'message_id' => $message->id,
                    ]
                );
            }
        }
    }
    
    /**
     * Update message status
     */
    public function updateMessageStatus(int $messageId, int $userId, string $status): ChatMessageStatus
    {
        $validStatuses = ['delivered', 'read'];
        if (!in_array($status, $validStatuses)) {
            throw new \InvalidArgumentException('Invalid status');
        }
        
        $message = ChatMessage::findOrFail($messageId);
        
        $messageStatus = ChatMessageStatus::firstOrNew([
            'message_id' => $messageId,
            'user_id' => $userId,
        ]);
        
        $statusOrder = ['sent' => 1, 'delivered' => 2, 'read' => 3];
        if (!$messageStatus->exists || $statusOrder[$status] > $statusOrder[$messageStatus->status]) {
            $messageStatus->status = $status;
            $messageStatus->status_timestamp = Carbon::now();
            $messageStatus->save();
            
            try {
                Queue::connection($this->chatQueueConnection)->push('chat.status', json_encode([
                    'message_id' => $messageId,
                    'user_id' => $userId,
                    'status' => $status,
                    'timestamp' => Carbon::now()->toIso8601String(),
                ]));
                
                Log::info('Chat message status update queued', [
                    'message_id' => $messageId,
                    'user_id' => $userId,
                    'status' => $status,
                    'queue_connection' => $this->chatQueueConnection,
                    'timestamp' => Carbon::now()->toIso8601String(),
                ]);
            } catch (\Exception $e) {
                Log::error('Failed to queue chat message status update', [
                    'message_id' => $messageId,
                    'user_id' => $userId,
                    'status' => $status,
                    'queue_connection' => $this->chatQueueConnection,
                    'error_code' => $e->getCode(),
                    'error_message' => $e->getMessage(),
                    'error_file' => $e->getFile(),
                    'error_line' => $e->getLine(),
                    'timestamp' => Carbon::now()->toIso8601String(),
                ]);
                
                if ($this->chatQueueConnection !== config('queue.default')) {
                    try {
                        Queue::connection(config('queue.default'))->push('chat.status', json_encode([
                            'message_id' => $messageId,
                            'user_id' => $userId,
                            'status' => $status,
                            'timestamp' => Carbon::now()->toIso8601String(),
                        ]));
                        
                        Log::info('Chat message status update queued using fallback connection', [
                            'message_id' => $messageId,
                            'user_id' => $userId,
                            'status' => $status,
                            'fallback_queue_connection' => config('queue.default'),
                            'timestamp' => Carbon::now()->toIso8601String(),
                        ]);
                    } catch (\Exception $fallbackException) {
                        Log::error('Failed to queue chat message status update using fallback connection', [
                            'message_id' => $messageId,
                            'user_id' => $userId,
                            'status' => $status,
                            'fallback_queue_connection' => config('queue.default'),
                            'error_code' => $fallbackException->getCode(),
                            'error_message' => $fallbackException->getMessage(),
                            'error_file' => $fallbackException->getFile(),
                            'error_line' => $fallbackException->getLine(),
                            'timestamp' => Carbon::now()->toIso8601String(),
                        ]);
                    }
                }
            }
        }
        
        return $messageStatus;
    }
    
    /**
     * Get conversations for a user
     */
    public function getUserConversations(int $userId, int $page = 1, int $perPage = 15)
    {
        return ChatConversation::where(function ($query) use ($userId) {
            $query->where('user_id', $userId)
                ->orWhere('talent_id', $userId);
        })
        ->with(['user', 'talent'])
        ->orderBy('last_message_at', 'desc')
        ->paginate($perPage, ['*'], 'page', $page);
    }
    
    /**
     * Get messages for a conversation
     */
    public function getConversationMessages(int $conversationId, int $userId, int $page = 1, int $perPage = 20)
    {
        $conversation = ChatConversation::where('id', $conversationId)
            ->where(function ($query) use ($userId) {
                $query->where('user_id', $userId)
                    ->orWhere('talent_id', $userId);
            })
            ->firstOrFail();
        
        $messages = ChatMessage::where('conversation_id', $conversationId)
            ->with(['sender', 'attachments', 'statuses'])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage, ['*'], 'page', $page);
        
        $unreadMessages = $messages->filter(function ($message) use ($userId) {
            return $message->sender_id !== $userId && 
                   !$message->statuses->where('user_id', $userId)->where('status', 'read')->count();
        });
        
        foreach ($unreadMessages as $message) {
            $this->updateMessageStatus($message->id, $userId, 'read');
        }
        
        return $messages;
    }
}
