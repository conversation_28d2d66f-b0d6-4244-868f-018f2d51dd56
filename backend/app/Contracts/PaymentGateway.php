<?php

namespace App\Contracts;

use App\Models\CreditPackage;
use App\Models\User;

interface PaymentGateway
{
    /**
     * Create a payment for the given user and credit package.
     *
     * @param User $user
     * @param CreditPackage $creditPackage
     * @param array $options
     * @return array Payment details including redirect URL and transaction ID
     */
    public function createPayment(User $user, CreditPackage $creditPackage, array $options = []): array;
    
    /**
     * Verify a payment callback from the payment gateway.
     *
     * @param array $data Callback data from the payment gateway
     * @return array Payment verification details
     */
    public function verifyPayment(array $data): array;
}
