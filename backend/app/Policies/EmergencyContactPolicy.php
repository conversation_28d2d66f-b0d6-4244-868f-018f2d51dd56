<?php

namespace App\Policies;

use App\Models\User;
use App\Models\EmergencyContact;
use Illuminate\Auth\Access\HandlesAuthorization;
use Illuminate\Support\Facades\Log;

class EmergencyContactPolicy
{
    use HandlesAuthorization;

    public function update(User $user, EmergencyContact $contact): bool
    {
        Log::info('EmergencyContact Policy Check', [
            'user_id' => $user->id,
            'contact_user_id' => $contact->user_id,
            'contact_id' => $contact->id
        ]);
        return $user->id === $contact->user_id;
    }

    public function delete(User $user, EmergencyContact $contact): bool
    {
        return $user->id === $contact->user_id;
    }
}
