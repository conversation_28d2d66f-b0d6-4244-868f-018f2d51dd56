<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;

class SocialFeedCacheCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cache:feed {--flush : Flush the social feed cache}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Manage the social feed cache';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        if ($this->option('flush')) {
            Cache::tags('social_feed')->flush();
            $this->info('Social feed cache has been flushed.');
            return 0;
        }

        $this->info('Social feed cache status:');
        
        // In a real implementation, we would show cache statistics here
        // For now, we'll just show a placeholder message
        $this->line('Cache is active. Use --flush to clear the cache.');
        
        return 0;
    }
}
