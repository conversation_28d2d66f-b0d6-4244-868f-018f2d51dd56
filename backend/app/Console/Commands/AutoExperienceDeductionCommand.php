<?php

namespace App\Console\Commands;

use App\Services\AutoDeductionService;
use Illuminate\Console\Command;

class AutoExperienceDeductionCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'experience:auto-deduct';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Run automatic experience deduction based on configuration';

    /**
     * The auto deduction service.
     *
     * @var AutoDeductionService
     */
    protected $autoDeductionService;

    /**
     * Create a new command instance.
     */
    public function __construct(AutoDeductionService $autoDeductionService)
    {
        parent::__construct();
        $this->autoDeductionService = $autoDeductionService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Running automatic experience deduction...');
        
        $log = $this->autoDeductionService->runDeduction();
        
        if ($log) {
            $this->info("Deduction completed: {$log->users_affected} users affected, {$log->total_experience_deducted} total experience deducted.");
            return 0;
        }
        
        $this->info('No deduction was performed. Check the configuration or schedule.');
        return 0;
    }
}
