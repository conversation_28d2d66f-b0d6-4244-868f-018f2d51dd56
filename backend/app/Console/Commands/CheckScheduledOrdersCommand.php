<?php

namespace App\Console\Commands;

use App\Models\ScheduledOrder;
use App\Services\CreditService;
use App\Services\FirebaseNotificationService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CheckScheduledOrdersCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'orders:check-scheduled';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check for scheduled orders that have passed their scheduled time without response';

    /**
     * Execute the console command.
     */
    public function handle(CreditService $creditService, FirebaseNotificationService $firebaseService)
    {
        $this->info('Checking for scheduled orders past their scheduled time...');
        
        $now = Carbon::now();
        
        $expiredOrders = ScheduledOrder::where('status', 'pending')
            ->where('scheduled_start_time', '<', $now)
            ->where('is_credit_held', true)
            ->get();
            
        $this->info("Found {$expiredOrders->count()} expired scheduled orders");
        
        foreach ($expiredOrders as $order) {
            $this->info("Processing scheduled order #{$order->id}");
            
            try {
                DB::beginTransaction();
                
                $order->status = 'rejected';
                
                if ($order->is_credit_held) {
                    $customer = $order->customer;
                    if ($customer) {
                        $creditService->addCredits(
                            $customer,
                            $order->credit_amount,
                            "Credit refund for rejected Scheduled Order #{$order->id} (talent did not respond by scheduled time)",
                            ['order_id' => $order->id]
                        );
                        
                        $order->is_credit_held = false;
                        $order->credit_released_at = now();
                        
                        $this->info("Released {$order->credit_amount} credits back to customer #{$customer->id}");
                    }
                }
                
                $order->save();
                
                if (class_exists('App\Models\ScheduledOrderAcceptanceLog')) {
                    \App\Models\ScheduledOrderAcceptanceLog::create([
                        'talent_id' => $order->talent_id,
                        'scheduled_order_id' => $order->id,
                        'action' => 'rejected',
                        'response_time_seconds' => $now->diffInSeconds($order->created_at),
                    ]);
                }
                
                $talent = $order->talent;
                if ($talent) {
                    $talent->total_scheduled_orders_received = ($talent->total_scheduled_orders_received ?? 0) + 1;
                    if (isset($talent->scheduled_acceptance_rate)) {
                        $talent->scheduled_acceptance_rate = $talent->total_scheduled_orders_accepted / $talent->total_scheduled_orders_received;
                    }
                    $talent->save();
                }
                
                DB::commit();
                
                if ($order->customer && isset($order->customer->device_tokens) && count($order->customer->device_tokens) > 0) {
                    foreach ($order->customer->device_tokens as $deviceToken) {
                        $firebaseService->sendToDevice(
                            $deviceToken->token,
                            'Scheduled Order Rejected',
                            'The talent did not respond to your scheduled order by the scheduled time and it was automatically rejected.',
                            [
                                'order_id' => $order->id,
                                'notification_type' => 'scheduled_order_rejected',
                            ]
                        );
                    }
                }
                
                $this->info("Successfully processed scheduled order #{$order->id}");
                
            } catch (\Exception $e) {
                DB::rollBack();
                $this->error("Failed to process scheduled order #{$order->id}: {$e->getMessage()}");
                Log::error('Failed to process scheduled order timeout', [
                    'order_id' => $order->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]);
            }
        }
        
        $this->info('Scheduled order check completed');
        
        return 0;
    }
}
