<?php

namespace App\Console\Commands;

use App\Models\Mission;
use App\Models\MissionApplicant;
use App\Services\MissionCreditService;
use App\Services\FirebaseNotificationService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CheckExpiredMissionsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'missions:check-expired';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check for open missions past their service end date and cancel them';

    /**
     * Execute the console command.
     */
    public function handle(MissionCreditService $missionCreditService, FirebaseNotificationService $firebaseService)
    {
        $this->info('Checking for expired missions...');
        
        $now = Carbon::now();
        
        $expiredMissions = Mission::where('status', 'open')
            ->whereNotNull('service_end_date')
            ->where('service_end_date', '<', $now)
            ->get();
            
        $this->info("Found {$expiredMissions->count()} expired missions");
        
        foreach ($expiredMissions as $mission) {
            $this->info("Processing mission #{$mission->id}");
            
            try {
                DB::beginTransaction();
                
                $mission->status = 'cancelled';
                $mission->save();
                
                $applicantsWithHeldCredits = $mission->applicants()
                    ->where('is_credit_held', true)
                    ->get();
                    
                foreach ($applicantsWithHeldCredits as $applicant) {
                    $missionCreditService->refundCreditsToTalent($mission, $applicant);
                    $this->info("Refunded credits for applicant #{$applicant->id}");
                }
                
                DB::commit();
                
                $creator = $mission->user;
                if ($creator && isset($creator->device_tokens) && count($creator->device_tokens) > 0) {
                    foreach ($creator->device_tokens as $deviceToken) {
                        $firebaseService->sendToDevice(
                            $deviceToken->token,
                            'Mission Cancelled',
                            'Your mission has been automatically cancelled as it reached its end date.',
                            [
                                'mission_id' => $mission->id,
                                'notification_type' => 'mission_auto_cancelled',
                            ]
                        );
                    }
                }
                
                $pendingApplicants = $mission->applicants()->where('status', 'pending')->get();
                foreach ($pendingApplicants as $applicant) {
                    $user = $applicant->user;
                    if ($user && isset($user->device_tokens) && count($user->device_tokens) > 0) {
                        foreach ($user->device_tokens as $deviceToken) {
                            $firebaseService->sendToDevice(
                                $deviceToken->token,
                                'Mission Cancelled',
                                'A mission you applied to has been automatically cancelled as it reached its end date.',
                                [
                                    'mission_id' => $mission->id,
                                    'notification_type' => 'mission_auto_cancelled',
                                ]
                            );
                        }
                    }
                }
                
                $this->info("Successfully processed mission #{$mission->id}");
                
            } catch (\Exception $e) {
                DB::rollBack();
                $this->error("Failed to process mission #{$mission->id}: {$e->getMessage()}");
                Log::error('Failed to process expired mission', [
                    'mission_id' => $mission->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]);
            }
        }
        
        $this->info('Expired mission check completed');
        
        return 0;
    }
}
