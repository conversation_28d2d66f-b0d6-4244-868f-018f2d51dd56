<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // Run the auto experience deduction command daily
        $schedule->command('experience:auto-deduct')
                 ->daily()
                 ->appendOutputTo(storage_path('logs/auto-deduction.log'));
                 
        $schedule->command('orders:check-scheduled')
                 ->everyFiveMinutes()
                 ->appendOutputTo(storage_path('logs/scheduled-orders-check.log'));
                 
        $schedule->command('missions:check-expired')
                 ->everyMinute()
                 ->appendOutputTo(storage_path('logs/expired-missions-check.log'));
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');
    }
}
