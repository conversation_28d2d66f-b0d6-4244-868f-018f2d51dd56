<?php

namespace App\Observers;

use App\Models\OrderReview;
use App\Models\User;
use Illuminate\Support\Facades\DB;

class OrderReviewObserver
{
    /**
     * Handle the OrderReview "created" event.
     */
    public function created(OrderReview $orderReview): void
    {
        $this->updateUserAverageRating($orderReview->reviewee_id);
    }

    /**
     * Handle the OrderReview "updated" event.
     */
    public function updated(OrderReview $orderReview): void
    {
        $this->updateUserAverageRating($orderReview->reviewee_id);
        
        if ($orderReview->isDirty('reviewee_id')) {
            $this->updateUserAverageRating($orderReview->getOriginal('reviewee_id'));
        }
    }

    /**
     * Handle the OrderReview "deleted" event.
     */
    public function deleted(OrderReview $orderReview): void
    {
        $this->updateUserAverageRating($orderReview->reviewee_id);
    }

    /**
     * Handle the OrderReview "restored" event.
     */
    public function restored(OrderReview $orderReview): void
    {
        $this->updateUserAverageRating($orderReview->reviewee_id);
    }

    /**
     * Handle the OrderReview "force deleted" event.
     */
    public function forceDeleted(OrderReview $orderReview): void
    {
        $this->updateUserAverageRating($orderReview->reviewee_id);
    }
    
    /**
     * Update the average rating for a user
     */
    private function updateUserAverageRating($userId): void
    {
        $averageRating = OrderReview::where('reviewee_id', $userId)->avg('rating');
        
        User::where('id', $userId)->update([
            'average_rating' => $averageRating
        ]);
    }
}
