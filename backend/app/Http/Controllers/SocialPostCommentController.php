<?php

namespace App\Http\Controllers;

use App\Models\SocialPost;
use App\Models\SocialPostComment;
use App\Rules\CleanContent;
use App\Services\BadWordService;
use App\Services\InputSanitizationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class SocialPostCommentController extends Controller
{
    /**
     * @var InputSanitizationService
     */
    protected $sanitizationService;

    /**
     * @var BadWordService
     */
    protected $badWordService;

    /**
     * Create a new controller instance.
     */
    public function __construct(
        InputSanitizationService $sanitizationService,
        BadWordService $badWordService
    ) {
        $this->sanitizationService = $sanitizationService;
        $this->badWordService = $badWordService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(SocialPost $socialPost)
    {
        $comments = $socialPost->comments()
            ->with('user')
            ->whereNull('parent_id')
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return response()->json($comments);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request, SocialPost $socialPost)
    {
        $request->validate([
            'content' => ['required', 'string', 'max:500', new CleanContent($this->badWordService)],
            'parent_id' => 'nullable|exists:social_post_comments,id',
        ]);

        // Sanitize user input
        $content = $this->sanitizationService->sanitize($request->content);
        
        $content = $this->badWordService->filterContent($content);

        $comment = SocialPostComment::create([
            'user_id' => Auth::id(),
            'social_post_id' => $socialPost->id,
            'parent_id' => $request->parent_id,
            'content' => $content,
        ]);

        return response()->json($comment, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(SocialPost $socialPost, SocialPostComment $comment)
    {
        if ($comment->social_post_id !== $socialPost->id) {
            return response()->json(['message' => 'Comment not found'], 404);
        }

        $comment->load('user', 'replies.user');
        return response()->json($comment);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, SocialPost $socialPost, SocialPostComment $comment)
    {
        if ($comment->social_post_id !== $socialPost->id) {
            return response()->json(['message' => 'Comment not found'], 404);
        }

        if ($comment->user_id !== Auth::id()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $request->validate([
            'content' => ['required', 'string', 'max:500', new CleanContent($this->badWordService)],
        ]);

        // Sanitize user input
        $content = $this->sanitizationService->sanitize($request->content);
        
        $content = $this->badWordService->filterContent($content);

        $comment->update([
            'content' => $content,
        ]);

        return response()->json($comment);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(SocialPost $socialPost, SocialPostComment $comment)
    {
        if ($comment->social_post_id !== $socialPost->id) {
            return response()->json(['message' => 'Comment not found'], 404);
        }

        if ($comment->user_id !== Auth::id()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $comment->delete();

        return response()->json(['message' => 'Comment deleted successfully']);
    }
}
