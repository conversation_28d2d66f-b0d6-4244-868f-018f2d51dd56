<?php

namespace App\Http\Controllers;

use App\Models\DeviceToken;
use App\Models\ScheduledOrder;
use App\Models\ScheduledOrderAcceptanceLog;
use App\Models\OrderReview;
use App\Models\User;
use App\Models\UserService;
use App\Models\UserServicePricingOption;
use App\Jobs\ProcessScheduledOrderTimeout;
use App\Models\ServiceCategory;
use App\Services\CommissionService;
use App\Services\CreditService;
use App\Services\DisputeService;
use App\Services\ExperienceService;
use App\Services\FirebaseNotificationService;
use App\Services\InputSanitizationService;
use App\Rules\CleanContent;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class ScheduledOrderController extends Controller
{
    protected $firebaseService;
    protected $creditService;
    protected $commissionService;
    protected $disputeService;
    protected $inputSanitizationService;
    protected $experienceService;

    public function __construct(
        FirebaseNotificationService $firebaseService,
        CreditService $creditService,
        CommissionService $commissionService,
        DisputeService $disputeService,
        InputSanitizationService $inputSanitizationService,
        ExperienceService $experienceService = null
    ) {
        $this->firebaseService = $firebaseService;
        $this->creditService = $creditService;
        $this->commissionService = $commissionService;
        $this->disputeService = $disputeService;
        $this->inputSanitizationService = $inputSanitizationService;
        $this->experienceService = $experienceService;
    }

    /**
     * Check if a scheduled order time conflicts with talent availability or existing orders
     *
     * @param User $talent
     * @param Carbon $startTime
     * @param int $durationMinutes
     * @return array|null Return null if no conflict, or an array with conflict details
     */
    private function checkSchedulingConflicts(User $talent, Carbon $startTime, int $durationMinutes): ?array
    {
        $dayOfWeek = $startTime->format('l'); // Monday, Tuesday, etc.
        $date = $startTime->format('Y-m-d');

        $specialAvailability = $talent->specialAvailabilities()
            ->whereRaw("JSON_CONTAINS(special_dates, JSON_OBJECT('date', ?), '$')", [$date])
            ->get();

        if ($specialAvailability->isNotEmpty() && $specialAvailability->contains('is_available', false)) {
            return [
                'type' => 'date_unavailable',
                'message' => 'Talent is not available on this date'
            ];
        }

        if ($specialAvailability->isEmpty()) {
            $weeklyAvailability = $talent->weeklyAvailabilities()
                ->whereJsonContains('availability_data', ['day' => $dayOfWeek])
                ->get();

            if ($weeklyAvailability->isEmpty()) {
                return [
                    'type' => 'day_unavailable',
                    'message' => 'Talent is not available on this day of the week'
                ];
            }

            $timeInRange = false;
            foreach ($weeklyAvailability as $slot) {
                $slotStart = Carbon::parse($slot->start_time)->setDateFrom($startTime);
                $slotEnd = Carbon::parse($slot->end_time)->setDateFrom($startTime);

                $orderEndTime = $startTime->copy()->addMinutes($durationMinutes);

                if ($startTime->greaterThanOrEqualTo($slotStart) && $orderEndTime->lessThanOrEqualTo($slotEnd)) {
                    $timeInRange = true;
                    break;
                }
            }

            if (!$timeInRange) {
                return [
                    'type' => 'time_unavailable',
                    'message' => 'The requested time is outside of talent\'s availability hours'
                ];
            }
        }

        $endTime = $startTime->copy()->addMinutes($durationMinutes);

        $conflictingOrders = ScheduledOrder::where('talent_id', $talent->id)
            ->where('status', 'accepted')
            ->where(function ($query) use ($startTime, $endTime) {
                $query->where(function ($q) use ($startTime, $endTime) {
                    $q->where('scheduled_start_time', '>=', $startTime)
                        ->where('scheduled_start_time', '<', $endTime);
                })
                    ->orWhere(function ($q) use ($startTime, $endTime) {
                        $q->where('scheduled_end_time', '>', $startTime)
                            ->where('scheduled_end_time', '<=', $endTime);
                    })
                    ->orWhere(function ($q) use ($startTime, $endTime) {
                        $q->where('scheduled_start_time', '<=', $startTime)
                            ->where('scheduled_end_time', '>=', $endTime);
                    });
            })
            ->count();

        if ($conflictingOrders > 0) {
            return [
                'type' => 'order_conflict',
                'message' => 'The requested time conflicts with existing orders'
            ];
        }

        return null; // No conflicts
    }

    /**
     * Create a new scheduled order for later
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function orderForLater(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'talent_id' => 'required|exists:users,id',
            'user_service_id' => 'required|exists:user_services,id',
            'pricing_option_type_id' => 'required|exists:pricing_option_types,id',
            'service_category_id' => 'required|exists:service_categories,id',
            'service_style_id' => 'required_if:service_category_id,1|exists:service_styles,id',
            'quantity' => 'required|integer|min:1',
            'scheduled_start_time' => 'required|date|after:now',
            'remarks' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $customer = auth()->user();
            $talentId = $request->input('talent_id');
            $userServiceId = $request->input('user_service_id');
            $pricingOptionTypeId = $request->input('pricing_option_type_id');
            $serviceCategoryId = $request->input('service_category_id');
            $serviceStyleId = $request->input('service_style_id');

            $talent = User::find($talentId);
            if (!$talent) {
                return response()->json([
                    'success' => false,
                    'message' => 'Talent not found',
                ], 404);
            }

            $userService = UserService::where('id', $userServiceId)
                ->where('user_id', $talentId)
                ->where('is_active', true)
                ->first();

            if (!$userService) {
                return response()->json([
                    'success' => false,
                    'message' => 'Service not found or not active for this talent',
                ], 404);
            }

            $pricingOption = UserServicePricingOption::where('user_service_id', $userServiceId)
                ->where('pricing_option_type_id', $pricingOptionTypeId)
                ->where('is_active', true)
                ->first();

            if (!$pricingOption) {
                return response()->json([
                    'success' => false,
                    'message' => 'Pricing option not found or not active for this service',
                ], 404);
            }

            $pricingOptionType = $pricingOption->pricingOptionType;
            if (!$pricingOptionType) {
                return response()->json([
                    'success' => false,
                    'message' => 'Pricing option type not found',
                ], 404);
            }

            $userService = UserService::find($userServiceId);
            $quantity = $request->input('quantity', 1);
            $creditAmount = 0;

            if ($serviceCategoryId != ServiceCategory::OTHER_CATEGORY_ID) { // Not Talent category
                $serviceStyle = $userService->serviceStyles()
                    ->where('service_style_id', $serviceStyleId)
                    ->first();

                if (!$serviceStyle) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Service style not found or not active for this service',
                    ], 404);
                }

                $creditAmount = $serviceStyle->pivot->price * $quantity;
            } else {
                $creditAmount = $userService->price * $quantity;
            }

            if ($customer->credits_balance < $creditAmount) {
                return response()->json([
                    'success' => false,
                    'message' => 'Insufficient credit',
                ], 400);
            }

            $scheduledStartTime = Carbon::parse($request->input('scheduled_start_time'));

            $durationMinutes = null;

            if (!$pricingOptionType) {
                Log::error('Pricing option type not found', [
                    'pricing_option_type_id' => $pricingOptionTypeId,
                    'user_service_id' => $userServiceId,
                ]);
                return response()->json([
                    'success' => false,
                    'message' => 'Pricing option type not found',
                    'error_code' => 'pricing_option_type_not_found',
                ], 404);
            }

            $durationMinutes = 60; // Default to 1 hour

            if ($pricingOptionType->unit) {
                $value = $quantity;
                $unit = $pricingOptionType->unit;

                if ($unit === 'hours') {
                    $durationMinutes = $value * 60; // Convert hours to minutes
                } else if ($unit === 'days') {
                    $durationMinutes = $value * 24 * 60; // Convert days to minutes
                } else {
                    $durationMinutes = $value; // Assume minutes by default
                }

                Log::info('Using duration from pricing option type', [
                    'pricing_option_type_id' => $pricingOptionTypeId,
                    'unit' => $unit,
                    'value' => $value,
                    'duration_minutes' => $durationMinutes,
                ]);
            } else {
                Log::info('Using default duration for service', [
                    'pricing_option_type_id' => $pricingOptionTypeId,
                    'default_duration_minutes' => $durationMinutes,
                ]);
            }

            $scheduledEndTime = $scheduledStartTime->copy()->addMinutes($durationMinutes);

            $date = $scheduledStartTime->copy()->startOfDay();
            $dateString = $date->format('Y-m-d');

            $dayOfWeek = $date->format('l'); // Monday, Tuesday, etc.

            $specialAvailability = $talent->specialAvailabilities()
                ->whereRaw("JSON_CONTAINS(special_dates, JSON_OBJECT('date', ?), '$')", [$dateString])
                ->get();

            if ($specialAvailability->isNotEmpty() && $specialAvailability->contains('is_available', false)) {
                Log::info('Order placed for talent with special unavailability', [
                    'talent_id' => $talentId,
                    'date' => $dateString,
                ]);
            }

            $availabilitySlots = [];

            if ($specialAvailability->isNotEmpty()) {
                foreach ($specialAvailability as $slot) {
                    $availabilitySlots[] = [
                        'start_time' => $slot->start_time,
                        'end_time' => $slot->end_time,
                    ];
                }
            } else {
                $weeklyAvailability = $talent->weeklyAvailabilities()
                    ->whereJsonContains('availability_data', ['day' => $dayOfWeek])
                    ->get();

                if ($weeklyAvailability->isEmpty()) {
                    Log::info('Order placed for talent with no weekly availability', [
                        'talent_id' => $talentId,
                        'day_of_week' => $dayOfWeek,
                    ]);
                }

                foreach ($weeklyAvailability as $slot) {
                    $availabilitySlots[] = [
                        'start_time' => $slot->start_time,
                        'end_time' => $slot->end_time,
                    ];
                }
            }

            $existingOrders = ScheduledOrder::where('talent_id', $talentId)
                ->where('status', 'accepted')
                ->whereDate('scheduled_start_time', $dateString)
                ->get();

            $hasOrderConflict = false;

            foreach ($existingOrders as $order) {
                $orderStart = Carbon::parse($order->scheduled_start_time);
                $orderEnd = $order->scheduled_end_time ? Carbon::parse($order->scheduled_end_time) : $orderStart->copy()->addMinutes(60); // Default 1 hour if not specified

                if (
                    ($scheduledStartTime >= $orderStart && $scheduledStartTime < $orderEnd) || // Start during order
                    ($scheduledEndTime > $orderStart && $scheduledEndTime <= $orderEnd) || // End during order
                    ($scheduledStartTime <= $orderStart && $scheduledEndTime >= $orderEnd) // Contains order
                ) {
                    $hasOrderConflict = true;
                    break;
                }
            }

            if ($hasOrderConflict) {
                return response()->json([
                    'success' => false,
                    'message' => 'The selected time conflicts with an existing order. Please choose a different time.',
                    'error_code' => 'order_conflict',
                ], 400);
            }

            $existingPendingOrder = ScheduledOrder::where('customer_id', $customer->id)
                ->where('talent_id', $talentId)
                ->where('user_service_id', $userServiceId)
                ->where('status', 'pending')
                ->exists();

            if ($existingPendingOrder) {
                return response()->json([
                    'success' => false,
                    'message' => 'You already have a pending scheduled order for this service with this talent',
                ], 400);
            }

            DB::beginTransaction();

            try {
                $this->creditService->deductCredits(
                    $customer,
                    $creditAmount,
                    "Credit hold for pre-booked Order placement",
                    ['service_id' => $userServiceId]
                );

                $availabilityRecord = null;

                $specialAvailability = $talent->specialAvailabilities()
                    ->whereRaw("JSON_CONTAINS(special_dates, JSON_OBJECT('date', ?), '$')", [$dateString])
                    ->get();

                if ($specialAvailability->isNotEmpty()) {
                    foreach ($specialAvailability as $slot) {
                        $slotStart = Carbon::parse($slot->start_time)->setDateFrom($date);
                        $slotEnd = Carbon::parse($slot->end_time)->setDateFrom($date);

                        if (
                            $scheduledStartTime->greaterThanOrEqualTo($slotStart) &&
                            $scheduledEndTime->lessThanOrEqualTo($slotEnd)
                        ) {
                            $availabilityRecord = $slot;
                            break;
                        }
                    }
                } else {
                    $weeklyAvailability = $talent->weeklyAvailabilities()
                        ->whereJsonContains('availability_data', ['day' => $dayOfWeek])
                        ->get();

                    foreach ($weeklyAvailability as $slot) {
                        $slotStart = Carbon::parse($slot->start_time)->setDateFrom($date);
                        $slotEnd = Carbon::parse($slot->end_time)->setDateFrom($date);

                        if (
                            $scheduledStartTime->greaterThanOrEqualTo($slotStart) &&
                            $scheduledEndTime->lessThanOrEqualTo($slotEnd)
                        ) {
                            $availabilityRecord = $slot;
                            break;
                        }
                    }
                }

                $order = ScheduledOrder::create([
                    'customer_id' => $customer->id,
                    'talent_id' => $talentId,
                    'user_service_id' => $userServiceId,
                    'service_category_id' => $serviceCategoryId,
                    'pricing_option_type_id' => $pricingOptionTypeId,
                    'service_style_id' => $serviceCategoryId != 2 ? $serviceStyleId : null,
                    'quantity' => $request->input('quantity', 1),
                    'status' => 'pending',
                    'remarks' => $request->input('remarks'),
                    'credit_amount' => $creditAmount,
                    'is_credit_held' => true,
                    'credit_held_at' => now(),
                    'scheduled_start_time' => $scheduledStartTime,
                    'scheduled_end_time' => $scheduledEndTime,
                ]);

                DB::commit();
            } catch (\Exception $e) {
                DB::rollBack();
                Log::error('Failed to hold credits for pre-booked order', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                    'error_code' => 'credit_hold_failed',
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Failed to hold credits for order',
                    'error' => app()->environment('production') ? 'Server error' : $e->getMessage(),
                ], 500);
            }

            $deviceTokens = DeviceToken::where('user_id', $talentId)->pluck('token')->toArray();
            if (count($deviceTokens) > 0) {
                $serviceName = $userService->service_name ?? 'your service';
                foreach ($deviceTokens as $token) {
                    $this->firebaseService->sendToDevice(
                        $token,
                        'New Pre-booked Order Request',
                        "{$customer->nickname} has requested to book {$serviceName} in advance",
                        [
                            'order_id' => $order->id,
                            'notification_type' => 'prebooked_order_request',
                        ]
                    );
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Pre-booked order created successfully',
                'data' => [
                    'order_id' => $order->id,
                    'scheduled_start_time' => $scheduledStartTime,
                    'scheduled_end_time' => $scheduledEndTime,
                ],
            ], 201);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to create pre-booked order', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'error_code' => 'order_creation_failed',
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to create order',
                'error' => app()->environment('production') ? 'Server error' : $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Respond to a scheduled order (accept or reject)
     *
     * @param Request $request
     * @param int $orderId
     * @return \Illuminate\Http\JsonResponse
     */
    public function respondToScheduledOrder(Request $request, $orderId)
    {
        $validator = Validator::make($request->all(), [
            'action' => 'required|in:accept,reject',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $talent = auth()->user();

            $order = ScheduledOrder::where('id', $orderId)
                ->where('talent_id', $talent->id)
                ->where('status', 'pending')
                ->first();

            if (!$order) {
                return response()->json([
                    'success' => false,
                    'message' => 'Scheduled order not found or already processed',
                ], 404);
            }

            if ($order->scheduled_start_time->isPast()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot respond to a scheduled order after its start time has passed',
                ], 400);
            }

            $action = $request->input('action');
            $responseTimeSeconds = Carbon::now()->diffInSeconds($order->created_at);

            DB::beginTransaction();

            $order->status = $action === 'accept' ? 'accepted' : 'rejected';
            $order->responded_at = Carbon::now();

            if ($action === 'reject' && $order->is_credit_held) {
                $customer = User::find($order->customer_id);

                try {
                    $this->creditService->addCredits(
                        $customer,
                        $order->credit_amount,
                        "Credit refund for rejected Scheduled Order #{$order->id}",
                        ['order_id' => $order->id]
                    );

                    $order->is_credit_held = false;
                    $order->credit_released_at = now();
                } catch (\Exception $e) {
                    DB::rollBack();
                    Log::error('Failed to refund credits for rejected scheduled order', [
                        'order_id' => $order->id,
                        'error' => $e->getMessage(),
                    ]);

                    return response()->json([
                        'success' => false,
                        'message' => 'Failed to refund credits for rejected scheduled order',
                    ], 500);
                }
            }

            $order->save();

            try {
                $userServiceId = $order->user_service_id;

                $totalReceived = ScheduledOrder::where('talent_id', $talent->id)
                    ->where('user_service_id', $userServiceId)
                    ->count();
                $totalAccepted = ScheduledOrder::where('talent_id', $talent->id)
                    ->where('user_service_id', $userServiceId)
                    ->where('status', 'accepted')
                    ->count();
                $totalRejected = ScheduledOrder::where('talent_id', $talent->id)
                    ->where('user_service_id', $userServiceId)
                    ->where('status', 'rejected')
                    ->count();
                $totalNotResponded = ScheduledOrder::where('talent_id', $talent->id)
                    ->where('user_service_id', $userServiceId)
                    ->whereNotIn('status', ['accepted', 'rejected'])
                    ->count();

                $responseRate = $totalReceived > 0
                    ? (($totalAccepted + $totalRejected) / $totalReceived) * 100
                    : 0;

                $existingLog = ScheduledOrderAcceptanceLog::where('talent_id', $talent->id)
                    ->where('user_service_id', $userServiceId)
                    ->first();

                if ($existingLog) {
                    $existingLog->update([
                        'action' => $action === 'accept' ? 'accepted' : 'rejected',
                        'scheduled_orders_accepted' => $totalAccepted,
                        'scheduled_orders_rejected' => $totalRejected,
                        'total_scheduled_orders_received' => $totalReceived,
                        'scheduled_orders_not_responded' => $totalNotResponded,
                        'response_rate' => round($responseRate, 2),
                    ]);
                } else {
                    ScheduledOrderAcceptanceLog::create([
                        'talent_id' => $talent->id,
                        'user_service_id' => $userServiceId,
                        'action' => $action === 'accept' ? 'accepted' : 'rejected',
                        'scheduled_orders_accepted' => $totalAccepted,
                        'scheduled_orders_rejected' => $totalRejected,
                        'total_scheduled_orders_received' => $totalReceived,
                        'scheduled_orders_not_responded' => $totalNotResponded,
                        'response_rate' => round($responseRate, 2),
                    ]);
                }
            } catch (\Exception $e) {
                Log::error('Failed to create scheduled order acceptance log', [
                    'order_id' => $order->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]);
            }

            $talent->total_orders_received += 1;
            if ($action === 'accept') {
                $talent->total_orders_accepted += 1;
            }
            $talent->acceptance_rate = $talent->total_orders_accepted / $talent->total_orders_received;
            $talent->save();

            DB::commit();

            $customer = User::find($order->customer_id);
            if ($customer) {
                $deviceTokens = DeviceToken::where('user_id', $customer->id)->pluck('token')->toArray();
                if (count($deviceTokens) > 0) {
                    $status = $action === 'accept' ? 'accepted' : 'rejected';
                    foreach ($deviceTokens as $token) {
                        $this->firebaseService->sendToDevice(
                            $token,
                            'Scheduled Order ' . ucfirst($status),
                            "{$talent->nickname} has {$status} your scheduled order",
                            [
                                'order_id' => $order->id,
                                'notification_type' => 'scheduled_order_response',
                                'status' => $status
                            ]
                        );
                    }
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Scheduled order ' . ($action === 'accept' ? 'accepted' : 'rejected') . ' successfully',
                'data' => [
                    'order_id' => $order->id,
                    'status' => $order->status,
                    'responded_at' => $order->responded_at->toIso8601String(),
                ],
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to respond to scheduled order', [
                'order_id' => $orderId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to respond to scheduled order',
                'error' => app()->environment('production') ? 'Server error' : $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Complete a scheduled order
     *
     * @param Request $request
     * @param int $orderId
     * @return \Illuminate\Http\JsonResponse
     */
    public function completeScheduledOrder(Request $request, $orderId)
    {
        try {
            $user = auth()->user();

            $order = ScheduledOrder::where('id', $orderId)
                ->where('customer_id', $user->id)
                ->where('status', 'accepted')
                ->first();

            if (!$order) {
                return response()->json([
                    'success' => false,
                    'message' => 'Scheduled order not found or cannot be completed',
                ], 404);
            }

            DB::beginTransaction();

            $order->status = 'completed';
            $order->save();

            if ($order->is_credit_held) {
                $talent = User::find($order->talent_id);

                try {
                    $commissionResult = $this->commissionService->applyCommission(
                        $talent,
                        $order->id,
                        $order->credit_amount,
                        'scheduled'
                    );

                    $order->credit_released_at = now();
                    $order->save();
                } catch (\Exception $e) {
                    DB::rollBack();
                    Log::error('Failed to transfer credits to talent for scheduled order', [
                        'order_id' => $order->id,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString(),
                    ]);

                    return response()->json([
                        'success' => false,
                        'message' => 'Failed to complete scheduled order payment',
                        'error' => app()->environment('production') ? 'Server error' : $e->getMessage(),
                    ], 500);
                }
            }

            if ($this->experienceService) {
                try {
                    $serviceType = $order->userService->serviceType;
                    $experiencePoints = $serviceType->experience ?? 0;

                    if ($experiencePoints > 0) {
                        $customer = User::find($order->customer_id);
                        $talent = User::find($order->talent_id);

                        if ($customer) {
                            $this->experienceService->awardExperience(
                                $customer,
                                $experiencePoints,
                                'Completed scheduled order #' . $order->id,
                                [
                                    'scheduled_order_id' => $order->id,
                                    'service_type_id' => $serviceType->id,
                                    'service_type_name' => $serviceType->name,
                                    'role' => 'customer'
                                ]
                            );
                        }

                        if ($talent) {
                            $this->experienceService->awardExperience(
                                $talent,
                                $experiencePoints,
                                'Completed scheduled order #' . $order->id,
                                [
                                    'scheduled_order_id' => $order->id,
                                    'service_type_id' => $serviceType->id,
                                    'service_type_name' => $serviceType->name,
                                    'role' => 'talent'
                                ]
                            );
                        }
                    }
                } catch (\Exception $e) {
                    Log::error('Error awarding experience for scheduled order completion', [
                        'order_id' => $order->id,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                }
            } else {
                Log::warning('ExperienceService not available for scheduled order completion processing', [
                    'order_id' => $order->id
                ]);
            }

            DB::commit();

            $deviceTokens = DeviceToken::where('user_id', $order->talent_id)->pluck('token')->toArray();
            if (count($deviceTokens) > 0) {
                foreach ($deviceTokens as $token) {
                    $this->firebaseService->sendToDevice(
                        $token,
                        'Scheduled Order Completed',
                        "Scheduled Order #{$order->id} has been marked as completed",
                        [
                            'order_id' => $order->id,
                            'notification_type' => 'scheduled_order_completed',
                        ]
                    );
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Scheduled order completed successfully',
                'data' => [
                    'order_id' => $order->id,
                    'status' => $order->status,
                ],
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to complete scheduled order', [
                'order_id' => $orderId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to complete scheduled order',
                'error' => app()->environment('production') ? 'Server error' : $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get scheduled order details
     *
     * @param int $orderId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getScheduledOrder($orderId)
    {
        try {
            $user = auth()->user();

            $order = ScheduledOrder::with(['customer', 'talent', 'userService', 'pricingOptionType', 'serviceCategory', 'serviceStyle'])
                ->where('id', $orderId)
                ->where(function ($query) use ($user) {
                    $query->where('customer_id', $user->id)
                        ->orWhere('talent_id', $user->id);
                })
                ->first();

            if (!$order) {
                return response()->json([
                    'success' => false,
                    'message' => 'Scheduled order not found',
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $order,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get scheduled order details', [
                'order_id' => $orderId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get scheduled order details',
                'error' => app()->environment('production') ? 'Server error' : $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get user's scheduled orders
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUserScheduledOrders(Request $request)
    {
        try {
            $user = auth()->user();
            $role = $request->input('role', 'all'); // all, customer, talent
            $status = $request->input('status'); // optional filter by status
            $perPage = $request->input('per_page', 15);

            $query = ScheduledOrder::with(['customer', 'talent', 'userService', 'pricingOptionType', 'serviceCategory', 'serviceStyle']);

            if ($role === 'customer') {
                $query->where('customer_id', $user->id);
            } elseif ($role === 'talent') {
                $query->where('talent_id', $user->id);
            } else {
                $query->where(function ($q) use ($user) {
                    $q->where('customer_id', $user->id)
                        ->orWhere('talent_id', $user->id);
                });
            }

            if ($status) {
                $query->where('status', $status);
            }

            $query->orderBy('scheduled_start_time', 'asc');

            $orders = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $orders,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get user scheduled orders', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get user scheduled orders',
                'error' => app()->environment('production') ? 'Server error' : $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get talent's availability schedule
     *
     * @param Request $request
     * @param int $talentId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTalentAvailability(Request $request, $talentId)
    {
        try {
            $talent = User::findOrFail($talentId);

            $query = $talent->availabilities();

            if ($request->has('type') && $request->input('type') === 'weekly') {
                $query->whereNotNull('day_of_week')->where('is_recurring', true);
            } elseif ($request->has('type') && $request->input('type') === 'special') {
                $query->whereNotNull('special_date');
            }

            if ($request->has('date')) {
                $date = Carbon::parse($request->input('date'))->format('Y-m-d');
                $query->whereRaw("JSON_CONTAINS(special_dates, JSON_OBJECT('date', ?), '$')", [$date]);
            }

            if ($request->has('day')) {
                $query->whereJsonContains('availability_data', ['day' => $request->input('day')]);
            }

            if (!$request->has('date') && !$request->has('day') && !$request->has('type')) {
                $startDate = Carbon::now();
                $endDate = Carbon::now()->addDays(30);

                $weeklyAvailability = $talent->weeklyAvailabilities()->get();
                $specialAvailability = $talent->specialAvailabilities()
                    ->whereBetween('special_date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
                    ->get();

                return response()->json([
                    'success' => true,
                    'data' => [
                        'weekly' => $weeklyAvailability,
                        'special' => $specialAvailability,
                        'status' => $talent->getAvailabilityStatus(),
                    ]
                ]);
            }

            $availabilities = $query->get();

            return response()->json([
                'success' => true,
                'data' => $availabilities,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get talent availability', [
                'talent_id' => $talentId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get talent availability',
                'error' => app()->environment('production') ? 'Server error' : $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get talent's special availability hours
     *
     * @param Request $request
     * @param int $talentId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTalentSpecialHours(Request $request, $talentId)
    {
        try {
            $talent = User::findOrFail($talentId);

            $startDate = $request->has('start_date')
                ? Carbon::parse($request->input('start_date'))
                : Carbon::now();

            $endDate = $request->has('end_date')
                ? Carbon::parse($request->input('end_date'))
                : Carbon::now()->addDays(30);

            $specialAvailability = $talent->specialAvailabilities()
                ->whereBetween('special_date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
                ->get();

            return response()->json([
                'success' => true,
                'data' => [
                    'special_hours' => $specialAvailability,
                    'date_range' => [
                        'start' => $startDate->format('Y-m-d'),
                        'end' => $endDate->format('Y-m-d'),
                    ],
                ],
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get talent special hours', [
                'talent_id' => $talentId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get talent special hours',
                'error' => app()->environment('production') ? 'Server error' : $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Check if a talent is available at a specific time
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkTalentAvailability(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'talent_id' => 'required|exists:users,id',
            'scheduled_start_time' => 'required|date',
            'duration_minutes' => 'required|integer|min:1',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $talentId = $request->input('talent_id');
            $scheduledStartTime = Carbon::parse($request->input('scheduled_start_time'));
            $durationMinutes = $request->input('duration_minutes');

            $talent = User::findOrFail($talentId);

            $conflictingOrders = ScheduledOrder::where('talent_id', $talent->id)
                ->where('status', 'accepted')
                ->where(function ($query) use ($scheduledStartTime, $durationMinutes) {
                    $endTime = $scheduledStartTime->copy()->addMinutes($durationMinutes);
                    $query->where(function ($q) use ($scheduledStartTime, $endTime) {
                        $q->where('scheduled_start_time', '>=', $scheduledStartTime)
                            ->where('scheduled_start_time', '<', $endTime);
                    })
                        ->orWhere(function ($q) use ($scheduledStartTime, $endTime) {
                            $q->where('scheduled_end_time', '>', $scheduledStartTime)
                                ->where('scheduled_end_time', '<=', $endTime);
                        })
                        ->orWhere(function ($q) use ($scheduledStartTime, $endTime) {
                            $q->where('scheduled_start_time', '<=', $scheduledStartTime)
                                ->where('scheduled_end_time', '>=', $endTime);
                        });
                })
                ->count();

            if ($conflictingOrders > 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'The requested time conflicts with existing orders',
                    'conflict_type' => 'order_conflict',
                    'is_available' => false,
                ], 200);
            }

            return response()->json([
                'success' => true,
                'message' => 'Talent is available at the requested time',
                'is_available' => true,
                'scheduled_end_time' => $scheduledStartTime->copy()->addMinutes($durationMinutes)->toIso8601String(),
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to check talent availability', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to check talent availability',
                'error' => app()->environment('production') ? 'Server error' : $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get available time slots for a talent on a specific date
     *
     * @param Request $request
     * @param int $talentId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTalentAvailableTimeSlots(Request $request, $talentId)
    {
        $validator = Validator::make($request->all(), [
            'date' => 'required|date|after_or_equal:today',
            'service_id' => 'required|exists:user_services,id',
            'pricing_option_id' => 'required|exists:pricing_options,id',
            'quantity' => 'required|integer|min:1',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $date = Carbon::parse($request->input('date'))->startOfDay();
            $serviceId = $request->input('service_id');
            $pricingOptionId = $request->input('pricing_option_id');
            $quantity = $request->input('quantity');

            $talent = User::findOrFail($talentId);

            $userService = UserService::where('id', $serviceId)
                ->where('user_id', $talentId)
                ->where('is_active', true)
                ->first();

            if (!$userService) {
                return response()->json([
                    'success' => false,
                    'message' => 'Service not found or not active for this talent',
                ], 404);
            }

            $pricingOption = UserServicePricingOption::where('user_service_id', $serviceId)
                ->where('pricing_option_id', $pricingOptionId)
                ->where('is_active', true)
                ->first();

            if (!$pricingOption) {
                return response()->json([
                    'success' => false,
                    'message' => 'Pricing option not found or not active for this service',
                ], 404);
            }

            $durationMinutes = null;
            $pricingOptionDetails = $pricingOption->pricingOption;

            if (!$pricingOptionDetails) {
                Log::error('Pricing option details not found', [
                    'pricing_option_id' => $pricingOptionId,
                    'user_service_id' => $userServiceId,
                ]);
                return response()->json([
                    'success' => false,
                    'message' => 'Pricing option details not found',
                    'error_code' => 'pricing_option_not_found',
                ], 404);
            }

            if (!$pricingOptionType) {
                Log::error('Pricing option type not found', [
                    'pricing_option_type_id' => $pricingOptionTypeId,
                    'user_service_id' => $userServiceId,
                ]);
                return response()->json([
                    'success' => false,
                    'message' => 'Pricing option type not found',
                    'error_code' => 'pricing_option_type_not_found',
                ], 404);
            }

            $durationMinutes = 60; // Default to 1 hour

            if ($pricingOptionType->unit) {
                $value = $quantity;
                $unit = $pricingOptionType->unit;

                if ($unit === 'hours') {
                    $durationMinutes = $value * 60; // Convert hours to minutes
                } else if ($unit === 'days') {
                    $durationMinutes = $value * 24 * 60; // Convert days to minutes
                } else {
                    $durationMinutes = $value; // Assume minutes by default
                }

                Log::info('Using duration from pricing option type', [
                    'pricing_option_type_id' => $pricingOptionTypeId,
                    'unit' => $unit,
                    'value' => $value,
                    'duration_minutes' => $durationMinutes,
                ]);
            } else {
                Log::info('Using default duration for service', [
                    'pricing_option_type_id' => $pricingOptionTypeId,
                    'default_duration_minutes' => $durationMinutes,
                ]);
            }

            $dayOfWeek = $date->format('l'); // Monday, Tuesday, etc.
            $dateString = $date->format('Y-m-d');

            $specialAvailability = $talent->specialAvailabilities()
                ->whereRaw("JSON_CONTAINS(special_dates, JSON_OBJECT('date', ?), '$')", [$dateString])
                ->get();

            $isSpecialUnavailable = $specialAvailability->isNotEmpty() && $specialAvailability->contains('is_available', false);

            if ($isSpecialUnavailable) {
                Log::info('Retrieving available slots for talent with special unavailability', [
                    'talent_id' => $talent->id,
                    'date' => $dateString,
                ]);
            }

            $availabilitySlots = [];

            if ($specialAvailability->isNotEmpty()) {
                foreach ($specialAvailability as $slot) {
                    $availabilitySlots[] = [
                        'start_time' => $slot->start_time,
                        'end_time' => $slot->end_time,
                    ];
                }
            } else {
                $weeklyAvailability = $talent->weeklyAvailabilities()
                    ->whereJsonContains('availability_data', ['day' => $dayOfWeek])
                    ->get();

                if ($weeklyAvailability->isEmpty()) {
                    Log::info('Retrieving available slots for talent with no weekly availability', [
                        'talent_id' => $talent->id,
                        'day_of_week' => $dayOfWeek,
                    ]);
                    $availabilitySlots = [];
                }

                foreach ($weeklyAvailability as $slot) {
                    $availabilitySlots[] = [
                        'start_time' => $slot->start_time,
                        'end_time' => $slot->end_time,
                    ];
                }
            }

            $existingOrders = ScheduledOrder::where('talent_id', $talentId)
                ->where('status', 'accepted')
                ->whereDate('scheduled_start_time', $dateString)
                ->get();

            $timeSlots = [];

            foreach ($availabilitySlots as $slot) {
                $slotStart = Carbon::parse($slot['start_time'])->setDateFrom($date);
                $slotEnd = Carbon::parse($slot['end_time'])->setDateFrom($date);

                $current = $slotStart->copy();

                while ($current->copy()->addMinutes($durationMinutes)->lessThanOrEqualTo($slotEnd)) {
                    $slotEndTime = $current->copy()->addMinutes($durationMinutes);

                    $hasConflict = false;

                    foreach ($existingOrders as $order) {
                        $orderStart = Carbon::parse($order->scheduled_start_time);
                        $orderEnd = $order->scheduled_end_time ? Carbon::parse($order->scheduled_end_time) : $orderStart->copy()->addMinutes(60); // Default 1 hour if not specified

                        if (
                            ($current >= $orderStart && $current < $orderEnd) || // Slot start during order
                            ($slotEndTime > $orderStart && $slotEndTime <= $orderEnd) || // Slot end during order
                            ($current <= $orderStart && $slotEndTime >= $orderEnd) // Slot contains order
                        ) {
                            $hasConflict = true;
                            break;
                        }
                    }

                    if (!$hasConflict) {
                        $timeSlots[] = [
                            'start_time' => $current->format('Y-m-d H:i:s'),
                            'end_time' => $slotEndTime->format('Y-m-d H:i:s'),
                            'formatted_start_time' => $current->format('h:i A'),
                            'formatted_end_time' => $slotEndTime->format('h:i A'),
                        ];
                    }

                    $current->addMinutes(30);
                }
            }

            return response()->json([
                'success' => true,
                'message' => count($timeSlots) > 0 ? 'Available time slots found' : 'No available time slots for this date',
                'date' => $dateString,
                'day_of_week' => $dayOfWeek,
                'duration_minutes' => $durationMinutes,
                'available_slots' => $timeSlots,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get talent available time slots', [
                'talent_id' => $talentId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get talent available time slots',
                'error' => app()->environment('production') ? 'Server error' : $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Create a review for a scheduled order
     *
     * @param Request $request
     * @param int $orderId
     * @return \Illuminate\Http\JsonResponse
     */
    public function createReview(Request $request, $orderId)
    {
        $validator = Validator::make($request->all(), [
            'rating' => 'required|integer|min:1|max:5',
            'review_text' => 'nullable|string|max:1000',
            'is_anonymous' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $user = auth()->user();

            $order = ScheduledOrder::where('id', $orderId)
                ->where(function ($query) use ($user) {
                    $query->where('customer_id', $user->id)
                        ->orWhere('talent_id', $user->id);
                })
                ->where('status', 'completed')
                ->first();

            if (!$order) {
                return response()->json([
                    'success' => false,
                    'message' => 'Scheduled order not found or not completed',
                ], 404);
            }

            $isCustomer = $order->customer_id === $user->id;
            $reviewerId = $user->id;
            $revieweeId = $isCustomer ? $order->talent_id : $order->customer_id;

            $existingReview = OrderReview::where('reviewable_type', 'App\\Models\\ScheduledOrder')
                ->where('reviewable_id', $order->id)
                ->where('reviewer_id', $reviewerId)
                ->first();

            if ($existingReview) {
                return response()->json([
                    'success' => false,
                    'message' => 'You have already reviewed this scheduled order',
                ], 400);
            }

            $isAnonymous = $isCustomer && $request->input('is_anonymous', false);

            $review = OrderReview::create([
                'reviewable_type' => 'App\\Models\\ScheduledOrder',
                'reviewable_id' => $order->id,
                'reviewer_id' => $reviewerId,
                'reviewee_id' => $revieweeId,
                'rating' => $request->input('rating'),
                'review_text' => $request->input('review_text'),
                'is_anonymous' => $isAnonymous,
                'is_hidden' => false,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Review submitted successfully',
                'data' => $review,
            ], 201);
        } catch (\Exception $e) {
            Log::error('Failed to create scheduled order review', [
                'order_id' => $orderId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to create review',
                'error' => app()->environment('production') ? 'Server error' : $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get reviews for a scheduled order
     *
     * @param int $orderId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getReviews($orderId)
    {
        try {
            $user = auth()->user();

            $order = ScheduledOrder::where('id', $orderId)
                ->where(function ($query) use ($user) {
                    $query->where('customer_id', $user->id)
                        ->orWhere('talent_id', $user->id);
                })
                ->first();

            if (!$order) {
                return response()->json([
                    'success' => false,
                    'message' => 'Scheduled order not found',
                ], 404);
            }

            $reviews = OrderReview::where('reviewable_type', 'App\\Models\\ScheduledOrder')
                ->where('reviewable_id', $order->id)
                ->with(['reviewer'])
                ->get()
                ->map(function ($review) use ($user) {
                    if ($review->is_anonymous && $review->reviewer_id !== $user->id) {
                        $review->reviewer->nickname = 'Anonymous';
                        $review->reviewer->profile_picture = null;
                    }
                    return $review;
                });

            return response()->json([
                'success' => true,
                'data' => $reviews,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get scheduled order reviews', [
                'order_id' => $orderId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get reviews',
                'error' => app()->environment('production') ? 'Server error' : $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Create a dispute for a scheduled order
     *
     * @param Request $request
     * @param int $orderId
     * @return \Illuminate\Http\JsonResponse
     */
    public function disputeScheduledOrder(Request $request, $orderId)
    {
        $validator = Validator::make($request->all(), [
            'dispute_type_id' => 'required|integer|exists:dispute_types,id',
            'description' => ['required', 'string', 'max:1000', new CleanContent],
            'images.*' => 'nullable|file|mimes:jpeg,png,jpg,heic,heif|max:10240', // 10MB max per image
            'video' => 'nullable|file|mimes:mp4,mov,avi,flv|max:20480', // 20MB max for video
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        if ($request->hasFile('images') && count($request->file('images')) > 3) {
            return response()->json([
                'success' => false,
                'message' => 'Maximum 3 images allowed',
            ], 422);
        }

        try {
            $user = $request->user();

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthenticated',
                ], 401);
            }

            $scheduledOrder = ScheduledOrder::where('id', $orderId)
                ->where(function ($query) use ($user) {
                    $query->where('customer_id', $user->id)
                        ->orWhere('talent_id', $user->id);
                })
                ->first();

            if (!$scheduledOrder) {
                return response()->json([
                    'success' => false,
                    'message' => 'Scheduled order not found or you are not related to this order',
                ], 404);
            }

            if (!$scheduledOrder->isAccepted()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Only accepted scheduled orders can have disputes',
                ], 400);
            }

            if ($scheduledOrder->isCompleted()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Completed scheduled orders cannot have disputes',
                ], 400);
            }

            $requestorType = $user->id === $scheduledOrder->customer_id ? 'client' : 'talent';

            $data = $validator->validated();
            $data['description'] = $this->inputSanitizationService->sanitize($data['description']);

            $files = [];
            if ($request->hasFile('images')) {
                foreach ($request->file('images') as $image) {
                    $files[] = $image;
                }
            }

            if ($request->hasFile('video')) {
                $videoFile = $request->file('video');
                if ($videoFile->isValid()) {
                    $files[] = $videoFile;
                    Log::info('Video file added to upload queue', [
                        'original_name' => $videoFile->getClientOriginalName(),
                        'size' => $videoFile->getSize(),
                        'mime_type' => $videoFile->getMimeType()
                    ]);
                } else {
                    Log::error('Invalid video file uploaded', [
                        'error' => $videoFile->getError(),
                        'error_message' => $videoFile->getErrorMessage(),
                        'original_name' => $videoFile->getClientOriginalName(),
                        'size' => $videoFile->getSize()
                    ]);
                    return response()->json([
                        'success' => false,
                        'message' => 'Video upload failed',
                        'errors' => [
                            'video' => ['The video file is invalid or corrupted.']
                        ]
                    ], 422);
                }
            }

            $dispute = $this->disputeService->createScheduledOrderDispute(
                $user,
                $scheduledOrder,
                $data,
                $files,
                $requestorType
            );

            return response()->json([
                'success' => true,
                'message' => 'Dispute created successfully',
                'data' => $dispute->load(['disputeType', 'media']),
            ], 201);
        } catch (\Exception $e) {
            Log::error('Failed to create dispute for scheduled order', [
                'order_id' => $orderId,
                'error' => $e->getMessage(),
                'error_code' => $e->getCode(),
                'user_id' => isset($user) ? $user->id : 'unknown',
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'error' => app()->environment('production') ? 'Server error' : $e->getMessage(),
            ], $e->getCode() >= 400 && $e->getCode() < 500 ? $e->getCode() : 500);
        }
    }

    /**
     * Cancel a scheduled order
     *
     * @param Request $request
     * @param int $orderId
     * @return \Illuminate\Http\JsonResponse
     */
    public function cancelScheduledOrder(Request $request, $orderId)
    {
        try {
            $request->validate([
                'cancellation_reason' => 'required|string|max:500',
            ]);
            
            $customer = auth()->user();

            $order = ScheduledOrder::where('id', $orderId)
                ->where('customer_id', $customer->id)
                ->first();

            if (!$order) {
                return response()->json([
                    'success' => false,
                    'message' => 'Scheduled order not found or you are not authorized to cancel it',
                ], 404);
            }

            if (!in_array($order->status, ['pending', 'accepted'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'Scheduled order cannot be cancelled in its current state',
                ], 400);
            }

            DB::beginTransaction();

            $order->status = 'cancelled';
            $order->cancellation_reason = $request->cancellation_reason;
            $order->save();

            if ($order->is_credit_held) {
                try {
                    $this->creditService->addCredits(
                        $customer,
                        $order->credit_amount,
                        "Credit refund for cancelled Scheduled Order #{$order->id}",
                        ['order_id' => $order->id]
                    );

                    $order->is_credit_held = false;
                    $order->credit_released_at = now();
                    $order->save();
                } catch (\Exception $e) {
                    DB::rollBack();
                    Log::error('Failed to refund credits for cancelled scheduled order', [
                        'order_id' => $order->id,
                        'error' => $e->getMessage(),
                    ]);

                    return response()->json([
                        'success' => false,
                        'message' => 'Failed to refund credits for cancelled scheduled order',
                    ], 500);
                }
            }

            DB::commit();

            $deviceTokens = DeviceToken::where('user_id', $order->talent_id)->pluck('token')->toArray();
            if (count($deviceTokens) > 0) {
                foreach ($deviceTokens as $token) {
                    $this->firebaseService->sendToDevice(
                        $token,
                        'Scheduled Order Cancelled',
                        "Scheduled Order #{$order->id} has been cancelled by the customer",
                        [
                            'order_id' => $order->id,
                            'notification_type' => 'scheduled_order_cancelled',
                        ]
                    );
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Scheduled order cancelled successfully',
                'data' => [
                    'order_id' => $order->id,
                    'status' => $order->status,
                ],
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to cancel scheduled order', [
                'order_id' => $orderId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to cancel scheduled order',
                'error' => app()->environment('production') ? 'Server error' : $e->getMessage(),
            ], 500);
        }
    }
}
