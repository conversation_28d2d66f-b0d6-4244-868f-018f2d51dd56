<?php

namespace App\Http\Controllers;

use App\Models\MalaysianBank;
use Illuminate\Http\Request;

class MalaysianBankController extends Controller
{
    /**
     * Get all active Malaysian banks.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $banks = MalaysianBank::where('is_active', true)
            ->orderBy('display_order')
            ->get();

        return response()->json($banks);
    }
}

