<?php

namespace App\Http\Controllers;

use App\Models\MalaysianBank;
use Illuminate\Http\Request;

class MalaysianBankController extends Controller
{
    /**
     * Display a listing of active Malaysian banks.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        $banks = MalaysianBank::where('is_active', true)
            ->orderBy('display_order')
            ->orderBy('name')
            ->get();

        return response()->json($banks);
    }
}
