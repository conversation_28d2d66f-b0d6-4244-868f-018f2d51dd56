<?php

namespace App\Http\Controllers;

use App\Models\Mission;
use App\Models\MissionDispute;
use App\Services\DisputeService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class MissionDisputeController extends Controller
{
    protected $disputeService;

    public function __construct(DisputeService $disputeService)
    {
        $this->disputeService = $disputeService;
    }

    /**
     * Create a new mission dispute
     *
     * @param Request $request
     * @param Mission $mission
     * @return \Illuminate\Http\JsonResponse
     */
    public function __invoke(Request $request, Mission $mission)
    {
        $validator = Validator::make($request->all(), [
            'dispute_type_id' => 'required|exists:dispute_types,id',
            'description' => 'required|string|max:1000',
            'media.*' => 'nullable|file|max:20480', // 20MB max
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        
        $requestorType = $mission->user_id === $user->id ? 'creator' : 'applicant';
        
        if ($requestorType === 'applicant' && !$mission->applicants()->where('user_id', $user->id)->exists()) {
            return response()->json([
                'message' => 'You are not authorized to create a dispute for this mission'
            ], 403);
        }

        try {
            // $childId = null;
            // if ($requestorType === 'creator') {
            //     $childId = $request->input('child_id');
            //     if (!$childId || !$mission->applicants()->where('user_id', $childId)->exists()) {
            //         return response()->json([
            //             'message' => 'You must specify a valid applicant for this dispute'
            //         ], 422);
            //     }
            // } else {
            //     $applicant = $mission->applicants()->where('user_id', $user->id)->first();
            //     $childId = $applicant ? $applicant->child_id : null;
            // }

            $data = [
                'mission_id' => $mission->id,
                'dispute_type_id' => $request->input('dispute_type_id'),
                'description' => $request->input('description'),
                // 'child_id' => $childId,
            ];

            $dispute = $this->disputeService->createMissionDispute(
                $user,
                $mission,
                $data,
                $request->file('media') ?? [],
                $requestorType
            );

            return response()->json([
                'message' => 'Dispute created successfully',
                'data' => $dispute->load('media')
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'message' => $e->getMessage()
            ], $e->getCode() >= 400 && $e->getCode() < 500 ? $e->getCode() : 500);
        }
    }
}
