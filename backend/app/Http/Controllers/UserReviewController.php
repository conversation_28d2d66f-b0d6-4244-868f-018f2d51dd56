<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\OrderReview;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Carbon;

class UserReviewController extends Controller
{
    /**
     * Get reviews for a user with filtering options.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $userId
     * @return \Illuminate\Http\Response
     */
    public function getUserReviews(Request $request, $userId)
    {
        try {
            $user = User::findOrFail($userId);
            
            $request->validate([
                'rating' => 'nullable|integer|between:1,5',
                'from_date' => 'nullable|date_format:Y-m-d',
                'to_date' => 'nullable|date_format:Y-m-d',
                'is_anonymous' => 'nullable|boolean',
            ]);
            
            $reviews = OrderReview::where('reviewee_id', $userId)
                ->with(['reviewer', 'reviewable']);
            
            if ($request->has('rating')) {
                $rating = $request->input('rating');
                $reviews->where('rating', $rating);
            }
            
            if ($request->has('from_date')) {
                $fromDate = Carbon::parse($request->input('from_date'))->startOfDay();
                $reviews->whereDate('created_at', '>=', $fromDate);
            }
            
            if ($request->has('to_date')) {
                $toDate = Carbon::parse($request->input('to_date'))->endOfDay();
                $reviews->whereDate('created_at', '<=', $toDate);
            }
            
            if ($request->has('is_anonymous')) {
                $isAnonymous = $request->boolean('is_anonymous');
                $reviews->where('is_anonymous', $isAnonymous);
            }
            
            $reviewsCollection = $reviews->get();
            
            $formattedReviews = $reviewsCollection->map(function ($review) {
                $orderType = $review->reviewable_type === 'App\\Models\\Order' ? 'regular' : 'scheduled';
                
                return [
                    'id' => $review->id,
                    'order_type' => $orderType,
                    'order_id' => $review->reviewable_id,
                    'reviewer' => $review->reviewer,
                    'rating' => $review->rating,
                    'review_text' => $review->review_text,
                    'is_anonymous' => $review->is_anonymous,
                    'created_at' => $review->created_at,
                    'updated_at' => $review->updated_at
                ];
            })->sortByDesc('created_at')->values();
            
            return response()->json([
                'success' => true,
                'data' => $formattedReviews
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get user reviews', [
                'user_id' => $userId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to get user reviews',
                'error' => app()->environment('production') ? 'Server error' : $e->getMessage()
            ], 500);
        }
    }
}
