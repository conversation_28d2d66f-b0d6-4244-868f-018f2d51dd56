<?php

namespace App\Http\Controllers;

use App\Models\StaticContent;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class BackOfficeStaticContentController extends Controller
{
    /**
     * List all static content.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(): JsonResponse
    {
        $contents = StaticContent::all();
        return response()->json($contents);
    }

    /**
     * Get a specific static content.
     *
     * @param string $key
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($key): JsonResponse
    {
        $content = StaticContent::where('key', $key)->first();
        
        if (!$content) {
            return response()->json([
                'message' => 'Static content not found'
            ], 404);
        }

        return response()->json($content);
    }

    /**
     * Update static content.
     *
     * @param \Illuminate\Http\Request $request
     * @param string $key
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $key): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'content' => 'required|string',
            'is_active' => 'sometimes|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $content = StaticContent::where('key', $key)->first();
        
        if (!$content) {
            // Create if it doesn't exist
            $content = new StaticContent();
            $content->key = $key;
        }

        $content->content = $request->content;
        
        if ($request->has('is_active')) {
            $content->is_active = $request->is_active;
        }
        
        $content->save();

        return response()->json([
            'message' => 'Static content updated successfully',
            'content' => $content
        ]);
    }
}
