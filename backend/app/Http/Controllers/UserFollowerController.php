<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\DeviceToken;
use App\Services\FirebaseNotificationService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

class UserFollowerController extends Controller
{
    protected $firebaseNotificationService;

    /**
     * Create a new controller instance.
     *
     * @param FirebaseNotificationService $firebaseNotificationService
     */
    public function __construct(FirebaseNotificationService $firebaseNotificationService)
    {
        $this->firebaseNotificationService = $firebaseNotificationService;
    }

    /**
     * Toggle follow/unfollow a user
     *
     * @param Request $request
     * @param int $userId
     * @return JsonResponse
     */
    public function toggleFollow(Request $request, int $userId): JsonResponse
    {
        try {
            $request->validate([
                'user_id' => 'sometimes|integer|exists:users,id'
            ]);

            $currentUser = Auth::user();
            
            if ($currentUser->id === $userId) {
                return response()->json([
                    'success' => false,
                    'message' => 'You cannot follow yourself'
                ], 400);
            }
            
            $userToFollow = User::findOrFail($userId);
            
            $isFollowing = $currentUser->following()->where('following_id', $userId)->exists();
            
            if ($isFollowing) {
                $currentUser->following()->detach($userId);
                $message = 'You have unfollowed this user';
                $action = 'unfollowed';
            } else {
                $currentUser->following()->attach($userId);
                $message = 'You are now following this user';
                $action = 'followed';
                
                $this->sendFollowNotification($userToFollow, $currentUser);
            }
            
            Log::info("User {$currentUser->id} {$action} user {$userId}");
            
            return response()->json([
                'success' => true,
                'message' => $message,
                'is_following' => !$isFollowing
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error("Error in toggleFollow: " . $e->getMessage(), [
                'user_id' => Auth::id(),
                'target_user_id' => $userId
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while processing your request'
            ], 500);
        }
    }
    
    /**
     * Send follow notification to a user
     *
     * @param User $userToNotify
     * @param User $follower
     * @return void
     */
    private function sendFollowNotification(User $userToNotify, User $follower): void
    {
        try {
            $deviceTokens = DeviceToken::where('user_id', $userToNotify->id)
                ->pluck('fcm_token')
                ->toArray();
            
            if (empty($deviceTokens)) {
                Log::info("No device tokens found for user {$userToNotify->id}");
                return;
            }
            
            $followerName = $follower->name ?? $follower->nickname ?? 'Someone';
            
            foreach ($deviceTokens as $token) {
                $this->firebaseNotificationService->sendFollowNotification(
                    $token,
                    $follower->id,
                    $followerName
                );
            }
            
            Log::info("Follow notification sent to user {$userToNotify->id} about follower {$follower->id}");
        } catch (\Exception $e) {
            Log::error("Error sending follow notification: " . $e->getMessage(), [
                'user_id' => $userToNotify->id,
                'follower_id' => $follower->id
            ]);
        }
    }
    
    /**
     * Get followers of a user
     *
     * @param Request $request
     * @param int $userId
     * @return JsonResponse
     */
    public function getFollowers(Request $request, int $userId): JsonResponse
    {
        try {
            $request->validate([
                'page' => 'sometimes|integer|min:1',
                'per_page' => 'sometimes|integer|min:1|max:100'
            ]);
            
            $perPage = $request->input('per_page', 15);
            
            $user = User::findOrFail($userId);
            
            $followers = $user->followers()
                ->select('users.id', 'users.name', 'users.nickname', 'users.profile_picture')
                ->paginate($perPage);
            
            $currentUser = Auth::user();
            $followers->getCollection()->transform(function ($follower) use ($currentUser) {
                $follower->is_following = $currentUser->following()
                    ->where('following_id', $follower->id)
                    ->exists();
                return $follower;
            });
            
            return response()->json([
                'success' => true,
                'data' => $followers
            ]);
            
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error("Error in getFollowers: " . $e->getMessage(), [
                'user_id' => $userId
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while processing your request'
            ], 500);
        }
    }
    
    /**
     * Get users that a user is following
     *
     * @param Request $request
     * @param int $userId
     * @return JsonResponse
     */
    public function getFollowing(Request $request, int $userId): JsonResponse
    {
        try {
            $request->validate([
                'page' => 'sometimes|integer|min:1',
                'per_page' => 'sometimes|integer|min:1|max:100'
            ]);
            
            $perPage = $request->input('per_page', 15);
            
            $user = User::findOrFail($userId);
            
            $following = $user->following()
                ->select('users.id', 'users.name', 'users.nickname', 'users.profile_picture')
                ->paginate($perPage);
            
            $currentUser = Auth::user();
            $following->getCollection()->transform(function ($followedUser) use ($currentUser) {
                $followedUser->is_following = true; // Already following these users
                return $followedUser;
            });
            
            return response()->json([
                'success' => true,
                'data' => $following
            ]);
            
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error("Error in getFollowing: " . $e->getMessage(), [
                'user_id' => $userId
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while processing your request'
            ], 500);
        }
    }
}
