<?php

namespace App\Http\Controllers;

use App\Http\Requests\UserAvailability\StoreRequest;
use App\Http\Requests\UserAvailability\UpdateRequest;
use App\Http\Requests\UserAvailability\BatchUpdateRequest;
use App\Http\Requests\UserAvailability\OverrideRequest;
use App\Models\UserAvailability;
use App\Models\UserAvailabilityOverride;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

use function React\Promise\all;

class UserAvailabilityController extends Controller
{
    public function index(Request $request)
    {
        Log::info('Test log', $request->all());
        $query = $request->user()->availabilities();

        if ($request->has('type') && $request->input('type') === 'weekly') {
            $query->whereNull('special_dates')->where('is_recurring', true);
        } elseif ($request->has('type') && $request->input('type') === 'special') {
            $query->whereNotNull('special_dates');
        }

        if ($request->has('date')) {
            $date = Carbon::parse($request->input('date'))->format('Y-m-d');
            $query->whereJsonContains('special_dates', $date);
        }

        if ($request->has('day')) {
            $dayOfWeek = $request->input('day');
            $query->whereJsonContains('availability_data', ['day' => $dayOfWeek]);
        }

        return response()->json($query->get());
    }

    public function store(StoreRequest $request)
    {
        Log::info('Raw Data:', $request->all());
        Log::info('Validated Data:', $request->validated());

        $data = $request->validated();

        if (isset($data['time_periods']) && !isset($data['availability_data'])) {
            $groupedByDay = [];
            $special_dates_periods = [];
            
            if (isset($data['special_dates']) && !empty($data['special_dates'])) {
                foreach ($data['time_periods'] as $period) {
                    if (!isset($period['day']) || $period['day'] === null) {
                        $special_dates_periods[] = [
                            'start_time' => $period['start_time'],
                            'end_time' => $period['end_time']
                        ];
                    }
                }
            }
            
            foreach ($data['time_periods'] as $period) {
                $day = $period['day'] ?? null;
                
                if ($day) {
                    if (!isset($groupedByDay[$day])) {
                        $groupedByDay[$day] = [
                            'day' => $day,
                            'periods' => []
                        ];
                    }
                    
                    $groupedByDay[$day]['periods'][] = [
                        'start_time' => $period['start_time'],
                        'end_time' => $period['end_time']
                    ];
                }
            }
            
            if (isset($data['special_dates'])) {
                $isCorrectFormat = !empty($data['special_dates']) && 
                                  isset($data['special_dates'][0]['date']) && 
                                  isset($data['special_dates'][0]['periods']);
                
                if (!$isCorrectFormat && !empty($special_dates_periods)) {
                    $special_dates_array = [];
                    foreach ($data['special_dates'] as $date) {
                        $special_dates_array[] = [
                            'date' => $date,
                            'periods' => $special_dates_periods
                        ];
                    }
                    $data['special_dates'] = $special_dates_array;
                }
            }
            
            $data['availability_data'] = array_values($groupedByDay);
            unset($data['time_periods']);
        }
        
        if (isset($data['special_dates']) && !empty($data['special_dates'])) {
            $isCorrectFormat = isset($data['special_dates'][0]['date']) && 
                              isset($data['special_dates'][0]['periods']);
            
            if (!$isCorrectFormat) {
                $special_dates_array = [];
                $periods = [];
                
                if (isset($data['availability_data'])) {
                    foreach ($data['availability_data'] as $dayData) {
                        if (empty($dayData['day']) && isset($dayData['periods'])) {
                            $periods = $dayData['periods'];
                            break;
                        }
                    }
                }
                
                foreach ($data['special_dates'] as $date) {
                    $special_dates_array[] = [
                        'date' => $date,
                        'periods' => $periods
                    ];
                }
                
                $data['special_dates'] = $special_dates_array;
                
            }
        }

        $user = $request->user();
        $existingAvailability = $user->availabilities()->first();

        if ($existingAvailability) {
            $existingAvailability->update($data);
            $availability = $existingAvailability;
        } else {
            // Create new record
            $availability = $user->availabilities()->create($data);
        }

        return response()->json($availability, 201);
    }

    public function show(UserAvailability $availability)
    {
        $this->authorize('view', $availability);

        return response()->json($availability);
    }

    public function update(UpdateRequest $request, UserAvailability $availability)
    {
        $this->authorize('update', $availability);

        $data = $request->validated();

        if (isset($data['time_periods']) && !isset($data['availability_data'])) {
            $groupedByDay = [];
            $special_dates_periods = [];
            
            if (isset($data['special_dates']) && !empty($data['special_dates'])) {
                foreach ($data['time_periods'] as $period) {
                    if (!isset($period['day']) || $period['day'] === null) {
                        $special_dates_periods[] = [
                            'start_time' => $period['start_time'],
                            'end_time' => $period['end_time']
                        ];
                    }
                }
            }
            
            foreach ($data['time_periods'] as $period) {
                $day = $period['day'] ?? null;
                
                if ($day) {
                    if (!isset($groupedByDay[$day])) {
                        $groupedByDay[$day] = [
                            'day' => $day,
                            'periods' => []
                        ];
                    }
                    
                    $groupedByDay[$day]['periods'][] = [
                        'start_time' => $period['start_time'],
                        'end_time' => $period['end_time']
                    ];
                }
            }
            
            if (isset($data['special_dates'])) {
                $isCorrectFormat = !empty($data['special_dates']) && 
                                  isset($data['special_dates'][0]['date']) && 
                                  isset($data['special_dates'][0]['periods']);
                
                if (!$isCorrectFormat && !empty($special_dates_periods)) {
                    $special_dates_array = [];
                    foreach ($data['special_dates'] as $date) {
                        $special_dates_array[] = [
                            'date' => $date,
                            'periods' => $special_dates_periods
                        ];
                    }
                    $data['special_dates'] = $special_dates_array;
                }
            }
            
            $data['availability_data'] = array_values($groupedByDay);
            unset($data['time_periods']);
        }
        
        if (isset($data['special_dates']) && !empty($data['special_dates'])) {
            $isCorrectFormat = isset($data['special_dates'][0]['date']) && 
                              isset($data['special_dates'][0]['periods']);
            
            if (!$isCorrectFormat) {
                $special_dates_array = [];
                $periods = [];
                
                if (isset($data['availability_data'])) {
                    foreach ($data['availability_data'] as $dayData) {
                        if (empty($dayData['day']) && isset($dayData['periods'])) {
                            $periods = $dayData['periods'];
                            break;
                        }
                    }
                }
                
                foreach ($data['special_dates'] as $date) {
                    $special_dates_array[] = [
                        'date' => $date,
                        'periods' => $periods
                    ];
                }
                
                $data['special_dates'] = $special_dates_array;
                
            }
        }

        $availability->update($data);

        return response()->json($availability);
    }

    public function destroy(UserAvailability $availability)
    {
        $this->authorize('delete', $availability);
        
        $availability->update([
            'availability_data' => []
        ]);

        return response()->noContent();
    }

    public function batchUpdate(BatchUpdateRequest $request)
    {
        $validated = $request->validated();
        $user = $request->user();

        $startDate = Carbon::parse($validated['date_range']['start']);
        $endDate = Carbon::parse($validated['date_range']['end']);

        $dates = [];
        $currentDate = $startDate->copy();

        // Generate all dates in the range
        while ($currentDate->lte($endDate)) {
            $dates[] = $currentDate->format('Y-m-d');
            $currentDate->addDay();
        }

        DB::beginTransaction();

        try {
            $availability = $user->availabilities()->first();
            
            if (!$availability) {
                $availability = new UserAvailability([
                    'user_id' => $user->id,
                    'is_recurring' => false,
                    'is_available' => true,
                    'availability_data' => []
                ]);
            }
            
            $availabilityData = $availability->availability_data ?? [];
            
            $availabilityData = array_filter($availabilityData, function($item) {
                return isset($item['day']) && !isset($item['date']);
            });
            
            $special_dates = $availability->special_dates ?? [];
            
            // Prepare periods for all dates in the range
            $periods = [];
            if ($validated['is_available'] && isset($validated['time_slots'])) {
                foreach ($validated['time_slots'] as $timeSlot) {
                    $periods[] = [
                        'start_time' => $timeSlot['start_time'],
                        'end_time' => $timeSlot['end_time']
                    ];
                }
            }
            
            // Create special_dates array with date and periods structure
            $special_dates_array = [];
            foreach ($dates as $date) {
                $special_dates_array[] = [
                    'date' => $date,
                    'periods' => $periods
                ];
            }
            
            $availability->special_dates = $special_dates_array;
            
            $availability->availability_data = $availabilityData;
            $availability->remarks = $validated['remarks'] ?? null;
            $availability->save();

            DB::commit();

            return response()->json([
                'message' => 'Availability updated successfully',
                'dates' => $dates,
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['message' => 'Failed to update availability: ' . $e->getMessage()], 500);
        }
    }

    public function getStatus(Request $request)
    {
        $user = $request->user();
        $status = $user->getAvailabilityStatus();

        return response()->json([
            'status' => $status,
        ]);
    }

    /**
     * Set a temporary availability override.
     */
    public function setOverride(OverrideRequest $request)
    {
        $validated = $request->validated();
        $user = $request->user();

        // Calculate start and end dates based on override type
        $startDate = now();
        $endDate = null;

        switch ($validated['override_type']) {
            case 'today':
                $endDate = $startDate->copy()->endOfDay();
                break;
            case 'three_days':
                $endDate = $startDate->copy()->addDays(3)->endOfDay();
                break;
            case 'one_week':
                $endDate = $startDate->copy()->addWeek()->endOfDay();
                break;
            case 'one_month':
                $endDate = $startDate->copy()->addMonth()->endOfDay();
                break;
            case 'fifteen_minutes':
                $endDate = $startDate->copy()->addMinutes(15);
                break;
            case 'thirty_minutes':
                $endDate = $startDate->copy()->addMinutes(30);
                break;
            case 'one_hour':
                $endDate = $startDate->copy()->addHour();
                break;
            case 'two_hours':
                $endDate = $startDate->copy()->addHours(2);
                break;
            case 'six_hours':
                $endDate = $startDate->copy()->addHours(6);
                break;
            case 'twelve_hours':
                $endDate = $startDate->copy()->addHours(12);
                break;
            case 'always':
                $endDate = null;
                break;
            case 'available':
                $startDate = null;
                $endDate = null;
                break;
        }

        // Create or update the override
        $override = $user->availabilityOverride()->updateOrCreate(
            ['user_id' => $user->id],
            [
                'override_type' => $validated['override_type'],
                'start_date' => $startDate,
                'end_date' => $endDate,
                'remarks' => $validated['remarks'] ?? null,
            ]
        );

        return response()->json([
            'message' => 'Availability override set successfully',
            'override' => $override,
        ]);
    }

    /**
     * Get the current availability override.
     */
    public function getOverride(Request $request)
    {
        $user = $request->user();
        $override = $user->availabilityOverride;

        if (!$override) {
            return response()->json([
                'override_type' => 'available',
                'is_active' => false,
            ]);
        }

        return response()->json([
            'id' => $override->id,
            'override_type' => $override->override_type,
            'start_date' => $override->start_date,
            'end_date' => $override->end_date,
            'remarks' => $override->remarks,
            'is_active' => $override->isActive(),
            'created_at' => $override->created_at,
            'updated_at' => $override->updated_at,
        ]);
    }

    /**
     * Remove the availability override.
     */
    public function removeOverride(Request $request)
    {
        $user = $request->user();
        $override = $user->availabilityOverride;

        if ($override) {
            $override->update(['override_type' => 'available']);
        }

        return response()->json([
            'message' => 'Availability override removed successfully',
        ]);
    }
}
