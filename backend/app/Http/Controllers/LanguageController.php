<?php

namespace App\Http\Controllers;

use App\Models\Language;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;

class LanguageController extends Controller
{
    /**
     * Display a listing of the languages.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $languages = Language::where('is_active', true)
                           ->orderBy('display_order')
                           ->get();

        return response()->json($languages);
    }

    /**
     * Display the specified language.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $language = Language::findOrFail($id);

        return response()->json($language);
    }
    
    /**
     * Get the languages associated with the authenticated user.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function getUserLanguages(Request $request)
    {
        $languages = $request->user()
            ->languages()
            ->get()
            ->map(function ($language) {
                return [
                    'id' => $language->id,
                    'name' => $language->name,
                    'description' => $language->description,
                ];
            });

        return response()->json([
            'languages' => $languages
        ]);
    }
    
    /**
     * Update the languages associated with the authenticated user.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function updateUserLanguages(Request $request)
    {
        $validated = $request->validate([
            'language_ids' => 'nullable|array',
            'language_ids.*' => 'exists:languages,id',
        ]);

        $request->user()->languages()->sync($validated['language_ids'] ?? []);

        return response()->json([
            'message' => 'Languages updated successfully',
        ]);
    }
}
