<?php

namespace App\Http\Controllers;

use App\Models\CreditPackage;
use App\Models\CreditChannel;
use App\Models\User;
use App\Services\CreditService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class CreditController extends Controller
{
    protected $creditService;

    public function __construct(CreditService $creditService)
    {
        $this->creditService = $creditService;
    }

    /**
     * Get available credit packages for the authenticated user based on channel.
     *
     * @param Request $request
     * @param string $channel
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAvailablePackages(Request $request, $channel)
    {
        $user = $request->user();
        $countryCode = $user->country_code ?? 'MY'; // Default to Malaysia if not set
        
        $creditChannel = CreditChannel::where('slug', $channel)
            ->where('is_active', true)
            ->firstOrFail();
        
        $packages = CreditPackage::where('country_code', $countryCode)
            ->where('credit_channel_id', $creditChannel->id)
            ->where('is_active', true)
            ->orderBy('display_order')
            ->orderBy('credits')
            ->get();
        
        return response()->json([
            'packages' => $packages
        ]);
    }

    /**
     * Get credit transaction history for the authenticated user.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTransactionHistory(Request $request)
    {
        $transactions = $request->user()->creditTransactions()
            ->orderBy('created_at', 'desc')
            ->paginate(15);
        
        return response()->json([
            'transactions' => $transactions
        ]);
    }

    /**
     * Get credit balance for the authenticated user.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getBalance(Request $request)
    {
        return response()->json([
            'credits_balance' => $request->user()->credits_balance
        ]);
    }

    /**
     * Admin endpoint to create a new credit package.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createPackage(Request $request)
    {
        // Validate admin role
        if ($request->user()->role !== 'admin') {
            return response()->json([
                'message' => 'Unauthorized'
            ], 403);
        }
        
        $validator = Validator::make($request->all(), [
            'credit_channel_id' => 'required|exists:credit_channels,id',
            'credits' => 'required|integer|min:1',
            'price' => 'required|numeric|min:0.01',
            'currency_code' => 'required|string|size:3',
            'country_code' => 'required|string|size:2',
            'is_active' => 'boolean',
            'display_order' => 'integer|min:0'
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }
        
        $package = CreditPackage::create($request->all());
        
        return response()->json([
            'message' => 'Credit package created successfully',
            'package' => $package
        ], 201);
    }

    /**
     * Admin endpoint to update an existing credit package.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updatePackage(Request $request, $id)
    {
        // Validate admin role
        if ($request->user()->role !== 'admin') {
            return response()->json([
                'message' => 'Unauthorized'
            ], 403);
        }
        
        $package = CreditPackage::findOrFail($id);
        
        $validator = Validator::make($request->all(), [
            'credit_channel_id' => 'exists:credit_channels,id',
            'credits' => 'integer|min:1',
            'price' => 'numeric|min:0.01',
            'currency_code' => 'string|size:3',
            'country_code' => 'string|size:2',
            'is_active' => 'boolean',
            'display_order' => 'integer|min:0'
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }
        
        $package->update($request->all());
        
        return response()->json([
            'message' => 'Credit package updated successfully',
            'package' => $package
        ]);
    }

    /**
     * Admin endpoint to delete a credit package.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function deletePackage(Request $request, $id)
    {
        // Validate admin role
        if ($request->user()->role !== 'admin') {
            return response()->json([
                'message' => 'Unauthorized'
            ], 403);
        }
        
        $package = CreditPackage::findOrFail($id);
        $package->delete();
        
        return response()->json([
            'message' => 'Credit package deleted successfully'
        ]);
    }

    /**
     * Admin endpoint to list all credit packages.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function listAllPackages(Request $request)
    {
        // Validate admin role
        if ($request->user()->role !== 'admin') {
            return response()->json([
                'message' => 'Unauthorized'
            ], 403);
        }
        
        $packages = CreditPackage::orderBy('country_code')
            ->orderBy('display_order')
            ->orderBy('credits')
            ->paginate(15);
        
        return response()->json([
            'packages' => $packages
        ]);
    }
    
    /**
     * Get available credit channels.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAvailableChannels(Request $request)
    {
        $channels = CreditChannel::where('is_active', true)
            ->orderBy('name')
            ->get();
        
        return response()->json([
            'channels' => $channels
        ]);
    }
}
