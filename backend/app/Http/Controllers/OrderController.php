<?php

namespace App\Http\Controllers;

use App\Events\OrderPlaced;
use App\Jobs\ProcessOrderTimeout;
use App\Models\DeviceToken;
use App\Models\Order;
use App\Models\OrderAcceptanceLog;
use App\Models\OrderReview;
use App\Models\ServiceCategory;
use App\Models\User;
use App\Models\UserService;
use App\Models\UserServicePricingOption;
use App\Rules\CleanContent;
use App\Services\CommissionService;
use App\Services\CreditService;
use App\Services\DisputeService;
use App\Services\ExperienceService;
use App\Services\FirebaseNotificationService;
use App\Services\ReferralService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;





class OrderController extends Controller
{
    protected $firebaseService;
    protected $creditService;
    protected $commissionService;
    protected $referralService;
    protected $experienceService;


    public function __construct(
        FirebaseNotificationService $firebaseService,
        CreditService $creditService,
        CommissionService $commissionService,
        ReferralService $referralService = null,
        ExperienceService $experienceService = null
    ) {
        $this->firebaseService = $firebaseService;
        $this->creditService = $creditService;
        $this->commissionService = $commissionService;
        $this->referralService = $referralService;
        $this->experienceService = $experienceService;
    }

    /**
     * Create a new order
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function orderNow(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'talent_id' => 'required|exists:users,id',
            'user_service_id' => 'required|exists:user_services,id',
            'pricing_option_type_id' => 'required|exists:pricing_option_types,id',
            'service_category_id' => 'required|exists:service_categories,id',
            'service_style_id' => 'required_if:service_category_id,1|exists:service_styles,id',
            'quantity' => 'required|integer|min:1',
            'remarks' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $customer = auth()->user();
            $talentId = $request->input('talent_id');
            $userServiceId = $request->input('user_service_id');
            $pricingOptionTypeId = $request->input('pricing_option_type_id');
            $serviceCategoryId = $request->input('service_category_id');
            $serviceStyleId = $request->input('service_style_id');

            $talent = User::find($talentId);
            if (!$talent) {
                return response()->json([
                    'success' => false,
                    'message' => 'Talent not found',
                ], 404);
            }

            $availabilityStatus = $talent->getAvailabilityStatus();
            if ($availabilityStatus !== 'Green') {
                return response()->json([
                    'success' => false,
                    'message' => 'Talent is not available at this time',
                ], 400);
            }

            $userService = UserService::where('id', $userServiceId)
                ->where('user_id', $talentId)
                ->where('is_active', true)
                ->first();

            if (!$userService) {
                return response()->json([
                    'success' => false,
                    'message' => 'Service not found or not active for this talent',
                ], 404);
            }

            $pricingOption = UserServicePricingOption::where('user_service_id', $userServiceId)
                ->where('pricing_option_type_id', $pricingOptionTypeId)
                ->where('is_active', true)
                ->first();

            if (!$pricingOption) {
                return response()->json([
                    'success' => false,
                    'message' => 'Pricing option not found or not active for this service',
                ], 404);
            }

            $pricingOptionType = $pricingOption->pricingOptionType;
            if (!$pricingOptionType) {
                return response()->json([
                    'success' => false,
                    'message' => 'Pricing option type not found',
                ], 404);
            }

            $userService = UserService::find($userServiceId);
            $quantity = $request->input('quantity', 1);
            $creditAmount = 0;

            if ($serviceCategoryId != ServiceCategory::OTHER_CATEGORY_ID) { // Not Talent category
                $serviceStyle = $userService->serviceStyles()
                    ->where('service_style_id', $serviceStyleId)
                    ->first();

                if (!$serviceStyle) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Service style not found or not active for this service',
                    ], 404);
                }

                $creditAmount = $serviceStyle->pivot->price * $quantity;
            } else {
                $creditAmount = $userService->price * $quantity;
            }

            if ($customer->credits_balance < $creditAmount) {
                return response()->json([
                    'success' => false,
                    'message' => 'Insufficient credit',
                ], 400);
            }

            $existingPendingOrder = Order::where('customer_id', $customer->id)
                ->where('talent_id', $talentId)
                ->where('user_service_id', $userServiceId)
                ->where('status', 'pending')
                ->exists();

            if ($existingPendingOrder) {
                return response()->json([
                    'success' => false,
                    'message' => 'You already have a pending order for this service with this talent',
                ], 400);
            }

            DB::beginTransaction();

            try {
                $this->creditService->deductCredits(
                    $customer,
                    $creditAmount,
                    "Credit hold for Order placement",
                    ['service_id' => $userServiceId]
                );

                $expiresAt = Carbon::now()->addSeconds(60);
                $order = Order::create([
                    'customer_id' => $customer->id,
                    'talent_id' => $talentId,
                    'user_service_id' => $userServiceId,
                    'service_category_id' => $serviceCategoryId,
                    'pricing_option_type_id' => $pricingOptionTypeId,
                    'service_style_id' => $serviceCategoryId != 2 ? $serviceStyleId : null,
                    'quantity' => $request->input('quantity', 1),
                    'status' => 'pending',
                    'remarks' => $request->input('remarks'),
                    'credit_amount' => $creditAmount,
                    'is_credit_held' => true,
                    'credit_held_at' => now(),
                    'expires_at' => $expiresAt,
                ]);

                Log::info('OrderController: Before dispatching OrderPlaced', [
                    'order_id' => $order->id,
                    'talent_id' => $order->talent_id,
                    'customer_id' => $order->customer_id,
                ]);

                try {
                    event(new OrderPlaced($order));
                    Log::info('OrderController: OrderPlaced event dispatched successfully', [
                        'order_id' => $order->id,
                    ]);
                } catch (\Exception $e) {
                    Log::error('OrderController: Failed to dispatch OrderPlaced', [
                        'order_id' => $order->id,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString(),
                    ]);
                }

                ProcessOrderTimeout::dispatch($order->id)->onConnection('rabbitmq')->delay($expiresAt);

                DB::commit();
            } catch (\Exception $e) {
                DB::rollBack();
                Log::error('Failed to hold credits for order', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Failed to hold credits for order',
                    'error' => app()->environment('production') ? 'Server error' : $e->getMessage(),
                ], 500);
            }

            $deviceTokens = DeviceToken::where('user_id', $talentId)->pluck('token')->toArray();
            if (count($deviceTokens) > 0) {
                $serviceName = $userService->service_name ?? 'your service';
                foreach ($deviceTokens as $token) {
                    $this->firebaseService->sendOrderNotification(
                        $token,
                        $order->id,
                        $customer->nickname,
                        $serviceName
                    );
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Order created successfully',
                'data' => [
                    'order_id' => $order->id,
                    'expires_at' => $expiresAt->toIso8601String(),
                ],
            ], 201);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to create order', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to create order',
                'error' => app()->environment('production') ? 'Server error' : $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Respond to an order (accept or reject)
     *
     * @param Request $request
     * @param int $orderId
     * @return \Illuminate\Http\JsonResponse
     */
    public function respondToOrder(Request $request, $orderId)
    {
        $validator = Validator::make($request->all(), [
            'action' => 'required|in:accept,reject',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $talent = auth()->user();

            $order = Order::where('id', $orderId)
                ->where('talent_id', $talent->id)
                ->where('status', 'pending')
                ->first();

            if (!$order) {
                return response()->json([
                    'success' => false,
                    'message' => 'Order not found or already processed',
                ], 404);
            }

            if ($order->hasExpired()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Order has expired and cannot be responded to',
                ], 400);
            }

            $action = $request->input('action');
            $responseTimeSeconds = Carbon::now()->diffInSeconds($order->created_at);

            DB::beginTransaction();

            $order->status = $action === 'accept' ? 'accepted' : 'rejected';
            $order->responded_at = Carbon::now();

            if ($action === 'reject' && $order->is_credit_held) {
                $customer = User::find($order->customer_id);

                try {
                    $this->creditService->addCredits(
                        $customer,
                        $order->credit_amount,
                        "Credit refund for rejected Order #{$order->id}",
                        ['order_id' => $order->id]
                    );

                    $order->is_credit_held = false;
                    $order->credit_released_at = now();
                } catch (\Exception $e) {
                    DB::rollBack();
                    Log::error('Failed to refund credits for rejected order', [
                        'order_id' => $order->id,
                        'error' => $e->getMessage(),
                    ]);

                    return response()->json([
                        'success' => false,
                        'message' => 'Failed to refund credits for rejected order',
                    ], 500);
                }
            }

            if ($action === 'accept') {
                $talent->availabilityOverride()->updateOrCreate(
                    ['user_id' => $talent->id],
                    [
                        'override_type' => 'engaging',
                        'start_date' => now(),
                        'end_date' => now()->addDays(1), // Default 24-hour override
                        'remarks' => 'Automatically set while handling orders'
                    ]
                );
            }

            $order->save();

            OrderAcceptanceLog::create([
                'talent_id' => $talent->id,
                'order_id' => $order->id,
                'action' => $action === 'accept' ? 'accepted' : 'rejected',
                'response_time_seconds' => $responseTimeSeconds,
            ]);

            $talent->total_orders_received += 1;
            if ($action === 'accept') {
                $talent->total_orders_accepted += 1;
            }
            $talent->acceptance_rate = $talent->total_orders_accepted / $talent->total_orders_received;
            $talent->save();

            DB::commit();

            $customer = User::find($order->customer_id);
            if ($customer) {
                $deviceTokens = DeviceToken::where('user_id', $customer->id)->pluck('token')->toArray();
                if (count($deviceTokens) > 0) {
                    $status = $action === 'accept' ? 'accepted' : 'rejected';
                    foreach ($deviceTokens as $token) {
                        $this->firebaseService->sendOrderResponseNotification(
                            $token,
                            $order->id,
                            $talent->nickname,
                            $status
                        );
                    }
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Order ' . ($action === 'accept' ? 'accepted' : 'rejected') . ' successfully',
                'data' => [
                    'order_id' => $order->id,
                    'status' => $order->status,
                    'responded_at' => $order->responded_at->toIso8601String(),
                ],
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to respond to order', [
                'order_id' => $orderId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to respond to order',
                'error' => app()->environment('production') ? 'Server error' : $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get order details
     *
     * @param int $orderId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getOrder($orderId)
    {
        try {
            $user = auth()->user();

            $order = Order::with(['customer', 'talent', 'userService', 'pricingOptionType', 'serviceCategory', 'serviceStyle'])
                ->where('id', $orderId)
                ->where(function ($query) use ($user) {
                    $query->where('customer_id', $user->id)
                        ->orWhere('talent_id', $user->id);
                })
                ->first();

            if (!$order) {
                return response()->json([
                    'success' => false,
                    'message' => 'Order not found',
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $order,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get order details', [
                'order_id' => $orderId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get order details',
                'error' => app()->environment('production') ? 'Server error' : $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get user's orders
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUserOrders(Request $request)
    {
        try {
            $user = auth()->user();
            $type = $request->input('type', 'all'); // all, placed, received
            $status = $request->input('status'); // optional filter by status
            $perPage = $request->input('per_page', 15);

            $query = Order::with(['customer', 'talent', 'userService', 'pricingOptionType', 'serviceCategory', 'serviceStyle']);

            if ($type === 'placed') {
                $query->where('customer_id', $user->id);
            } elseif ($type === 'received') {
                $query->where('talent_id', $user->id);
            } else {
                $query->where(function ($q) use ($user) {
                    $q->where('customer_id', $user->id)
                        ->orWhere('talent_id', $user->id);
                });
            }

            if ($status) {
                $query->where('status', $status);
            }

            $query->orderBy('created_at', 'desc');

            $orders = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $orders,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get user orders', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get user orders',
                'error' => app()->environment('production') ? 'Server error' : $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Complete an order
     *
     * @param Request $request
     * @param int $orderId
     * @return \Illuminate\Http\JsonResponse
     */
    public function completeOrder(Request $request, $orderId)
    {
        try {
            $user = auth()->user();

            $order = Order::where('id', $orderId)
                ->where('customer_id', $user->id)
                ->where('status', 'accepted')
                ->first();

            if (!$order) {
                return response()->json([
                    'success' => false,
                    'message' => 'Order not found or cannot be completed',
                ], 404);
            }

            DB::beginTransaction();

            $order->status = 'completed';
            $order->save();

            if ($this->referralService) {
                try {
                    $customer = User::find($order->customer_id);
                    $talent = User::find($order->talent_id);

                    if ($customer) {
                        $this->referralService->processReferralOrderCompletion($customer);
                    }

                    if ($talent) {
                        $this->referralService->processReferralOrderCompletion($talent);
                    }
                } catch (\Exception $e) {
                    Log::error('Error processing referral order completion points', [
                        'order_id' => $order->id,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                }
            } else {
                Log::warning('ReferralService not available for order completion processing', [
                    'order_id' => $order->id
                ]);
            }

            if ($this->experienceService) {
                try {
                    $serviceType = $order->userService->serviceType;
                    $experiencePoints = $serviceType->experience ?? 0;

                    if ($experiencePoints > 0) {
                        $customer = User::find($order->customer_id);
                        $talent = User::find($order->talent_id);

                        if ($customer) {
                            $this->experienceService->awardExperience(
                                $customer,
                                $experiencePoints,
                                'Completed order #' . $order->id,
                                [
                                    'order_id' => $order->id,
                                    'service_type_id' => $serviceType->id,
                                    'service_type_name' => $serviceType->name,
                                    'role' => 'customer'
                                ]
                            );
                        }

                        if ($talent) {
                            $this->experienceService->awardExperience(
                                $talent,
                                $experiencePoints,
                                'Completed order #' . $order->id,
                                [
                                    'order_id' => $order->id,
                                    'service_type_id' => $serviceType->id,
                                    'service_type_name' => $serviceType->name,
                                    'role' => 'talent'
                                ]
                            );
                        }
                    }
                } catch (\Exception $e) {
                    Log::error('Error awarding experience for order completion', [
                        'order_id' => $order->id,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                }
            } else {
                Log::warning('ExperienceService not available for order completion processing', [
                    'order_id' => $order->id
                ]);
            }

            $hasActiveOrders = Order::where('talent_id', $order->talent_id)
                ->where('id', '!=', $order->id)
                ->where('status', 'accepted')
                ->exists();

            if (!$hasActiveOrders) {
                $talent = User::find($order->talent_id);
                if ($talent) {
                    $talent->availabilityOverride()
                        ->where('override_type', 'engaging')
                        ->delete();
                }
            }

            if ($order->is_credit_held) {
                $talent = User::find($order->talent_id);

                try {
                    $commissionResult = $this->commissionService->applyCommission(
                        $talent,
                        $order->id,
                        $order->credit_amount,
                        'order now'
                    );

                    $order->credit_released_at = now();
                    $order->save();
                } catch (\Exception $e) {
                    DB::rollBack();
                    Log::error('Failed to transfer credits to talent', [
                        'order_id' => $order->id,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString(),
                    ]);

                    return response()->json([
                        'success' => false,
                        'message' => 'Failed to complete order payment',
                        'error' => app()->environment('production') ? 'Server error' : $e->getMessage(),
                    ], 500);
                }
            }

            DB::commit();

            $deviceTokens = DeviceToken::where('user_id', $order->talent_id)->pluck('token')->toArray();
            if (count($deviceTokens) > 0) {
                foreach ($deviceTokens as $token) {
                    $this->firebaseService->sendToDevice(
                        $token,
                        'Order Completed',
                        "Order #{$order->id} has been marked as completed",
                        [
                            'order_id' => $order->id,
                            'notification_type' => 'order_completed',
                        ]
                    );
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Order completed successfully',
                'data' => [
                    'order_id' => $order->id,
                    'status' => $order->status,
                ],
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to complete order', [
                'order_id' => $orderId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to complete order',
                'error' => app()->environment('production') ? 'Server error' : $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Create a review for an order
     *
     * @param Request $request
     * @param int $orderId
     * @return \Illuminate\Http\JsonResponse
     */
    public function createReview(Request $request, $orderId)
    {
        $validator = Validator::make($request->all(), [
            'rating' => 'required|integer|min:1|max:5',
            'review_text' => 'nullable|string|max:1000',
            'is_anonymous' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $user = auth()->user();

            $order = Order::where('id', $orderId)
                ->where(function ($query) use ($user) {
                    $query->where('customer_id', $user->id)
                        ->orWhere('talent_id', $user->id);
                })
                ->where('status', 'completed')
                ->first();

            if (!$order) {
                return response()->json([
                    'success' => false,
                    'message' => 'Order not found or not completed',
                ], 404);
            }

            $isCustomer = $order->customer_id === $user->id;
            $reviewerId = $user->id;
            $revieweeId = $isCustomer ? $order->talent_id : $order->customer_id;

            $existingReview = OrderReview::where('reviewable_type', 'App\\Models\\Order')
                ->where('reviewable_id', $order->id)
                ->where('reviewer_id', $reviewerId)
                ->first();

            if ($existingReview) {
                return response()->json([
                    'success' => false,
                    'message' => 'You have already reviewed this order',
                ], 400);
            }

            $isAnonymous = $isCustomer && $request->input('is_anonymous', false);

            $review = OrderReview::create([
                'reviewable_type' => 'App\\Models\\Order',
                'reviewable_id' => $order->id,
                'reviewer_id' => $reviewerId,
                'reviewee_id' => $revieweeId,
                'rating' => $request->input('rating'),
                'review_text' => $request->input('review_text'),
                'is_anonymous' => $isAnonymous,
                'is_hidden' => false,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Review submitted successfully',
                'data' => $review,
            ], 201);
        } catch (\Exception $e) {
            Log::error('Failed to create review', [
                'order_id' => $orderId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to create review',
                'error' => app()->environment('production') ? 'Server error' : $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get reviews for an order
     *
     * @param int $orderId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getReviews($orderId)
    {
        try {
            $user = auth()->user();

            $order = Order::where('id', $orderId)
                ->where(function ($query) use ($user) {
                    $query->where('customer_id', $user->id)
                        ->orWhere('talent_id', $user->id);
                })
                ->first();

            if (!$order) {
                return response()->json([
                    'success' => false,
                    'message' => 'Order not found',
                ], 404);
            }

            $reviews = Review::where('order_id', $order->id)
                ->with(['reviewer'])
                ->get()
                ->map(function ($review) use ($user) {
                    if ($review->is_anonymous && $review->reviewer_id !== $user->id) {
                        $review->reviewer->nickname = 'Anonymous';
                        $review->reviewer->profile_picture = null;
                    }
                    return $review;
                });

            return response()->json([
                'success' => true,
                'data' => $reviews,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get reviews', [
                'order_id' => $orderId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get reviews',
                'error' => app()->environment('production') ? 'Server error' : $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Check if accepting an order would cause overtime for a talent.
     *
     * @param Request $request
     * @param int $orderId
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkOrderOvertime(Request $request, $orderId)
    {
        try {
            $user = auth()->user();

            $order = Order::where('id', $orderId)
                ->where('talent_id', $user->id)
                ->where('status', 'pending')
                ->first();

            if (!$order) {
                return response()->json([
                    'success' => false,
                    'message' => 'Order not found',
                ], 404);
            }

            $orderDuration = $order->estimatedDuration();

            if ($orderDuration === null) {
                return response()->json([
                    'success' => true,
                    'overtime' => false,
                    'message' => 'No duration associated with this service',
                ]);
            }

            $today = now()->startOfDay();
            $tomorrow = now()->addDay()->startOfDay();

            $activeOrdersDuration = Order::where('talent_id', $user->id)
                ->where('status', 'accepted')
                ->where('created_at', '>=', $today)
                ->where('created_at', '<', $tomorrow)
                ->get()
                ->sum(function ($order) {
                    return $order->estimatedDuration() ?? 0;
                });

            $dailyLimitMinutes = 8 * 60;

            $isOvertime = ($activeOrdersDuration + $orderDuration) > $dailyLimitMinutes;

            return response()->json([
                'success' => true,
                'overtime' => $isOvertime,
                'current_duration' => $activeOrdersDuration,
                'order_duration' => $orderDuration,
                'daily_limit' => $dailyLimitMinutes,
                'message' => $isOvertime ? 'This order would exceed your daily availability time' : 'Within daily availability limit',
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to check order overtime', [
                'order_id' => $orderId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to check overtime',
                'error' => app()->environment('production') ? 'Server error' : $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get estimated wait time for a talent.
     *
     * @param Request $request
     * @param int $talentId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getEstimatedWaitTime(Request $request, $talentId)
    {
        try {
            $talent = User::findOrFail($talentId);

            $activeOrders = Order::where('talent_id', $talentId)
                ->where('status', 'accepted')
                ->orderBy('responded_at')
                ->get();

            if ($activeOrders->isEmpty()) {
                return response()->json([
                    'success' => true,
                    'wait_time_minutes' => 0,
                    'estimated_completion_time' => now()->toIso8601String(),
                    'active_orders_count' => 0,
                    'active_orders' => [],
                    'message' => 'No waiting time, talent is available now',
                ]);
            }

            $totalDurationMinutes = 0;
            $estimatedCompletionTime = now();
            $orderDetails = [];

            foreach ($activeOrders as $order) {
                $orderDuration = $order->estimatedDuration() ?? 0;
                $totalDurationMinutes += $orderDuration;

                $estimatedCompletionTime = $estimatedCompletionTime->addMinutes($orderDuration);

                $orderDetails[] = [
                    'order_id' => $order->id,
                    'duration_minutes' => $orderDuration,
                    'responded_at' => $order->responded_at ? $order->responded_at->toIso8601String() : null,
                ];
            }

            return response()->json([
                'success' => true,
                'wait_time_minutes' => $totalDurationMinutes,
                'estimated_completion_time' => $estimatedCompletionTime->toIso8601String(),
                'active_orders_count' => $activeOrders->count(),
                'active_orders' => $orderDetails,
                'message' => $totalDurationMinutes > 0 ?
                    "Estimated wait time: {$totalDurationMinutes} minutes" :
                    'No waiting time, talent is available now',
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get estimated wait time', [
                'talent_id' => $talentId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get estimated wait time',
                'error' => app()->environment('production') ? 'Server error' : $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Cancel an order
     *
     * @param Request $request
     * @param int $orderId
     * @return \Illuminate\Http\JsonResponse
     */
    public function cancelOrder(Request $request, $orderId)
    {
        try {
            $request->validate([
                'cancellation_reason' => 'required|string|max:500',
            ]);

            $customer = auth()->user();

            $order = Order::where('id', $orderId)
                ->where('customer_id', $customer->id)
                ->first();

            if (!$order) {
                return response()->json([
                    'success' => false,
                    'message' => 'Order not found or you are not authorized to cancel it',
                ], 404);
            }

            if (!$order->isCancellable()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Order cannot be cancelled in its current state',
                ], 400);
            }

            DB::beginTransaction();

            $order->status = 'cancelled';
            $order->cancellation_reason = $request->cancellation_reason;
            $order->save();

            if ($order->is_credit_held) {
                try {
                    $this->creditService->addCredits(
                        $customer,
                        $order->credit_amount,
                        "Credit refund for cancelled Order #{$order->id}",
                        ['order_id' => $order->id]
                    );

                    $order->is_credit_held = false;
                    $order->credit_released_at = now();
                    $order->save();
                } catch (\Exception $e) {
                    DB::rollBack();
                    Log::error('Failed to refund credits for cancelled order', [
                        'order_id' => $order->id,
                        'error' => $e->getMessage(),
                    ]);

                    return response()->json([
                        'success' => false,
                        'message' => 'Failed to refund credits for cancelled order',
                    ], 500);
                }
            }

            DB::commit();

            $deviceTokens = DeviceToken::where('user_id', $order->talent_id)->pluck('token')->toArray();
            if (count($deviceTokens) > 0) {
                foreach ($deviceTokens as $token) {
                    $this->firebaseService->sendToDevice(
                        $token,
                        'Order Cancelled',
                        "Order #{$order->id} has been cancelled by the customer",
                        [
                            'order_id' => $order->id,
                            'notification_type' => 'order_cancelled',
                        ]
                    );
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Order cancelled successfully',
                'data' => [
                    'order_id' => $order->id,
                    'status' => $order->status,
                ],
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to cancel order', [
                'order_id' => $orderId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to cancel order',
                'error' => app()->environment('production') ? 'Server error' : $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Create a dispute for an order
     *
     * @param Request $request
     * @param int $orderId
     * @return \Illuminate\Http\JsonResponse
     */
    public function disputeOrder(Request $request, $orderId)
    {
        $validator = Validator::make($request->all(), [
            'dispute_type_id' => 'required|integer|exists:dispute_types,id',
            'description' => ['required', 'string', 'max:1000', new CleanContent],
            'images.*' => 'nullable|file|mimes:jpeg,png,jpg,heic,heif|max:10240', // 10MB max per image
            'video' => 'nullable|file|mimes:mp4,mov,avi,flv|max:20480', // 20MB max for video
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        if ($request->hasFile('images') && count($request->file('images')) > 3) {
            return response()->json([
                'success' => false,
                'message' => 'Maximum 3 images allowed',
            ], 422);
        }

        try {
            $user = $request->user();

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthenticated',
                ], 401);
            }

            $order = Order::where('id', $orderId)
                ->where(function ($query) use ($user) {
                    $query->where('customer_id', $user->id)
                        ->orWhere('talent_id', $user->id);
                })
                ->first();

            if (!$order) {
                return response()->json([
                    'success' => false,
                    'message' => 'Order not found or you are not related to this order',
                ], 404);
            }

            if (!$order->isAccepted()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Only accepted orders can have disputes',
                ], 400);
            }

            if ($order->isCompleted()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Completed orders cannot have disputes',
                ], 400);
            }

            $requestorType = $user->id === $order->customer_id ? 'client' : 'talent';

            $files = [];
            if ($request->hasFile('images')) {
                foreach ($request->file('images') as $image) {
                    $files[] = $image;
                }
            }

            if ($request->hasFile('video')) {
                $videoFile = $request->file('video');
                if ($videoFile->isValid()) {
                    $files[] = $videoFile;
                    Log::info('Video file added to upload queue', [
                        'original_name' => $videoFile->getClientOriginalName(),
                        'size' => $videoFile->getSize(),
                        'mime_type' => $videoFile->getMimeType()
                    ]);
                } else {
                    Log::error('Invalid video file uploaded', [
                        'error' => $videoFile->getError(),
                        'error_message' => $videoFile->getErrorMessage(),
                        'original_name' => $videoFile->getClientOriginalName(),
                        'size' => $videoFile->getSize()
                    ]);
                    return response()->json([
                        'success' => false,
                        'message' => 'Video upload failed',
                        'errors' => [
                            'video' => ['The video file is invalid or corrupted.']
                        ]
                    ], 422);
                }
            }

            $disputeService = app(DisputeService::class);
            $dispute = $disputeService->createOrderDispute(
                $user,
                $order,
                $validator->validated(),
                $files,
                $requestorType
            );

            return response()->json([
                'success' => true,
                'message' => 'Dispute created successfully',
                'data' => $dispute->load(['disputeType', 'media']),
            ], 201);
        } catch (\Exception $e) {
            Log::error('Failed to create dispute for order', [
                'order_id' => $orderId,
                'error' => $e->getMessage(),
                'error_code' => $e->getCode(),
                'user_id' => isset($user) ? $user->id : 'unknown',
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'error' => app()->environment('production') ? 'Server error' : $e->getMessage(),
            ], $e->getCode() >= 400 && $e->getCode() < 500 ? $e->getCode() : 500);
        }
    }
}
