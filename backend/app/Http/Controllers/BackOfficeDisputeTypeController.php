<?php

namespace App\Http\Controllers;

use App\Models\DisputeType;
use App\Models\DisputeTypeTranslation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class BackOfficeDisputeTypeController extends Controller
{
    /**
     * Get all dispute types
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $perPage = $request->input('per_page', 15);
        $disputeTypes = DisputeType::withCount('disputes')
            ->orderBy('display_order')
            ->paginate($perPage);
            
        return response()->json([
            'success' => true,
            'data' => $disputeTypes,
        ]);
    }
    
    /**
     * Get a specific dispute type
     * 
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $disputeType = DisputeType::with('translations')
            ->withCount('disputes')
            ->findOrFail($id);
            
        return response()->json([
            'success' => true,
            'data' => $disputeType,
        ]);
    }
    
    /**
     * Create a new dispute type
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'is_active' => 'boolean',
            'display_order' => 'integer',
            'translations' => 'array',
            'translations.*.locale' => 'required|string|size:5',
            'translations.*.name' => 'required|string|max:255',
            'translations.*.description' => 'nullable|string',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
                'error_code' => 422,
            ], 422);
        }
        
        try {
            $data = $validator->validated();
            
            return DB::transaction(function () use ($data) {
                $disputeType = DisputeType::create([
                    'name' => $data['name'],
                    'description' => $data['description'] ?? null,
                    'is_active' => $data['is_active'] ?? true,
                    'display_order' => $data['display_order'] ?? 0,
                ]);
                
                if (isset($data['translations']) && is_array($data['translations'])) {
                    foreach ($data['translations'] as $translation) {
                        DisputeTypeTranslation::create([
                            'dispute_type_id' => $disputeType->id,
                            'locale' => $translation['locale'],
                            'name' => $translation['name'],
                            'description' => $translation['description'] ?? null,
                        ]);
                    }
                }
                
                return response()->json([
                    'success' => true,
                    'message' => 'Dispute type created successfully',
                    'data' => $disputeType->load('translations'),
                ], 201);
            });
        } catch (\Exception $e) {
            Log::error('Failed to create dispute type', [
                'error' => $e->getMessage(),
                'error_code' => $e->getCode(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to create dispute type: ' . $e->getMessage(),
                'error_code' => 500,
            ], 500);
        }
    }
    
    /**
     * Update a dispute type
     * 
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'string|max:255',
            'description' => 'nullable|string',
            'is_active' => 'boolean',
            'display_order' => 'integer',
            'translations' => 'array',
            'translations.*.id' => 'nullable|integer|exists:dispute_type_translations,id',
            'translations.*.locale' => 'required|string|size:5',
            'translations.*.name' => 'required|string|max:255',
            'translations.*.description' => 'nullable|string',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
                'error_code' => 422,
            ], 422);
        }
        
        try {
            $disputeType = DisputeType::findOrFail($id);
            $data = $validator->validated();
            
            return DB::transaction(function () use ($disputeType, $data) {
                if (isset($data['name'])) {
                    $disputeType->name = $data['name'];
                }
                
                if (array_key_exists('description', $data)) {
                    $disputeType->description = $data['description'];
                }
                
                if (isset($data['is_active'])) {
                    $disputeType->is_active = $data['is_active'];
                }
                
                if (isset($data['display_order'])) {
                    $disputeType->display_order = $data['display_order'];
                }
                
                $disputeType->save();
                
                if (isset($data['translations']) && is_array($data['translations'])) {
                    foreach ($data['translations'] as $translationData) {
                        if (isset($translationData['id'])) {
                            $translation = DisputeTypeTranslation::find($translationData['id']);
                            if ($translation && $translation->dispute_type_id == $disputeType->id) {
                                $translation->update([
                                    'locale' => $translationData['locale'],
                                    'name' => $translationData['name'],
                                    'description' => $translationData['description'] ?? null,
                                ]);
                            }
                        } else {
                            $translation = DisputeTypeTranslation::where('dispute_type_id', $disputeType->id)
                                ->where('locale', $translationData['locale'])
                                ->first();
                                
                            if ($translation) {
                                $translation->update([
                                    'name' => $translationData['name'],
                                    'description' => $translationData['description'] ?? null,
                                ]);
                            } else {
                                DisputeTypeTranslation::create([
                                    'dispute_type_id' => $disputeType->id,
                                    'locale' => $translationData['locale'],
                                    'name' => $translationData['name'],
                                    'description' => $translationData['description'] ?? null,
                                ]);
                            }
                        }
                    }
                }
                
                return response()->json([
                    'success' => true,
                    'message' => 'Dispute type updated successfully',
                    'data' => $disputeType->load('translations'),
                ]);
            });
        } catch (\Exception $e) {
            Log::error('Failed to update dispute type', [
                'error' => $e->getMessage(),
                'error_code' => $e->getCode(),
                'dispute_type_id' => $id,
                'trace' => $e->getTraceAsString(),
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to update dispute type: ' . $e->getMessage(),
                'error_code' => $e->getCode() >= 400 && $e->getCode() < 500 ? $e->getCode() : 500,
            ], $e->getCode() >= 400 && $e->getCode() < 500 ? $e->getCode() : 500);
        }
    }
    
    /**
     * Delete a dispute type
     * 
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        try {
            $disputeType = DisputeType::withCount('disputes')->findOrFail($id);
            
            if ($disputeType->disputes_count > 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot delete dispute type that is in use',
                    'error_code' => 400,
                ], 400);
            }
            
            DB::transaction(function () use ($disputeType) {
                $disputeType->translations()->delete();
                $disputeType->delete();
            });
            
            return response()->json([
                'success' => true,
                'message' => 'Dispute type deleted successfully',
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to delete dispute type', [
                'error' => $e->getMessage(),
                'error_code' => $e->getCode(),
                'dispute_type_id' => $id,
                'trace' => $e->getTraceAsString(),
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete dispute type: ' . $e->getMessage(),
                'error_code' => $e->getCode() >= 400 && $e->getCode() < 500 ? $e->getCode() : 500,
            ], $e->getCode() >= 400 && $e->getCode() < 500 ? $e->getCode() : 500);
        }
    }
    
    /**
     * Toggle dispute type active status
     * 
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function toggleActive($id)
    {
        try {
            $disputeType = DisputeType::findOrFail($id);
            $disputeType->is_active = !$disputeType->is_active;
            $disputeType->save();
            
            return response()->json([
                'success' => true,
                'message' => 'Dispute type status updated successfully',
                'data' => $disputeType,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to toggle dispute type status', [
                'error' => $e->getMessage(),
                'error_code' => $e->getCode(),
                'dispute_type_id' => $id,
                'trace' => $e->getTraceAsString(),
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to toggle dispute type status: ' . $e->getMessage(),
                'error_code' => $e->getCode() >= 400 && $e->getCode() < 500 ? $e->getCode() : 500,
            ], $e->getCode() >= 400 && $e->getCode() < 500 ? $e->getCode() : 500);
        }
    }
}
