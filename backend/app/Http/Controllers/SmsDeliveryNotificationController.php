<?php

namespace App\Http\Controllers;

use App\Models\Otp;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class SmsDeliveryNotificationController extends Controller
{
    /**
     * Handle the delivery notification from Bulk360 SMS API
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function handleDeliveryNotification(Request $request)
    {
        // Log the delivery notification
        Log::info('SMS delivery notification received', [
            'status' => $request->input('status'),
            'error_code' => $request->input('error-code'),
            'msisdn' => $request->input('msisdn'),
            'msgid' => $request->input('msgid'),
        ]);

        // Find the OTP record by message ID
        $messageId = $request->input('msgid');
        $otp = Otp::where('message_id', $messageId)->first();

        if (!$otp) {
            Log::warning('OTP record not found for message ID', [
                'message_id' => $messageId,
            ]);
            return response()->json(['message' => 'OTP record not found'], 404);
        }

        // Update the OTP record with delivery status and error code
        $otp->update([
            'delivery_status' => $request->input('status'),
            'delivery_error_code' => $request->input('error-code'),
        ]);

        Log::info('OTP record updated with delivery status', [
            'mobile_number' => $otp->mobile_number,
            'message_id' => $messageId,
            'delivery_status' => $request->input('status'),
            'delivery_error_code' => $request->input('error-code'),
        ]);

        return response()->json(['message' => 'Delivery notification processed successfully']);
    }
}
