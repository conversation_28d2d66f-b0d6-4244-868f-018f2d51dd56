<?php

namespace App\Http\Controllers;

use App\Services\GooglePlacesService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class PlacesController extends Controller
{
    /**
     * @var GooglePlacesService
     */
    protected $placesService;

    /**
     * Create a new controller instance.
     */
    public function __construct(GooglePlacesService $placesService)
    {
        $this->placesService = $placesService;
    }

    /**
     * Search for places based on a query.
     */
    public function search(Request $request)
    {
        $request->validate([
            'query' => 'required|string|min:2|max:100',
        ]);

        // Generate a session token for billing purposes
        $sessionToken = $this->placesService->generateSessionToken();
        
        // Store the session token in the session for later use
        session(['places_session_token' => $sessionToken]);
        
        // Get place suggestions
        $suggestions = $this->placesService->getPlaceSuggestions($request->query('query'), $sessionToken);
        
        return response()->json($suggestions);
    }

    /**
     * Get details for a specific place.
     */
    public function details(Request $request)
    {
        $request->validate([
            'place_id' => 'required|string',
        ]);

        // Get the session token from the session
        $sessionToken = session('places_session_token');
        
        // Get place details
        $details = $this->placesService->getPlaceDetails($request->query('place_id'), $sessionToken);
        
        if (!$details) {
            return response()->json(['message' => 'Place not found'], 404);
        }
        
        return response()->json($details);
    }
}
