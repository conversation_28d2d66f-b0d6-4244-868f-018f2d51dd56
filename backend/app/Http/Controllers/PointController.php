<?php

namespace App\Http\Controllers;

use App\Services\PointService;
use Illuminate\Http\Request;

class PointController extends Controller
{
    protected $pointService;

    public function __construct(PointService $pointService)
    {
        $this->pointService = $pointService;
    }

    /**
     * Get point transaction history for the authenticated user.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTransactionHistory(Request $request)
    {
        $transactions = $request->user()->pointTransactions()
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return response()->json([
            'transactions' => $transactions
        ]);
    }
}
