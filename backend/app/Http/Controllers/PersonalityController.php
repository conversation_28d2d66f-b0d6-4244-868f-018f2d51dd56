<?php

namespace App\Http\Controllers;

use App\Models\Personality;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;

class PersonalityController extends Controller
{
    public function index()
    {
        $locale = App::getLocale();
        
        return Personality::with(['translations' => function ($query) use ($locale) {
            $query->where('locale', $locale);
        }])
        ->where('is_active', true)
        ->orderBy('display_order')
        ->get()
        ->map(function ($personality) {
            $translation = $personality->translations->first();
            return [
                'id' => $personality->id,
                'name' => $translation?->name,
                'description' => $translation?->description,
            ];
        });
    }

    public function updateUserPersonalities(Request $request)
    {
        $validated = $request->validate([
            'personality_ids' => 'nullable|array',
            'personality_ids.*' => 'exists:personalities,id',
        ]);

        $request->user()->personalities()->sync($validated['personality_ids'] ?? []);

        return response()->json([
            'message' => 'Personalities updated successfully',
        ]);
    }

    public function getUserPersonalities(Request $request)
    {
        $locale = App::getLocale();
        
        $personalities = $request->user()
            ->personalities()
            ->with(['translations' => function ($query) use ($locale) {
                $query->where('locale', $locale);
            }])
            ->get()
            ->map(function ($personality) {
                $translation = $personality->translations->first();
                return [
                    'id' => $personality->id,
                    'name' => $translation?->name,
                    'description' => $translation?->description,
                ];
            });

        return response()->json([
            'personalities' => $personalities
        ]);
    }
}
