<?php

namespace App\Http\Controllers;

use App\Models\ServiceCategory;
use App\Models\ServiceType;
use App\Models\ServiceStyle;
use App\Models\PricingOptionType;
use App\Models\ServiceLink;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;

class ServiceConfigurationController extends Controller
{
    /**
     * Get all service categories.
     *
     * @return \Illuminate\Http\Response
     */
    public function getServiceCategories()
    {
        $categories = ServiceCategory::where('is_active', true)
            ->orderBy('display_order')
            ->get();
        
        return response()->json($categories);
    }

    /**
     * Get all service types.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function getServiceTypes(Request $request)
    {
        $query = ServiceType::where('is_active', true);
        
        if ($request->has('service_category_id')) {
            $query->where('service_category_id', $request->input('service_category_id'));
        }
        
        $types = $query->orderBy('display_order')->get();
        
        return response()->json($types);
    }

    /**
     * Get all service styles.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function getServiceStyles(Request $request)
    {
        $query = ServiceStyle::where('is_active', true);
        
        if ($request->has('service_category_id')) {
            $query->where('service_category_id', $request->input('service_category_id'));
        }
        
        $styles = $query->orderBy('display_order')->get();
        
        // Filter by user level if needed
        if ($request->user()) {
            $userLevelId = $request->user()->level_id ?? 0;
            $serviceTypeId = $request->input('service_type_id');
            
            $styles = $styles->filter(function ($style) use ($userLevelId, $request, $serviceTypeId) {
                // Check if user meets minimum level requirement
                if ($userLevelId >= $style->min_level_id) {
                    return true;
                }
                
                // Check if user has bypass for this style and type
                if ($style->can_bypass) {
                    $query = $style->bypassUsers()->where('user_id', $request->user()->id);
                    if ($serviceTypeId) {
                        $query->where(function($q) use ($serviceTypeId) {
                            $q->where('service_type_id', $serviceTypeId)
                              ->orWhereNull('service_type_id');
                        });
                    }
                    if ($query->exists()) {
                        return true;
                    }
                }
                
                return false;
            });
        }
        
        return response()->json($styles->values());
    }


    /**
     * Get all pricing option types.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function getPricingOptionTypes(Request $request)
    {
        $query = PricingOptionType::where('is_active', true);
        
        $types = $query->orderBy('display_order')->get();
        
        return response()->json($types);
    }


    /**
     * Get all pricing options for a service style.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function getServiceStylePricingOptions($id)
    {
        return response()->json([]);
    }
    
    /**
     * Get service links with optional filtering.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function getServiceLinks(Request $request)
    {
        $query = ServiceLink::query();
        
        if ($request->has('service_category_id')) {
            $query->where('service_category_id', $request->input('service_category_id'));
        }
        
        if ($request->has('service_type_id')) {
            $query->where('service_type_id', $request->input('service_type_id'));
        }
        
        if ($request->has('service_style_id')) {
            $query->where('service_style_id', $request->input('service_style_id'));
        }
        
        $links = $query->with([
            'serviceCategory', 
            'serviceType', 
            'serviceStyle'
        ])->get();
        
        return response()->json($links);
    }
}
