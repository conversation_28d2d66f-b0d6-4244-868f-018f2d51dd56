<?php

namespace App\Http\Controllers;

use App\Models\UserBankAccount;
use App\Models\UserGift;
use App\Models\CurrencyConversionRate;
use App\Services\WithdrawalService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class WithdrawalController extends Controller
{
    protected $withdrawalService;
    
    public function __construct(WithdrawalService $withdrawalService)
    {
        $this->withdrawalService = $withdrawalService;
    }
    
    /**
     * Request withdrawal of platform credits.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function withdrawCredits(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'payment_mode_id' => 'required|exists:payment_modes,id',
            'amount' => 'required|integer|min:1',
            'bank_account_id' => 'required|exists:user_bank_accounts,id',
            'fiat_currency' => 'required|string|size:3',
            'payment_mode_id' => 'required|exists:payment_modes,id',
            'recipient_reference' => 'nullable|string|max:255',
            'other_payment_details' => 'nullable|string',
            'id_validation' => 'nullable|boolean',
            'id_validation_type' => [
                'nullable',
                'integer',
                'exists:validation_types,id',
                function ($attribute, $value, $fail) use ($request) {
                    if ($request->input('id_validation') === true && empty($value)) {
                        $fail('The id_validation_type field is required when id_validation is true.');
                    }
                },
            ],
            'id_validation_value' => [
                'nullable',
                'numeric',
                function ($attribute, $value, $fail) use ($request) {
                    if ($request->input('id_validation') === true && empty($value)) {
                        $fail('The id_validation_value field is required when id_validation is true.');
                    }
                },
            ],
            'transaction_type' => 'nullable|string|max:255',
            'purpose_of_transfer' => 'nullable|string|max:255',
            'email' => 'nullable|email|max:255',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }
        
        try {
            $bankAccount = UserBankAccount::where('id', $request->input('bank_account_id'))
                ->where('user_id', $request->user()->id)
                ->firstOrFail();
                
            $transaction = $this->withdrawalService->requestCreditWithdrawal(
                $request->user(),
                $bankAccount,
                $request->input('amount'),
                $request->input('fiat_currency'),
                [
                    'payment_mode_id' => $request->input('payment_mode_id'),
                    'recipient_reference' => $request->input('recipient_reference'),
                    'other_payment_details' => $request->input('other_payment_details'),
                    'id_validation' => $request->input('id_validation'),
                    'id_validation_type' => $request->input('id_validation_type'),
                    'id_validation_value' => $request->input('id_validation_value'),
                    'transaction_type' => $request->input('transaction_type'),
                    'purpose_of_transfer' => $request->input('purpose_of_transfer'),
                    'email' => $request->input('email') ?? $request->user()->email,
                ]
            );
            
            return response()->json([
                'message' => 'Credit withdrawal request submitted successfully',
                'transaction' => $transaction->load('bankAccount'),
                'credits_balance' => $request->user()->credits_balance
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => $e->getMessage()
            ], 400);
        }
    }
    
    /**
     * Exchange gift item for platform credits.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function exchangeGiftForCredits(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_gift_id' => 'required|exists:user_gifts,id',
            'quantity' => 'required|integer|min:1',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }
        
        try {
            $userGift = UserGift::where('id', $request->input('user_gift_id'))
                ->where('user_id', $request->user()->id)
                ->firstOrFail();
                
            $transaction = $this->withdrawalService->exchangeGiftForCredits(
                $request->user(),
                $userGift,
                $request->input('quantity')
            );
            
            return response()->json([
                'message' => 'Gift exchanged for credits successfully',
                'transaction' => $transaction,
                'credits_balance' => $request->user()->credits_balance
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => $e->getMessage()
            ], 400);
        }
    }
    
    /**
     * Get available fiat currencies for withdrawal.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAvailableCurrencies(Request $request)
    {
        $type = $request->input('type', 'credits');
        
        $currencies = CurrencyConversionRate::where('from_currency', $type)
            ->where('is_active', true)
            ->get()
            ->pluck('to_currency')
            ->toArray();
            
        return response()->json([
            'currencies' => $currencies
        ]);
    }
    
    /**
     * Get withdrawal transaction history.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTransactionHistory(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'limit' => 'integer|min:1|max:100',
            'offset' => 'integer|min:0',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }
        
        $limit = $request->input('limit', 10);
        $offset = $request->input('offset', 0);
        
        $transactions = $this->withdrawalService->getTransactionHistory(
            $request->user(),
            $limit,
            $offset
        );
        
        return response()->json([
            'transactions' => $transactions,
            'pagination' => [
                'total' => $request->user()->withdrawalTransactions()->count(),
                'limit' => $limit,
                'offset' => $offset
            ]
        ]);
    }
    
    /**
     * Get a specific withdrawal transaction.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTransaction(Request $request, $id)
    {
        try {
            $transaction = $this->withdrawalService->getTransaction($request->user(), $id);
            
            return response()->json([
                'transaction' => $transaction
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Transaction not found'
            ], 404);
        }
    }
}
