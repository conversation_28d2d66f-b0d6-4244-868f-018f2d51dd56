<?php

namespace App\Http\Controllers;

use App\Models\UserSetting;
use Illuminate\Http\JsonResponse;

class UserSettingController extends Controller
{
    /**
     * Get allow 3rd party access setting.
     * This endpoint is publicly accessible and returns the global setting.
     * For authenticated users, use the user-specific endpoint instead.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAllow3rdPartyAccess(): JsonResponse
    {
        $setting = UserSetting::where('key', 'allow_3rd_party_access')->first();

        if (!$setting) {
            // Return false if the setting doesn't exist
            return response()->json(false);
        }

        return response()->json($setting->value);
    }
}
