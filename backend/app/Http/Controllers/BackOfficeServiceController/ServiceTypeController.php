<?php

namespace App\Http\Controllers\BackOfficeServiceController;

use App\Http\Controllers\Controller;
use App\Models\ServiceType;
use App\Models\ServiceTypeTranslation;
use App\Rules\CleanContent;
use App\Services\ImageProcessingService;
use App\Services\InputSanitizationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;

class ServiceTypeController extends Controller
{
    protected $imageProcessingService;
    protected $inputSanitizationService;
    
    public function __construct(
        ImageProcessingService $imageProcessingService,
        InputSanitizationService $inputSanitizationService
    ) {
        $this->imageProcessingService = $imageProcessingService;
        $this->inputSanitizationService = $inputSanitizationService;
    }
    
    /**
     * List all service types.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function listServiceTypes(Request $request)
    {
        $query = ServiceType::with(['translations', 'serviceCategory']);
        
        // Apply filters if provided
        if ($request->has('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }
        
        if ($request->has('service_category_id')) {
            $query->where('service_category_id', $request->input('service_category_id'));
        }
        
        // Apply sorting
        $sortField = $request->input('sort_by', 'display_order');
        $sortDirection = $request->input('sort_direction', 'asc');
        $query->orderBy($sortField, $sortDirection);
        
        // Include soft deleted items if requested
        if ($request->boolean('with_trashed')) {
            $query->withTrashed();
        }
        
        // Paginate results
        $perPage = $request->input('per_page', 15);
        $types = $query->paginate($perPage);
        
        // Transform the collection to include translations for the current locale
        $locale = App::getLocale();
        $types->getCollection()->transform(function ($type) use ($locale) {
            $translation = $type->translations->where('locale', $locale)->first();
            $categoryTranslation = $type->serviceCategory->translations->where('locale', $locale)->first();
            
            return [
                'id' => $type->id,
                'service_category_id' => $type->service_category_id,
                'service_category_name' => $categoryTranslation ? $categoryTranslation->name : $type->serviceCategory->name,
                'name' => $translation ? $translation->name : $type->name,
                'description' => $translation ? $translation->description : $type->description,
                'icon_path' => $type->icon_path,
                'required_elements' => $type->required_elements,
                'is_active' => $type->is_active,
                'display_order' => $type->display_order,
                'created_at' => $type->created_at,
                'updated_at' => $type->updated_at,
                'deleted_at' => $type->deleted_at,
                'translations' => $type->translations,
            ];
        });
        
        return response()->json($types);
    }
    
    /**
     * Create a new service type.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createServiceType(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'service_category_id' => 'required|exists:service_categories,id',
            'name' => ['required', 'string', 'max:255', new CleanContent],
            'description' => ['nullable', 'string', new CleanContent],
            'icon' => 'nullable|image|max:2048',
            'required_elements' => 'nullable|array',
            'is_active' => 'boolean',
            'display_order' => 'integer',
            'translations' => 'array',
            'translations.*.locale' => ['required', 'string', 'size:5'],
            'translations.*.name' => ['required', 'string', 'max:255', new CleanContent],
            'translations.*.description' => ['nullable', 'string', new CleanContent],
        ]);
        
        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }
        
        try {
            DB::beginTransaction();
            
            // Process icon if provided
            $iconPath = null;
            if ($request->hasFile('icon')) {
                $iconPath = $this->imageProcessingService->processImage(
                    $request->file('icon'),
                    'service_types',
                    [
                        'resize' => [
                            'width' => 512,
                            'height' => 512,
                            'maintain_aspect_ratio' => true
                        ]
                    ]
                );
            }
            
            // Create the service type
            $type = ServiceType::create([
                'service_category_id' => $request->input('service_category_id'),
                'name' => $this->inputSanitizationService->sanitize($request->input('name')),
                'description' => $request->has('description') ? $this->inputSanitizationService->sanitize($request->input('description')) : null,
                'icon_path' => $iconPath,
                'required_elements' => $request->input('required_elements'),
                'is_active' => $request->input('is_active', true),
                'display_order' => $request->input('display_order', 0),
            ]);
            
            // Create translations if provided
            if ($request->has('translations')) {
                foreach ($request->input('translations') as $translationData) {
                    $type->translations()->create([
                        'locale' => $translationData['locale'],
                        'name' => $this->inputSanitizationService->sanitize($translationData['name']),
                        'description' => isset($translationData['description']) ? $this->inputSanitizationService->sanitize($translationData['description']) : null,
                    ]);
                }
            }
            
            DB::commit();
            
            // Load relationships
            $type->load(['translations', 'serviceCategory']);
            
            return response()->json($type, 201);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to create service type', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);
            
            return response()->json(['error' => 'Failed to create service type'], 500);
        }
    }
    
    /**
     * Get a specific service type.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getServiceType($id)
    {
        $type = ServiceType::with(['translations', 'serviceCategory'])->find($id);
        
        if (!$type) {
            return response()->json(['error' => 'Service type not found'], 404);
        }
        
        return response()->json($type);
    }
    
    /**
     * Update a service type.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateServiceType(Request $request, $id)
    {
        $type = ServiceType::find($id);
        
        if (!$type) {
            return response()->json(['error' => 'Service type not found'], 404);
        }
        
        $validator = Validator::make($request->all(), [
            'service_category_id' => 'sometimes|exists:service_categories,id',
            'name' => ['sometimes', 'string', 'max:255', new CleanContent],
            'description' => ['nullable', 'string', new CleanContent],
            'icon' => 'nullable|image|max:2048',
            'required_elements' => 'nullable|array',
            'is_active' => 'sometimes|boolean',
            'display_order' => 'sometimes|integer',
            'translations' => 'sometimes|array',
            'translations.*.locale' => ['required', 'string', 'size:5'],
            'translations.*.name' => ['required', 'string', 'max:255', new CleanContent],
            'translations.*.description' => ['nullable', 'string', new CleanContent],
        ]);
        
        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }
        
        try {
            DB::beginTransaction();
            
            // Process icon if provided
            if ($request->hasFile('icon')) {
                $iconPath = $this->imageProcessingService->processImage(
                    $request->file('icon'),
                    'service_types',
                    [
                        'resize' => [
                            'width' => 512,
                            'height' => 512,
                            'maintain_aspect_ratio' => true
                        ]
                    ]
                );
                $type->icon_path = $iconPath;
            }
            
            // Update the service type
            if ($request->has('service_category_id')) {
                $type->service_category_id = $request->input('service_category_id');
            }
            
            if ($request->has('name')) {
                $type->name = $this->inputSanitizationService->sanitize($request->input('name'));
            }
            
            if ($request->has('description')) {
                $type->description = $this->inputSanitizationService->sanitize($request->input('description'));
            }
            
            if ($request->has('required_elements')) {
                $type->required_elements = $request->input('required_elements');
            }
            
            if ($request->has('is_active')) {
                $type->is_active = $request->boolean('is_active');
            }
            
            if ($request->has('display_order')) {
                $type->display_order = $request->input('display_order');
            }
            
            $type->save();
            
            // Update translations if provided
            if ($request->has('translations')) {
                foreach ($request->input('translations') as $translationData) {
                    $translation = $type->translations()
                        ->where('locale', $translationData['locale'])
                        ->first();
                    
                    if ($translation) {
                        $translation->update([
                            'name' => $this->inputSanitizationService->sanitize($translationData['name']),
                            'description' => isset($translationData['description']) ? $this->inputSanitizationService->sanitize($translationData['description']) : null,
                        ]);
                    } else {
                        $type->translations()->create([
                            'locale' => $translationData['locale'],
                            'name' => $this->inputSanitizationService->sanitize($translationData['name']),
                            'description' => isset($translationData['description']) ? $this->inputSanitizationService->sanitize($translationData['description']) : null,
                        ]);
                    }
                }
            }
            
            DB::commit();
            
            // Load relationships
            $type->load(['translations', 'serviceCategory']);
            
            return response()->json($type);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update service type', [
                'error' => $e->getMessage(),
                'type_id' => $id,
                'request' => $request->all()
            ]);
            
            return response()->json(['error' => 'Failed to update service type'], 500);
        }
    }
    
    /**
     * Delete a service type.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteServiceType($id)
    {
        $type = ServiceType::find($id);
        
        if (!$type) {
            return response()->json(['error' => 'Service type not found'], 404);
        }
        
        try {
            $type->delete();
            
            return response()->json(['message' => 'Service type deleted successfully']);
        } catch (\Exception $e) {
            Log::error('Failed to delete service type', [
                'error' => $e->getMessage(),
                'type_id' => $id
            ]);
            
            return response()->json(['error' => 'Failed to delete service type'], 500);
        }
    }
    
    /**
     * Restore a soft-deleted service type.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function restoreServiceType($id)
    {
        $type = ServiceType::withTrashed()->find($id);
        
        if (!$type) {
            return response()->json(['error' => 'Service type not found'], 404);
        }
        
        if (!$type->trashed()) {
            return response()->json(['error' => 'Service type is not deleted'], 400);
        }
        
        try {
            $type->restore();
            
            return response()->json(['message' => 'Service type restored successfully']);
        } catch (\Exception $e) {
            Log::error('Failed to restore service type', [
                'error' => $e->getMessage(),
                'type_id' => $id
            ]);
            
            return response()->json(['error' => 'Failed to restore service type'], 500);
        }
    }
}
