<?php

namespace App\Http\Controllers\BackOfficeServiceController;

use App\Http\Controllers\Controller;
use App\Models\ServiceStyle;
use App\Models\ServiceStyleTranslation;
use App\Models\User;
use App\Rules\CleanContent;
use App\Services\InputSanitizationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;

class ServiceStyleController extends Controller
{
    protected $inputSanitizationService;
    
    public function __construct(InputSanitizationService $inputSanitizationService)
    {
        $this->inputSanitizationService = $inputSanitizationService;
    }
    
    /**
     * List all service styles.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function listServiceStyles(Request $request)
    {
        $query = ServiceStyle::with(['translations', 'serviceType', 'minLevel', 'pricingOptionGroups']);
        
        // Apply filters if provided
        if ($request->has('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }
        
        if ($request->has('service_type_id')) {
            $query->where('service_type_id', $request->input('service_type_id'));
        }
        
        if ($request->has('min_level_id')) {
            $query->where('min_level_id', $request->input('min_level_id'));
        }
        
        // Apply sorting
        $sortField = $request->input('sort_by', 'display_order');
        $sortDirection = $request->input('sort_direction', 'asc');
        $query->orderBy($sortField, $sortDirection);
        
        // Include soft deleted items if requested
        if ($request->boolean('with_trashed')) {
            $query->withTrashed();
        }
        
        // Paginate results
        $perPage = $request->input('per_page', 15);
        $styles = $query->paginate($perPage);
        
        // Transform the collection to include translations for the current locale
        $locale = App::getLocale();
        $styles->getCollection()->transform(function ($style) use ($locale) {
            $translation = $style->translations->where('locale', $locale)->first();
            $typeTranslation = $style->serviceType->translations->where('locale', $locale)->first();
            
            $pricingGroups = $style->pricingOptionGroups->map(function ($group) use ($locale) {
                $groupTranslation = $group->translations->where('locale', $locale)->first();
                return [
                    'id' => $group->id,
                    'name' => $groupTranslation ? $groupTranslation->name : $group->name,
                    'description' => $groupTranslation ? $groupTranslation->description : $group->description,
                    'is_active' => $group->is_active,
                    'display_order' => $group->display_order,
                ];
            });
            
            return [
                'id' => $style->id,
                'service_type_id' => $style->service_type_id,
                'service_type_name' => $typeTranslation ? $typeTranslation->name : $style->serviceType->name,
                'name' => $translation ? $translation->name : $style->name,
                'description' => $translation ? $translation->description : $style->description,
                'min_level_id' => $style->min_level_id,
                'min_level_name' => $style->minLevel ? $style->minLevel->name : null,
                'can_bypass' => $style->can_bypass,
                'is_active' => $style->is_active,
                'display_order' => $style->display_order,
                'recommended_price' => $style->recommended_price,
                'preset_price' => $style->preset_price,
                'created_at' => $style->created_at,
                'updated_at' => $style->updated_at,
                'deleted_at' => $style->deleted_at,
                'translations' => $style->translations,
                'pricing_option_groups' => $pricingGroups,
            ];
        });
        
        return response()->json($styles);
    }
    
    /**
     * Create a new service style.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createServiceStyle(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'service_type_id' => 'required|exists:service_types,id',
            'name' => ['required', 'string', 'max:255', new CleanContent],
            'description' => ['nullable', 'string', new CleanContent],
            'min_level_id' => 'nullable|exists:user_levels,id',
            'can_bypass' => 'boolean',
            'is_active' => 'boolean',
            'display_order' => 'integer',
            'recommended_price' => 'nullable|integer',
            'preset_price' => 'nullable|integer',
            'pricing_option_group_ids' => 'array',
            'pricing_option_group_ids.*' => 'exists:pricing_option_groups,id',
            'translations' => 'array',
            'translations.*.locale' => ['required', 'string', 'size:5'],
            'translations.*.name' => ['required', 'string', 'max:255', new CleanContent],
            'translations.*.description' => ['nullable', 'string', new CleanContent],
        ]);
        
        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }
        
        try {
            DB::beginTransaction();
            
            // Create the service style
            $style = ServiceStyle::create([
                'service_type_id' => $request->input('service_type_id'),
                'name' => $this->inputSanitizationService->sanitize($request->input('name')),
                'description' => $request->has('description') ? $this->inputSanitizationService->sanitize($request->input('description')) : null,
                'min_level_id' => $request->input('min_level_id'),
                'can_bypass' => $request->input('can_bypass', false),
                'is_active' => $request->input('is_active', true),
                'display_order' => $request->input('display_order', 0),
                'recommended_price' => $request->input('recommended_price'),
                'preset_price' => $request->input('preset_price'),
            ]);
            
            // Create translations if provided
            if ($request->has('translations')) {
                foreach ($request->input('translations') as $translationData) {
                    $style->translations()->create([
                        'locale' => $translationData['locale'],
                        'name' => $this->inputSanitizationService->sanitize($translationData['name']),
                        'description' => isset($translationData['description']) ? $this->inputSanitizationService->sanitize($translationData['description']) : null,
                    ]);
                }
            }
            
            // Attach pricing option groups if provided
            if ($request->has('pricing_option_group_ids')) {
                $style->pricingOptionGroups()->attach($request->input('pricing_option_group_ids'));
            }
            
            DB::commit();
            
            // Load relationships
            $style->load(['translations', 'serviceType', 'minLevel', 'pricingOptionGroups']);
            
            return response()->json($style, 201);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to create service style', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);
            
            return response()->json(['error' => 'Failed to create service style'], 500);
        }
    }
    
    /**
     * Get a specific service style.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getServiceStyle($id)
    {
        $style = ServiceStyle::with(['translations', 'serviceType', 'minLevel', 'pricingOptionGroups'])->find($id);
        
        if (!$style) {
            return response()->json(['error' => 'Service style not found'], 404);
        }
        
        return response()->json($style);
    }
    
    /**
     * Update a service style.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateServiceStyle(Request $request, $id)
    {
        $style = ServiceStyle::find($id);
        
        if (!$style) {
            return response()->json(['error' => 'Service style not found'], 404);
        }
        
        $validator = Validator::make($request->all(), [
            'service_type_id' => 'sometimes|exists:service_types,id',
            'name' => ['sometimes', 'string', 'max:255', new CleanContent],
            'description' => ['nullable', 'string', new CleanContent],
            'min_level_id' => 'nullable|exists:user_levels,id',
            'can_bypass' => 'sometimes|boolean',
            'is_active' => 'sometimes|boolean',
            'display_order' => 'sometimes|integer',
            'recommended_price' => 'nullable|integer',
            'preset_price' => 'nullable|integer',
            'pricing_option_group_ids' => 'sometimes|array',
            'pricing_option_group_ids.*' => 'exists:pricing_option_groups,id',
            'translations' => 'sometimes|array',
            'translations.*.locale' => ['required', 'string', 'size:5'],
            'translations.*.name' => ['required', 'string', 'max:255', new CleanContent],
            'translations.*.description' => ['nullable', 'string', new CleanContent],
        ]);
        
        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }
        
        try {
            DB::beginTransaction();
            
            // Update the service style
            if ($request->has('service_type_id')) {
                $style->service_type_id = $request->input('service_type_id');
            }
            
            if ($request->has('name')) {
                $style->name = $this->inputSanitizationService->sanitize($request->input('name'));
            }
            
            if ($request->has('description')) {
                $style->description = $this->inputSanitizationService->sanitize($request->input('description'));
            }
            
            if ($request->has('min_level_id')) {
                $style->min_level_id = $request->input('min_level_id');
            }
            
            if ($request->has('can_bypass')) {
                $style->can_bypass = $request->boolean('can_bypass');
            }
            
            if ($request->has('is_active')) {
                $style->is_active = $request->boolean('is_active');
            }
            
            if ($request->has('display_order')) {
                $style->display_order = $request->input('display_order');
            }
            
            if ($request->has('recommended_price')) {
                $style->recommended_price = $request->input('recommended_price');
            }
            
            if ($request->has('preset_price')) {
                $style->preset_price = $request->input('preset_price');
            }
            
            $style->save();
            
            // Update translations if provided
            if ($request->has('translations')) {
                foreach ($request->input('translations') as $translationData) {
                    $translation = $style->translations()
                        ->where('locale', $translationData['locale'])
                        ->first();
                    
                    if ($translation) {
                        $translation->update([
                            'name' => $this->inputSanitizationService->sanitize($translationData['name']),
                            'description' => isset($translationData['description']) ? $this->inputSanitizationService->sanitize($translationData['description']) : null,
                        ]);
                    } else {
                        $style->translations()->create([
                            'locale' => $translationData['locale'],
                            'name' => $this->inputSanitizationService->sanitize($translationData['name']),
                            'description' => isset($translationData['description']) ? $this->inputSanitizationService->sanitize($translationData['description']) : null,
                        ]);
                    }
                }
            }
            
            // Update pricing option groups if provided
            if ($request->has('pricing_option_group_ids')) {
                $style->pricingOptionGroups()->sync($request->input('pricing_option_group_ids'));
            }
            
            DB::commit();
            
            // Load relationships
            $style->load(['translations', 'serviceType', 'minLevel', 'pricingOptionGroups']);
            
            return response()->json($style);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update service style', [
                'error' => $e->getMessage(),
                'style_id' => $id,
                'request' => $request->all()
            ]);
            
            return response()->json(['error' => 'Failed to update service style'], 500);
        }
    }
    
    /**
     * Delete a service style.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteServiceStyle($id)
    {
        $style = ServiceStyle::find($id);
        
        if (!$style) {
            return response()->json(['error' => 'Service style not found'], 404);
        }
        
        try {
            $style->delete();
            
            return response()->json(['message' => 'Service style deleted successfully']);
        } catch (\Exception $e) {
            Log::error('Failed to delete service style', [
                'error' => $e->getMessage(),
                'style_id' => $id
            ]);
            
            return response()->json(['error' => 'Failed to delete service style'], 500);
        }
    }
    
    /**
     * Restore a soft-deleted service style.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function restoreServiceStyle($id)
    {
        $style = ServiceStyle::withTrashed()->find($id);
        
        if (!$style) {
            return response()->json(['error' => 'Service style not found'], 404);
        }
        
        if (!$style->trashed()) {
            return response()->json(['error' => 'Service style is not deleted'], 400);
        }
        
        try {
            $style->restore();
            
            return response()->json(['message' => 'Service style restored successfully']);
        } catch (\Exception $e) {
            Log::error('Failed to restore service style', [
                'error' => $e->getMessage(),
                'style_id' => $id
            ]);
            
            return response()->json(['error' => 'Failed to restore service style'], 500);
        }
    }
    
    /**
     * Add a user to the bypass list for a service style.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function addUserBypass(Request $request, $id)
    {
        $style = ServiceStyle::find($id);
        
        if (!$style) {
            return response()->json(['error' => 'Service style not found'], 404);
        }
        
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
        ]);
        
        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }
        
        try {
            $userId = $request->input('user_id');
            $user = User::find($userId);
            
            if (!$user) {
                return response()->json(['error' => 'User not found'], 404);
            }
            
            // Check if the user is already in the bypass list
            if ($style->bypassUsers()->where('user_id', $userId)->exists()) {
                return response()->json(['message' => 'User is already in the bypass list'], 200);
            }
            
            // Add the user to the bypass list
            $style->bypassUsers()->attach($userId);
            
            return response()->json(['message' => 'User added to bypass list successfully']);
        } catch (\Exception $e) {
            Log::error('Failed to add user to bypass list', [
                'error' => $e->getMessage(),
                'style_id' => $id,
                'user_id' => $request->input('user_id')
            ]);
            
            return response()->json(['error' => 'Failed to add user to bypass list'], 500);
        }
    }
    
    /**
     * Remove a user from the bypass list for a service style.
     *
     * @param int $id
     * @param int $userId
     * @return \Illuminate\Http\JsonResponse
     */
    public function removeUserBypass($id, $userId)
    {
        $style = ServiceStyle::find($id);
        
        if (!$style) {
            return response()->json(['error' => 'Service style not found'], 404);
        }
        
        try {
            // Check if the user is in the bypass list
            if (!$style->bypassUsers()->where('user_id', $userId)->exists()) {
                return response()->json(['error' => 'User is not in the bypass list'], 404);
            }
            
            // Remove the user from the bypass list
            $style->bypassUsers()->detach($userId);
            
            return response()->json(['message' => 'User removed from bypass list successfully']);
        } catch (\Exception $e) {
            Log::error('Failed to remove user from bypass list', [
                'error' => $e->getMessage(),
                'style_id' => $id,
                'user_id' => $userId
            ]);
            
            return response()->json(['error' => 'Failed to remove user from bypass list'], 500);
        }
    }
    
    /**
     * List all users in the bypass list for a service style.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function listUserBypasses($id)
    {
        $style = ServiceStyle::find($id);
        
        if (!$style) {
            return response()->json(['error' => 'Service style not found'], 404);
        }
        
        try {
            $users = $style->bypassUsers()->get(['users.id', 'users.name', 'users.email']);
            
            return response()->json($users);
        } catch (\Exception $e) {
            Log::error('Failed to list users in bypass list', [
                'error' => $e->getMessage(),
                'style_id' => $id
            ]);
            
            return response()->json(['error' => 'Failed to list users in bypass list'], 500);
        }
    }
}
