<?php

namespace App\Http\Controllers\BackOfficeServiceController;

use App\Http\Controllers\Controller;
use App\Models\PricingOption;
use App\Models\PricingOptionTranslation;
use App\Rules\CleanContent;
use App\Services\InputSanitizationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;

class PricingOptionController extends Controller
{
    protected $inputSanitizationService;
    
    public function __construct(InputSanitizationService $inputSanitizationService)
    {
        $this->inputSanitizationService = $inputSanitizationService;
    }
    
    /**
     * List all pricing options.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function listPricingOptions(Request $request)
    {
        $query = PricingOption::with(['translations', 'pricingOptionType']);
        
        // Apply filters if provided
        if ($request->has('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }
        
        if ($request->has('pricing_option_type_id')) {
            $query->where('pricing_option_type_id', $request->input('pricing_option_type_id'));
        }
        
        // Apply sorting
        $sortField = $request->input('sort_by', 'display_order');
        $sortDirection = $request->input('sort_direction', 'asc');
        $query->orderBy($sortField, $sortDirection);
        
        // Include soft deleted items if requested
        if ($request->boolean('with_trashed')) {
            $query->withTrashed();
        }
        
        // Paginate results
        $perPage = $request->input('per_page', 15);
        $options = $query->paginate($perPage);
        
        // Transform the collection to include translations for the current locale
        $locale = App::getLocale();
        $options->getCollection()->transform(function ($option) use ($locale) {
            $translation = $option->translations->where('locale', $locale)->first();
            $typeTranslation = $option->pricingOptionType->translations->where('locale', $locale)->first();
            
            return [
                'id' => $option->id,
                'pricing_option_type_id' => $option->pricing_option_type_id,
                'pricing_option_type_name' => $typeTranslation ? $typeTranslation->name : $option->pricingOptionType->name,
                'name' => $translation ? $translation->name : $option->name,
                'description' => $translation ? $translation->description : $option->description,
                'credits' => $option->credits,
                'is_active' => $option->is_active,
                'display_order' => $option->display_order,
                'created_at' => $option->created_at,
                'updated_at' => $option->updated_at,
                'deleted_at' => $option->deleted_at,
                'translations' => $option->translations,
            ];
        });
        
        return response()->json($options);
    }
    
    /**
     * Create a new pricing option.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createPricingOption(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'pricing_option_type_id' => 'required|exists:pricing_option_types,id',
            'name' => ['required', 'string', 'max:255', new CleanContent],
            'description' => ['nullable', 'string', new CleanContent],
            'credits' => 'required|integer|min:1',
            'is_active' => 'boolean',
            'display_order' => 'integer',
            'translations' => 'array',
            'translations.*.locale' => ['required', 'string', 'size:5'],
            'translations.*.name' => ['required', 'string', 'max:255', new CleanContent],
            'translations.*.description' => ['nullable', 'string', new CleanContent],
        ]);
        
        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }
        
        try {
            DB::beginTransaction();
            
            // Create the pricing option
            $option = PricingOption::create([
                'pricing_option_type_id' => $request->input('pricing_option_type_id'),
                'name' => $this->inputSanitizationService->sanitize($request->input('name')),
                'description' => $request->has('description') ? $this->inputSanitizationService->sanitize($request->input('description')) : null,
                'credits' => $request->input('credits'),
                'is_active' => $request->input('is_active', true),
                'display_order' => $request->input('display_order', 0),
            ]);
            
            // Create translations if provided
            if ($request->has('translations')) {
                foreach ($request->input('translations') as $translationData) {
                    $option->translations()->create([
                        'locale' => $translationData['locale'],
                        'name' => $this->inputSanitizationService->sanitize($translationData['name']),
                        'description' => isset($translationData['description']) ? $this->inputSanitizationService->sanitize($translationData['description']) : null,
                    ]);
                }
            }
            
            DB::commit();
            
            // Load relationships
            $option->load(['translations', 'pricingOptionType']);
            
            return response()->json($option, 201);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to create pricing option', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);
            
            return response()->json(['error' => 'Failed to create pricing option'], 500);
        }
    }
    
    /**
     * Get a specific pricing option.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPricingOption($id)
    {
        $option = PricingOption::with(['translations', 'pricingOptionType'])->find($id);
        
        if (!$option) {
            return response()->json(['error' => 'Pricing option not found'], 404);
        }
        
        return response()->json($option);
    }
    
    /**
     * Update a pricing option.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updatePricingOption(Request $request, $id)
    {
        $option = PricingOption::find($id);
        
        if (!$option) {
            return response()->json(['error' => 'Pricing option not found'], 404);
        }
        
        $validator = Validator::make($request->all(), [
            'pricing_option_type_id' => 'sometimes|exists:pricing_option_types,id',
            'name' => ['sometimes', 'string', 'max:255', new CleanContent],
            'description' => ['nullable', 'string', new CleanContent],
            'credits' => 'sometimes|integer|min:1',
            'is_active' => 'sometimes|boolean',
            'display_order' => 'sometimes|integer',
            'translations' => 'sometimes|array',
            'translations.*.locale' => ['required', 'string', 'size:5'],
            'translations.*.name' => ['required', 'string', 'max:255', new CleanContent],
            'translations.*.description' => ['nullable', 'string', new CleanContent],
        ]);
        
        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }
        
        try {
            DB::beginTransaction();
            
            // Update the pricing option
            if ($request->has('pricing_option_type_id')) {
                $option->pricing_option_type_id = $request->input('pricing_option_type_id');
            }
            
            if ($request->has('name')) {
                $option->name = $this->inputSanitizationService->sanitize($request->input('name'));
            }
            
            if ($request->has('description')) {
                $option->description = $this->inputSanitizationService->sanitize($request->input('description'));
            }
            
            if ($request->has('credits')) {
                $option->credits = $request->input('credits');
            }
            
            if ($request->has('is_active')) {
                $option->is_active = $request->boolean('is_active');
            }
            
            if ($request->has('display_order')) {
                $option->display_order = $request->input('display_order');
            }
            
            $option->save();
            
            // Update translations if provided
            if ($request->has('translations')) {
                foreach ($request->input('translations') as $translationData) {
                    $translation = $option->translations()
                        ->where('locale', $translationData['locale'])
                        ->first();
                    
                    if ($translation) {
                        $translation->update([
                            'name' => $this->inputSanitizationService->sanitize($translationData['name']),
                            'description' => isset($translationData['description']) ? $this->inputSanitizationService->sanitize($translationData['description']) : null,
                        ]);
                    } else {
                        $option->translations()->create([
                            'locale' => $translationData['locale'],
                            'name' => $this->inputSanitizationService->sanitize($translationData['name']),
                            'description' => isset($translationData['description']) ? $this->inputSanitizationService->sanitize($translationData['description']) : null,
                        ]);
                    }
                }
            }
            
            DB::commit();
            
            // Load relationships
            $option->load(['translations', 'pricingOptionType']);
            
            return response()->json($option);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update pricing option', [
                'error' => $e->getMessage(),
                'option_id' => $id,
                'request' => $request->all()
            ]);
            
            return response()->json(['error' => 'Failed to update pricing option'], 500);
        }
    }
    
    /**
     * Delete a pricing option.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function deletePricingOption($id)
    {
        $option = PricingOption::find($id);
        
        if (!$option) {
            return response()->json(['error' => 'Pricing option not found'], 404);
        }
        
        try {
            $option->delete();
            
            return response()->json(['message' => 'Pricing option deleted successfully']);
        } catch (\Exception $e) {
            Log::error('Failed to delete pricing option', [
                'error' => $e->getMessage(),
                'option_id' => $id
            ]);
            
            return response()->json(['error' => 'Failed to delete pricing option'], 500);
        }
    }
    
    /**
     * Restore a soft-deleted pricing option.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function restorePricingOption($id)
    {
        $option = PricingOption::withTrashed()->find($id);
        
        if (!$option) {
            return response()->json(['error' => 'Pricing option not found'], 404);
        }
        
        if (!$option->trashed()) {
            return response()->json(['error' => 'Pricing option is not deleted'], 400);
        }
        
        try {
            $option->restore();
            
            return response()->json(['message' => 'Pricing option restored successfully']);
        } catch (\Exception $e) {
            Log::error('Failed to restore pricing option', [
                'error' => $e->getMessage(),
                'option_id' => $id
            ]);
            
            return response()->json(['error' => 'Failed to restore pricing option'], 500);
        }
    }
}
