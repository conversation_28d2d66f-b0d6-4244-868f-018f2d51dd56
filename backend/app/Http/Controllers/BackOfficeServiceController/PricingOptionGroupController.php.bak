<?php

namespace App\Http\Controllers\BackOfficeServiceController;

use App\Http\Controllers\Controller;
use App\Models\PricingOptionGroup;
use App\Models\PricingOptionGroupTranslation;
use App\Rules\CleanContent;
use App\Services\InputSanitizationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;

class PricingOptionGroupController extends Controller
{
    protected $inputSanitizationService;
    
    public function __construct(InputSanitizationService $inputSanitizationService)
    {
        $this->inputSanitizationService = $inputSanitizationService;
    }
    
    /**
     * List all pricing option groups.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function listPricingOptionGroups(Request $request)
    {
        $query = PricingOptionGroup::with(['translations', 'pricingOptionTypes']);
        
        // Apply filters if provided
        if ($request->has('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }
        
        // Apply sorting
        $sortField = $request->input('sort_by', 'display_order');
        $sortDirection = $request->input('sort_direction', 'asc');
        $query->orderBy($sortField, $sortDirection);
        
        // Include soft deleted items if requested
        if ($request->boolean('with_trashed')) {
            $query->withTrashed();
        }
        
        // Paginate results
        $perPage = $request->input('per_page', 15);
        $groups = $query->paginate($perPage);
        
        // Transform the collection to include translations for the current locale
        $locale = App::getLocale();
        $groups->getCollection()->transform(function ($group) use ($locale) {
            $translation = $group->translations->where('locale', $locale)->first();
            
            return [
                'id' => $group->id,
                'name' => $translation ? $translation->name : $group->name,
                'description' => $translation ? $translation->description : $group->description,
                'is_active' => $group->is_active,
                'display_order' => $group->display_order,
                'created_at' => $group->created_at,
                'updated_at' => $group->updated_at,
                'deleted_at' => $group->deleted_at,
                'translations' => $group->translations,
                'pricing_option_types_count' => $group->pricingOptionTypes->count(),
            ];
        });
        
        return response()->json($groups);
    }
    
    /**
     * Create a new pricing option group.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createPricingOptionGroup(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => ['required', 'string', 'max:255', new CleanContent],
            'description' => ['nullable', 'string', new CleanContent],
            'unit' => ['nullable', 'string', 'max:50', new CleanContent],
            'quantity' => ['nullable', 'integer', 'min:1'],
            'is_active' => 'boolean',
            'display_order' => 'integer',
            'translations' => 'array',
            'translations.*.locale' => ['required', 'string', 'size:5'],
            'translations.*.name' => ['required', 'string', 'max:255', new CleanContent],
            'translations.*.description' => ['nullable', 'string', new CleanContent],
        ]);
        
        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }
        
        try {
            DB::beginTransaction();
            
            // Create the pricing option group
            $group = PricingOptionGroup::create([
                'name' => $this->inputSanitizationService->sanitize($request->input('name')),
                'description' => $request->has('description') ? $this->inputSanitizationService->sanitize($request->input('description')) : null,
                'unit' => $request->has('unit') ? $this->inputSanitizationService->sanitize($request->input('unit')) : null,
                'quantity' => $request->input('quantity'),
                'is_active' => $request->input('is_active', true),
                'display_order' => $request->input('display_order', 0),
            ]);
            
            // Create translations if provided
            if ($request->has('translations')) {
                foreach ($request->input('translations') as $translationData) {
                    $group->translations()->create([
                        'locale' => $translationData['locale'],
                        'name' => $this->inputSanitizationService->sanitize($translationData['name']),
                        'description' => isset($translationData['description']) ? $this->inputSanitizationService->sanitize($translationData['description']) : null,
                    ]);
                }
            }
            
            DB::commit();
            
            // Load translations relationship
            $group->load('translations');
            
            return response()->json($group, 201);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to create pricing option group', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);
            
            return response()->json(['error' => 'Failed to create pricing option group'], 500);
        }
    }
    
    /**
     * Get a specific pricing option group.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPricingOptionGroup($id)
    {
        $group = PricingOptionGroup::with(['translations', 'pricingOptionTypes'])->find($id);
        
        if (!$group) {
            return response()->json(['error' => 'Pricing option group not found'], 404);
        }
        
        return response()->json($group);
    }
    
    /**
     * Update a pricing option group.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updatePricingOptionGroup(Request $request, $id)
    {
        $group = PricingOptionGroup::find($id);
        
        if (!$group) {
            return response()->json(['error' => 'Pricing option group not found'], 404);
        }
        
        $validator = Validator::make($request->all(), [
            'name' => ['sometimes', 'string', 'max:255', new CleanContent],
            'description' => ['nullable', 'string', new CleanContent],
            'unit' => ['nullable', 'string', 'max:50', new CleanContent],
            'quantity' => ['nullable', 'integer', 'min:1'],
            'is_active' => 'sometimes|boolean',
            'display_order' => 'sometimes|integer',
            'translations' => 'sometimes|array',
            'translations.*.locale' => ['required', 'string', 'size:5'],
            'translations.*.name' => ['required', 'string', 'max:255', new CleanContent],
            'translations.*.description' => ['nullable', 'string', new CleanContent],
        ]);
        
        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }
        
        try {
            DB::beginTransaction();
            
            // Update the pricing option group
            if ($request->has('name')) {
                $group->name = $this->inputSanitizationService->sanitize($request->input('name'));
            }
            
            if ($request->has('description')) {
                $group->description = $this->inputSanitizationService->sanitize($request->input('description'));
            }
            
            if ($request->has('unit')) {
                $group->unit = $this->inputSanitizationService->sanitize($request->input('unit'));
            }
            
            if ($request->has('quantity')) {
                $group->quantity = $request->input('quantity');
            }
            
            if ($request->has('is_active')) {
                $group->is_active = $request->boolean('is_active');
            }
            
            if ($request->has('display_order')) {
                $group->display_order = $request->input('display_order');
            }
            
            $group->save();
            
            // Update translations if provided
            if ($request->has('translations')) {
                foreach ($request->input('translations') as $translationData) {
                    $translation = $group->translations()
                        ->where('locale', $translationData['locale'])
                        ->first();
                    
                    if ($translation) {
                        $translation->update([
                            'name' => $this->inputSanitizationService->sanitize($translationData['name']),
                            'description' => isset($translationData['description']) ? $this->inputSanitizationService->sanitize($translationData['description']) : null,
                        ]);
                    } else {
                        $group->translations()->create([
                            'locale' => $translationData['locale'],
                            'name' => $this->inputSanitizationService->sanitize($translationData['name']),
                            'description' => isset($translationData['description']) ? $this->inputSanitizationService->sanitize($translationData['description']) : null,
                        ]);
                    }
                }
            }
            
            DB::commit();
            
            // Load translations relationship
            $group->load('translations');
            
            return response()->json($group);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update pricing option group', [
                'error' => $e->getMessage(),
                'group_id' => $id,
                'request' => $request->all()
            ]);
            
            return response()->json(['error' => 'Failed to update pricing option group'], 500);
        }
    }
    
    /**
     * Delete a pricing option group.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function deletePricingOptionGroup($id)
    {
        $group = PricingOptionGroup::find($id);
        
        if (!$group) {
            return response()->json(['error' => 'Pricing option group not found'], 404);
        }
        
        try {
            $group->delete();
            
            return response()->json(['message' => 'Pricing option group deleted successfully']);
        } catch (\Exception $e) {
            Log::error('Failed to delete pricing option group', [
                'error' => $e->getMessage(),
                'group_id' => $id
            ]);
            
            return response()->json(['error' => 'Failed to delete pricing option group'], 500);
        }
    }
    
    /**
     * Restore a soft-deleted pricing option group.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function restorePricingOptionGroup($id)
    {
        $group = PricingOptionGroup::withTrashed()->find($id);
        
        if (!$group) {
            return response()->json(['error' => 'Pricing option group not found'], 404);
        }
        
        if (!$group->trashed()) {
            return response()->json(['error' => 'Pricing option group is not deleted'], 400);
        }
        
        try {
            $group->restore();
            
            return response()->json(['message' => 'Pricing option group restored successfully']);
        } catch (\Exception $e) {
            Log::error('Failed to restore pricing option group', [
                'error' => $e->getMessage(),
                'group_id' => $id
            ]);
            
            return response()->json(['error' => 'Failed to restore pricing option group'], 500);
        }
    }
}
