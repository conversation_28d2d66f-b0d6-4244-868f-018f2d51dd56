<?php

namespace App\Http\Controllers\BackOfficeServiceController;

use App\Http\Controllers\Controller;
use App\Models\PricingOptionType;
use App\Models\PricingOptionTypeTranslation;
use App\Rules\CleanContent;
use App\Services\InputSanitizationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;

class PricingOptionTypeController extends Controller
{
    protected $inputSanitizationService;
    
    public function __construct(InputSanitizationService $inputSanitizationService)
    {
        $this->inputSanitizationService = $inputSanitizationService;
    }
    
    /**
     * List all pricing option types.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function listPricingOptionTypes(Request $request)
    {
        $query = PricingOptionType::with(['translations']);
        
        // Apply filters if provided
        if ($request->has('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }
        
        // Apply sorting
        $sortField = $request->input('sort_by', 'display_order');
        $sortDirection = $request->input('sort_direction', 'asc');
        $query->orderBy($sortField, $sortDirection);
        
        // Include soft deleted items if requested
        if ($request->boolean('with_trashed')) {
            $query->withTrashed();
        }
        
        // Paginate results
        $perPage = $request->input('per_page', 15);
        $types = $query->paginate($perPage);
        
        // Transform the collection to include translations for the current locale
        $locale = App::getLocale();
        $types->getCollection()->transform(function ($type) use ($locale) {
            $translation = $type->translations->where('locale', $locale)->first();
            
            return [
                'id' => $type->id,
                'name' => $translation ? $translation->name : $type->name,
                'description' => $translation ? $translation->description : $type->description,
                'is_active' => $type->is_active,
                'display_order' => $type->display_order,
                'has_duration' => $type->has_duration,
                'unit' => $type->unit,
                'quantity' => $type->quantity,
                'created_at' => $type->created_at,
                'updated_at' => $type->updated_at,
                'deleted_at' => $type->deleted_at,
                'translations' => $type->translations,
            ];
        });
        
        return response()->json($types);
    }
    
    /**
     * Create a new pricing option type.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createPricingOptionType(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => ['required', 'string', 'max:255', new CleanContent],
            'description' => ['nullable', 'string', new CleanContent],
            'is_active' => 'boolean',
            'display_order' => 'integer',
            'has_duration' => 'boolean',
            'unit' => 'nullable|string|max:50',
            'quantity' => 'nullable|integer',
            'translations' => 'array',
            'translations.*.locale' => ['required', 'string', 'size:5'],
            'translations.*.name' => ['required', 'string', 'max:255', new CleanContent],
            'translations.*.description' => ['nullable', 'string', new CleanContent],
        ]);
        
        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }
        
        try {
            DB::beginTransaction();
            
            // Create the pricing option type
            $type = PricingOptionType::create([
                'name' => $this->inputSanitizationService->sanitize($request->input('name')),
                'description' => $request->has('description') ? $this->inputSanitizationService->sanitize($request->input('description')) : null,
                'is_active' => $request->input('is_active', true),
                'display_order' => $request->input('display_order', 0),
                'has_duration' => $request->input('has_duration', false),
                'unit' => $request->input('unit'),
                'quantity' => $request->input('quantity'),
            ]);
            
            // Create translations if provided
            if ($request->has('translations')) {
                foreach ($request->input('translations') as $translationData) {
                    $type->translations()->create([
                        'locale' => $translationData['locale'],
                        'name' => $this->inputSanitizationService->sanitize($translationData['name']),
                        'description' => isset($translationData['description']) ? $this->inputSanitizationService->sanitize($translationData['description']) : null,
                    ]);
                }
            }
            
            DB::commit();
            
            // Load relationships
            $type->load(['translations']);
            
            return response()->json($type, 201);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to create pricing option type', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);
            
            return response()->json(['error' => 'Failed to create pricing option type'], 500);
        }
    }
    
    /**
     * Get a specific pricing option type.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPricingOptionType($id)
    {
        $type = PricingOptionType::with(['translations'])->find($id);
        
        if (!$type) {
            return response()->json(['error' => 'Pricing option type not found'], 404);
        }
        
        return response()->json($type);
    }
    
    /**
     * Update a pricing option type.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updatePricingOptionType(Request $request, $id)
    {
        $type = PricingOptionType::find($id);
        
        if (!$type) {
            return response()->json(['error' => 'Pricing option type not found'], 404);
        }
        
        $validator = Validator::make($request->all(), [
            'name' => ['sometimes', 'string', 'max:255', new CleanContent],
            'description' => ['nullable', 'string', new CleanContent],
            'is_active' => 'sometimes|boolean',
            'display_order' => 'sometimes|integer',
            'has_duration' => 'sometimes|boolean',
            'unit' => 'nullable|string|max:50',
            'quantity' => 'nullable|integer',
            'translations' => 'sometimes|array',
            'translations.*.locale' => ['required', 'string', 'size:5'],
            'translations.*.name' => ['required', 'string', 'max:255', new CleanContent],
            'translations.*.description' => ['nullable', 'string', new CleanContent],
        ]);
        
        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }
        
        try {
            DB::beginTransaction();
            
            // Update the pricing option type            
            if ($request->has('name')) {
                $type->name = $this->inputSanitizationService->sanitize($request->input('name'));
            }
            
            if ($request->has('description')) {
                $type->description = $this->inputSanitizationService->sanitize($request->input('description'));
            }
            
            if ($request->has('is_active')) {
                $type->is_active = $request->boolean('is_active');
            }
            
            if ($request->has('display_order')) {
                $type->display_order = $request->input('display_order');
            }
            
            if ($request->has('has_duration')) {
                $type->has_duration = $request->boolean('has_duration');
            }
            
            if ($request->has('unit')) {
                $type->unit = $request->input('unit');
            }
            
            if ($request->has('quantity')) {
                $type->quantity = $request->input('quantity');
            }
            
            $type->save();
            
            // Update translations if provided
            if ($request->has('translations')) {
                foreach ($request->input('translations') as $translationData) {
                    $translation = $type->translations()
                        ->where('locale', $translationData['locale'])
                        ->first();
                    
                    if ($translation) {
                        $translation->update([
                            'name' => $this->inputSanitizationService->sanitize($translationData['name']),
                            'description' => isset($translationData['description']) ? $this->inputSanitizationService->sanitize($translationData['description']) : null,
                        ]);
                    } else {
                        $type->translations()->create([
                            'locale' => $translationData['locale'],
                            'name' => $this->inputSanitizationService->sanitize($translationData['name']),
                            'description' => isset($translationData['description']) ? $this->inputSanitizationService->sanitize($translationData['description']) : null,
                        ]);
                    }
                }
            }
            
            DB::commit();
            
            // Load relationships
            $type->load(['translations']);
            
            return response()->json($type);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update pricing option type', [
                'error' => $e->getMessage(),
                'type_id' => $id,
                'request' => $request->all()
            ]);
            
            return response()->json(['error' => 'Failed to update pricing option type'], 500);
        }
    }
    
    /**
     * Delete a pricing option type.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function deletePricingOptionType($id)
    {
        $type = PricingOptionType::find($id);
        
        if (!$type) {
            return response()->json(['error' => 'Pricing option type not found'], 404);
        }
        
        try {
            $type->delete();
            
            return response()->json(['message' => 'Pricing option type deleted successfully']);
        } catch (\Exception $e) {
            Log::error('Failed to delete pricing option type', [
                'error' => $e->getMessage(),
                'type_id' => $id
            ]);
            
            return response()->json(['error' => 'Failed to delete pricing option type'], 500);
        }
    }
    
    /**
     * Restore a soft-deleted pricing option type.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function restorePricingOptionType($id)
    {
        $type = PricingOptionType::withTrashed()->find($id);
        
        if (!$type) {
            return response()->json(['error' => 'Pricing option type not found'], 404);
        }
        
        if (!$type->trashed()) {
            return response()->json(['error' => 'Pricing option type is not deleted'], 400);
        }
        
        try {
            $type->restore();
            
            return response()->json(['message' => 'Pricing option type restored successfully']);
        } catch (\Exception $e) {
            Log::error('Failed to restore pricing option type', [
                'error' => $e->getMessage(),
                'type_id' => $id
            ]);
            
            return response()->json(['error' => 'Failed to restore pricing option type'], 500);
        }
    }
}
