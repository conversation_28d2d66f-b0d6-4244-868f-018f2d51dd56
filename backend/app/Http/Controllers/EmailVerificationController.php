<?php

namespace App\Http\Controllers;

use App\Http\Requests\Email\SendVerificationRequest;
use App\Services\EmailVerificationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;

class EmailVerificationController extends Controller
{
    public function __construct(
        private EmailVerificationService $emailVerificationService
    ) {
    }

    /**
     * Send a verification email to the user.
     *
     * @param SendVerificationRequest $request
     * @return JsonResponse
     */
    public function sendVerification(SendVerificationRequest $request): JsonResponse
    {
        $user = auth()->user();
        $result = $this->emailVerificationService->sendVerificationEmail($user, $request->validated('email'));

        if (!$result['success']) {
            return response()->json([
                'message' => $result['message'],
                'error' => $result['error'] ?? null,
                'error_code' => $result['error_code'] ?? null,
            ], 500)->header('Content-Type', 'application/json');
        }

        return response()->json([
            'message' => 'Verification email sent successfully'
        ])->header('Content-Type', 'application/json');
    }

    /**
     * Verify the user's email using the provided token.
     *
     * @param Request $request
     * @return JsonResponse|RedirectResponse
     */
    public function verify(Request $request)
    {
        $token = $request->query('token');
        
        if (!$token) {
            return response()->json([
                'message' => 'Verification token is required'
            ], 422)->header('Content-Type', 'application/json');
        }

        $result = $this->emailVerificationService->verifyEmail($token);

        if ($request->expectsJson()) {
            return response()->json([
                'message' => $result['message'],
                'success' => $result['success'],
            ], $result['success'] ? 200 : 422)->header('Content-Type', 'application/json');
        }

        if ($result['success']) {
            return redirect(config('app.frontend_url', '/') . '/email-verification-success');
        } else {
            return redirect(config('app.frontend_url', '/') . '/email-verification-error?message=' . urlencode($result['message']));
        }
    }
}
