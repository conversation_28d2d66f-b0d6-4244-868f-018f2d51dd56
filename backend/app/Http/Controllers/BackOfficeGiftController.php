<?php

namespace App\Http\Controllers;

use App\Models\GiftItem;
use App\Models\GiftItemTranslation;
use App\Rules\CleanContent;
use App\Services\ImageProcessingService;
use App\Services\InputSanitizationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class BackOfficeGiftController extends Controller
{
    protected $imageProcessingService;
    protected $inputSanitizationService;
    
    public function __construct(
        ImageProcessingService $imageProcessingService,
        InputSanitizationService $inputSanitizationService
    ) {
        $this->imageProcessingService = $imageProcessingService;
        $this->inputSanitizationService = $inputSanitizationService;
    }
    
    /**
     * List all gift items for back office management.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function listGiftItems(Request $request)
    {
        $query = GiftItem::with('translations');
        
        // Apply filters if provided
        if ($request->has('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }
        
        // Apply sorting
        $sortField = $request->input('sort_by', 'display_order');
        $sortDirection = $request->input('sort_direction', 'asc');
        $query->orderBy($sortField, $sortDirection);
        
        // Include soft deleted items if requested
        if ($request->boolean('with_trashed')) {
            $query->withTrashed();
        }
        
        // Paginate results
        $perPage = $request->input('per_page', 15);
        $giftItems = $query->paginate($perPage);
        
        return response()->json($giftItems);
    }
    
    /**
     * Create a new gift item.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createGiftItem(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => ['required', 'string', 'max:255', new CleanContent],
            'description' => ['nullable', 'string', new CleanContent],
            'credit_price' => 'required|integer|min:1',
            'sell_back_price' => 'required|integer|min:0',
            'is_active' => 'boolean',
            'display_order' => 'integer|min:0',
            'icon' => 'nullable|image|max:2048',
            'translations' => 'nullable|array',
            'translations.*.locale' => 'required|string|size:5',
            'translations.*.name' => ['required', 'string', 'max:255', new CleanContent],
            'translations.*.description' => ['nullable', 'string', new CleanContent],
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }
        
        // Sanitize input
        $name = $this->inputSanitizationService->sanitize($request->input('name'));
        $description = $request->has('description') 
            ? $this->inputSanitizationService->sanitize($request->input('description')) 
            : null;
        
        // Process icon if provided
        $iconPath = null;
        if ($request->hasFile('icon')) {
            $iconMetadata = $this->imageProcessingService->processImage(
                $request->file('icon'),
                0 // Admin user ID or system ID
            );
            $iconPath = $iconMetadata['optimized'];
        }
        
        // Create gift item
        $giftItem = GiftItem::create([
            'name' => $name,
            'description' => $description,
            'icon_path' => $iconPath,
            'credit_price' => $request->input('credit_price'),
            'sell_back_price' => $request->input('sell_back_price'),
            'is_active' => $request->input('is_active', true),
            'display_order' => $request->input('display_order', 0),
        ]);
        
        // Create translations if provided
        if ($request->has('translations')) {
            foreach ($request->input('translations') as $translation) {
                GiftItemTranslation::create([
                    'gift_item_id' => $giftItem->id,
                    'locale' => $translation['locale'],
                    'name' => $this->inputSanitizationService->sanitize($translation['name']),
                    'description' => isset($translation['description']) 
                        ? $this->inputSanitizationService->sanitize($translation['description']) 
                        : null,
                ]);
            }
        }
        
        return response()->json([
            'message' => 'Gift item created successfully',
            'gift_item' => $giftItem->load('translations')
        ], 201);
    }
    
    /**
     * Update an existing gift item.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateGiftItem(Request $request, $id)
    {
        $giftItem = GiftItem::findOrFail($id);
        
        $validator = Validator::make($request->all(), [
            'name' => ['sometimes', 'string', 'max:255', new CleanContent],
            'description' => ['nullable', 'string', new CleanContent],
            'credit_price' => 'sometimes|integer|min:1',
            'sell_back_price' => 'sometimes|integer|min:0',
            'is_active' => 'sometimes|boolean',
            'display_order' => 'sometimes|integer|min:0',
            'icon' => 'nullable|image|max:2048',
            'translations' => 'nullable|array',
            'translations.*.locale' => 'required|string|size:5',
            'translations.*.name' => ['required', 'string', 'max:255', new CleanContent],
            'translations.*.description' => ['nullable', 'string', new CleanContent],
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }
        
        // Update basic fields if provided
        if ($request->has('name')) {
            $giftItem->name = $this->inputSanitizationService->sanitize($request->input('name'));
        }
        
        if ($request->has('description')) {
            $giftItem->description = $this->inputSanitizationService->sanitize($request->input('description'));
        }
        
        if ($request->has('credit_price')) {
            $giftItem->credit_price = $request->input('credit_price');
        }
        
        if ($request->has('sell_back_price')) {
            $giftItem->sell_back_price = $request->input('sell_back_price');
        }
        
        if ($request->has('is_active')) {
            $giftItem->is_active = $request->boolean('is_active');
        }
        
        if ($request->has('display_order')) {
            $giftItem->display_order = $request->input('display_order');
        }
        
        // Process icon if provided
        if ($request->hasFile('icon')) {
            $iconMetadata = $this->imageProcessingService->processImage(
                $request->file('icon'),
                0 // Admin user ID or system ID
            );
            $giftItem->icon_path = $iconMetadata['optimized'];
        }
        
        $giftItem->save();
        
        // Update translations if provided
        if ($request->has('translations')) {
            foreach ($request->input('translations') as $translation) {
                GiftItemTranslation::updateOrCreate(
                    [
                        'gift_item_id' => $giftItem->id,
                        'locale' => $translation['locale'],
                    ],
                    [
                        'name' => $this->inputSanitizationService->sanitize($translation['name']),
                        'description' => isset($translation['description']) 
                            ? $this->inputSanitizationService->sanitize($translation['description']) 
                            : null,
                    ]
                );
            }
        }
        
        return response()->json([
            'message' => 'Gift item updated successfully',
            'gift_item' => $giftItem->fresh()->load('translations')
        ]);
    }
    
    /**
     * Delete a gift item.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteGiftItem($id)
    {
        $giftItem = GiftItem::findOrFail($id);
        $giftItem->delete();
        
        return response()->json([
            'message' => 'Gift item deleted successfully'
        ]);
    }
    
    /**
     * Get a specific gift item.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getGiftItem($id)
    {
        $giftItem = GiftItem::with('translations')->findOrFail($id);
        
        return response()->json($giftItem);
    }
}
