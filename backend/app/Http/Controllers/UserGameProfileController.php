<?php

namespace App\Http\Controllers;

use App\Models\GameId;
use App\Models\ServiceType;
use App\Models\UserGameProfile;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class UserGameProfileController extends Controller
{
    /**
     * Display a listing of the user's game profiles.
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        
        $query = $user->gameProfiles()->with(['serviceType']);
        
        if ($request->has('service_type_id')) {
            $query->where('service_type_id', $request->input('service_type_id'));
        }
        
        if ($request->has('is_hidden')) {
            $query->where('is_hidden', $request->boolean('is_hidden'));
        }
        
        $profiles = $query->get();
        
        return response()->json($profiles)
            ->header('Content-Type', 'application/json');
    }

    /**
     * Store a newly created game profile.
     */
    public function store(Request $request)
    {
        $user = Auth::user();
        
        $validated = $request->validate([
            'service_type_id' => 'required|exists:service_types,id',
            'identifiers' => 'required|array',
            'is_hidden' => 'boolean',
            'status' => 'in:pendingReview,Reviewed',
        ]);
        
        $serviceType = ServiceType::with('gameIds')->findOrFail($validated['service_type_id']);
        
        $gameIds = $serviceType->gameIds;
        
        if ($gameIds->isNotEmpty()) {
            $identifiersValidator = Validator::make($validated['identifiers'], []);
            
            $validSlugs = $gameIds->pluck('slug')->toArray();
            foreach (array_keys($validated['identifiers']) as $key) {
                if (!in_array($key, $validSlugs)) {
                    $identifiersValidator->errors()->add($key, "The identifier '{$key}' is not valid for this service type.");
                }
            }
            
            foreach ($gameIds as $gameId) {
                if (isset($validated['identifiers'][$gameId->slug])) {
                    $value = $validated['identifiers'][$gameId->slug];
                    
                    if (!$gameId->validate($value)) {
                        $identifiersValidator->errors()->add(
                            $gameId->slug,
                            $gameId->getErrorMessage()
                        );
                    }
                }
            }
            
            if (!empty($serviceType->required_game_id_groups)) {
                foreach ($serviceType->required_game_id_groups as $group) {
                    if (isset($group['game_ids']) && is_array($group['game_ids'])) {
                        $groupGameIds = $gameIds->whereIn('id', $group['game_ids']);
                        $groupSlugs = $groupGameIds->pluck('slug')->toArray();
                        
                        $providedFromGroup = false;
                        foreach ($groupSlugs as $slug) {
                            if (isset($validated['identifiers'][$slug]) && !empty($validated['identifiers'][$slug])) {
                                $providedFromGroup = true;
                                break;
                            }
                        }
                        
                        if ($providedFromGroup) {
                            $missingFromGroup = [];
                            foreach ($groupSlugs as $slug) {
                                if (!isset($validated['identifiers'][$slug]) || empty($validated['identifiers'][$slug])) {
                                    $missingFromGroup[] = $gameIds->firstWhere('slug', $slug)->name;
                                }
                            }
                            
                            if (!empty($missingFromGroup)) {
                                $groupName = $group['group_name'] ?? 'This group of identifiers';
                                $identifiersValidator->errors()->add(
                                    'identifiers',
                                    "{$groupName} requires all of the following identifiers: " . implode(', ', $groupGameIds->pluck('name')->toArray()) . 
                                    ". Missing: " . implode(', ', $missingFromGroup)
                                );
                            }
                        }
                    }
                }
            }
            
            if (empty($validated['identifiers']) || count(array_filter($validated['identifiers'])) === 0) {
                $identifiersValidator->errors()->add('identifiers', 'At least one game identifier must be provided.');
            }
            
            if ($identifiersValidator->errors()->isNotEmpty()) {
                return response()->json(['errors' => $identifiersValidator->errors()], 422)
                    ->header('Content-Type', 'application/json');
            }
        }
        
        $profile = $user->gameProfiles()->create([
            'service_type_id' => $validated['service_type_id'],
            'identifiers' => $validated['identifiers'],
            'is_hidden' => $validated['is_hidden'] ?? true, // Default to hidden (true)
            'status' => $validated['status'] ?? 'pendingReview',
        ]);
        
        return response()->json($profile->load('serviceType'), 201)
            ->header('Content-Type', 'application/json');
    }

    /**
     * Display the specified game profile.
     */
    public function show($id)
    {
        $user = Auth::user();
        
        $profile = $user->gameProfiles()
            ->with(['serviceType'])
            ->findOrFail($id);
        
        return response()->json($profile)
            ->header('Content-Type', 'application/json');
    }

    /**
     * Update the specified game profile.
     */
    public function update(Request $request, $id)
    {
        $user = Auth::user();
        
        $profile = $user->gameProfiles()->findOrFail($id);
        
        $validated = $request->validate([
            'identifiers' => 'array',
            'is_hidden' => 'boolean',
            'status' => 'in:pendingReview,Reviewed',
        ]);
        
        $serviceType = $profile->serviceType()->with('gameIds')->first();
        
        if (isset($validated['identifiers'])) {
            $gameIds = $serviceType->gameIds;
            
            if ($gameIds->isNotEmpty()) {
                $identifiersValidator = Validator::make($validated['identifiers'], []);
                
                $validSlugs = $gameIds->pluck('slug')->toArray();
                foreach (array_keys($validated['identifiers']) as $key) {
                    if (!in_array($key, $validSlugs)) {
                        $identifiersValidator->errors()->add($key, "The identifier '{$key}' is not valid for this service type.");
                    }
                }
                
                foreach ($gameIds as $gameId) {
                    if (isset($validated['identifiers'][$gameId->slug])) {
                        $value = $validated['identifiers'][$gameId->slug];
                        
                        if (!$gameId->validate($value)) {
                            $identifiersValidator->errors()->add(
                                $gameId->slug,
                                $gameId->getErrorMessage()
                            );
                        }
                    }
                }
                
                if (!empty($serviceType->required_game_id_groups)) {
                    foreach ($serviceType->required_game_id_groups as $group) {
                        if (isset($group['game_ids']) && is_array($group['game_ids'])) {
                            $groupGameIds = $gameIds->whereIn('id', $group['game_ids']);
                            $groupSlugs = $groupGameIds->pluck('slug')->toArray();
                            
                            $providedFromGroup = false;
                            foreach ($groupSlugs as $slug) {
                                if (isset($validated['identifiers'][$slug]) && !empty($validated['identifiers'][$slug])) {
                                    $providedFromGroup = true;
                                    break;
                                }
                            }
                            
                            if ($providedFromGroup) {
                                $missingFromGroup = [];
                                foreach ($groupSlugs as $slug) {
                                    if (!isset($validated['identifiers'][$slug]) || empty($validated['identifiers'][$slug])) {
                                        $missingFromGroup[] = $gameIds->firstWhere('slug', $slug)->name;
                                    }
                                }
                                
                                if (!empty($missingFromGroup)) {
                                    $groupName = $group['group_name'] ?? 'This group of identifiers';
                                    $identifiersValidator->errors()->add(
                                        'identifiers',
                                        "{$groupName} requires all of the following identifiers: " . implode(', ', $groupGameIds->pluck('name')->toArray()) . 
                                        ". Missing: " . implode(', ', $missingFromGroup)
                                    );
                                }
                            }
                        }
                    }
                }
                
                if (empty($validated['identifiers']) || count(array_filter($validated['identifiers'])) === 0) {
                    $identifiersValidator->errors()->add('identifiers', 'At least one game identifier must be provided.');
                }
                
                if ($identifiersValidator->errors()->isNotEmpty()) {
                    return response()->json(['errors' => $identifiersValidator->errors()], 422)
                        ->header('Content-Type', 'application/json');
                }
            }
        }
        
        
        $profile->update($validated);
        
        return response()->json($profile->load('serviceType'))
            ->header('Content-Type', 'application/json');
    }

    /**
     * Remove the specified game profile.
     */
    public function destroy($id)
    {
        $user = Auth::user();
        
        $profile = $user->gameProfiles()->findOrFail($id);
        $profile->delete();
        
        return response()->json(null, 204)
            ->header('Content-Type', 'application/json');
    }

    /**
     * Set a game profile as visible (unhidden).
     */
    public function setVisible($id)
    {
        $user = Auth::user();
        
        $profile = $user->gameProfiles()->findOrFail($id);
        
        if ($profile->setAsVisible()) {
            return response()->json([
                'message' => 'Game profile set as visible successfully',
                'profile' => $profile->load('serviceType'),
            ])->header('Content-Type', 'application/json');
        }
        
        return response()->json([
            'message' => 'Failed to set game profile as visible',
        ], 500)->header('Content-Type', 'application/json');
    }
}
