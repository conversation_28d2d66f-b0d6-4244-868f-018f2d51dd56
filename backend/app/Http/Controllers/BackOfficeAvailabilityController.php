<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\UserAvailability;
use App\Models\UserAvailabilityOverride;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class BackOfficeAvailabilityController extends Controller
{
    public function listUserAvailabilities(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'type' => 'nullable|in:weekly,special',
            'date' => 'nullable|date',
            'day' => 'nullable|in:Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday',
        ]);
        
        $user = User::findOrFail($request->input('user_id'));
        $query = $user->availabilities();
        
        if ($request->has('type') && $request->input('type') === 'weekly') {
            $query->whereNotNull('day_of_week')->where('is_recurring', true);
        } elseif ($request->has('type') && $request->input('type') === 'special') {
            $query->whereNotNull('special_date');
        }
        
        if ($request->has('date')) {
            $query->where('special_date', $request->input('date'));
        }
        
        if ($request->has('day')) {
            $query->where('day_of_week', $request->input('day'));
        }
        
        $availabilities = $query->get();
        
        return response()->json($availabilities);
    }
    
    public function updateUserAvailability(Request $request, $id)
    {
        $availability = UserAvailability::findOrFail($id);
        
        $validated = $request->validate([
            'start_time' => 'sometimes|date_format:H:i',
            'end_time' => 'sometimes|date_format:H:i|after:start_time',
            'is_available' => 'sometimes|boolean',
            'remarks' => 'nullable|string|max:255',
        ]);
        
        $availability->update($validated);
        
        return response()->json([
            'message' => 'Availability updated successfully',
            'availability' => $availability,
        ]);
    }
    
    public function deleteUserAvailability($id)
    {
        $availability = UserAvailability::findOrFail($id);
        $availability->delete();
        
        return response()->json([
            'message' => 'Availability deleted successfully',
        ]);
    }
    
    public function getUserAvailabilityStatus(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
        ]);
        
        $user = User::findOrFail($request->input('user_id'));
        $status = $user->getAvailabilityStatus();
        
        return response()->json([
            'user_id' => $user->id,
            'status' => $status,
        ]);
    }
    
    /**
     * Get a user's availability override.
     */
    public function getUserAvailabilityOverride(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
        ]);
        
        $user = User::findOrFail($request->input('user_id'));
        $override = $user->availabilityOverride;
        
        if (!$override) {
            return response()->json([
                'override_type' => 'available',
                'is_active' => false,
            ]);
        }
        
        return response()->json([
            'id' => $override->id,
            'user_id' => $override->user_id,
            'override_type' => $override->override_type,
            'start_date' => $override->start_date,
            'end_date' => $override->end_date,
            'remarks' => $override->remarks,
            'is_active' => $override->isActive(),
            'created_at' => $override->created_at,
            'updated_at' => $override->updated_at,
        ]);
    }

    /**
     * Update a user's availability override.
     */
    public function updateUserAvailabilityOverride(Request $request)
    {
        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
            'override_type' => ['required', Rule::in(['available', 'today', 'three_days', 'one_week', 'one_month', 'always', 'fifteen_minutes', 'thirty_minutes', 'one_hour', 'two_hours', 'six_hours', 'twelve_hours'])],
            'remarks' => 'nullable|string|max:255',
        ]);
        
        $user = User::findOrFail($validated['user_id']);
        
        // Calculate start and end dates based on override type
        $startDate = now();
        $endDate = null;
        
        switch ($validated['override_type']) {
            case 'today':
                $endDate = $startDate->copy()->endOfDay();
                break;
            case 'three_days':
                $endDate = $startDate->copy()->addDays(3)->endOfDay();
                break;
            case 'one_week':
                $endDate = $startDate->copy()->addWeek()->endOfDay();
                break;
            case 'one_month':
                $endDate = $startDate->copy()->addMonth()->endOfDay();
                break;
            case 'fifteen_minutes':
                $endDate = $startDate->copy()->addMinutes(15);
                break;
            case 'thirty_minutes':
                $endDate = $startDate->copy()->addMinutes(30);
                break;
            case 'one_hour':
                $endDate = $startDate->copy()->addHour();
                break;
            case 'two_hours':
                $endDate = $startDate->copy()->addHours(2);
                break;
            case 'six_hours':
                $endDate = $startDate->copy()->addHours(6);
                break;
            case 'twelve_hours':
                $endDate = $startDate->copy()->addHours(12);
                break;
            case 'always':
                $endDate = null;
                break;
            case 'available':
                $startDate = null;
                $endDate = null;
                break;
        }
        
        // Create or update the override
        $override = $user->availabilityOverride()->updateOrCreate(
            ['user_id' => $user->id],
            [
                'override_type' => $validated['override_type'],
                'start_date' => $startDate,
                'end_date' => $endDate,
                'remarks' => $validated['remarks'] ?? null,
            ]
        );
        
        return response()->json([
            'message' => 'User availability override updated successfully',
            'override' => $override,
        ]);
    }

    /**
     * Remove a user's availability override.
     */
    public function removeUserAvailabilityOverride(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
        ]);
        
        $user = User::findOrFail($request->input('user_id'));
        $override = $user->availabilityOverride;
        
        if ($override) {
            $override->update(['override_type' => 'available']);
        }
        
        return response()->json([
            'message' => 'User availability override removed successfully',
        ]);
    }
}
