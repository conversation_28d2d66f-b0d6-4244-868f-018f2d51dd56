<?php

namespace App\Http\Controllers;

use App\Http\Requests\EmergencyContact\StoreRequest;
use App\Models\EmergencyContact;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class EmergencyContactController extends Controller
{
    public function index(Request $request)
    {
        return $request->user()->emergencyContacts()
            ->with('relationship.translations')
            ->get();
    }

    public function store(StoreRequest $request)
    {
        if ($request->input('is_default')) {
            $request->user()->emergencyContacts()
                ->where('is_default', true)
                ->update(['is_default' => false]);
        }

        $contact = $request->user()->emergencyContacts()->create($request->validated());

        return response()->json($contact->load('relationship.translations'), 201);
    }

    public function update(StoreRequest $request, $id)
    {
        $contact = $request->user()->emergencyContacts()->find($id);

        if (!$contact) {
            return response()->json(['message' => 'Emergency contact not found'], 404);
        }

        $this->authorize('update', $contact);

        if ($request->input('is_default')) {
            $request->user()->emergencyContacts()
                ->where('id', '!=', $contact->id)
                ->where('is_default', true)
                ->update(['is_default' => false]);
        }

        $contact->update($request->validated());

        return response()->json($contact->load('relationship.translations'));
    }


    public function destroy(EmergencyContact $contact)
    {
        Log::info('EmergencyContact Delete Request', [
            'contact_id' => $contact->id,
            'contact_user_id' => $contact->user_id,
            'request_user_id' => request()->user()->id
        ]);
        
        $this->authorize('delete', $contact);
        
        $contact->delete();
        return response()->noContent();
    }
}
