<?php

namespace App\Http\Controllers;

use App\Models\Dispute;
use App\Models\DisputeType;
use App\Rules\CleanContent;
use App\Services\DisputeService;
use App\Services\InputSanitizationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class DisputeController extends Controller
{
    protected $disputeService;
    protected $inputSanitizationService;

    public function __construct(DisputeService $disputeService, InputSanitizationService $inputSanitizationService)
    {
        $this->disputeService = $disputeService;
        $this->inputSanitizationService = $inputSanitizationService;
    }

    /**
     * Get a list of active dispute types
     * 
     * @return \Illuminate\Http\JsonResponse
     */
    public function getDisputeTypes()
    {
        $disputeTypes = DisputeType::where('is_active', true)
            ->orderBy('display_order')
            ->get();
            
        return response()->json([
            'success' => true,
            'data' => $disputeTypes
        ]);
    }

    /**
     * Create a new dispute
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createDispute(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'order_type' => 'required|in:order,mission',
            'order_id' => 'required|integer|min:1',
            'dispute_type_id' => 'required|exists:dispute_types,id',
            'description' => ['required', 'string', 'max:1000', new CleanContent],
            'media.*' => 'nullable|file|max:20480', // 20MB max
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $user = $request->user();
            $data = $validator->validated();
            
            $data['description'] = $this->inputSanitizationService->sanitize($data['description']);
            
            $files = $request->file('media') ?? [];
            
            Log::info('Creating dispute with files', [
                'has_files' => !empty($files),
                'files_count' => count($files),
                'files_type' => gettype($files),
                'request_has_file' => $request->hasFile('media'),
                'request_has_file_array' => $request->hasFile('media[]'),
                'all_files' => $request->allFiles(),
                'all_input' => $request->all(),
                'content_type' => $request->header('Content-Type'),
                'request_keys' => array_keys($request->all())
            ]);
            
            $dispute = $this->disputeService->createDispute($user, $data, $files);
            
            return response()->json([
                'success' => true,
                'message' => 'Dispute created successfully',
                'data' => $dispute->load(['disputeType', 'media']),
            ], 201);
        } catch (\Exception $e) {
            Log::error('Failed to create dispute', [
                'error' => $e->getMessage(),
                'error_code' => $e->getCode(),
                'user_id' => $request->user()->id,
                'order_type' => $request->input('order_type'),
                'order_id' => $request->input('order_id'),
                'trace' => $e->getTraceAsString(),
            ]);
            
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'error_code' => $e->getCode() >= 400 && $e->getCode() < 500 ? $e->getCode() : 400,
            ], $e->getCode() >= 400 && $e->getCode() < 500 ? $e->getCode() : 400);
        }
    }

    /**
     * Get user's disputes
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUserDisputes(Request $request)
    {
        $user = $request->user();
        $status = $request->input('status');
        $perPage = $request->input('per_page', 15);
        
        $query = Dispute::where('user_id', $user->id);
        
        if ($status) {
            $query->where('status', $status);
        }
        
        $disputes = $query->with(['disputeType', 'media'])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
            
        return response()->json([
            'success' => true,
            'data' => $disputes,
        ]);
    }

    /**
     * Get dispute details
     * 
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getDispute(Request $request, $id)
    {
        $user = $request->user();
        
        $dispute = Dispute::where('id', $id)
            ->where('user_id', $user->id)
            ->with(['disputeType', 'media'])
            ->first();
            
        if (!$dispute) {
            return response()->json([
                'success' => false,
                'message' => 'Dispute not found',
                'error_code' => 404,
            ], 404);
        }
        
        return response()->json([
            'success' => true,
            'data' => $dispute,
        ]);
    }
}
