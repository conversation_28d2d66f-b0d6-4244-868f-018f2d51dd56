<?php

namespace App\Http\Controllers;

use App\Models\Mission;
use App\Models\ServiceType;
use App\Models\User;
use App\Models\UserService;
use App\Models\ClientRepeatRate;
use App\Models\TalentView;
use App\Models\ChatResponseMetric;
use Exception;
use Illuminate\Database\QueryException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class HomeController extends Controller
{
    /**
     * Get all homepage data in a single endpoint.
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getHomepageData(Request $request)
    {
        try {
            $perPage = $request->input('per_page', 10);
            $limit = $request->input('limit', 10);
            
            $newTalents = $this->getNewTalentsData($perPage);
            
            $recommendedTalents = $this->getRecommendedTalentsData($perPage);
            
            $onlineTalents = $this->getOnlineTalentsData($perPage);
            
            $popularTalents = $this->getPopularTalentsData($perPage);
            
            $missionsCount = $this->getAvailableMissionsCountData();
            
            $serviceTypes = $this->getPopularServiceTypesData($limit);
            
            $homepageData = [
                'new_talents' => $newTalents,
                'recommended_talents' => $recommendedTalents,
                'online_talents' => $onlineTalents,
                'popular_talents' => $popularTalents,
                'available_missions_count' => $missionsCount,
                'popular_service_types' => $serviceTypes
            ];
            
            return response()->json($homepageData);
        } catch (QueryException $e) {
            Log::error('Database error in getHomepageData: ' . $e->getMessage());
            return response()->json([
                'error' => 'Failed to retrieve homepage data',
                'message' => 'A database error occurred while retrieving homepage data',
                'details' => $e->getMessage()
            ], 500);
        } catch (Exception $e) {
            Log::error('Error in getHomepageData: ' . $e->getMessage());
            return response()->json([
                'error' => 'Failed to retrieve homepage data',
                'message' => 'An unexpected error occurred',
                'details' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Get newly registered talents.
     * 
     * @param int $perPage
     * @return array
     */
    private function getNewTalentsData($perPage)
    {
        $talents = User::whereHas('services', function ($query) {
            $query->where('status', 'approved')
                  ->where('is_active', true);
        })
        ->orderBy('created_at', 'desc')
        ->with(['services.serviceType', 'services.serviceStyles', 'level'])
        ->paginate($perPage);
        
        return $talents->toArray();
    }
    
    /**
     * Get recommended talents based on acceptance rate and reviews.
     * 
     * @param int $perPage
     * @return array
     */
    private function getRecommendedTalentsData($perPage)
    {
        $talentsWithReviews = User::whereHas('services', function ($query) {
            $query->where('status', 'approved')
                  ->where('is_active', true);
        })
        ->whereHas('ordersReceived', function ($query) {
            $query->whereHas('reviews');
        })
        ->select('users.*')
        ->where('acceptance_rate', '>', 0.7) // Only talents with at least 70% acceptance rate
        ->orderBy('average_rating', 'desc')
        ->orderBy('acceptance_rate', 'desc')
        ->with(['services.serviceType', 'services.serviceStyles', 'level'])
        ->limit($perPage)
        ->get();
        
        if ($talentsWithReviews->count() >= $perPage) {
            return [
                'current_page' => 1,
                'data' => $talentsWithReviews,
                'first_page_url' => url('/api/homepage?page=1'),
                'from' => 1,
                'last_page' => 1,
                'last_page_url' => url('/api/homepage?page=1'),
                'next_page_url' => null,
                'path' => url('/api/homepage'),
                'per_page' => $perPage,
                'prev_page_url' => null,
                'to' => $talentsWithReviews->count(),
                'total' => $talentsWithReviews->count()
            ];
        }
        
        $remainingCount = $perPage - $talentsWithReviews->count();
        
        $talentsWithoutReviews = User::whereHas('services', function ($query) {
            $query->where('status', 'approved')
                  ->where('is_active', true);
        })
        ->whereDoesntHave('ordersReceived', function ($query) {
            $query->whereHas('reviews');
        })
        ->where('acceptance_rate', '>', 0.7)
        ->orderBy('acceptance_rate', 'desc')
        ->orderBy('created_at', 'desc') // Newer talents as secondary sort
        ->with(['services.serviceType', 'services.serviceStyles', 'level'])
        ->limit($remainingCount)
        ->get();
        
        $combinedTalents = $talentsWithReviews->concat($talentsWithoutReviews);
        
        return [
            'current_page' => 1,
            'data' => $combinedTalents,
            'first_page_url' => url('/api/homepage?page=1'),
            'from' => 1,
            'last_page' => 1,
            'last_page_url' => url('/api/homepage?page=1'),
            'next_page_url' => null,
            'path' => url('/api/homepage'),
            'per_page' => $perPage,
            'prev_page_url' => null,
            'to' => $combinedTalents->count(),
            'total' => $combinedTalents->count()
        ];
    }
    
    /**
     * Get list of online talents.
     * 
     * @param int $perPage
     * @return array
     */
    private function getOnlineTalentsData($perPage)
    {
        $onlineTalents = User::whereHas('services', function ($query) {
            $query->where('status', 'approved')
                  ->where('is_active', true);
        })
        ->whereHas('availabilities')
        ->whereDoesntHave('availabilityOverride', function ($query) {
            $query->where('override_type', 'always')
                  ->orWhere(function ($q) {
                      $now = now();
                      $q->where('override_type', '!=', 'available')
                        ->where('start_date', '<=', $now)
                        ->where(function ($qry) use ($now) {
                            $qry->whereNull('end_date')
                                ->orWhere('end_date', '>=', $now);
                        });
                  });
        })
        ->with(['services.serviceType', 'services.serviceStyles', 'level'])
        ->paginate($perPage);
        
        return $onlineTalents->toArray();
    }
    
    /**
     * Get count of available missions.
     * 
     * @return int
     */
    private function getAvailableMissionsCountData()
    {
        return Mission::where('status', 'open')->count();
    }
    
    /**
     * Get popular service types with icons.
     * 
     * @param int $limit
     * @return array
     */
    private function getPopularServiceTypesData($limit)
    {
        $popularServiceTypes = ServiceType::select('service_types.*', DB::raw('COUNT(user_services.id) as usage_count'))
            ->leftJoin('user_services', 'service_types.id', '=', 'user_services.service_type_id')
            ->where('service_types.is_active', true)
            ->whereHas('serviceCategory', function ($query) {
                $query->where('is_active', true);
            })
            ->groupBy('service_types.id')
            ->orderBy('usage_count', 'desc')
            ->orderBy('display_order', 'asc')
            ->limit($limit)
            ->get();
            
        return $popularServiceTypes->toArray();
    }
    
    /**
     * Get popular talents based on client repeat rate, profile views, and chat response rate.
     * 
     * @param int $perPage
     * @return array
     */
    private function getPopularTalentsData($perPage)
    {
        try {
            $talents = User::whereHas('services', function ($query) {
                $query->where('status', 'approved')
                      ->where('is_active', true);
            })
            ->with(['services.serviceType', 'services.serviceStyles', 'level', 
                    'clientRepeatRates', 'profileViews', 'chatResponseMetrics'])
            ->get();
            
            $maxOrderCount = ClientRepeatRate::max('order_count') ?: 1; // Prevent division by zero
            $maxViewCount = TalentView::sum('view_count') ?: 1; // Prevent division by zero
            
            $talentsWithScores = $talents->map(function ($talent) use ($maxOrderCount, $maxViewCount) {
                $totalRepeatOrders = $talent->clientRepeatRates->sum('order_count');
                $normalizedRepeatRate = ($totalRepeatOrders / $maxOrderCount) * 100;
                
                $totalViews = $talent->profileViews->sum('view_count');
                $normalizedViews = ($totalViews / $maxViewCount) * 100;
                
                $responseRate = $talent->chatResponseMetrics?->response_rate ?? 0;
                
                $popularityScore = (0.4 * $normalizedRepeatRate) + 
                                   (0.3 * $normalizedViews) + 
                                   (0.3 * $responseRate);
                
                $talent->popularity_score = $popularityScore;
                
                return $talent;
            });
            
            $sortedTalents = $talentsWithScores->sortByDesc('popularity_score')->take($perPage);
            
            return [
                'current_page' => 1,
                'data' => $sortedTalents->values(),
                'first_page_url' => url('/api/homepage?page=1'),
                'from' => 1,
                'last_page' => 1,
                'last_page_url' => url('/api/homepage?page=1'),
                'next_page_url' => null,
                'path' => url('/api/homepage'),
                'per_page' => $perPage,
                'prev_page_url' => null,
                'to' => $sortedTalents->count(),
                'total' => $sortedTalents->count()
            ];
        } catch (Exception $e) {
            Log::error('Error in getPopularTalentsData: ' . $e->getMessage());
            return [
                'current_page' => 1,
                'data' => [],
                'first_page_url' => url('/api/homepage?page=1'),
                'from' => 0,
                'last_page' => 1,
                'last_page_url' => url('/api/homepage?page=1'),
                'next_page_url' => null,
                'path' => url('/api/homepage'),
                'per_page' => $perPage,
                'prev_page_url' => null,
                'to' => 0,
                'total' => 0
            ];
        }
    }
    
    /**
     * Get newly registered talents.
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getNewRegisteredTalents(Request $request)
    {
        try {
            $perPage = $request->input('per_page', 10);
            $talents = User::whereHas('services', function ($query) {
                $query->where('status', 'approved')
                      ->where('is_active', true);
            })
            ->orderBy('created_at', 'desc')
            ->with(['services.serviceType', 'services.serviceStyles', 'level'])
            ->paginate($perPage);

            return response()->json($talents);
        } catch (QueryException $e) {
            Log::error('Database error in getNewRegisteredTalents: ' . $e->getMessage());
            return response()->json([
                'error' => 'Failed to retrieve new talents',
                'message' => 'A database error occurred while retrieving new talents'
            ], 500);
        } catch (Exception $e) {
            Log::error('Error in getNewRegisteredTalents: ' . $e->getMessage());
            return response()->json([
                'error' => 'Failed to retrieve new talents',
                'message' => 'An unexpected error occurred'
            ], 500);
        }
    }

    /**
     * Get recommended talents based on acceptance rate and reviews.
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getRecommendedTalents(Request $request)
    {
        try {
            $perPage = $request->input('per_page', 10);
            
            $talentsWithReviews = User::whereHas('services', function ($query) {
                $query->where('status', 'approved')
                      ->where('is_active', true);
            })
            ->whereHas('ordersReceived', function ($query) {
                $query->whereHas('reviews');
            })
            ->select('users.*', DB::raw('(
                SELECT AVG(r.rating) 
                FROM reviews r
                JOIN orders o ON r.order_id = o.id
                WHERE o.talent_id = users.id
            ) as average_rating'))
            ->where('acceptance_rate', '>', 0.7) // Only talents with at least 70% acceptance rate
            ->orderBy('average_rating', 'desc')
            ->orderBy('acceptance_rate', 'desc')
            ->with(['services.serviceType', 'services.serviceStyles', 'level'])
            ->limit($perPage)
            ->get();
            
            if ($talentsWithReviews->count() >= $perPage) {
                return response()->json([
                    'current_page' => 1,
                    'data' => $talentsWithReviews,
                    'first_page_url' => url('/api/homepage/recommended-talents?page=1'),
                    'from' => 1,
                    'last_page' => 1,
                    'last_page_url' => url('/api/homepage/recommended-talents?page=1'),
                    'next_page_url' => null,
                    'path' => url('/api/homepage/recommended-talents'),
                    'per_page' => $perPage,
                    'prev_page_url' => null,
                    'to' => $talentsWithReviews->count(),
                    'total' => $talentsWithReviews->count()
                ]);
            }
            
            $remainingCount = $perPage - $talentsWithReviews->count();
            
            $talentsWithoutReviews = User::whereHas('services', function ($query) {
                $query->where('status', 'approved')
                      ->where('is_active', true);
            })
            ->whereDoesntHave('ordersReceived', function ($query) {
                $query->whereHas('reviews');
            })
            ->where('acceptance_rate', '>', 0.7)
            ->orderBy('acceptance_rate', 'desc')
            ->orderBy('created_at', 'desc') // Newer talents as secondary sort
            ->with(['services.serviceType', 'services.serviceStyles', 'level'])
            ->limit($remainingCount)
            ->get();
            
            $combinedTalents = $talentsWithReviews->concat($talentsWithoutReviews);
            
            return response()->json([
                'current_page' => 1,
                'data' => $combinedTalents,
                'first_page_url' => url('/api/homepage/recommended-talents?page=1'),
                'from' => 1,
                'last_page' => 1,
                'last_page_url' => url('/api/homepage/recommended-talents?page=1'),
                'next_page_url' => null,
                'path' => url('/api/homepage/recommended-talents'),
                'per_page' => $perPage,
                'prev_page_url' => null,
                'to' => $combinedTalents->count(),
                'total' => $combinedTalents->count()
            ]);
        } catch (QueryException $e) {
            Log::error('Database error in getRecommendedTalents: ' . $e->getMessage());
            return response()->json([
                'error' => 'Failed to retrieve recommended talents',
                'message' => 'A database error occurred while retrieving recommended talents'
            ], 500);
        } catch (Exception $e) {
            Log::error('Error in getRecommendedTalents: ' . $e->getMessage());
            return response()->json([
                'error' => 'Failed to retrieve recommended talents',
                'message' => 'An unexpected error occurred'
            ], 500);
        }
    }

    /**
     * Get list of online talents.
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getOnlineTalents(Request $request)
    {
        try {
            $perPage = $request->input('per_page', 10);
            
            $onlineTalents = User::whereHas('services', function ($query) {
                $query->where('status', 'approved')
                      ->where('is_active', true);
            })
            ->whereHas('availabilities')
            ->whereDoesntHave('availabilityOverride', function ($query) {
                $query->where('override_type', 'always')
                      ->orWhere(function ($q) {
                          $now = now();
                          $q->where('override_type', '!=', 'available')
                            ->where('start_date', '<=', $now)
                            ->where(function ($qry) use ($now) {
                                $qry->whereNull('end_date')
                                    ->orWhere('end_date', '>=', $now);
                            });
                      });
            })
            ->with(['services.serviceType', 'services.serviceStyles', 'level'])
            ->paginate($perPage);

            return response()->json($onlineTalents);
        } catch (QueryException $e) {
            Log::error('Database error in getOnlineTalents: ' . $e->getMessage());
            return response()->json([
                'error' => 'Failed to retrieve online talents',
                'message' => 'A database error occurred while retrieving online talents'
            ], 500);
        } catch (Exception $e) {
            Log::error('Error in getOnlineTalents: ' . $e->getMessage());
            return response()->json([
                'error' => 'Failed to retrieve online talents',
                'message' => 'An unexpected error occurred'
            ], 500);
        }
    }

    /**
     * Get count of available missions.
     * 
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAvailableMissionsCount()
    {
        try {
            $count = Mission::where('status', 'open')->count();
            
            return response()->json([
                'available_missions_count' => $count
            ]);
        } catch (QueryException $e) {
            Log::error('Database error in getAvailableMissionsCount: ' . $e->getMessage());
            return response()->json([
                'error' => 'Failed to retrieve available missions count',
                'message' => 'A database error occurred while retrieving available missions count'
            ], 500);
        } catch (Exception $e) {
            Log::error('Error in getAvailableMissionsCount: ' . $e->getMessage());
            return response()->json([
                'error' => 'Failed to retrieve available missions count',
                'message' => 'An unexpected error occurred'
            ], 500);
        }
    }
    
    /**
     * Get popular service types with icons.
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPopularServiceTypes(Request $request)
    {
        try {
            $limit = $request->input('limit', 10);
            
            $popularServiceTypes = ServiceType::select('service_types.*', DB::raw('COUNT(user_services.id) as usage_count'))
                ->leftJoin('user_services', 'service_types.id', '=', 'user_services.service_type_id')
                ->where('service_types.is_active', true)
                ->whereHas('serviceCategory', function ($query) {
                    $query->where('is_active', true);
                })
                ->groupBy('service_types.id')
                ->orderBy('usage_count', 'desc')
                ->orderBy('display_order', 'asc')
                ->limit($limit)
                ->get();
                
            return response()->json($popularServiceTypes);
        } catch (QueryException $e) {
            Log::error('Database error in getPopularServiceTypes: ' . $e->getMessage());
            return response()->json([
                'error' => 'Failed to retrieve popular service types',
                'message' => 'A database error occurred while retrieving popular service types'
            ], 500);
        } catch (Exception $e) {
            Log::error('Error in getPopularServiceTypes: ' . $e->getMessage());
            return response()->json([
                'error' => 'Failed to retrieve popular service types',
                'message' => 'An unexpected error occurred'
            ], 500);
        }
    }
    

}
