<?php

namespace App\Http\Controllers;

use App\Models\StaticContent;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class StaticContentController extends Controller
{
    /**
     * Get terms and conditions content.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTermsConditions(): JsonResponse
    {
        $content = StaticContent::where('key', 'terms_conditions')
            ->where('is_active', true)
            ->first();

        if (!$content) {
            return response()->json([
                'message' => 'Terms and conditions not found'
            ], 404);
        }

        return response()->json([
            'content' => $content->content
        ]);
    }

    /**
     * Get privacy policy content.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPrivacyPolicy(): JsonResponse
    {
        $content = StaticContent::where('key', 'privacy_policy')
            ->where('is_active', true)
            ->first();

        if (!$content) {
            return response()->json([
                'message' => 'Privacy policy not found'
            ], 404);
        }

        return response()->json([
            'content' => $content->content
        ]);
    }

    /**
     * Get about us content.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAboutUs(): JsonResponse
    {
        $content = StaticContent::where('key', 'about_us')
            ->where('is_active', true)
            ->first();

        if (!$content) {
            return response()->json([
                'message' => 'About us content not found'
            ], 404);
        }

        return response()->json([
            'content' => $content->content
        ]);
    }

    /**
     * Get contact us content.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getContactUs(): JsonResponse
    {
        $content = StaticContent::where('key', 'contact_us')
            ->where('is_active', true)
            ->first();

        if (!$content) {
            return response()->json([
                'message' => 'Contact us content not found'
            ], 404);
        }

        return response()->json([
            'content' => $content->content
        ]);
    }
}
