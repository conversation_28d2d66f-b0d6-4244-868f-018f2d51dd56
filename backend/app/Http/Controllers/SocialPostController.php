<?php

namespace App\Http\Controllers;

use App\Jobs\OptimizeImageJob;
use App\Models\SocialPost;
use App\Models\SocialPostLike;
use App\Rules\CleanContent;
use App\Services\BadWordService;
use App\Services\GooglePlacesService;
use App\Services\ImageProcessingService;
use App\Services\InputSanitizationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class SocialPostController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $posts = SocialPost::with(['user', 'likes', 'comments'])
            ->where('is_hidden', false)
            ->whereNull('deleted_at')
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return response()->json($posts);
    }

    /**
     * Store a newly created resource in storage.
     */
    /**
     * @var ImageProcessingService
     */
    protected $imageService;

    /**
     * @var InputSanitizationService
     */
    protected $sanitizationService;

    /**
     * @var GooglePlacesService
     */
    protected $placesService;
    
    /**
     * @var BadWordService
     */
    protected $badWordService;

    /**
     * Create a new controller instance.
     */
    public function __construct(
        ImageProcessingService $imageService,
        InputSanitizationService $sanitizationService,
        GooglePlacesService $placesService,
        BadWordService $badWordService
    ) {
        $this->imageService = $imageService;
        $this->sanitizationService = $sanitizationService;
        $this->placesService = $placesService;
        $this->badWordService = $badWordService;
    }

    public function store(Request $request)
    {
        $request->validate([
            'title' => ['required', 'string', 'max:45', new CleanContent],
            'description' => ['required', 'string', 'max:1000', new CleanContent],
            'media_files' => 'nullable|array|max:5',
            'media_files.*' => 'file|mimes:jpeg,png,jpg,heic,heif,webp|max:20480', // 20MB max
            'location_data' => 'nullable|array',
        ]);

        $userId = Auth::id();
        $mediaFiles = [];

        if ($request->hasFile('media_files')) {
            foreach ($request->file('media_files') as $file) {
                // Validate image content beyond just file extension
                if (!$this->imageService->validateImageContent($file)) {
                    return response()->json(['message' => 'Invalid image content detected'], 422);
                }
                
                // Check if this is a mobile image format
                if ($this->imageService->isMobileImageFormat($file)) {
                    // Process mobile image format
                    if (strtolower($file->getClientOriginalExtension()) === 'webp') {
                        $fileMetadata = $this->imageService->processWebpImage($file, $userId, 'social_posts');
                    } else {
                        $fileMetadata = $this->imageService->processMobileImage($file, $userId, 'social_posts');
                    }
                } else {
                    // Process regular image
                    $fileMetadata = $this->imageService->processImage($file, $userId, 'social_posts');
                }
                
                // Add device info to metadata
                $fileMetadata['device_info'] = $request->header('User-Agent');
                
                // Store in our media files array
                $mediaFiles[] = $fileMetadata;
                
                // Dispatch a job to generate responsive images
                dispatch(new OptimizeImageJob($fileMetadata['original'], $userId, 'social_posts'));
            }
        }

        // Sanitize user input
        $title = $this->sanitizationService->sanitize($request->title);
        $description = $this->sanitizationService->sanitize($request->description);
        
        $title = $this->badWordService->filterContent($title);
        $description = $this->badWordService->filterContent($description);
        
        // Process location data if provided
        $locationData = $request->location_data;
        if ($locationData) {
            $locationData = $this->sanitizationService->sanitizeArray($locationData);
        }

        $post = SocialPost::create([
            'user_id' => $userId,
            'title' => $title,
            'description' => $description,
            'media_files' => $mediaFiles,
            'location_data' => $locationData,
        ]);

        return response()->json($post, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(SocialPost $socialPost)
    {
        // Check if post is hidden or deleted
        if ($socialPost->is_hidden && Auth::id() !== $socialPost->user_id) {
            return response()->json(['message' => 'Post not found'], 404);
        }

        $socialPost->load(['user', 'likes', 'comments']);
        return response()->json($socialPost);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, SocialPost $socialPost)
    {
        // Check if user is the owner of the post
        if (Auth::id() !== $socialPost->user_id) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $request->validate([
            'title' => ['sometimes', 'required', 'string', 'max:45', new CleanContent($this->badWordService)],
            'description' => ['sometimes', 'required', 'string', 'max:1000', new CleanContent($this->badWordService)],
            'location_data' => 'nullable|array',
        ]);

        // Sanitize user input
        $data = [];
        if ($request->has('title')) {
            $title = $this->sanitizationService->sanitize($request->title);
            $data['title'] = $this->badWordService->filterContent($title);
        }
        if ($request->has('description')) {
            $description = $this->sanitizationService->sanitize($request->description);
            $data['description'] = $this->badWordService->filterContent($description);
        }
        if ($request->has('location_data')) {
            $data['location_data'] = $this->sanitizationService->sanitizeArray($request->location_data);
        }

        $socialPost->update($data);

        return response()->json($socialPost);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(SocialPost $socialPost)
    {
        // Check if user is the owner of the post
        if (Auth::id() !== $socialPost->user_id) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $socialPost->delete();

        return response()->json(['message' => 'Post deleted successfully']);
    }

    /**
     * Toggle post visibility.
     */
    public function hidePost(Request $request, SocialPost $socialPost)
    {
        // Check if user is the owner of the post
        if (Auth::id() !== $socialPost->user_id) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $socialPost->update(['is_hidden' => !$socialPost->is_hidden]);

        return response()->json(['hidden' => $socialPost->is_hidden]);
    }

    /**
     * Like or unlike a post.
     */
    public function toggleLike(Request $request, SocialPost $socialPost)
    {
        $userId = Auth::id();
        $like = SocialPostLike::where('user_id', $userId)
            ->where('social_post_id', $socialPost->id)
            ->first();

        if ($like) {
            $like->delete();
            $action = 'unliked';
        } else {
            SocialPostLike::create([
                'user_id' => $userId,
                'social_post_id' => $socialPost->id,
            ]);
            $action = 'liked';
        }

        return response()->json(['action' => $action]);
    }

    /**
     * Get random posts.
     */
    public function random(Request $request)
    {
        $limit = $request->input('limit', 10);
        
        $posts = SocialPost::where('is_hidden', false)
            ->whereNull('deleted_at')
            ->inRandomOrder()
            ->limit($limit)
            ->with(['user', 'likes', 'comments'])
            ->get();

        return response()->json($posts);
    }

    /**
     * Get paginated feed.
     */
    public function paginatedFeed(Request $request)
    {
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 10);
        $cacheKey = 'social_feed_page_' . $page . '_' . $perPage;
        
        // Use cache with fallback for drivers that don't support tagging
        try {
            // Try to use cache tags if supported
            $posts = Cache::tags(['social_feed'])
                ->remember($cacheKey, now()->addMinutes(15), function () use ($perPage, $page) {
                    return $this->getFeedPosts($perPage, $page);
                });
        } catch (\BadMethodCallException $e) {
            // Fallback to regular cache if tagging is not supported
            $posts = Cache::remember($cacheKey, now()->addMinutes(15), function () use ($perPage, $page) {
                return $this->getFeedPosts($perPage, $page);
            });
        }

        return response()->json($posts);
    }
    
    /**
     * Get feed posts with consistent query.
     */
    private function getFeedPosts($perPage, $page)
    {
        return SocialPost::where('is_hidden', false)
            ->whereNull('deleted_at')
            ->orderBy('created_at', 'desc')
            ->with(['user', 'likes', 'comments'])
            ->paginate($perPage, ['*'], 'page', $page);
    }
}
