<?php

namespace App\Http\Controllers;

use App\Jobs\OptimizeImageJob;
use App\Models\CarouselSlide;
use App\Services\ImageProcessingService;
use App\Services\InputSanitizationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class CarouselSlideController extends Controller
{
    /**
     * @var ImageProcessingService
     */
    protected $imageService;

    /**
     * @var InputSanitizationService
     */
    protected $sanitizationService;

    /**
     * Create a new controller instance.
     */
    public function __construct(
        ImageProcessingService $imageService,
        InputSanitizationService $sanitizationService
    ) {
        $this->imageService = $imageService;
        $this->sanitizationService = $sanitizationService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $slides = CarouselSlide::where('is_active', true)
            ->orderBy('display_order')
            ->get();

        return response()->json($slides);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'nullable|string|max:100',
            'content' => 'nullable|string|max:500',
            'media_files' => 'required|array|max:1',
            'media_files.*' => 'file|mimes:jpeg,png,jpg,heic,heif,webp|max:20480', // 20MB max
            'target_url' => 'nullable|string|max:2000',
            'is_clickable' => 'nullable|boolean',
            'display_order' => 'nullable|integer',
            'is_active' => 'nullable|boolean',
        ]);

        $mediaFiles = [];

        if ($request->hasFile('media_files')) {
            foreach ($request->file('media_files') as $file) {
                if (!$this->imageService->validateImageContent($file)) {
                    return response()->json(['message' => 'Invalid image content detected'], 422);
                }
                
                if ($this->imageService->isMobileImageFormat($file)) {
                    if (strtolower($file->getClientOriginalExtension()) === 'webp') {
                        $fileMetadata = $this->imageService->processWebpImage($file, 0, 'carousel_slides');
                    } else {
                        $fileMetadata = $this->imageService->processMobileImage($file, 0, 'carousel_slides');
                    }
                } else {
                    $fileMetadata = $this->imageService->processImage($file, 0, 'carousel_slides');
                }
                
                $fileMetadata['device_info'] = $request->header('User-Agent');
                
                $mediaFiles[] = $fileMetadata;
                
                dispatch(new OptimizeImageJob($fileMetadata['original'], 0, 'carousel_slides'));
            }
        }

        $title = $this->sanitizationService->sanitize($request->title ?? '');
        $content = $this->sanitizationService->sanitize($request->content ?? '');
        
        $slide = CarouselSlide::create([
            'title' => $title,
            'content' => $content,
            'media_files' => $mediaFiles,
            'target_url' => $request->target_url,
            'is_clickable' => $request->is_clickable ?? false,
            'display_order' => $request->display_order ?? 0,
            'is_active' => $request->is_active ?? true,
        ]);

        return response()->json($slide, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(CarouselSlide $carouselSlide)
    {
        return response()->json($carouselSlide);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, CarouselSlide $carouselSlide)
    {
        $request->validate([
            'title' => 'nullable|string|max:100',
            'content' => 'nullable|string|max:500',
            'media_files' => 'nullable|array|max:1',
            'media_files.*' => 'file|mimes:jpeg,png,jpg,heic,heif,webp|max:20480', // 20MB max
            'target_url' => 'nullable|string|max:2000',
            'is_clickable' => 'nullable|boolean',
            'display_order' => 'nullable|integer',
            'is_active' => 'nullable|boolean',
        ]);

        $data = [];
        
        if ($request->has('title')) {
            $data['title'] = $this->sanitizationService->sanitize($request->title);
        }
        if ($request->has('content')) {
            $data['content'] = $this->sanitizationService->sanitize($request->content);
        }
        if ($request->has('target_url')) {
            $data['target_url'] = $request->target_url;
        }
        if ($request->has('is_clickable')) {
            $data['is_clickable'] = $request->is_clickable;
        }
        if ($request->has('display_order')) {
            $data['display_order'] = $request->display_order;
        }
        if ($request->has('is_active')) {
            $data['is_active'] = $request->is_active;
        }

        if ($request->hasFile('media_files')) {
            $mediaFiles = [];
            
            foreach ($request->file('media_files') as $file) {
                if (!$this->imageService->validateImageContent($file)) {
                    return response()->json(['message' => 'Invalid image content detected'], 422);
                }
                
                if ($this->imageService->isMobileImageFormat($file)) {
                    if (strtolower($file->getClientOriginalExtension()) === 'webp') {
                        $fileMetadata = $this->imageService->processWebpImage($file, 0, 'carousel_slides');
                    } else {
                        $fileMetadata = $this->imageService->processMobileImage($file, 0, 'carousel_slides');
                    }
                } else {
                    $fileMetadata = $this->imageService->processImage($file, 0, 'carousel_slides');
                }
                
                $fileMetadata['device_info'] = $request->header('User-Agent');
                
                $mediaFiles[] = $fileMetadata;
                
                dispatch(new OptimizeImageJob($fileMetadata['original'], 0, 'carousel_slides'));
            }
            
            if (!empty($carouselSlide->media_files)) {
                foreach ($carouselSlide->media_files as $oldFile) {
                    if (isset($oldFile['original'])) {
                        Storage::disk('cdn')->delete($oldFile['original']);
                    }
                    if (isset($oldFile['optimized'])) {
                        Storage::disk('cdn')->delete($oldFile['optimized']);
                    }
                }
            }
            
            $data['media_files'] = $mediaFiles;
        }

        $carouselSlide->update($data);

        return response()->json($carouselSlide);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(CarouselSlide $carouselSlide)
    {
        $carouselSlide->delete();

        return response()->json(['message' => 'Carousel slide deleted successfully']);
    }
}
