<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\UserService;
use App\Models\ServiceCategory;
use App\Models\ServiceType;
use App\Models\ServiceStyle;
use App\Models\UserLevel;
use App\Models\Language;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class TalentFilterController extends Controller
{
    /**
     * Filter talents based on multiple criteria.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function filterTalents(Request $request)
    {
        $query = User::whereHas('services', function ($query) {
            $query->where('status', 'approved')
                  ->where('is_active', true);
        });

        if ($request->has('service_category_id')) {
            $categoryId = $request->input('service_category_id');
            $query->whereHas('services', function ($query) use ($categoryId) {
                $query->where('service_category_id', $categoryId);
            });
        }

        if ($request->has('service_type_id')) {
            $typeIds = $request->input('service_type_id');
            
            if (is_string($typeIds) && strpos($typeIds, ',') !== false) {
                $typeIds = explode(',', $typeIds);
                $typeIds = array_map(function($id) {
                    return trim(str_replace('.', '', $id));
                }, $typeIds);
            }
            
            if (is_array($typeIds)) {
                $query->whereHas('services', function ($query) use ($typeIds) {
                    $query->whereIn('service_type_id', $typeIds);
                });
            } else {
                $query->whereHas('services', function ($query) use ($typeIds) {
                    $query->where('service_type_id', $typeIds);
                });
            }
        }

        if ($request->has('service_style_id')) {
            $styleId = $request->input('service_style_id');
            $query->whereHas('services', function ($query) use ($styleId) {
                $query->whereHas('serviceStyles', function ($q) use ($styleId) {
                    $q->where('service_styles.id', $styleId);
                });
            });
        }

        if ($request->has('rating')) {
            $rating = $request->input('rating');
            $query->where(function($query) use ($rating) {
                $query->where('average_rating', '>=', $rating)
                      ->orWhereNull('average_rating');
            });
        }

        $hasMinLevel = $request->has('min_experience_level') || $request->has('Min_experience_level') || $request->has('Min_Experience_Level');
        $hasMaxLevel = $request->has('max_experience_level') || $request->has('Max_Experience_level') || $request->has('Max_Experience_Level');
        
        if ($hasMinLevel || $hasMaxLevel) {
            $minLevel = $request->input('min_experience_level', 
                         $request->input('Min_experience_level', 
                         $request->input('Min_Experience_Level', 0)));
                         
            $maxLevel = $request->input('max_experience_level', 
                         $request->input('Max_Experience_level', 
                         $request->input('Max_Experience_Level', PHP_INT_MAX)));
            
            $query->where(function($query) use ($minLevel, $maxLevel) {
                $query->whereHas('level', function ($levelQuery) use ($minLevel, $maxLevel) {
                    if ($minLevel > 0 && $maxLevel < PHP_INT_MAX) {
                        $levelQuery->whereBetween('level', [$minLevel, $maxLevel]);
                    } else if ($minLevel > 0) {
                        $levelQuery->where('level', '>=', $minLevel);
                    } else if ($maxLevel < PHP_INT_MAX) {
                        $levelQuery->where('level', '<=', $maxLevel);
                    }
                });
                
                if ($minLevel == 0) {
                    $query->orWhereNull('level_id');
                }
            });
        }

        $hasMinPrice = $request->has('price_from');
        $hasMaxPrice = $request->has('price_to');
        
        if ($hasMinPrice || $hasMaxPrice) {
            $minPrice = $request->input('price_from', 0);
            $maxPrice = $request->input('price_to', PHP_INT_MAX);
            
            $query->where(function($query) use ($minPrice, $maxPrice) {
                $query->whereHas('services', function ($q1) use ($minPrice, $maxPrice) {
                    $q1->where('service_category_id', ServiceCategory::OTHER_CATEGORY_ID)
                       ->where(function ($q2) use ($minPrice, $maxPrice) {
                            if ($minPrice > 0 && $maxPrice < PHP_INT_MAX) {
                                $q2->whereBetween('price', [$minPrice, $maxPrice]);
                            } else if ($minPrice > 0) {
                                $q2->where('price', '>=', $minPrice);
                            } else if ($maxPrice < PHP_INT_MAX) {
                                $q2->where('price', '<=', $maxPrice);
                            }
                       });
                });
                
                $query->orWhereHas('services', function ($q1) use ($minPrice, $maxPrice) {
                    $q1->where('service_category_id', '!=', ServiceCategory::OTHER_CATEGORY_ID)
                       ->whereHas('serviceStyles', function ($q2) use ($minPrice, $maxPrice) {
                            if ($minPrice > 0 && $maxPrice < PHP_INT_MAX) {
                                $q2->whereBetween('user_service_styles.price', [$minPrice, $maxPrice]);
                            } else if ($minPrice > 0) {
                                $q2->where('user_service_styles.price', '>=', $minPrice);
                            } else if ($maxPrice < PHP_INT_MAX) {
                                $q2->where('user_service_styles.price', '<=', $maxPrice);
                            }
                       });
                });
            });
        }

        if ($request->has('race_id')) {
            $raceId = $request->input('race_id');
            $query->where('race_id', $raceId);
        }

        if ($request->has('keyword')) {
            $keyword = $request->input('keyword');
            $searchIds = User::search($keyword)
                ->query(function ($query) {
                    $query->whereHas('services', function ($query) {
                        $query->where('status', 'approved')
                              ->where('is_active', true);
                    });
                })
                ->get(['id'])
                ->pluck('id')
                ->toArray();
            $query->whereIn('id', $searchIds);
        }

        if ($request->has('gender')) {
            $query->where('gender', $request->input('gender'));
        }

        if ($request->has('language')) {
            $languageIds = $request->input('language');
            
            if (is_string($languageIds) && strpos($languageIds, ',') !== false) {
                $languageIds = explode(',', $languageIds);
                $languageIds = array_map('trim', $languageIds);
            }
            
            if (is_array($languageIds)) {
                $query->whereHas('languages', function ($query) use ($languageIds) {
                    $query->whereIn('languages.id', $languageIds);
                });
            } else {
                $query->whereHas('languages', function ($query) use ($languageIds) {
                    $query->where('languages.id', $languageIds);
                });
            }
        }

        $sortField = $request->input('sort_by', 'created_at');
        $sortDirection = $request->input('sort_direction', 'desc');
        $query->orderBy($sortField, $sortDirection);

        $perPage = $request->input('per_page', 15);
        
        /*
        $talents = $query->with([
                            'services.serviceType', 
                            'services.serviceStyles', 
                            'services.pricingOptions.pricingOptionType',
                            'level'
                        ])
                        ->paginate($perPage);
        
        return response()->json($talents);
        */
        
        $talents = $query->with([
                            'services.serviceCategory',
                            'services.serviceType', 
                            'services.serviceStyles', 
                            'services.pricingOptions.pricingOptionType',
                            'level'
                        ])
                        ->paginate($perPage);
        
        $talents->through(function ($user) {
            $formattedServices = $user->services->map(function ($service) {
                $formatted = [
                    'id' => $service->id,
                    'total_person_order' => $service->total_person_order ?? 0,
                    'created_at' => $service->created_at,
                    'updated_at' => $service->updated_at,
                    'service_category' => null,
                    'service_type' => null,
                    'service_styles' => null,
                    'pricing_options' => null,
                    'service_type_title' => null,
                    'service_type_description' => null,
                    'price' => null
                ];
                
                if ($service->serviceCategory) {
                    $formatted['service_category'] = [
                        'id' => $service->serviceCategory->id,
                        'name' => $service->serviceCategory->name,
                        'description' => $service->serviceCategory->description
                    ];
                }
                
                if ($service->service_category_id == 2) { // Digital Services (OTHER_CATEGORY_ID)
                    $formatted['service_type'] = null;
                    $formatted['service_styles'] = null;
                    $formatted['service_type_title'] = $service->service_type_title;
                    $formatted['service_type_description'] = $service->service_type_description;
                    $formatted['price'] = $service->price;
                } else {
                    if ($service->serviceType) {
                        $formatted['service_type'] = [
                            'id' => $service->serviceType->id,
                            'name' => $service->serviceType->name,
                            'description' => $service->serviceType->description,
                            'icon_path' => $service->serviceType->icon_path,
                            'required_elements' => $service->serviceType->required_elements,
                            'created_at' => $service->serviceType->created_at,
                            'updated_at' => $service->serviceType->updated_at,
                            'deleted_at' => $service->serviceType->deleted_at
                        ];
                    }
                    
                    if ($service->serviceStyles) {
                        $formatted['service_styles'] = $service->serviceStyles->map(function ($style) {
                            return [
                                'id' => $style->id,
                                'name' => $style->name,
                                'description' => $style->description,
                                'min_level_id' => $style->min_level_id,
                                'can_bypass' => $style->can_bypass,
                                'recommended_price' => $style->recommended_price,
                                'preset_price' => $style->preset_price,
                                'display_order' => $style->display_order,
                                'created_at' => $style->created_at,
                                'updated_at' => $style->updated_at,
                                'is_active' => (bool) $style->pivot->is_active,
                                'price' => (int) ($style->pivot->price ?? $style->preset_price)
                            ];
                        });
                    }
                }
                
                if ($service->pricingOptions && $service->pricingOptions->isNotEmpty()) {
                    $pricingOption = $service->pricingOptions->first();
                    
                    if ($pricingOption->pricingOptionType) {
                        $pricingOptionType = $pricingOption->pricingOptionType;
                        $formatted['pricing_options'] = [
                            'id' => $pricingOptionType->id,
                            'name' => $pricingOptionType->name,
                            'description' => $pricingOptionType->description,
                            'is_active' => (bool) $pricingOption->is_active,
                            'display_order' => $pricingOptionType->display_order,
                            'created_at' => $pricingOptionType->created_at,
                            'updated_at' => $pricingOptionType->updated_at,
                            'deleted_at' => $pricingOptionType->deleted_at,
                            'has_duration' => (bool) $pricingOptionType->has_duration,
                            'unit' => $pricingOptionType->unit,
                            'quantity' => $pricingOptionType->quantity
                        ];
                    }
                }
                
                return $formatted;
            });
            
            return [
                'id' => $user->id,
                'name' => $user->name,
                'nickname' => $user->nickname,
                'gender' => $user->gender,
                'level' => $user->level ? [
                    'id' => $user->level->id,
                    'level' => $user->level->level,
                    'name' => $user->level->name
                ] : null,
                'profile_picture' => $user->profile_picture,
                'biography' => $user->biography,
                'average_rating' => $user->average_rating,
                'services' => $formattedServices

            ];
        });
        
        return response()->json($talents);
    }

    /**
     * Get service types filtered by service category.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function getFilteredServiceTypes(Request $request)
    {
        $query = ServiceType::where('is_active', true);
        
        if ($request->has('service_category_id')) {
            $query->where('service_category_id', $request->input('service_category_id'));
        }
        
        $types = $query->orderBy('display_order')->get();
        
        return response()->json($types);
    }

    /**
     * Get service styles filtered by service type.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function getFilteredServiceStyles(Request $request)
    {
        $query = ServiceStyle::where('is_active', true);
        
        if ($request->has('service_type_id')) {
            $query->where('service_type_id', $request->input('service_type_id'));
        }
        
        $styles = $query->orderBy('display_order')->get();
        
        return response()->json($styles);
    }
}
