<?php

namespace App\Http\Controllers;

use App\Services\ReferralService;
use Illuminate\Http\Request;
use App\Models\User;

class ReferralController extends Controller
{
    protected $referralService;

    public function __construct(ReferralService $referralService)
    {
        $this->referralService = $referralService;
    }

    /**
     * Get referred users for the authenticated user.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getReferredUsers(Request $request)
    {
        $referredUsers = $this->referralService->getReferredUsers($request->user());

        return response()->json([
            'referred_users' => $referredUsers
        ]);
    }

    /**
     * Get referred users by referral code or phone number.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getReferredUsersByCode(Request $request)
    {
        $validated = $request->validate([
            'referral_code' => 'required_without:phone_number|string',
            'phone_number' => 'required_without:referral_code|string'
        ]);

        $user = null;

        if (isset($validated['referral_code'])) {
            $user = User::where('referral_code', $validated['referral_code'])->first();
        } elseif (isset($validated['phone_number'])) {
            $user = User::where('mobile_number', $validated['phone_number'])->first();
        }

        if (!$user) {
            return response()->json([
                'message' => 'User not found'
            ], 404);
        }

        $referredUsers = $this->referralService->getReferredUsers($user);

        return response()->json([
            'referred_users' => $referredUsers
        ]);
    }
}
