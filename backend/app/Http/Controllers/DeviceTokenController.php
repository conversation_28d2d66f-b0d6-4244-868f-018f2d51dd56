<?php

namespace App\Http\Controllers;

use App\Models\DeviceToken;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class DeviceTokenController extends Controller
{
    /**
     * Register or update a device token for the authenticated user
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'device_token' => 'required|string',
            'device_type' => 'required|string',
            'device_name' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'The given data was invalid',
                'errors' => $validator->errors()
            ], 422)->header('Content-Type', 'application/json');
        }

        $userId = Auth::id();

        DeviceToken::updateOrCreate(
            ['token' => $request->input('device_token')],
            [
                'user_id' => $userId,
                'device_type' => $request->input('device_type'),
                'device_name' => $request->input('device_name'),
            ]
        );

        return response()->json([
            'message' => 'Device token registered successfully'
        ])->header('Content-Type', 'application/json');
    }

    /**
     * Remove a device token for the authenticated user
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function destroy(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'device_token' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'The given data was invalid',
                'errors' => $validator->errors()
            ], 422)->header('Content-Type', 'application/json');
        }

        $userId = Auth::id();

        DeviceToken::where('token', $request->input('device_token'))
            ->where('user_id', $userId)
            ->delete();

        return response()->json([
            'message' => 'Device token removed successfully'
        ])->header('Content-Type', 'application/json');
    }
}
