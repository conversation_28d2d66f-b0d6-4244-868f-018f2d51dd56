<?php

namespace App\Http\Controllers;

use App\Models\SocialPost;
use App\Models\UserLevel;
use App\Models\SocialPostComment;
use App\Models\PostModerationLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\App;
use App\Models\User;
use App\Models\UserSetting;
use Illuminate\Support\Facades\Validator;

class BackOfficeController extends Controller
{
    /**
     * List all social posts for back office management.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function listPosts(Request $request)
    {
        $query = SocialPost::with(['user', 'likes', 'comments']);

        // Apply filters if provided
        if ($request->has('is_hidden')) {
            $query->where('is_hidden', $request->boolean('is_hidden'));
        }

        if ($request->has('is_featured')) {
            $query->where('is_featured', $request->boolean('is_featured'));
        }

        if ($request->has('user_id')) {
            $query->where('user_id', $request->input('user_id'));
        }

        // Include soft deleted posts if requested
        if ($request->boolean('with_trashed')) {
            $query->withTrashed();
        }

        // Apply sorting
        $sortField = $request->input('sort_by', 'created_at');
        $sortDirection = $request->input('sort_direction', 'desc');
        $query->orderBy($sortField, $sortDirection);

        // Paginate results
        $perPage = $request->input('per_page', 15);
        $posts = $query->paginate($perPage);

        return response()->json($posts);
    }

    /**
     * View a specific social post for back office management.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function viewPost($id)
    {
        $post = SocialPost::with(['user', 'likes', 'comments.user', 'moderationLogs'])
            ->withTrashed()
            ->findOrFail($id);

        return response()->json($post);
    }

    /**
     * List all comments for a specific post.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function listPostComments($id)
    {
        $post = SocialPost::withTrashed()->findOrFail($id);

        $comments = $post->comments()
            ->with('user')
            ->withTrashed()
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json($comments);
    }

    /**
     * Toggle post visibility (hide/unhide).
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function togglePostVisibility(Request $request, $id)
    {
        $post = SocialPost::findOrFail($id);
        $post->is_hidden = !$post->is_hidden;
        $post->save();

        // Log the action
        PostModerationLog::create([
            'post_id' => $post->id,
            'action' => $post->is_hidden ? 'hidden' : 'unhidden',
            'moderator_id' => Auth::id(),
            'notes' => $request->input('notes', 'Post visibility toggled by back office'),
        ]);

        return response()->json([
            'message' => 'Post visibility updated successfully',
            'is_hidden' => $post->is_hidden
        ]);
    }

    /**
     * Toggle post featured status.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function togglePostFeatured(Request $request, $id)
    {
        $post = SocialPost::findOrFail($id);
        $post->is_featured = !$post->is_featured;
        $post->save();

        // Log the action
        PostModerationLog::create([
            'post_id' => $post->id,
            'action' => $post->is_featured ? 'featured' : 'unfeatured',
            'moderator_id' => Auth::id(),
            'notes' => $request->input('notes', 'Post featured status toggled by back office'),
        ]);

        return response()->json([
            'message' => 'Post featured status updated successfully',
            'is_featured' => $post->is_featured
        ]);
    }

    /**
     * Delete a social post from the back office.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function deletePost(Request $request, $id)
    {
        $post = SocialPost::findOrFail($id);

        // Log the deletion
        PostModerationLog::create([
            'post_id' => $post->id,
            'action' => 'deleted',
            'moderator_id' => Auth::id(),
            'notes' => $request->input('notes', 'Post deleted by back office'),
        ]);

        // Delete the post
        $post->delete();

        return response()->json(['message' => 'Post deleted successfully']);
    }

    /**
     * Delete a social post comment from the back office.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function deleteComment(Request $request, $id)
    {
        $comment = SocialPostComment::findOrFail($id);

        // Log the deletion
        PostModerationLog::create([
            'post_id' => $comment->social_post_id,
            'action' => 'comment_deleted',
            'moderator_id' => Auth::id(),
            'notes' => $request->input('notes', 'Comment deleted by back office'),
        ]);

        // Delete the comment
        $comment->delete();

        return response()->json(['message' => 'Comment deleted successfully']);
    }

    /**
     * List all user levels for back office management.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function listLevels(Request $request)
    {
        $levels = \App\Models\UserLevel::orderBy('level', 'asc')->get();

        return response()->json($levels);
    }

    /**
     * Get a specific user level.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function getLevel($id)
    {
        $level = \App\Models\UserLevel::findOrFail($id);

        return response()->json($level);
    }

    /**
     * Create a new user level.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function createLevel(Request $request)
    {
        // Placeholder for backoffice integration
        // return response()->json(['message' => 'Create level endpoint placeholder']);

        $request->validate([
            'level' => 'required|integer|unique:user_levels,level',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'min_experience' => 'required|integer|min:0',
            'is_active' => 'boolean'
        ]);

        try {
            $level = UserLevel::create([
                'level' => $request->level,
                'name' => $request->name,
                'description' => $request->description,
                'min_experience' => $request->min_experience,
                'indicator_image' => null,
                'is_active' => $request->input('is_active', true)
            ]);

            return response()->json([
                'message' => 'Level created successfully',
                'level' => $level
            ], 201);
        } catch (Exception $e) {
            Log::error('Error creating level: ' . $e->getMessage(), [
                'error_code' => $e->getCode(),
                'error_message' => $e->getMessage(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine()
            ]);

            return response()->json([
                'message' => 'Failed to create level',
                'error' => 'An unexpected error occurred'
            ], 500);
        }
    }

    /**
     * Update a user level.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function updateLevel(Request $request, $id)
    {
        // Placeholder for backoffice integration
        return response()->json(['message' => 'Update level endpoint placeholder']);
    }

    /**
     * Delete a user level.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function deleteLevel($id)
    {
        // Placeholder for backoffice integration
        return response()->json(['message' => 'Delete level endpoint placeholder']);
    }

    /**
     * Upload a level indicator image.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function uploadLevelIndicator(Request $request, $id)
    {
        // Placeholder for backoffice integration
        return response()->json(['message' => 'Upload level indicator endpoint placeholder']);
    }

    /**
     * Configure experience deduction amounts.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function configureExperienceDeduction(Request $request)
    {
        // Placeholder for backoffice integration
        return response()->json(['message' => 'Configure experience deduction endpoint placeholder']);
    }

    /**
     * Get auto experience deduction configuration.
     *
     * @return \Illuminate\Http\Response
     */
    public function getAutoExperienceDeduction()
    {
        $config = \App\Models\AutoExperienceDeduction::where('is_active', true)->first();

        if (!$config) {
            $config = \App\Models\AutoExperienceDeduction::create([
                'days_interval' => 30,
                'deduction_amount' => 10,
                'is_active' => true,
                'next_run_at' => now()->addDays(30)
            ]);
        }

        return response()->json($config);
    }

    /**
     * Configure auto experience deduction.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function configureAutoExperienceDeduction(Request $request)
    {
        // Placeholder for backoffice integration
        return response()->json(['message' => 'Configure auto experience deduction endpoint placeholder']);
    }

    /**
     * Get auto experience deduction history.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function getAutoExperienceDeductionHistory(Request $request)
    {
        $logs = \App\Models\AutoExperienceDeductionLog::orderBy('created_at', 'desc')
            ->paginate($request->input('per_page', 15));

        return response()->json($logs);
    }

    /**
     * Run auto experience deduction manually.
     *
     * @return \Illuminate\Http\Response
     */
    public function runAutoExperienceDeduction()
    {
        // Placeholder for backoffice integration
        return response()->json(['message' => 'Run auto experience deduction endpoint placeholder']);
    }

    /**
     * Manually credit experience to a specific user.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function creditUserExperience(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'experience' => 'required|integer|min:1',
            'description' => 'required|string|max:255',
        ]);

        $user = \App\Models\User::findOrFail($request->input('user_id'));
        $experienceService = app(\App\Services\ExperienceService::class);

        $transaction = $experienceService->awardExperience(
            $user,
            $request->input('experience'),
            $request->input('description'),
            ['source' => 'backoffice', 'admin_id' => auth()->id()]
        );

        return response()->json([
            'message' => 'Experience credited successfully',
            'transaction' => $transaction
        ]);
    }

    /**
     * List users for back office management.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function listUsers(Request $request)
    {

        //$query = User::query();

        $query = User::with('level');

        // Filter by level_id
        if ($request->has('level_id')) {
            $query->where('level_id', $request->input('level_id'));
        }

        // Sorting
        if ($request->has('sort')) {
            $direction = $request->input('direction', 'asc');
            $sortField = $request->input('sort');
            if ($sortField === 'level.name') {
                $query->join('user_levels', 'users.level_id', '=', 'user_levels.id')
                    ->orderBy('user_levels.name', $direction)
                    ->select('users.*');
            } else {
                $query->orderBy($sortField, $direction);
            }
        }
        // Fetch paginated users without roles
        // $users = User::paginate(50);

        $perPage = $request->input('per_page', 50);
        $users = $query->paginate($perPage);

        // Map the response to include only desired fields
        $users->getCollection()->transform(function ($user) {
            $locale = App::getLocale();

            // Handle personalities translation (if applicable)
            $personalities = $user->personalities->map(function ($personality) use ($locale) {
                $translation = $personality->translations->where('locale', $locale)->first();
                return [
                    'id' => $personality->id,
                    'name' => $translation ? $translation->name : $personality->name,
                    'description' => $translation ? $translation->description : $personality->description,
                ];
            });

            return [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'mobile_number' => $user->mobile_number,
                'country_code' => $user->country_code,
                'nickname' => $user->nickname,
                'gender' => $user->gender,
                'date_of_birth' => $user->date_of_birth ? $user->date_of_birth->format('Y-m-d') : null,
                'profile_picture' => $user->profile_picture,
                'referral_code' => $user->referral_code,
                'referrer_phone_number' => $user->referrer_phone_number,
                'oauth_data' => $user->oauth_data,
                'created_at' => $user->created_at->toDateTimeString(),
                'updated_at' => $user->updated_at->toDateTimeString(),
                'deleted_at' => $user->deleted_at ? $user->deleted_at->toDateTimeString() : null,
                'personalities' => $personalities->toArray(),
                'level_id' => $user->level_id,
                'level_name' => $user->level ? $user->level->name : null,
            ];
        });

        return response()->json($users);
    }

    /**
     * Delete a user from the back office.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function deleteUser($id)
    {
        $user = User::findOrFail($id);
        $user->delete();
        return response()->json(['message' => 'User deleted successfully']);
    }

    /**
     * Get all user settings.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUserSettings()
    {
        $settings = UserSetting::all();
        return response()->json($settings);
    }

    /**
     * Update a user setting.
     *
     * @param \Illuminate\Http\Request $request
     * @param string $key
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateUserSetting(Request $request, $key)
    {
        $validator = Validator::make($request->all(), [
            'value' => 'required|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $setting = UserSetting::where('key', $key)->first();
        
        if (!$setting) {
            // Create if it doesn't exist
            $setting = new UserSetting();
            $setting->key = $key;
        }

        $setting->value = $request->value;
        $setting->save();

        return response()->json([
            'message' => 'Setting updated successfully',
            'setting' => $setting
        ]);
    }
}
