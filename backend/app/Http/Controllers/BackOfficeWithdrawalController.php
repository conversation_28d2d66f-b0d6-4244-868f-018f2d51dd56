<?php

namespace App\Http\Controllers;

use App\Models\WithdrawalTransaction;
use App\Models\CurrencyConversionRate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class BackOfficeWithdrawalController extends Controller
{
    /**
     * List withdrawal transactions.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function listWithdrawals(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'status' => 'sometimes|in:pending,approved,rejected,completed',
            'type' => 'sometimes|in:platform_credit,gift',
            'limit' => 'sometimes|integer|min:1|max:100',
            'offset' => 'sometimes|integer|min:0',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }
        
        $query = WithdrawalTransaction::with(['user', 'bankAccount', 'giftItem']);
        
        if ($request->has('status')) {
            $query->where('status', $request->input('status'));
        }
        
        if ($request->has('type')) {
            $query->where('withdrawal_type', $request->input('type'));
        }
        
        $total = $query->count();
        $limit = $request->input('limit', 10);
        $offset = $request->input('offset', 0);
        
        $withdrawals = $query->orderBy('created_at', 'desc')
            ->skip($offset)
            ->take($limit)
            ->get();
            
        return response()->json([
            'withdrawals' => $withdrawals,
            'pagination' => [
                'total' => $total,
                'limit' => $limit,
                'offset' => $offset
            ]
        ]);
    }
    
    /**
     * Get a specific withdrawal transaction.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getWithdrawal($id)
    {
        try {
            $withdrawal = WithdrawalTransaction::with(['user', 'bankAccount', 'bankAccount.bank', 'giftItem', 'paymentMode'])
                ->findOrFail($id);
                
            $bankAccount = $withdrawal->bankAccount;
            $accountNumber = $bankAccount ? $bankAccount->account_number : '';
            $paymentMode = $withdrawal->paymentMode;
            $malaysianBank = $bankAccount ? $bankAccount->bank : null;
            $isMBNO = $malaysianBank && strtoupper($malaysianBank->code) === 'MBNO';
            
            if ($paymentMode && strtolower($paymentMode->payment_mode_code) === 'DUITNW' && !$isMBNO) {
                if (!empty($accountNumber) && !str_starts_with($accountNumber, 'D')) {
                    $accountNumber = 'D' . $accountNumber;
                }
            }
            
            $withdrawal->account_number_display = $accountNumber;
                
            return response()->json([
                'withdrawal' => $withdrawal
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Withdrawal not found',
                'error_code' => 'withdrawal_not_found'
            ], 404);
        }
    }
    
    /**
     * Update withdrawal status.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */

      /**
    public function updateWithdrawalStatus(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'status' => 'required|in:approved,rejected,completed',
            'rejection_reason' => 'required_if:status,rejected',
            'reference_number' => 'required_if:status,completed',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }
        
        try {
            $withdrawal = WithdrawalTransaction::findOrFail($id);
            
            if ($withdrawal->status === 'rejected') {
                return response()->json([
                    'message' => 'Cannot update a rejected withdrawal',
                    'error_code' => 'invalid_status_transition'
                ], 400);
            }
            
            if ($withdrawal->status === 'completed') {
                return response()->json([
                    'message' => 'Cannot update a completed withdrawal',
                    'error_code' => 'invalid_status_transition'
                ], 400);
            }
            
            $withdrawal->status = $request->input('status');
            
            if ($request->input('status') === 'rejected') {
                $withdrawal->rejection_reason = $request->input('rejection_reason');
            } elseif ($request->input('status') === 'completed') {
                $withdrawal->reference_number = $request->input('reference_number');
            }
            
            $withdrawal->save();
            
            return response()->json([
                'message' => 'Withdrawal status updated successfully',
                'withdrawal' => $withdrawal->fresh()->load(['user', 'bankAccount', 'giftItem'])
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Withdrawal not found',
                'error_code' => 'withdrawal_not_found'
            ], 404);
        }
    }
       */

    
    /**
     * List currency conversion rates.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function listConversionRates()
    {
        $rates = CurrencyConversionRate::all();
        
        return response()->json([
            'conversion_rates' => $rates
        ]);
    }
    
    /**
     * Create or update a currency conversion rate.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateConversionRate(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'from_currency' => 'required|string|in:credits,gift',
            'to_currency' => 'required|string|size:3',
            'rate' => 'required|numeric|min:0.000001',
            'is_active' => 'sometimes|boolean',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }
        
        $rate = CurrencyConversionRate::updateOrCreate(
            [
                'from_currency' => $request->input('from_currency'),
                'to_currency' => $request->input('to_currency'),
            ],
            [
                'rate' => $request->input('rate'),
                'is_active' => $request->input('is_active', true),
            ]
        );
        
        return response()->json([
            'message' => 'Conversion rate updated successfully',
            'conversion_rate' => $rate
        ]);
    }
}
