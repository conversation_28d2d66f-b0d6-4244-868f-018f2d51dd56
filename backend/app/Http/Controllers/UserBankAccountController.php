<?php

namespace App\Http\Controllers;

use App\Models\UserBankAccount;
use App\Rules\MalaysianBankAccount;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class UserBankAccountController extends Controller
{
    /**
     * Display a listing of the user's bank accounts.
     */
    public function index(Request $request)
    {
        $accounts = $request->user()->bankAccounts()->with('bank')->get();
        return response()->json($accounts);
    }

    /**
     * Store a newly created bank account in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'malaysian_bank_id' => 'required|exists:malaysian_banks,id',
            'account_number' => [
                'required',
                new MalaysianBankAccount($request->malaysian_bank_id)
            ],
            'account_holder_name' => 'required|string|max:100',
            'is_primary' => 'sometimes|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // If setting as primary, unset other primary accounts
        if ($request->is_primary) {
            $request->user()->bankAccounts()->update(['is_primary' => false]);
        }

        $bankAccount = $request->user()->bankAccounts()->create($request->all());

        return response()->json([
            'message' => 'Bank account added successfully',
            'bank_account' => $bankAccount->load('bank')
        ], 201);
    }

    /**
     * Update the specified bank account in storage.
     */
    public function update(Request $request, $id)
    {
        $bankAccount = $request->user()->bankAccounts()->findOrFail($id);

        $validator = Validator::make($request->all(), [
            'malaysian_bank_id' => 'sometimes|exists:malaysian_banks,id',
            'account_number' => [
                'sometimes',
                new MalaysianBankAccount($request->malaysian_bank_id ?? $bankAccount->malaysian_bank_id)
            ],
            'account_holder_name' => 'sometimes|string|max:100',
            'is_primary' => 'sometimes|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // If setting as primary, unset other primary accounts
        if ($request->is_primary) {
            $request->user()->bankAccounts()->where('id', '!=', $id)->update(['is_primary' => false]);
        }

        $bankAccount->update($request->all());

        return response()->json([
            'message' => 'Bank account updated successfully',
            'bank_account' => $bankAccount->load('bank')
        ]);
    }

    /**
     * Remove the specified bank account from storage.
     */
    public function destroy(Request $request, $id)
    {
        $bankAccount = $request->user()->bankAccounts()->findOrFail($id);
        $bankAccount->delete();

        return response()->json([
            'message' => 'Bank account deleted successfully'
        ]);
    }

    /**
     * Set the specified bank account as primary.
     */
    public function setPrimary(Request $request, $id)
    {
        $bankAccount = $request->user()->bankAccounts()->findOrFail($id);
        
        // Unset other primary accounts
        $request->user()->bankAccounts()->where('id', '!=', $id)->update(['is_primary' => false]);
        
        // Set this account as primary
        $bankAccount->update(['is_primary' => true]);

        return response()->json([
            'message' => 'Primary bank account updated successfully',
            'bank_account' => $bankAccount->load('bank')
        ]);
    }
}
