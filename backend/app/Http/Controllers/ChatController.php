<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Rules\CleanContent;
use App\Services\ChatService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class ChatController extends Controller
{
    protected $chatService;
    
    public function __construct(ChatService $chatService)
    {
        $this->chatService = $chatService;
    }
    
    /**
     * Get user conversations
     */
    public function getConversations(Request $request)
    {
        $user = Auth::user();
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 15);
        
        try {
            $conversations = $this->chatService->getUserConversations($user->id, $page, $perPage);
            
            return response()->json([
                'success' => true,
                'data' => $conversations,
            ])->header('Content-Type', 'application/json');
        } catch (\Exception $e) {
            Log::error('Failed to get user conversations', [
                'user_id' => $user->id,
                'error_code' => $e->getCode(),
                'error_message' => $e->getMessage(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'timestamp' => now()->toIso8601String()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to get conversations',
                'error' => app()->environment('production') ? 'Server error' : $e->getMessage(),
            ], 500)->header('Content-Type', 'application/json');
        }
    }
    
    /**
     * Get conversation messages
     */
    public function getMessages(Request $request, $conversationId)
    {
        $user = Auth::user();
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 20);
        
        try {
            $messages = $this->chatService->getConversationMessages(
                $conversationId, 
                $user->id, 
                $page, 
                $perPage
            );
            
            return response()->json([
                'success' => true,
                'data' => $messages,
            ])->header('Content-Type', 'application/json');
        } catch (\Exception $e) {
            Log::error('Failed to get conversation messages', [
                'user_id' => $user->id,
                'conversation_id' => $conversationId,
                'error_code' => $e->getCode(),
                'error_message' => $e->getMessage(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'timestamp' => now()->toIso8601String()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to get messages',
                'error' => app()->environment('production') ? 'Not found' : $e->getMessage(),
            ], 404)->header('Content-Type', 'application/json');
        }
    }
    
    /**
     * Start a new conversation
     */
    public function startConversation(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'talent_id' => 'required|exists:users,id',
            'order_id' => 'nullable|exists:orders,id',
            'message' => ['required', 'string', 'max:1000', new CleanContent],
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422)->header('Content-Type', 'application/json');
        }
        
        $user = Auth::user();
        $talentId = $request->input('talent_id');
        $orderId = $request->input('order_id');
        $messageContent = $request->input('message');
        
        try {
            $talent = User::findOrFail($talentId);
            
            $conversation = $this->chatService->getOrCreateConversation($user->id, $talentId, $orderId);
            
            $message = $this->chatService->sendTextMessage($conversation->id, $user->id, $messageContent);
            
            return response()->json([
                'success' => true,
                'data' => [
                    'conversation' => $conversation,
                    'message' => $message,
                ],
            ])->header('Content-Type', 'application/json');
        } catch (\Exception $e) {
            Log::error('Failed to start conversation', [
                'user_id' => $user->id,
                'talent_id' => $talentId,
                'order_id' => $orderId,
                'error_code' => $e->getCode(),
                'error_message' => $e->getMessage(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'timestamp' => now()->toIso8601String()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to start conversation',
                'error' => app()->environment('production') ? 'Server error' : $e->getMessage(),
            ], 500)->header('Content-Type', 'application/json');
        }
    }
    
    /**
     * Send a text message
     */
    public function sendTextMessage(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'conversation_id' => 'required|exists:chat_conversations,id',
            'message' => ['required', 'string', 'max:1000', new CleanContent],
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422)->header('Content-Type', 'application/json');
        }
        
        $user = Auth::user();
        $conversationId = $request->input('conversation_id');
        $messageContent = $request->input('message');
        
        try {
            $message = $this->chatService->sendTextMessage($conversationId, $user->id, $messageContent);
            
            return response()->json([
                'success' => true,
                'data' => $message,
            ])->header('Content-Type', 'application/json');
        } catch (\Exception $e) {
            Log::error('Failed to send text message', [
                'user_id' => $user->id,
                'conversation_id' => $conversationId,
                'error_code' => $e->getCode(),
                'error_message' => $e->getMessage(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'timestamp' => now()->toIso8601String()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to send message',
                'error' => app()->environment('production') ? 'Server error' : $e->getMessage(),
            ], 500)->header('Content-Type', 'application/json');
        }
    }
    
    /**
     * Send a message with attachment
     */
    public function sendMessageWithAttachment(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'conversation_id' => 'required|exists:chat_conversations,id',
            'message' => ['nullable', 'string', 'max:1000', new CleanContent],
            'attachment' => 'required|file|mimes:jpeg,png,jpg,gif,webp,heic,heif|max:10240',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422)->header('Content-Type', 'application/json');
        }
        
        $user = Auth::user();
        $conversationId = $request->input('conversation_id');
        $messageContent = $request->input('message', '');
        $file = $request->file('attachment');
        
        try {
            $message = $this->chatService->sendMessageWithAttachment(
                $conversationId, 
                $user->id, 
                $messageContent, 
                $file
            );
            
            return response()->json([
                'success' => true,
                'data' => $message,
            ])->header('Content-Type', 'application/json');
        } catch (\Exception $e) {
            Log::error('Failed to send message with attachment', [
                'user_id' => $user->id,
                'conversation_id' => $conversationId,
                'error_code' => $e->getCode(),
                'error_message' => $e->getMessage(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'timestamp' => now()->toIso8601String()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to send message with attachment',
                'error' => app()->environment('production') ? 'Server error' : $e->getMessage(),
            ], 500)->header('Content-Type', 'application/json');
        }
    }
    
    /**
     * Update message status
     */
    public function updateMessageStatus(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'message_id' => 'required|exists:chat_messages,id',
            'status' => 'required|in:delivered,read',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422)->header('Content-Type', 'application/json');
        }
        
        $user = Auth::user();
        $messageId = $request->input('message_id');
        $status = $request->input('status');
        
        try {
            $messageStatus = $this->chatService->updateMessageStatus($messageId, $user->id, $status);
            
            return response()->json([
                'success' => true,
                'data' => $messageStatus,
            ])->header('Content-Type', 'application/json');
        } catch (\Exception $e) {
            Log::error('Failed to update message status', [
                'user_id' => $user->id,
                'message_id' => $messageId,
                'status' => $status,
                'error_code' => $e->getCode(),
                'error_message' => $e->getMessage(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'timestamp' => now()->toIso8601String()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to update message status',
                'error' => app()->environment('production') ? 'Server error' : $e->getMessage(),
            ], 500)->header('Content-Type', 'application/json');
        }
    }
}
