<?php

namespace App\Http\Controllers;

use App\Models\Race;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;

class RaceController extends Controller
{
    /**
     * Get all active races
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $locale = App::getLocale();
        
        $races = Race::with(['translations' => function ($query) use ($locale) {
            $query->where('locale', $locale);
        }])
        ->where('is_active', true)
        ->orderBy('display_order')
        ->get();
        
        $formattedRaces = $races->map(function ($race) use ($locale) {
            $translation = $race->translations->first();
            
            return [
                'id' => $race->id,
                'name' => $translation ? $translation->name : $race->name,
                'description' => $translation ? $translation->description : $race->description,
            ];
        });
        
        return response()->json($formattedRaces);
    }
}
