<?php

namespace App\Http\Controllers;

use App\Models\ServiceCategory;
use App\Models\ServiceCategoryTranslation;
use App\Models\ServiceType;
use App\Models\ServiceTypeTranslation;
use App\Models\ServiceStyle;
use App\Models\ServiceStyleTranslation;
use App\Models\PricingOptionGroup;
use App\Models\PricingOptionGroupTranslation;
use App\Models\PricingOptionType;
use App\Models\PricingOptionTypeTranslation;
use App\Models\PricingOption;
use App\Models\PricingOptionTranslation;
use App\Models\User;
use App\Models\UserLevel;
use App\Rules\CleanContent;
use App\Services\ImageProcessingService;
use App\Services\InputSanitizationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;

class BackOfficeServiceController extends Controller
{
    // Controller implementation will be added in parts due to size limitations
    // This is part 1 - Service Category methods
    
    protected $imageProcessingService;
    protected $inputSanitizationService;
    
    public function __construct(
        ImageProcessingService $imageProcessingService,
        InputSanitizationService $inputSanitizationService
    ) {
        $this->imageProcessingService = $imageProcessingService;
        $this->inputSanitizationService = $inputSanitizationService;
    }
    
    /**
     * List all service categories.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function listServiceCategories(Request $request)
    {
        $query = ServiceCategory::with('translations');
        
        // Apply filters if provided
        if ($request->has('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }
        
        // Apply sorting
        $sortField = $request->input('sort_by', 'display_order');
        $sortDirection = $request->input('sort_direction', 'asc');
        $query->orderBy($sortField, $sortDirection);
        
        // Include soft deleted items if requested
        if ($request->boolean('with_trashed')) {
            $query->withTrashed();
        }
        
        // Paginate results
        $perPage = $request->input('per_page', 15);
        $categories = $query->paginate($perPage);
        
        // Transform the collection to include translations for the current locale
        $locale = App::getLocale();
        $categories->getCollection()->transform(function ($category) use ($locale) {
            $translation = $category->translations->where('locale', $locale)->first();
            
            return [
                'id' => $category->id,
                'name' => $translation ? $translation->name : $category->name,
                'slug' => $category->slug,
                'description' => $translation ? $translation->description : $category->description,
                'is_active' => $category->is_active,
                'display_order' => $category->display_order,
                'created_at' => $category->created_at,
                'updated_at' => $category->updated_at,
                'deleted_at' => $category->deleted_at,
                'translations' => $category->translations,
            ];
        });
        
        return response()->json($categories);
    }
    
    /**
     * Create a new service category.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createServiceCategory(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => ['required', 'string', 'max:255', new CleanContent],
            'description' => ['nullable', 'string', new CleanContent],
            'is_active' => 'boolean',
            'display_order' => 'integer',
            'translations' => 'array',
            'translations.*.locale' => ['required', 'string', 'size:5'],
            'translations.*.name' => ['required', 'string', 'max:255', new CleanContent],
            'translations.*.description' => ['nullable', 'string', new CleanContent],
        ]);
        
        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }
        
        try {
            DB::beginTransaction();
            
            // Create the service category
            $category = ServiceCategory::create([
                'name' => $this->inputSanitizationService->sanitize($request->input('name')),
                'description' => $request->has('description') ? $this->inputSanitizationService->sanitize($request->input('description')) : null,
                'is_active' => $request->input('is_active', true),
                'display_order' => $request->input('display_order', 0),
            ]);
            
            // Create translations if provided
            if ($request->has('translations')) {
                foreach ($request->input('translations') as $translationData) {
                    $category->translations()->create([
                        'locale' => $translationData['locale'],
                        'name' => $this->inputSanitizationService->sanitize($translationData['name']),
                        'description' => isset($translationData['description']) ? $this->inputSanitizationService->sanitize($translationData['description']) : null,
                    ]);
                }
            }
            
            DB::commit();
            
            // Load translations relationship
            $category->load('translations');
            
            return response()->json($category, 201);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to create service category', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);
            
            return response()->json(['error' => 'Failed to create service category'], 500);
        }
    }
    
    /**
     * Get a specific service category.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getServiceCategory($id)
    {
        $category = ServiceCategory::with('translations')->find($id);
        
        if (!$category) {
            return response()->json(['error' => 'Service category not found'], 404);
        }
        
        return response()->json($category);
    }
    
    /**
     * Update a service category.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateServiceCategory(Request $request, $id)
    {
        $category = ServiceCategory::find($id);
        
        if (!$category) {
            return response()->json(['error' => 'Service category not found'], 404);
        }
        
        $validator = Validator::make($request->all(), [
            'name' => ['sometimes', 'string', 'max:255', new CleanContent],
            'description' => ['nullable', 'string', new CleanContent],
            'is_active' => 'sometimes|boolean',
            'display_order' => 'sometimes|integer',
            'translations' => 'sometimes|array',
            'translations.*.locale' => ['required', 'string', 'size:5'],
            'translations.*.name' => ['required', 'string', 'max:255', new CleanContent],
            'translations.*.description' => ['nullable', 'string', new CleanContent],
        ]);
        
        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }
        
        try {
            DB::beginTransaction();
            
            // Update the service category
            if ($request->has('name')) {
                $category->name = $this->inputSanitizationService->sanitize($request->input('name'));
            }
            
            if ($request->has('description')) {
                $category->description = $this->inputSanitizationService->sanitize($request->input('description'));
            }
            
            if ($request->has('is_active')) {
                $category->is_active = $request->boolean('is_active');
            }
            
            if ($request->has('display_order')) {
                $category->display_order = $request->input('display_order');
            }
            
            $category->save();
            
            // Update translations if provided
            if ($request->has('translations')) {
                foreach ($request->input('translations') as $translationData) {
                    $translation = $category->translations()
                        ->where('locale', $translationData['locale'])
                        ->first();
                    
                    if ($translation) {
                        $translation->update([
                            'name' => $this->inputSanitizationService->sanitize($translationData['name']),
                            'description' => isset($translationData['description']) ? $this->inputSanitizationService->sanitize($translationData['description']) : null,
                        ]);
                    } else {
                        $category->translations()->create([
                            'locale' => $translationData['locale'],
                            'name' => $this->inputSanitizationService->sanitize($translationData['name']),
                            'description' => isset($translationData['description']) ? $this->inputSanitizationService->sanitize($translationData['description']) : null,
                        ]);
                    }
                }
            }
            
            DB::commit();
            
            // Load translations relationship
            $category->load('translations');
            
            return response()->json($category);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update service category', [
                'error' => $e->getMessage(),
                'category_id' => $id,
                'request' => $request->all()
            ]);
            
            return response()->json(['error' => 'Failed to update service category'], 500);
        }
    }
    
    /**
     * Delete a service category.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteServiceCategory($id)
    {
        $category = ServiceCategory::find($id);
        
        if (!$category) {
            return response()->json(['error' => 'Service category not found'], 404);
        }
        
        try {
            $category->delete();
            
            return response()->json(['message' => 'Service category deleted successfully']);
        } catch (\Exception $e) {
            Log::error('Failed to delete service category', [
                'error' => $e->getMessage(),
                'category_id' => $id
            ]);
            
            return response()->json(['error' => 'Failed to delete service category'], 500);
        }
    }
    
    /**
     * Restore a soft-deleted service category.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function restoreServiceCategory($id)
    {
        $category = ServiceCategory::withTrashed()->find($id);
        
        if (!$category) {
            return response()->json(['error' => 'Service category not found'], 404);
        }
        
        if (!$category->trashed()) {
            return response()->json(['error' => 'Service category is not deleted'], 400);
        }
        
        try {
            $category->restore();
            
            return response()->json(['message' => 'Service category restored successfully']);
        } catch (\Exception $e) {
            Log::error('Failed to restore service category', [
                'error' => $e->getMessage(),
                'category_id' => $id
            ]);
            
            return response()->json(['error' => 'Failed to restore service category'], 500);
        }
    }
}
