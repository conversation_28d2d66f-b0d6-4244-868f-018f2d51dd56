<?php

namespace App\Http\Controllers;

use App\Http\Requests\EKyc\MalaysianVerificationRequest;
use App\Http\Requests\EKyc\ForeignerVerificationRequest;
use App\Models\EKycVerification;
use App\Services\EKycServiceFactory;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class EKycController extends Controller
{
    protected $ekycServiceFactory;

    public function __construct(EKycServiceFactory $ekycServiceFactory)
    {
        $this->ekycServiceFactory = $ekycServiceFactory;
    }

    /**
     * Verify Malaysian citizen identity.
     *
     * @param MalaysianVerificationRequest $request
     * @return JsonResponse
     */
    public function verifyMalaysian(MalaysianVerificationRequest $request): JsonResponse
    {
        $method = $request->input('verification_method', 'manual');
        
        if (!$this->ekycServiceFactory->isMethodEnabled($method)) {
            return response()->json([
                'message' => "The selected verification method '{$method}' is currently disabled"
            ], 403);
        }
        
        $ekycService = $this->ekycServiceFactory->getService($method);
        
        if (!$ekycService->isVerificationTypeEnabled('malaysian')) {
            return response()->json([
                'message' => 'Malaysian E-KYC verification is currently disabled'
            ], 403);
        }

        $user = auth('api')->user();

        $result = $ekycService->verifyMalaysianIdentity(
            $user->id,
            $request->input('full_name'),
            $request->input('ic_number'),
            $request->file('front_ic_photo'),
            $request->file('back_ic_photo'),
            $request->file('selfie_photo')
        );

        return response()->json($result);
    }

    /**
     * Verify foreigner identity.
     *
     * @param ForeignerVerificationRequest $request
     * @return JsonResponse
     */
    public function verifyForeigner(ForeignerVerificationRequest $request): JsonResponse
    {
        $method = $request->input('verification_method', 'manual');
        
        if (!$this->ekycServiceFactory->isMethodEnabled($method)) {
            return response()->json([
                'message' => "The selected verification method '{$method}' is currently disabled"
            ], 403);
        }
        
        $ekycService = $this->ekycServiceFactory->getService($method);
        
        if (!$ekycService->isVerificationTypeEnabled('foreigner')) {
            return response()->json([
                'message' => 'Foreigner E-KYC verification is currently disabled'
            ], 403);
        }

        $user = auth('api')->user();

        $result = $ekycService->verifyForeignerIdentity(
            $user->id,
            $request->input('full_name'),
            $request->input('passport_number'),
            $request->input('country'),
            $request->file('selfie_photo'),
            $request->file('passport_photo')
        );

        return response()->json($result);
    }

    /**
     * Get user's verification status.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getVerificationStatus(Request $request): JsonResponse
    {
        $user = auth('api')->user();
        $method = $request->input('verification_method');
        $ekycService = $this->ekycServiceFactory->getService($method);
        $status = $ekycService->getUserVerificationStatus($user->id);

        return response()->json($status);
    }
    
    /**
     * Get available verification methods.
     *
     * @return JsonResponse
     */
    public function getAvailableMethods(): JsonResponse
    {
        $methods = $this->ekycServiceFactory->getAvailableMethods();
        
        return response()->json([
            'methods' => $methods
        ]);
    }

    /**
     * Get verification history for the current user.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getVerificationHistory(Request $request): JsonResponse
    {
        $user = auth('api')->user();
        
        $verifications = EKycVerification::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($verification) {
                return [
                    'id' => $verification->id,
                    'verification_type' => $verification->verification_type,
                    'verification_method' => $verification->verification_method,
                    'status' => $verification->status,
                    'created_at' => $verification->created_at->toDateTimeString(),
                    'verified_at' => $verification->verified_at ? $verification->verified_at->toDateTimeString() : null,
                ];
            });

        return response()->json([
            'verifications' => $verifications
        ]);
    }
    
    /**
     * Approve a manual verification (admin only).
     *
     * @param int $id
     * @return JsonResponse
     */
    public function approveManualVerification(int $id): JsonResponse
    {
        $manualService = $this->ekycServiceFactory->getService('manual');
        $result = $manualService->approveVerification($id);
        
        return response()->json($result, $result['status'] === 'error' ? 500 : 200);
    }
    
    /**
     * Reject a manual verification (admin only).
     *
     * @param int $id
     * @param Request $request
     * @return JsonResponse
     */
    public function rejectManualVerification(int $id, Request $request): JsonResponse
    {
        $request->validate([
            'reason' => 'required|string|max:255'
        ]);
        
        $manualService = $this->ekycServiceFactory->getService('manual');
        $result = $manualService->rejectVerification($id, $request->input('reason'));
        
        return response()->json($result, $result['status'] === 'error' ? 500 : 200);
    }
    
    /**
     * Get pending manual verifications (admin only).
     *
     * @return JsonResponse
     */
    public function getPendingManualVerifications(): JsonResponse
    {
        $verifications = EKycVerification::where('verification_method', 'manual')
            ->where('status', 'pending')
            ->with('user:id,name,email,mobile_number')
            ->orderBy('created_at', 'asc')
            ->get()
            ->map(function ($verification) {
                return [
                    'id' => $verification->id,
                    'user' => $verification->user,
                    'verification_type' => $verification->verification_type,
                    'full_name' => $verification->full_name,
                    'ic_number' => $verification->ic_number,
                    'passport_number' => $verification->passport_number,
                    'country' => $verification->country,
                    'front_ic_photo_path' => $verification->front_ic_photo_path,
                    'back_ic_photo_path' => $verification->back_ic_photo_path,
                    'selfie_photo_path' => $verification->selfie_photo_path,
                    'passport_photo_path' => $verification->passport_photo_path,
                    'created_at' => $verification->created_at->toDateTimeString(),
                ];
            });
            
        return response()->json([
            'verifications' => $verifications
        ]);
    }
}
