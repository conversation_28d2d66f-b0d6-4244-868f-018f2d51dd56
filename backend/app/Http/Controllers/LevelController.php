<?php

namespace App\Http\Controllers;

use App\Services\LevelService;
use App\Models\UserLevel;
use Illuminate\Http\Request;

class LevelController extends Controller
{
    protected $levelService;

    public function __construct(LevelService $levelService)
    {
        $this->levelService = $levelService;
    }

    /**
     * Get all active levels.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getLevels()
    {
        $levels = $this->levelService->getActiveLevels();
        
        return response()->json([
            'levels' => $levels
        ]);
    }

    /**
     * Get user's current level.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUserLevel(Request $request)
    {
        $user = $request->user()->load('level');
        
        return response()->json([
            'level' => $user->level,
            'experience' => $user->experience,
            'next_level' => $this->getNextLevel($user)
        ]);
    }
    
    /**
     * Get experience transaction history for the authenticated user.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getExperienceHistory(Request $request)
    {
        $transactions = $request->user()->experienceTransactions()
            ->orderBy('created_at', 'desc')
            ->paginate($request->input('per_page', 15));
        
        return response()->json([
            'transactions' => $transactions
        ]);
    }
    
    /**
     * Get information about the next level.
     *
     * @param \App\Models\User $user
     * @return array|null
     */
    private function getNextLevel($user)
    {
        $nextLevel = UserLevel::where('min_experience', '>', $user->experience)
            ->where('is_active', true)
            ->orderBy('min_experience', 'asc')
            ->first();
        
        if (!$nextLevel) {
            return null;
        }
        
        return [
            'level' => $nextLevel,
            'experience_needed' => $nextLevel->min_experience - $user->experience
        ];
    }
}
