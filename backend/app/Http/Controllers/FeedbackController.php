<?php

namespace App\Http\Controllers;

use App\Models\FeedbackSubmission;
use App\Models\FeedbackTopic;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class FeedbackController extends Controller
{
    public function submitFeedback(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'topic_id' => 'required|exists:feedback_topics,id',
            'description' => 'required|string',
            'attachments' => 'nullable|array|max:5',
            'attachments.*' => 'file|mimes:jpeg,png,jpg,gif,pdf,doc,docx,mp4,mov,avi|max:10240',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Check if topic is active
        $topic = FeedbackTopic::where('id', $request->topic_id)
            ->where('is_active', true)
            ->first();

        if (!$topic) {
            return response()->json([
                'message' => 'Invalid or inactive feedback topic'
            ], 422);
        }

        // Handle file uploads
        $attachments = [];
        if ($request->hasFile('attachments')) {
            foreach ($request->file('attachments') as $file) {
                $path = $file->store('feedback_attachments', 'cdn');
                $attachments[] = [
                    'path' => $path,
                    'original_name' => $file->getClientOriginalName(),
                    'mime_type' => $file->getMimeType(),
                    'size' => $file->getSize(),
                ];
            }
        }

        // Create feedback submission
        $submission = FeedbackSubmission::create([
            'user_id' => $request->user()->id,
            'feedback_topic_id' => $request->topic_id,
            'description' => $request->description,
            'attachments' => $attachments,
        ]);

        // Get topic with translations
        $locale = App::getLocale();
        $topicTranslation = $topic->translations->where('locale', $locale)->first();

        return response()->json([
            'message' => 'Feedback submitted successfully',
            'submission' => [
                'id' => $submission->id,
                'user_id' => $submission->user_id,
                'topic' => [
                    'id' => $topic->id,
                    'name' => $topicTranslation ? $topicTranslation->name : $topic->name,
                    'description' => $topicTranslation ? $topicTranslation->description : $topic->description,
                ],
                'description' => $submission->description,
                'attachments' => $submission->attachments,
                'created_at' => $submission->created_at,
                'updated_at' => $submission->updated_at,
            ]
        ], 201);
    }

    public function getTopics()
    {
        $locale = App::getLocale();
        
        return FeedbackTopic::with(['translations' => function ($query) use ($locale) {
            $query->where('locale', $locale);
        }])
        ->where('is_active', true)
        ->orderBy('display_order')
        ->get()
        ->map(function ($topic) {
            $translation = $topic->translations->first();
            return [
                'id' => $topic->id,
                'name' => $translation?->name ?? $topic->name,
                'description' => $translation?->description ?? $topic->description,
            ];
        });
    }
}
