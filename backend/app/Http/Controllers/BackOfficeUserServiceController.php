<?php

namespace App\Http\Controllers;

use App\Models\UserService;
use App\Models\UserServiceRevision;
use App\Services\UserRoleService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class BackOfficeUserServiceController extends Controller
{
    protected $userRoleService;
    
    public function __construct(UserRoleService $userRoleService)
    {
        $this->userRoleService = $userRoleService;
    }
    /**
     * List all user services for back office management.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function listUserServices(Request $request)
    {
        $query = UserService::with(['user', 'serviceCategory', 'serviceType']);
        
        // Filter by service category if provided
        if ($request->has('service_category_id')) {
            $query->where('service_category_id', $request->input('service_category_id'));
        }
        
        // Apply filters if provided
        if ($request->has('status')) {
            $query->where('status', $request->input('status'));
        }
        
        if ($request->has('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }
        
        if ($request->has('user_id')) {
            $query->where('user_id', $request->input('user_id'));
        }
        
        // Include soft deleted services if requested
        if ($request->boolean('with_trashed')) {
            $query->withTrashed();
        }
        
        // Apply sorting
        $sortField = $request->input('sort_by', 'created_at');
        $sortDirection = $request->input('sort_direction', 'desc');
        $query->orderBy($sortField, $sortDirection);
        
        // Paginate results
        $perPage = $request->input('per_page', 15);
        $services = $query->paginate($perPage);
        
        return response()->json($services);
    }
    
    /**
     * Get a specific user service for back office management.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function getUserService($id)
    {
        $service = UserService::with([
            'user', 
            'serviceCategory',
            'serviceType', 
            'pricingOptions.pricingOptionType',
            'revisions'
        ])
        ->withTrashed()
        ->findOrFail($id);
        
        return response()->json($service);
    }
    
    /**
     * Approve a user service.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function approveUserService(Request $request, $id)
    {
        $service = UserService::findOrFail($id);
        
        if (!$service->isPending()) {
            return response()->json([
                'message' => 'Only pending services can be approved',
            ], 422);
        }
        
        DB::beginTransaction();
        
        try {
            // Get the latest pending revision
            $revision = $service->revisions()
                ->where('status', 'pending')
                ->latest()
                ->firstOrFail();
            
            // Update the service with the revision data
            $service->update([
                'title' => $revision->title ?? null,
                'description' => $revision->description,
                'service_elements' => $revision->service_elements,
                'service_type_title' => $revision->service_type_title,
                'service_type_description' => $revision->service_type_description,
                'price' => $revision->price,
                'status' => 'approved',
                'is_active' => true,
                'rejection_reason' => null,
            ]);
            
            // Update the revision status
            $revision->update([
                'status' => 'approved',
            ]);
            
            // Update user role to 'talent' if this is their first approved service
            $roleUpdated = $this->userRoleService->updateRoleOnServiceApproval($service);
            
            DB::commit();
            
            return response()->json([
                'message' => 'Service approved successfully' . ($roleUpdated ? ' and user role updated to talent' : ''),
                'service' => $service->load(['user', 'serviceCategory', 'serviceType', 'pricingOptions.pricingOptionType', 'revisions']),
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['message' => 'Failed to approve service: ' . $e->getMessage()], 500);
        }
    }
    
    /**
     * Reject a user service.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function rejectUserService(Request $request, $id)
    {
        $request->validate([
            'rejection_reason' => 'required|string',
        ]);
        
        $service = UserService::findOrFail($id);
        
        if (!$service->isPending()) {
            return response()->json([
                'message' => 'Only pending services can be rejected',
            ], 422);
        }
        
        DB::beginTransaction();
        
        try {
            // Get the latest pending revision
            $revision = $service->revisions()
                ->where('status', 'pending')
                ->latest()
                ->firstOrFail();
            
            // If this is a new service, mark it as rejected
            if ($service->revisions()->count() === 1) {
                $service->update([
                    'status' => 'rejected',
                    'rejection_reason' => $request->input('rejection_reason'),
                ]);
            } else {
                // If this is an update, keep the service as approved but reject the revision
                $service->update([
                    'status' => 'approved',
                    'rejection_reason' => $request->input('rejection_reason'),
                ]);
            }
            
            // Update the revision status
            $revision->update([
                'status' => 'rejected',
                'rejection_reason' => $request->input('rejection_reason'),
            ]);
            
            DB::commit();
            
            return response()->json([
                'message' => 'Service rejected successfully',
                'service' => $service->load(['user', 'serviceCategory', 'serviceType', 'pricingOptions.pricingOptionType', 'revisions']),
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['message' => 'Failed to reject service: ' . $e->getMessage()], 500);
        }
    }
}
