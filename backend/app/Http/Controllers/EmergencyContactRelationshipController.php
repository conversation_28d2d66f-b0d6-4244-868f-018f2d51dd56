<?php

namespace App\Http\Controllers;

use App\Models\EmergencyContactRelationship;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class EmergencyContactRelationshipController extends Controller
{
    /**
     * Get all active emergency contact relationships with translations.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(): JsonResponse
    {
        try {
            $relationships = EmergencyContactRelationship::where('is_active', true)
                ->orderBy('display_order')
                ->with('translations')
                ->get();

            return response()->json($relationships)
                ->header('Content-Type', 'application/json');
        } catch (\Exception $e) {
            Log::error('Failed to retrieve emergency contact relationships', [
                'error_code' => $e->getCode(),
                'error_message' => $e->getMessage(),
                'error_trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'message' => 'Failed to retrieve emergency contact relationships',
                'error' => $e->getMessage(),
                'error_code' => $e->getCode()
            ], 500)
            ->header('Content-Type', 'application/json');
        }
    }
}
