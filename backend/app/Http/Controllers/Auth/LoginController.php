<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use App\Models\DeviceToken;
use App\Models\User;
use App\Services\DeviceInfoService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;

class LoginController extends Controller
{
    protected $deviceInfoService;

    public function __construct(DeviceInfoService $deviceInfoService)
    {
        $this->deviceInfoService = $deviceInfoService;
    }

    public function login(LoginRequest $request): JsonResponse
    {
        $credentials = [
            'mobile_number' => $request->input('mobile_number'),
            'password' => $request->input('password')
        ];

        if (!$token = auth()->attempt($credentials)) {
            return response()->json(['message' => 'Invalid credentials'], 401);
        }

        // Log device information
        $user = auth()->user();
        $this->deviceInfoService->logLoginInfo($user->id, $request);
        // Store session data in Redis
        // Session::put('user_id', $user->id);
        // Session::put('login_time', now()->toDateTimeString());
        // Session::put('mobile_number', $user->mobile_number);
        // Session::save(); // Ensure session data is saved to Redis

        if ($request->has('device_token')) {
            $this->registerDeviceToken($user->id, $request);
        }

        return response()->json([
            'token' => $token,
            'token_type' => 'bearer',
            'user' => $user
        ])->header('Content-Type', 'application/json');
    }

    public function logout(Request $request): JsonResponse
    {
        if ($request->has('device_token')) {
            $this->removeDeviceToken($request->input('device_token'));
        }
        // Session::invalidate();
        auth()->logout();

        return response()->json(['message' => 'Successfully logged out'])
            ->header('Content-Type', 'application/json');
    }

    /**
     * Register or update a device token for the user
     *
     * @param int $userId
     * @param Request $request
     * @return void
     */
    private function registerDeviceToken(int $userId, Request $request): void
    {
        DeviceToken::updateOrCreate(
            ['token' => $request->input('device_token')],
            [
                'user_id' => $userId,
                'device_type' => $request->input('device_type'),
                'device_name' => $request->input('device_name'),
            ]
        );
    }

    /**
     * Remove a device token
     *
     * @param string $token
     * @return void
     */
    private function removeDeviceToken(string $token): void
    {
        DeviceToken::where('token', $token)->delete();
    }
}
