<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Services\DeviceInfoService;
use App\Services\OAuthService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class OAuthController extends Controller
{
    public function __construct(
        private OAuthService $oauthService,
        private DeviceInfoService $deviceInfoService
    ) {
    }

    public function redirect(Request $request, string $provider)
    {
        return $this->oauthService->redirect($provider);
    }

    public function callback(Request $request, string $provider)
    {
        $userData = $this->oauthService->callback($provider);
        
        $user = User::updateOrCreate(
            ['mobile_number' => $userData['mobile_number']],
            [
                'nickname' => $userData['nickname'],
                'oauth_data' => $userData['oauth_data'],
            ]
        );

        $token = auth()->login($user);
        
        // Log device information
        $this->deviceInfoService->logLoginInfo($user->id, $request);

        return response()->json([
            'token' => $token,
            'token_type' => 'bearer',
            'user' => $user
        ]);
    }
}
