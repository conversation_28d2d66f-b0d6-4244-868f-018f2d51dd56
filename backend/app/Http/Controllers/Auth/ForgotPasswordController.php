<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\VerifyOtpRequest;
use App\Http\Requests\Auth\ResetPasswordRequest;
use App\Models\Otp;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;

class ForgotPasswordController extends Controller
{
    /**
     * Log out user from all devices
     *
     * @param User $user
     * @return void
     */
    private function logoutFromAllDevices(User $user): void
    {
        
        // \App\Models\DeviceToken::where('user_id', $user->id)->delete();
        // 
        // $user->password_changed_at = now();
        // 
        // $user->setRememberToken(null);
        // $user->save();
    }

    /**
     * Verify OTP for password reset
     *
     * @param VerifyOtpRequest $request
     * @return JsonResponse
     */
    public function verifyOtp(VerifyOtpRequest $request): JsonResponse
    {
        $validated = $request->validated();
        
        $user = User::where('mobile_number', $validated['mobile_number'])->first();
        
        if (!$user) {
            return response()->json([
                'message' => 'Mobile number is not registered'
            ], 422)->header('Content-Type', 'application/json');
        }
        
        $otp = Otp::where('mobile_number', $validated['mobile_number'])
            ->where('otp', $validated['otp'])
            ->where('expires_at', '>', now())
            ->first();

        if (!$otp) {
            return response()->json([
                'message' => 'Invalid or expired OTP'
            ], 422)->header('Content-Type', 'application/json');
        }

        // session()->put('password_reset_user_id', $user->id);
        // session()->put('password_reset_verified', true);
        
        // Mark this OTP as verified for password reset
        $otp->verified_for_password_reset = true;
        $otp->save();
        
        return response()->json([
            'message' => 'OTP verified successfully. You can now reset your password.'
        ])->header('Content-Type', 'application/json');
    }

    /**
     * Reset password with new password
     *
     * @param ResetPasswordRequest $request
     * @return JsonResponse
     */
    public function resetPassword(ResetPasswordRequest $request): JsonResponse
    {
        $validated = $request->validated();
        
        // if (!session()->has('password_reset_verified') || !session()->has('password_reset_user_id')) {
        //     return response()->json([
        //         'message' => 'Please verify OTP before resetting password'
        //     ], 422)->header('Content-Type', 'application/json');
        // }
        // 
        // $userId = session()->get('password_reset_user_id');
        // $user = User::find($userId);

        if (!$request->has('mobile_number')) {
            return response()->json([
                'message' => 'Mobile number is required'
            ], 422)->header('Content-Type', 'application/json');
        }

        $otp = Otp::where('mobile_number', $request->mobile_number)
            ->where('verified_for_password_reset', true)
            ->where('expires_at', '>', now())
            ->first();

        if (!$otp) {
            return response()->json([
                'message' => 'Please verify OTP before resetting password'
            ], 422)->header('Content-Type', 'application/json');
        }

        $user = User::where('mobile_number', $request->mobile_number)->first();
        
        if (!$user) {
            return response()->json([
                'message' => 'User not found'
            ], 404)->header('Content-Type', 'application/json');
        }
        
        if (Hash::check($validated['password'], $user->password)) {
            return response()->json([
                'message' => 'New password cannot be the same as the old password'
            ], 422)->header('Content-Type', 'application/json');
        }
        
        $user->password = $validated['password'];
        $user->save();
        
        // session()->forget(['password_reset_verified', 'password_reset_user_id']);
        
        Otp::where('mobile_number', $user->mobile_number)
            ->update([
                'verified_for_password_reset' => false,
                'expires_at' => now()->subMinutes(5)
            ]);
            
        $this->logoutFromAllDevices($user);
        
        return response()->json([
            'message' => 'Password has been successfully reset'
        ])->header('Content-Type', 'application/json');
    }
}
