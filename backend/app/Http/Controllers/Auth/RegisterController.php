<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\RegisterRequest;
use App\Http\Requests\Auth\VerifyOtpRequest;
use App\Http\Requests\Auth\ResendOtpRequest;
use App\Models\{User, Otp, DeviceToken};
use App\Services\DeviceInfoService;
use App\Services\ImageProcessingService;
use App\Services\ReferralService;
use App\Services\ThreeSixtyMySmsService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class RegisterController extends Controller
{
    public function __construct(
        private ThreeSixtyMySmsService $smsService,
        private DeviceInfoService $deviceInfoService,
        private ReferralService $referralService,
        private ImageProcessingService $imageProcessingService

    ) {}

    public function requestOtp(ResendOtpRequest $request)
    {
        $validated = $request->validated();

        // Generate OTP
        $otp = str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);

        // Store OTP
        Otp::create([
            'mobile_number' => $validated['mobile_number'],
            'otp' => $otp,
            'expires_at' => now()->addMinutes(5),
            'last_sent_at' => now(),
        ]);

        // Send OTP via SMS
        $result = $this->smsService->sendOtp($validated['mobile_number'], $otp);

        // Update OTP record with status code and message ID
        Otp::where('mobile_number', $validated['mobile_number'])
            ->where('otp', $otp)
            ->update([
                'status_code' => $result['status_code'],
                'message_id' => $result['message_id'] ?? null
            ]);

        return $result['success']
            ? response()->json(['message' => 'OTP sent successfully'])
            ->header('Content-Type', 'application/json')
            : response()->json([
                'message' => 'Failed to send OTP',
                'error' => $result['message']
            ], 500)->header('Content-Type', 'application/json');
    }

    public function register(RegisterRequest $request): JsonResponse
    {
        $validated = $request->validated();

        // Verify OTP
        $otp = Otp::where('mobile_number', $validated['mobile_number'])
            ->where('otp', $validated['otp'])
            ->where('expires_at', '>', now())
            ->first();

        if (!$otp) {
            return response()->json([
                'message' => 'Invalid or expired OTP'
            ], 422)->header('Content-Type', 'application/json');
        }

        // Create user
        $user = User::create([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'mobile_number' => $validated['mobile_number'],
            'country_code' => $validated['country_code'],
            'nickname' => $validated['nickname'],
            'gender' => $validated['gender'],
            'date_of_birth' => $validated['date_of_birth'],
            'password' => $validated['password'],
            'referral_type' => $validated['referral_type'] ?? null,
            'referral_value' => $validated['referral_value'] ?? null,
        ]);


        // Process profile picture after user creation
        if ($request->hasFile('profile_picture')) {
            $profilePicturePath = $this->processProfilePicture($request->file('profile_picture'), $user->id);
            if ($profilePicturePath) {
                $user->update(['profile_picture' => $profilePicturePath]);
            }
        }

        // Generate token
        $token = auth()->login($user);

        // Log device information
        $this->deviceInfoService->logLoginInfo($user->id, $request);

        if ($request->has('device_token')) {
            $this->registerDeviceToken($user->id, $request);
        }

        if (isset($validated['referral_type']) && isset($validated['referral_value'])) {
            $referrerPhoneNumber = $validated['referral_type'] === 'phone' ? $validated['referral_value'] : null;
            $referralCode = $validated['referral_type'] === 'code' ? $validated['referral_value'] : null;

            try {
                $this->referralService->processReferral($user, $referrerPhoneNumber, $referralCode);
            } catch (\Exception $e) {
                Log::error('Error processing referral:', [
                    'error' => $e->getMessage(),
                    'user_id' => $user->id
                ]);
            }
        }

        // $otp->delete();

        return response()->json([
            'token' => $token,
            'token_type' => 'bearer',
            'user' => $user
        ])->header('Content-Type', 'application/json');
    }

    public function verifyOtp(VerifyOtpRequest $request)
    {
        $validated = $request->validated();

        $otp = Otp::where('mobile_number', $validated['mobile_number'])
            ->where('otp', $validated['otp'])
            ->where('expires_at', '>', now())
            ->first();

        if (!$otp) {
            return response()->json([
                'message' => 'Invalid or expired OTP'
            ], 422)->header('Content-Type', 'application/json');
        }

        $registrationData = cache()->get('registration_' . $validated['mobile_number']);

        if (!$registrationData) {
            return response()->json([
                'message' => 'Registration session expired'
            ], 422)->header('Content-Type', 'application/json');
        }

        // Create user
        $user = User::create($registrationData);

        // Generate token
        $token = auth()->login($user);

        // Log device information
        $this->deviceInfoService->logLoginInfo($user->id, $request);

        if ($request->has('device_token')) {
            $this->registerDeviceToken($user->id, $request);
        }

        if (isset($registrationData['referral_type']) && isset($registrationData['referral_value'])) {
            $referrerPhoneNumber = $registrationData['referral_type'] === 'phone' ? $registrationData['referral_value'] : null;
            $referralCode = $registrationData['referral_type'] === 'code' ? $registrationData['referral_value'] : null;

            try {
                $this->referralService->processReferral($user, $referrerPhoneNumber, $referralCode);
            } catch (\Exception $e) {
                Log::error('Error processing referral:', [
                    'error' => $e->getMessage(),
                    'user_id' => $user->id
                ]);
            }
        }

        // Clear session
        session()->forget('registration_data');

        // $otp->delete();

        return response()->json([
            'token' => $token,
            'token_type' => 'bearer',
            'user' => $user
        ])->header('Content-Type', 'application/json');
    }

    public function resendOtp(ResendOtpRequest $request)
    {
        $validated = $request->validated();

        // Generate new OTP
        $otp = str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);

        // Store OTP
        Otp::create([
            'mobile_number' => $validated['mobile_number'],
            'otp' => $otp,
            'expires_at' => now()->addMinutes(5),
            'last_sent_at' => now(),
        ]);

        // Send OTP via SMS
        $result = $this->smsService->sendOtp($validated['mobile_number'], $otp);

        // Update OTP record with status code and message ID
        Otp::where('mobile_number', $validated['mobile_number'])
            ->where('otp', $otp)
            ->update([
                'status_code' => $result['status_code'],
                'message_id' => $result['message_id'] ?? null
            ]);

        if (!$result['success']) {
            return response()->json([
                'message' => 'Failed to send OTP. Please try again.',
                'error' => $result['message']
            ], 500)->header('Content-Type', 'application/json');
        }

        return response()->json([
            'message' => 'OTP sent successfully'
        ])->header('Content-Type', 'application/json');
    }

    /**
     * Register or update a device token for the user
     *
     * @param int $userId
     * @param Request $request
     * @return void
     */
    private function registerDeviceToken(int $userId, Request $request): void
    {
        DeviceToken::updateOrCreate(
            ['token' => $request->input('device_token')],
            [
                'user_id' => $userId,
                'device_type' => $request->input('device_type'),
                'device_name' => $request->input('device_name'),
            ]
        );
    }

    /**
     * Process profile picture upload using ImageProcessingService
     *
     * @param \Illuminate\Http\UploadedFile $file
     * @param int $userId
     * @return string|null
     */
    private function processProfilePicture($file, int $userId): ?string
    {
        try {
            if (!$this->imageProcessingService->validateImageContent($file)) {
                Log::error('Invalid image content detected during registration', [
                    'user_id' => $userId,
                    'file_name' => $file->getClientOriginalName()
                ]);
                return null;
            }

            if ($this->imageProcessingService->isMobileImageFormat($file)) {
                if (in_array(strtolower($file->getClientOriginalExtension()), ['heic', 'heif'])) {
                    $result = $this->imageProcessingService->processMobileImage($file, $userId, 'users');
                } else {
                    $result = $this->imageProcessingService->processWebpImage($file, $userId, 'users');
                }
            } else {
                $result = $this->imageProcessingService->processImage($file, $userId, 'users');
            }

            $extension = pathinfo($result['optimized'], PATHINFO_EXTENSION);
            $profilePhotoPath = "users/{$userId}/profile_photo.{$extension}";

            Storage::disk('cdn')->copy($result['optimized'], $profilePhotoPath);

            Storage::disk('cdn')->delete($result['optimized']);



            return $profilePhotoPath;
        } catch (\Exception $e) {
            Log::error('Error processing profile picture during registration', [
                'user_id' => $userId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }
}
