<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\PricingOptionGroup;
use App\Models\ServiceCategory;
use App\Models\ServiceLink;
use App\Models\ServiceStyle;
use App\Models\ServiceType;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class ServiceConfigurationController extends Controller
{
    /**
     * Get all active service categories
     *
     * @return JsonResponse
     */
    public function getCategories(): JsonResponse
    {
        $categories = ServiceCategory::where('is_active', true)
            ->orderBy('display_order')
            ->get();

        return response()->json($categories);
    }

    /**
     * Get service types filtered by category
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getTypes(Request $request): JsonResponse
    {
        $query = ServiceType::query()->where('is_active', true);

        if ($request->has('category_id')) {
            $query->where('service_category_id', $request->category_id);
        }

        $types = $query->orderBy('display_order')->get();

        return response()->json($types);
    }

    /**
     * Get service styles filtered by type
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getStyles(Request $request): JsonResponse
    {
        $query = ServiceStyle::query()->where('is_active', true);

        if ($request->has('type_id')) {
            $query->where('service_type_id', $request->type_id);
        }

        $styles = $query->orderBy('display_order')->get();
        
        $styles->transform(function ($style) {
            return [
                'id' => $style->id,
                'service_type_id' => $style->service_type_id,
                'name' => $style->name,
                'description' => $style->description,
                'is_active' => $style->is_active,
                'display_order' => $style->display_order,
                'min_level_id' => $style->min_level_id,
                'can_bypass' => $style->can_bypass,
                'recommended_price' => $style->recommended_price,
                'preset_price' => $style->preset_price,
                'created_at' => $style->created_at,
                'updated_at' => $style->updated_at,
            ];
        });

        return response()->json($styles);
    }

    /**
     * Get pricing option groups filtered by service style
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getPricingOptionGroups(Request $request): JsonResponse
    {
        $query = PricingOptionGroup::query()->where('is_active', true);

        if ($request->has('style_id')) {
            $styleId = $request->style_id;

            $query->whereHas('serviceLinks', function($q) use ($styleId) {
                $q->where('service_style_id', $styleId);
            });
        }

        $groups = $query->orderBy('display_order')->get();

        return response()->json($groups);
    }

    /**
     * Get service links with optional filtering
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getServiceLinks(Request $request): JsonResponse
    {
        $query = ServiceLink::query();

        if ($request->has('category_id')) {
            $query->where('service_category_id', $request->category_id);
        }

        if ($request->has('type_id')) {
            $query->where('service_type_id', $request->type_id);
        }

        if ($request->has('style_id')) {
            $query->where('service_style_id', $request->style_id);
        }

        if ($request->has('group_id')) {
            $query->where('pricing_option_group_id', $request->group_id);
        }

        $links = $query->with([
            'serviceCategory',
            'serviceType',
            'serviceStyle',
            'pricingOptionGroup'
        ])->get();

        return response()->json($links);
    }
}
