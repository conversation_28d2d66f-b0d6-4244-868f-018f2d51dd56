<?php

namespace App\Http\Controllers;

use App\Models\Mission;
use App\Models\MissionApplicant;
use App\Rules\CleanContent;
use App\Services\MissionCreditService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class MissionApplicantController extends Controller
{
    /**
     * Apply for a mission.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $mission_id
     * @return \Illuminate\Http\JsonResponse
     */
    public function apply(Request $request, $mission_id)
    {
        $mission = Mission::findOrFail($mission_id);
        
        // Check if mission is open for applications
        if (!$mission->isPending()) {
            return response()->json([
                'message' => 'This mission is not accepting applications',
            ], 422);
        }
        
        // Check if user already applied
        $existingApplication = MissionApplicant::where('mission_id', $mission->id)
            ->where('user_id', $request->user()->id)
            ->first();
            
        if ($existingApplication) {
            return response()->json([
                'message' => 'You have already applied for this mission',
            ], 422);
        }
        
        // Check if user meets minimum level requirement
        $userLevel = $request->user()->level()->first();
        if (!$userLevel || $userLevel->level < $mission->minLevel->level) {
            return response()->json([
                'message' => 'You do not meet the minimum level requirement for this mission',
            ], 422);
        }
        
        $latestChildId = MissionApplicant::where('mission_id', $mission->id)
            ->max('child_id') ?? 0;
            
        // Create application with sequential child_id
        $application = MissionApplicant::create([
            'mission_id' => $mission->id,
            'user_id' => $request->user()->id,
            'status' => 'pending',
            'notes' => $request->input('notes'),
            'child_id' => $latestChildId + 1,
        ]);
        
        return response()->json([
            'message' => 'Application submitted successfully',
            'application' => $application,
        ], 201);
    }
    
    /**
     * Get applicants for a mission.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $mission_id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getApplicants(Request $request, $mission_id)
    {
        $mission = Mission::findOrFail($mission_id);
        
        $this->authorize('manageApplicants', $mission);
        
        $applicants = $mission->applicants()->with('user')->get();
        
        return response()->json($applicants);
    }
    
    /**
     * Approve an applicant.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $mission_id
     * @param  int  $child_id
     * @return \Illuminate\Http\JsonResponse
     */
    public function approveApplicant(Request $request, $mission_id, $child_id)
    {
        $mission = Mission::findOrFail($mission_id);
        
        $this->authorize('manageApplicants', $mission);
        
        $applicant = MissionApplicant::where('mission_id', $mission_id)
            ->where('child_id', $child_id)
            ->first();
        
        if (!$applicant) {
            return response()->json([
                'message' => 'Applicant not found for this mission',
            ], 404);
        }
        
        // Check if application is already approved or rejected
        if (!$applicant->isPending()) {
            return response()->json([
                'message' => 'This application has already been processed',
            ], 422);
        }
        
        // Check if mission is still open
        if (!$mission->isPending()) {
            return response()->json([
                'message' => 'Cannot approve applicants for a closed or completed mission',
            ], 422);
        }
        
        $talent = $mission->user;
        
        // Count approved applicants
        $approvedCount = MissionApplicant::where('mission_id', $mission_id)
            ->where('status', 'approved')
            ->count();
            
        // No longer limiting the number of approved applicants
        // Missions can have more approved applicants than pax_required
        
        DB::beginTransaction();
        
        try {
            // Approve the applicant
            $applicant->update([
                'status' => 'approved',
            ]);
            
            app(MissionCreditService::class)->holdCreditsForMission($mission, $applicant);
            
            // If this approval fills the mission, update the mission status
            $newApprovedCount = $approvedCount + 1;
            if ($newApprovedCount >= $mission->pax_required) {
                $mission->update([
                    'status' => 'closed',
                ]);
            }
            
            DB::commit();
            
            return response()->json([
                'message' => 'Applicant approved successfully',
                'mission_status' => $mission->status,
                'approved_count' => $newApprovedCount,
                'required_count' => $mission->pax_required,
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['message' => 'Failed to approve applicant: ' . $e->getMessage()], 500);
        }
    }
    
    /**
     * Reject an applicant.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $mission_id
     * @param  int  $child_id
     * @return \Illuminate\Http\JsonResponse
     */
    public function rejectApplicant(Request $request, $mission_id, $child_id)
    {
        $request->validate([
            'rejection_reason' => ['nullable', 'string', new CleanContent],
        ]);
        
        $mission = Mission::findOrFail($mission_id);
        
        $this->authorize('manageApplicants', $mission);
        
        $applicant = MissionApplicant::where('mission_id', $mission_id)
            ->where('child_id', $child_id)
            ->first();
        
        if (!$applicant) {
            return response()->json([
                'message' => 'Applicant not found for this mission',
            ], 404);
        }
        
        // Check if application is already approved or rejected
        if (!$applicant->isPending()) {
            return response()->json([
                'message' => 'This application has already been processed',
            ], 422);
        }
        
        // Check if mission is still open
        if (!$mission->isPending()) {
            return response()->json([
                'message' => 'Cannot reject applicants for a closed or completed mission',
            ], 422);
        }
        
        // Reject the applicant
        $applicant->update([
            'status' => 'rejected',
            'notes' => $request->input('rejection_reason'),
        ]);
        
        return response()->json([
            'message' => 'Applicant rejected successfully',
        ]);
    }
}
