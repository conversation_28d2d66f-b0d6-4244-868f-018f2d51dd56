<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\UserService;
use App\Models\UserServiceRevision;
use App\Models\UserServicePricingOption;
use App\Models\UserServiceStyle;
use App\Models\ServiceType;
use App\Models\ServiceStyle;
use App\Models\ServiceCategory;
use App\Models\PricingOption;
use App\Models\Order;
use App\Models\ScheduledOrder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class UserServiceController extends Controller
{
    /**
     * Get all services for the authenticated user.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $query = $request->user()->services()
            ->with(['serviceCategory', 'serviceType', 'serviceStyles', 'pricingOptions.pricingOptionType']);

        if ($request->has('status')) {
            $query->where('status', $request->input('status'));
        }

        if ($request->has('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        if ($request->has('service_category_id')) {
            $query->where('service_category_id', $request->input('service_category_id'));
        }

        $services = $query->get();
        
        $services->each(function ($service) {
            if ($service->service_category_id == 2) { // Digital Services (OTHER_CATEGORY_ID)
            } else {
                $service->service_type_title = null;
                $service->service_type_description = null;
                $service->price = null;
            }
            
            if ($service->serviceStyles) {
                $service->service_styles = $service->serviceStyles->map(function ($style) {
                    return [
                        'id' => $style->id,
                        'name' => $style->name,
                        'description' => $style->description,
                        'min_level_id' => $style->min_level_id,
                        'can_bypass' => $style->can_bypass,
                        'recommended_price' => $style->recommended_price,
                        'preset_price' => $style->preset_price,
                        'display_order' => $style->display_order,
                        'created_at' => $style->created_at,
                        'updated_at' => $style->updated_at,
                        'is_active' => (bool) $style->pivot->is_active,
                        'price' => (int) ($style->pivot->price ?? $style->preset_price)
                    ];
                });
            }
            
            if ($service->pricingOptions && $service->pricingOptions->isNotEmpty()) {
                $pricingOption = $service->pricingOptions->first();
                
                if ($pricingOption->pricingOptionType) {
                    $pricingOptionType = $pricingOption->pricingOptionType;
                    $service->pricing_options = [
                        'id' => $pricingOptionType->id,
                        'name' => $pricingOptionType->name,
                        'description' => $pricingOptionType->description,
                        'is_active' => (bool) $pricingOption->is_active,
                        'display_order' => $pricingOptionType->display_order,
                        'created_at' => $pricingOptionType->created_at,
                        'updated_at' => $pricingOptionType->updated_at,
                        'deleted_at' => $pricingOptionType->deleted_at,
                        'has_duration' => (bool) $pricingOptionType->has_duration,
                        'unit' => $pricingOptionType->unit,
                        'quantity' => $pricingOptionType->quantity
                    ];
                }
            }
        });
        
        return response()->json($services);
    }

    /**
     * Get approved services for a user.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $userId
     * @return \Illuminate\Http\Response
     */
    public function getApprovedServices(Request $request, $userId)
    {
        $user = User::findOrFail($userId);

        $query = $user->services()
            ->approved()
            ->with(['serviceType', 'serviceStyles', 'pricingOptions.pricingOptionType']);

        if ($request->has('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        if ($request->has('service_type_id')) {
            $query->where('service_type_id', $request->input('service_type_id'));
        }

        if ($request->has('service_style_id')) {
            $query->where('service_style_id', $request->input('service_style_id'));
        }

        $services = $query->get();

        return response()->json($services);
    }

    /**
     * Store newly created services.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'services' => 'required|array|min:1',
            'services.*.service_category_id' => 'required|in:1,' . ServiceCategory::OTHER_CATEGORY_ID,
            'services.*.service_type_id' => 'required_if:services.*.service_category_id,1|nullable|exists:service_types,id',
            'services.*.pricing_option_id' => 'required|exists:pricing_option_types,id',
            'services.*.service_style' => 'required_if:services.*.service_category_id,1|nullable|array',
            'services.*.service_style.*.service_style_id' => 'required_if:services.*.service_category_id,1|exists:service_styles,id',
            'services.*.service_style.*.is_active' => 'required_if:services.*.service_category_id,1|boolean',
            'services.*.service_style.*.price' => 'nullable|integer|min:0',
            'services.*.service_type_title' => 'required_if:services.*.service_category_id,' . ServiceCategory::OTHER_CATEGORY_ID . '|nullable|string|max:255',
            'services.*.service_type_description' => 'required_if:services.*.service_category_id,' . ServiceCategory::OTHER_CATEGORY_ID . '|nullable|string',
            'services.*.price' => 'required_if:services.*.service_category_id,' . ServiceCategory::OTHER_CATEGORY_ID . '|nullable|integer|min:0',
            'services.*.title' => 'nullable|string|max:255',
            'services.*.description' => 'nullable|string',
            'services.*.service_elements' => 'nullable|array',
        ]);

        DB::beginTransaction();

        try {
            $createdServices = [];

            foreach ($validated['services'] as $serviceData) {
                $serviceAttributes = [
                    'service_category_id' => $serviceData['service_category_id'],
                    'service_type_id' => $serviceData['service_type_id'] ?? null,
                    'title' => $serviceData['title'] ?? null,
                    'description' => $serviceData['description'] ?? null,
                    'service_elements' => $serviceData['service_elements'] ?? null,
                    'status' => 'pending',
                    'is_active' => false,
                ];

                if ($serviceData['service_category_id'] != ServiceCategory::OTHER_CATEGORY_ID) { // Any category except Talent/Other
                    $serviceAttributes['service_style_id'] = null; // Will be replaced by service_styles relationship
                    $serviceAttributes['service_type_title'] = null;
                    $serviceAttributes['service_type_description'] = null;
                    $serviceAttributes['price'] = null;
                } else if ($serviceData['service_category_id'] == ServiceCategory::OTHER_CATEGORY_ID) { // Talent category
                    $serviceAttributes['service_style_id'] = null;
                    $serviceAttributes['service_type_title'] = $serviceData['service_type_title'] ?? null;
                    $serviceAttributes['service_type_description'] = $serviceData['service_type_description'] ?? null;
                    $serviceAttributes['price'] = $serviceData['price'] ?? null;
                } else {
                    throw new \Exception('Invalid service category. Only Gamer (ID 1) and Talent (ID 2) categories are supported.');
                }

                // Create the service
                $service = $request->user()->services()->create($serviceAttributes);

                // Create pricing option using pricing option type
                $pricingOptionType = \App\Models\PricingOptionType::findOrFail($serviceData['pricing_option_id']);
                $service->pricingOptions()->create([
                    'pricing_option_type_id' => $pricingOptionType->id,
                    'is_active' => true,
                ]);

                if (isset($serviceData['service_style']) && is_array($serviceData['service_style'])) {
                    foreach ($serviceData['service_style'] as $styleData) {
                        $price = $styleData['price'] ?? null;

                        if ($price === null && $serviceData['service_category_id'] != ServiceCategory::OTHER_CATEGORY_ID) {
                            $serviceStyle = ServiceStyle::find($styleData['service_style_id']);
                            $price = $serviceStyle ? $serviceStyle->preset_price : null;
                        }

                        $service->serviceStyles()->attach($styleData['service_style_id'], [
                            'is_active' => $styleData['is_active'] ?? false,
                            'price' => $price,
                        ]);
                    }
                }

                // Create initial revision
                $service->revisions()->create([
                    'title' => $serviceData['title'] ?? null,
                    'description' => $serviceData['description'] ?? null,
                    'service_elements' => $serviceData['service_elements'] ?? null,
                    'service_type_title' => $serviceData['service_type_title'] ?? null,
                    'service_type_description' => $serviceData['service_type_description'] ?? null,
                    'price' => $serviceData['price'] ?? null,
                    'status' => 'pending',
                ]);

                $createdServices[] = $service->load(['serviceCategory', 'serviceType', 'pricingOptions.pricingOptionType', 'revisions']);
            }

            DB::commit();

            return response()->json([
                'message' => count($createdServices) > 1
                    ? 'Services created successfully and pending approval'
                    : 'Service created successfully and pending approval',
                'services' => $createdServices,
            ], 201);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['message' => 'Failed to create services: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Display the specified service.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $service = UserService::with(['serviceCategory', 'serviceType', 'pricingOptions.pricingOptionType', 'revisions'])
            ->findOrFail($id);
        
        $this->authorize('view', $service);
        
        $completedOrdersCount = Order::where('user_service_id', $id)
            ->where('status', 'completed')
            ->count();
        
        $completedScheduledOrdersCount = ScheduledOrder::where('user_service_id', $id)
            ->where('status', 'completed')
            ->count();
        
        $serviceData = $service->toArray();
        $serviceData['total_completed_orders'] = $completedOrdersCount + $completedScheduledOrdersCount;
        
        return response()->json($serviceData);
    }

    /**
     * Update the specified services.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
        $validated = $request->validate([
            'services' => 'present|array',
            'services.*.id' => 'nullable|exists:user_services,id', // Changed from 'required' to 'nullable'
            'services.*.service_category_id' => 'required|in:1,' . ServiceCategory::OTHER_CATEGORY_ID,
            'services.*.service_type_id' => 'required_if:services.*.service_category_id,1|nullable|exists:service_types,id',
            'services.*.pricing_option_id' => 'required|exists:pricing_option_types,id',
            'services.*.service_style' => 'required_if:services.*.service_category_id,1|nullable|array',
            'services.*.service_style.*.service_style_id' => 'required_if:services.*.service_category_id,1|exists:service_styles,id',
            'services.*.service_style.*.is_active' => 'required_if:services.*.service_category_id,1|boolean',
            'services.*.service_style.*.price' => 'nullable|integer|min:0',
            'services.*.service_type_title' => 'required_if:services.*.service_category_id,' . ServiceCategory::OTHER_CATEGORY_ID . '|nullable|string|max:255',
            'services.*.service_type_description' => 'required_if:services.*.service_category_id,' . ServiceCategory::OTHER_CATEGORY_ID . '|nullable|string',
            'services.*.price' => 'required_if:services.*.service_category_id,' . ServiceCategory::OTHER_CATEGORY_ID . '|nullable|integer|min:0',
            'services.*.title' => 'nullable|string|max:255',
            'services.*.description' => 'nullable|string',
            'services.*.service_elements' => 'nullable|array',
        ]);

        DB::beginTransaction();

        try {
            $updatedServices = [];

            $existingServiceIds = $request->user()->services()->pluck('id')->toArray();

            $updatedServiceIds = [];

            foreach ($validated['services'] as $serviceData) {
                if (isset($serviceData['id'])) {
                    $service = UserService::findOrFail($serviceData['id']);

                    $this->authorize('update', $service);

                    $updatedServiceIds[] = $service->id;

                    // Create a new revision with all fields
                    $revision = $service->revisions()->create([
                        'title' => $serviceData['title'] ?? null,
                        'description' => $serviceData['description'] ?? null,
                        'service_elements' => $serviceData['service_elements'] ?? null,
                        'service_type_title' => $serviceData['service_type_title'] ?? null,
                        'service_type_description' => $serviceData['service_type_description'] ?? null,
                        'price' => $serviceData['price'] ?? null,
                        'status' => 'pending',
                    ]);

                    $serviceAttributes = [
                        'service_category_id' => $serviceData['service_category_id'],
                        'service_type_id' => $serviceData['service_type_id'] ?? null,
                        'status' => 'pending',
                    ];

                    if ($serviceData['service_category_id'] != ServiceCategory::OTHER_CATEGORY_ID) { // Any category except Talent/Other
                        $serviceAttributes['service_style_id'] = null; // Will be replaced by service_styles relationship
                        $serviceAttributes['service_type_title'] = null;
                        $serviceAttributes['service_type_description'] = null;
                        $serviceAttributes['price'] = null;
                    } else if ($serviceData['service_category_id'] == ServiceCategory::OTHER_CATEGORY_ID) { // Talent category
                        $serviceAttributes['service_style_id'] = null;
                        $serviceAttributes['service_type_title'] = $serviceData['service_type_title'] ?? null;
                        $serviceAttributes['service_type_description'] = $serviceData['service_type_description'] ?? null;
                        $serviceAttributes['price'] = $serviceData['price'] ?? null;
                    } else {
                        throw new \Exception('Invalid service category. Only Gamer (ID 1) and Talent (ID 2) categories are supported.');
                    }

                    $service->update($serviceAttributes);

                    // First, deactivate all existing pricing options
                    $service->pricingOptions()->update(['is_active' => false]);

                    // Then create or update the selected pricing option type
                    $pricingOptionType = \App\Models\PricingOptionType::findOrFail($serviceData['pricing_option_id']);
                    $service->pricingOptions()->updateOrCreate(
                        ['pricing_option_type_id' => $pricingOptionType->id],
                        ['is_active' => true]
                    );

                    if (isset($serviceData['service_style']) && is_array($serviceData['service_style'])) {
                        // First, detach all existing service styles
                        $service->serviceStyles()->detach();

                        foreach ($serviceData['service_style'] as $styleData) {
                            $price = $styleData['price'] ?? null;

                            if ($price === null && $serviceData['service_category_id'] != ServiceCategory::OTHER_CATEGORY_ID) {
                                $serviceStyle = ServiceStyle::find($styleData['service_style_id']);
                                $price = $serviceStyle ? $serviceStyle->preset_price : null;
                            }

                            $service->serviceStyles()->attach($styleData['service_style_id'], [
                                'is_active' => $styleData['is_active'] ?? false,
                                'price' => $price,
                            ]);
                        }
                    }
                } else {
                    // Create new service (reuse logic from store method)
                    $serviceAttributes = [
                        'service_category_id' => $serviceData['service_category_id'],
                        'service_type_id' => $serviceData['service_type_id'] ?? null,
                        'title' => $serviceData['title'] ?? null,
                        'description' => $serviceData['description'] ?? null,
                        'service_elements' => $serviceData['service_elements'] ?? null,
                        'status' => 'pending',
                        'is_active' => false,
                    ];

                    if ($serviceData['service_category_id'] != ServiceCategory::OTHER_CATEGORY_ID) { // Any category except Talent/Other
                        $serviceAttributes['service_style_id'] = null; // Will be replaced by service_styles relationship
                        $serviceAttributes['service_type_title'] = null;
                        $serviceAttributes['service_type_description'] = null;
                        $serviceAttributes['price'] = null;
                    } else if ($serviceData['service_category_id'] == ServiceCategory::OTHER_CATEGORY_ID) { // Talent category
                        $serviceAttributes['service_style_id'] = null;
                        $serviceAttributes['service_type_title'] = $serviceData['service_type_title'] ?? null;
                        $serviceAttributes['service_type_description'] = $serviceData['service_type_description'] ?? null;
                        $serviceAttributes['price'] = $serviceData['price'] ?? null;
                    } else {
                        throw new \Exception('Invalid service category. Only Gamer (ID 1) and Talent (ID 2) categories are supported.');
                    }

                    // Create the service
                    $service = $request->user()->services()->create($serviceAttributes);

                    // Create pricing option using pricing option type
                    $pricingOptionType = \App\Models\PricingOptionType::findOrFail($serviceData['pricing_option_id']);
                    $service->pricingOptions()->create([
                        'pricing_option_type_id' => $pricingOptionType->id,
                        'is_active' => true,
                    ]);

                    if (isset($serviceData['service_style']) && is_array($serviceData['service_style'])) {
                        foreach ($serviceData['service_style'] as $styleData) {
                            $price = $styleData['price'] ?? null;

                            if ($price === null && $serviceData['service_category_id'] != ServiceCategory::OTHER_CATEGORY_ID) {
                                $serviceStyle = ServiceStyle::find($styleData['service_style_id']);
                                $price = $serviceStyle ? $serviceStyle->preset_price : null;
                            }

                            $service->serviceStyles()->attach($styleData['service_style_id'], [
                                'is_active' => $styleData['is_active'] ?? false,
                                'price' => $price,
                            ]);
                        }
                    }

                    // Create initial revision
                    $service->revisions()->create([
                        'title' => $serviceData['title'] ?? null,
                        'description' => $serviceData['description'] ?? null,
                        'service_elements' => $serviceData['service_elements'] ?? null,
                        'service_type_title' => $serviceData['service_type_title'] ?? null,
                        'service_type_description' => $serviceData['service_type_description'] ?? null,
                        'price' => $serviceData['price'] ?? null,
                        'status' => 'pending',
                    ]);

                    $updatedServiceIds[] = $service->id;
                }

                $updatedServices[] = $service->load(['serviceCategory', 'serviceType', 'pricingOptions.pricingOptionType', 'revisions']);
            }

            $servicesToDelete = array_diff($existingServiceIds, $updatedServiceIds);
            $deletedCount = 0;

            if (!empty($servicesToDelete)) {
                $deletedCount = UserService::whereIn('id', $servicesToDelete)
                    ->where('user_id', $request->user()->id)
                    ->delete();
            }

            DB::commit();

            return response()->json([
                'message' => count($updatedServices) > 1
                    ? 'Services updated successfully and pending approval'
                    : 'Service updated successfully and pending approval',
                'services' => $updatedServices,
                'deleted_service_count' => $deletedCount,
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['message' => 'Failed to update services: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Toggle the active status of the specified service.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function toggleActive(Request $request, $id)
    {
        $service = UserService::findOrFail($id);

        $this->authorize('update', $service);

        // Only approved services can be toggled
        if (!$service->isApproved()) {
            return response()->json([
                'message' => 'Only approved services can be enabled or disabled',
            ], 422);
        }

        $service->update([
            'is_active' => !$service->is_active,
        ]);

        return response()->json([
            'message' => $service->is_active ? 'Service enabled successfully' : 'Service disabled successfully',
            'is_active' => $service->is_active,
        ]);
    }
}
