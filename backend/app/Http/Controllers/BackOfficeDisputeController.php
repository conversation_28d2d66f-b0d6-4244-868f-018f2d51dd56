<?php

namespace App\Http\Controllers;

use App\Models\Dispute;
use App\Models\DisputeType;
use App\Services\DisputeService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class BackOfficeDisputeController extends Controller
{
    protected $disputeService;

    public function __construct(DisputeService $disputeService)
    {
        $this->disputeService = $disputeService;
    }

    /**
     * Get a list of disputes with pagination and filtering
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getDisputes(Request $request)
    {
        $perPage = $request->input('per_page', 15);
        $status = $request->input('status');
        $orderType = $request->input('order_type');
        $fromDate = $request->input('from_date');
        $toDate = $request->input('to_date');
        
        $query = Dispute::with(['user', 'disputeType', 'resolver']);
        
        if ($status) {
            $query->where('status', $status);
        }
        
        if ($orderType) {
            $query->where('order_type', $orderType);
        }
        
        if ($fromDate) {
            $query->whereDate('created_at', '>=', $fromDate);
        }
        
        if ($toDate) {
            $query->whereDate('created_at', '<=', $toDate);
        }
        
        $disputes = $query->orderBy('created_at', 'desc')
            ->paginate($perPage);
            
        return response()->json([
            'success' => true,
            'data' => $disputes,
        ]);
    }

    /**
     * Get dispute details for backoffice
     * 
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getDispute(Request $request, $id)
    {
        $dispute = Dispute::with([
                'user', 
                'disputeType', 
                'media', 
                'resolver'
            ])
            ->findOrFail($id);
            
        if ($dispute->order_type === 'order') {
            $dispute->load(['order' => function($query) {
                $query->with(['customer', 'talent', 'userService', 'pricingOption']);
            }]);
            
            $chatConversation = \App\Models\ChatConversation::where('order_id', $dispute->order_id)
                ->with(['messages' => function($query) {
                    $query->orderBy('created_at', 'asc');
                }])
                ->first();
                
            $dispute->chat_conversation = $chatConversation;
        } else {
            $dispute->load(['mission' => function($query) {
                $query->with(['user', 'serviceCategory', 'minLevel', 'applicants']);
            }]);
        }
        
        return response()->json([
            'success' => true,
            'data' => $dispute,
        ]);
    }

    /**
     * Resolve or reject a dispute
     * 
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function resolveDispute(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'status' => 'required|in:resolved,rejected',
            'resolution_notes' => 'required|string|max:1000',
            'refund_amount' => 'nullable|numeric|min:0',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
                'error_code' => 422,
            ], 422);
        }
        
        try {
            $staff = $request->user();
            $dispute = Dispute::findOrFail($id);
            
            if ($dispute->isResolved() || $dispute->isRejected()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Dispute has already been resolved or rejected',
                    'error_code' => 400,
                ], 400);
            }
            
            $data = $validator->validated();
            
            $dispute = $this->disputeService->resolveDispute($dispute, $staff, $data);
            
            return response()->json([
                'success' => true,
                'message' => 'Dispute ' . $data['status'] . ' successfully',
                'data' => $dispute,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to resolve dispute', [
                'error' => $e->getMessage(),
                'error_code' => $e->getCode(),
                'dispute_id' => $id,
                'staff_id' => $request->user()->id,
                'status' => $request->input('status'),
                'trace' => $e->getTraceAsString(),
            ]);
            
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'error_code' => $e->getCode() >= 400 && $e->getCode() < 500 ? $e->getCode() : 500,
            ], $e->getCode() >= 400 && $e->getCode() < 500 ? $e->getCode() : 500);
        }
    }

    /**
     * Update dispute status to in_review
     * 
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function markInReview(Request $request, $id)
    {
        try {
            $dispute = Dispute::findOrFail($id);
            
            if (!$dispute->isSubmitted()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Only submitted disputes can be marked as in review',
                    'error_code' => 400,
                ], 400);
            }
            
            $dispute->update([
                'status' => 'in_review',
            ]);
            
            return response()->json([
                'success' => true,
                'message' => 'Dispute marked as in review',
                'data' => $dispute,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to mark dispute as in review', [
                'error' => $e->getMessage(),
                'error_code' => $e->getCode(),
                'dispute_id' => $id,
                'staff_id' => $request->user()->id,
                'trace' => $e->getTraceAsString(),
            ]);
            
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'error_code' => $e->getCode() >= 400 && $e->getCode() < 500 ? $e->getCode() : 500,
            ], $e->getCode() >= 400 && $e->getCode() < 500 ? $e->getCode() : 500);
        }
    }
}
