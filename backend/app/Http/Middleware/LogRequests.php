<?php

namespace App\Http\Middleware;

use App\Models\IpLog;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class LogRequests
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        if ($request->method() === 'OPTIONS' || $request->is('api/internal/*')) {
            return $response;
        }

        try {
            \Log::info('Attempting to log request', [
                'user_id' => auth()->id(),
                'ip_address' => $request->ip(),
                'route' => $request->path(),
                'method' => $request->method(),
            ]);
            
            IpLog::create([
                'user_id' => auth()->check() ? auth()->id() : null,
                'ip_address' => $request->ip(),
                'route' => $request->path(),
                'method' => $request->method(),
                'user_agent' => $request->header('User-Agent'),
            ]);
            
            \Log::info('Successfully logged request to ip_logs table');
        } catch (\Exception $e) {
            \Log::error('Failed to log request: ' . $e->getMessage(), [
                'error_code' => $e->getCode(),
                'detailed_message' => $e->getMessage(),
                'request_path' => $request->path(),
                'stack_trace' => $e->getTraceAsString(),
            ]);
        }

        return $response;
    }
}
