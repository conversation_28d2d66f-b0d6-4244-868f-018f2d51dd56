<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class EnsureEmailVerified
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $user = $request->user();
        
        if (!$user || !isset($user->email_verified_at) || $user->email_verified_at === null) {
            return response()->json([
                'message' => 'Email verification required for withdrawal operations.',
                'error_code' => 'email_verification_required'
            ], 403);
        }
        
        return $next($request);
    }
}
