<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class ForceJsonResponse
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $request->headers->set('Accept', 'application/json');
        
        $response = $next($request);
        
        if (!$request->isMethod('POST') || !str_contains($request->header('Content-Type') ?? '', 'multipart/form-data')) {
            $response->headers->set('Content-Type', 'application/json');
        }
        
        return $response;
    }
}
