<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class BackofficeAuthMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        $apiToken = config('services.backoffice.token');

        if ($request->header('Authorization') !== 'Bearer ' . $apiToken) {
            return response()->json(['message' => 'Unauthorized'], 401);
        }

        return $next($request);
    }
}
