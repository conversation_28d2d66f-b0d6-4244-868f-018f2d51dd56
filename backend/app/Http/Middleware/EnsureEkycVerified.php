<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class EnsureEkycVerified
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $user = $request->user();
        
        if (!$user || !isset($user->is_ekyc_verified) || !$user->is_ekyc_verified) {
            return response()->json([
                'message' => 'E-KYC verification required for withdrawal operations.',
                'error_code' => 'ekyc_required'
            ], 403);
        }
        
        return $next($request);
    }
}
