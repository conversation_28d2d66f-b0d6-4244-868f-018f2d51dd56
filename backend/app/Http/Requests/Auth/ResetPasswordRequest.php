<?php

namespace App\Http\Requests\Auth;

use App\Rules\Password;
use Illuminate\Foundation\Http\FormRequest;

class ResetPasswordRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'mobile_number' => ['required', 'string', new \App\Rules\MalaysianPhone],
            'password' => ['required', 'string', new Password],
        ];
    }
}
