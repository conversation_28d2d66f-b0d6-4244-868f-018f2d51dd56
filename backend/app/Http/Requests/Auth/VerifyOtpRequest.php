<?php

namespace App\Http\Requests\Auth;

use App\Rules\MalaysianPhone;
use Illuminate\Foundation\Http\FormRequest;

class VerifyOtpRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'mobile_number' => ['required', 'string', new MalaysianPhone],
            'otp' => 'required|string|size:6',
        ];
    }
}
