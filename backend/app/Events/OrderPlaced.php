<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Contracts\Broadcasting\ShouldBroadcastNow;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;

class OrderPlaced implements ShouldBroadcastNow
{
    use InteractsWithSockets, SerializesModels;

    public $order;
    public $queue = false;

    public function __construct($order)
    {
        $this->order = $order;
        Log::info('OrderPlaced: Event constructed', [
            'order_id' => $order->id,
            'talent_id' => $order->talent_id,
            'customer_id' => $order->customer_id,
            'expires_at' => $order->expires_at->toISOString(),
        ]);
    }

    public function broadcastOn()
    {
        $channel = 'talent.' . $this->order->talent_id;
        Log::info('OrderPlaced: Broadcasting on channel', [
            'channel' => $channel,
            'order_id' => $this->order->id,
        ]);
        return new Channel($channel);
    }

    public function broadcastAs()
    {
        $eventName = 'order.placed';
        Log::info('OrderPlaced: Broadcasting as event', [
            'event' => $eventName,
            'order_id' => $this->order->id,
        ]);
        return $eventName;
    }

    public function broadcastWith()
    {
        $data = [
            'id' => $this->order->id,
            'talent_id' => $this->order->talent_id,
            'customer_id' => $this->order->customer_id,
            'expires_at' => $this->order->expires_at->toISOString(),
            'user_service' => [
                'title' => ($this->order->userService)->title ?? 'Unknown Service',
                ''
            ],
            'customer_name' => ($this->order->customer)->name ?? 'Unknown Customer',
            'credit_amount' => $this->order->credit_amount,
        ];
        Log::info('OrderPlaced: Broadcast data prepared', [
            'order_id' => $this->order->id,
            'data' => $data,
        ]);

        // Manual Redis publish for debugging
        // try {
        //     Redis::connection('redis')->publish('talent.' . $this->order->talent_id, json_encode([
        //         'event' => 'order.placed',
        //         'data' => $data,
        //     ]));
        //     Log::info('OrderPlaced: Manual Redis publish succeeded', [
        //         'order_id' => $this->order->id,
        //         'channel' => 'talent.' . $this->order->talent_id,
        //     ]);
        // } catch (\Exception $e) {
        //     Log::error('OrderPlaced: Manual Redis publish failed', [
        //         'order_id' => $this->order->id,
        //         'channel' => 'talent.' . $this->order->talent_id,
        //         'error' => $e->getMessage(),
        //     ]);
        // }

        return $data;

        // return [
        //     'event' => 'order.placed',
        //     'data' => $data,
        // ];
    }

    // public function broadcastQueue()
    // {
    //     Log::info('OrderPlaced: Event queued for broadcasting', [
    //         'order_id' => $this->order->id,
    //     ]);
    // }

    public function broadcastWhen()
    {
        Log::info('OrderPlaced: Broadcast condition checked', [
            'order_id' => $this->order->id,
            'condition' => true,
        ]);
        return true;
    }


    public function broadcastVia()
    {
        Log::info('OrderPlaced: Broadcast via redis', [
            'order_id' => $this->order->id,
        ]);
        return 'redis';
    }
}
