<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserGameProfile extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'user_id',
        'service_type_id',
        'identifiers',
        'is_hidden',
        'status',
    ];

    protected $casts = [
        'identifiers' => 'array',
        'is_hidden' => 'boolean',
    ];

    /**
     * Get the user that owns the game profile.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the service type associated with this game profile.
     */
    public function serviceType(): BelongsTo
    {
        return $this->belongsTo(ServiceType::class);
    }

    /**
     * Set this profile as visible (unhidden).
     */
    public function setAsVisible(): bool
    {
        try {
            $this->is_hidden = false;
            $this->save();
            
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
}
