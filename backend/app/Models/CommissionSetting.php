<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CommissionSetting extends Model
{
    use HasFactory, SoftDeletes;
    
    protected $fillable = [
        'key',
        'value',
        'description',
    ];
    
    protected $casts = [
        'value' => 'integer',
    ];
}
