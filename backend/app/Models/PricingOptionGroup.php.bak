<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class PricingOptionGroup extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'name',
        'description',
        'unit',
        'has_duration',
        'quantity',
        'is_active',
        'display_order',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'display_order' => 'integer',
        'has_duration' => 'boolean',
        'unit' => 'string',
        'quantity' => 'integer',
    ];

    public function translations(): HasMany
    {
        return $this->hasMany(PricingOptionGroupTranslation::class);
    }

    public function pricingOptionTypes(): HasMany
    {
        return $this->hasMany(PricingOptionType::class);
    }

    public function serviceStyles(): BelongsToMany
    {
        return $this->belongsToMany(ServiceStyle::class, 'service_style_pricing_groups')
            ->withTimestamps();
    }
    
    public function serviceLinks(): HasMany
    {
        return $this->hasMany(ServiceLink::class);
    }
}
