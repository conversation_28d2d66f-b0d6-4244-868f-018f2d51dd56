<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ScheduledOrderReview extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'scheduled_order_id',
        'reviewer_id',
        'reviewee_id',
        'rating',
        'review_text',
        'is_anonymous',
        'is_hidden',
    ];

    protected $casts = [
        'rating' => 'integer',
        'is_anonymous' => 'boolean',
        'is_hidden' => 'boolean',
    ];

    public function scheduledOrder(): BelongsTo
    {
        return $this->belongsTo(ScheduledOrder::class);
    }

    public function reviewer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reviewer_id');
    }

    public function reviewee(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reviewee_id');
    }
}
