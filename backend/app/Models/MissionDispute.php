<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class MissionDispute extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'user_id',
        'requestor_type',
        'mission_id',
        'child_id',
        'dispute_type_id',
        'description',
        'status',
        'resolution_type',
        'resolution_notes',
        'refund_amount',
        'resolved_by',
        'resolved_at',
    ];

    protected $casts = [
        'refund_amount' => 'decimal:2',
        'resolved_at' => 'datetime',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function mission(): BelongsTo
    {
        return $this->belongsTo(Mission::class);
    }

    public function child(): BelongsTo
    {
        return $this->belongsTo(User::class, 'child_id');
    }

    public function disputeType(): BelongsTo
    {
        return $this->belongsTo(DisputeType::class);
    }

    public function resolver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'resolved_by');
    }

    public function media(): HasMany
    {
        return $this->hasMany(DisputeMissionMedia::class);
    }

    public function isSubmitted(): bool
    {
        return $this->status === 'submitted';
    }

    public function isInReview(): bool
    {
        return $this->status === 'in_review';
    }

    public function isResolved(): bool
    {
        return $this->status === 'resolved';
    }

    public function isRejected(): bool
    {
        return $this->status === 'rejected';
    }
}
