<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class ClientRepeatRate extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'customer_id',
        'talent_id',
        'order_count',
        'first_order_id',
        'last_order_id',
        'first_scheduled_order_id',
        'last_scheduled_order_id',
        'first_order_at',
        'last_order_at',
    ];

    protected $casts = [
        'first_order_at' => 'datetime',
        'last_order_at' => 'datetime',
    ];

    /**
     * Get the customer associated with this repeat rate.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'customer_id');
    }

    /**
     * Get the talent associated with this repeat rate.
     */
    public function talent(): BelongsTo
    {
        return $this->belongsTo(User::class, 'talent_id');
    }

    /**
     * Get the first order.
     */
    public function firstOrder(): BelongsTo
    {
        return $this->belongsTo(Order::class, 'first_order_id');
    }

    /**
     * Get the last order.
     */
    public function lastOrder(): BelongsTo
    {
        return $this->belongsTo(Order::class, 'last_order_id');
    }

    /**
     * Get the first scheduled order.
     */
    public function firstScheduledOrder(): BelongsTo
    {
        return $this->belongsTo(ScheduledOrder::class, 'first_scheduled_order_id');
    }

    /**
     * Get the last scheduled order.
     */
    public function lastScheduledOrder(): BelongsTo
    {
        return $this->belongsTo(ScheduledOrder::class, 'last_scheduled_order_id');
    }
}
