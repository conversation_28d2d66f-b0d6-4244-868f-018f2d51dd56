<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class ChatResponseMetric extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'talent_id',
        'total_received_messages',
        'total_responded_messages',
        'average_response_time',
        'response_rate',
        'last_calculation_at',
    ];

    protected $casts = [
        'average_response_time' => 'float',
        'response_rate' => 'float',
        'last_calculation_at' => 'datetime',
    ];

    /**
     * Get the talent associated with this response metric.
     */
    public function talent(): BelongsTo
    {
        return $this->belongsTo(User::class, 'talent_id');
    }
}
