<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class GameId extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'name',
        'slug',
        'type',
        'regex',
    ];

    protected $casts = [
    ];

    /**
     * Validate a value against this game ID's rules.
     */
    public function validate($value): bool
    {
        if ($this->regex && !preg_match('/' . $this->regex . '/', $value)) {
            return false;
        }

        if ($this->type === 'integer' && !is_numeric($value)) {
            return false;
        }

        return true;
    }

    /**
     * Get the error message for this game ID's validation.
     */
    public function getErrorMessage(): string
    {
        if ($this->type === 'integer') {
            return "The {$this->name} field must be a number.";
        }
        
        return "The {$this->name} field format is invalid.";
    }
    
    /**
     * Get the service types that use this game ID.
     */
    public function serviceTypes(): BelongsToMany
    {
        return $this->belongsToMany(ServiceType::class, 'game_id_service_type', 'game_id', 'service_type_id')
            ->withTimestamps();
    }
}
