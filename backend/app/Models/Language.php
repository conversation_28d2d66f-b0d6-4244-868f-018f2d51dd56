<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Language extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'description',
        'is_active',
        'display_order',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'display_order' => 'integer',
    ];

    /**
     * The users that belong to the language.
     */
    public function users()
    {
        return $this->belongsToMany(User::class, 'user_languages')
                    ->withTimestamps()
                    ->withPivot('deleted_at')
                    ->withTrashed();
    }
}
