<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SystemAccountTransaction extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'system_account_id',
        'amount',
        'type',
        'description',
        'reference_id',
        'reference_type',
        'metadata',
    ];

    protected $casts = [
        'amount' => 'integer',
        'metadata' => 'array',
    ];

    /**
     * Get the system account that owns the transaction.
     */
    public function systemAccount(): BelongsTo
    {
        return $this->belongsTo(SystemAccount::class);
    }

    /**
     * Scope a query to only include credit transactions.
     */
    public function scopeCredit($query)
    {
        return $query->where('type', 'credit');
    }

    /**
     * Scope a query to filter by reference type.
     */
    public function scopeByReferenceType($query, $type)
    {
        return $query->where('reference_type', $type);
    }
}
