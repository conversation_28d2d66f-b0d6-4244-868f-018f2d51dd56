<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class ChatResponseTime extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'conversation_id',
        'initial_message_id',
        'response_message_id',
        'sender_id',
        'responder_id',
        'response_time',
        'is_talent_response',
    ];

    protected $casts = [
        'response_time' => 'integer',
        'is_talent_response' => 'boolean',
    ];

    /**
     * Get the conversation associated with this response time.
     */
    public function conversation(): BelongsTo
    {
        return $this->belongsTo(ChatConversation::class, 'conversation_id');
    }

    /**
     * Get the initial message.
     */
    public function initialMessage(): BelongsTo
    {
        return $this->belongsTo(ChatMessage::class, 'initial_message_id');
    }

    /**
     * Get the response message.
     */
    public function responseMessage(): BelongsTo
    {
        return $this->belongsTo(ChatMessage::class, 'response_message_id');
    }

    /**
     * Get the sender of the initial message.
     */
    public function sender(): BelongsTo
    {
        return $this->belongsTo(User::class, 'sender_id');
    }

    /**
     * Get the responder.
     */
    public function responder(): BelongsTo
    {
        return $this->belongsTo(User::class, 'responder_id');
    }
}
