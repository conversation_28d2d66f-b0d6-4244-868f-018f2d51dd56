<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DisputeMissionMedia extends Model
{
    protected $table = 'dispute_mission_media';
    
    protected $fillable = [
        'mission_dispute_id',
        'media_type',
        'original_path',
        'optimized_path',
        'mime_type',
        'file_size',
    ];

    public function missionDispute(): BelongsTo
    {
        return $this->belongsTo(MissionDispute::class);
    }
}
