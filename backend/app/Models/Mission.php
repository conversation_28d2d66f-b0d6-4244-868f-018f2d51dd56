<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Mission extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'title',
        'service_type_id',
        'service_style_id',
        'description',
        'bounty',
        'pax_required',
        'min_level_id',
        'max_level_id',
        'service_start_date',
        'service_end_date',
        'status',
        'is_anonymous',
        'is_credit_held',
        'credit_held_at',
        'credit_amount',
        'commission_amount',
    ];

    protected $casts = [
        'bounty' => 'integer',
        'pax_required' => 'integer',
        'service_start_date' => 'datetime',
        'service_end_date' => 'datetime',
        'is_anonymous' => 'boolean',
        'is_credit_held' => 'boolean',
        'credit_held_at' => 'datetime',
        'credit_amount' => 'integer',
        'commission_amount' => 'integer',
    ];
    
    protected $appends = [
        'is_bookmarked',
        'remaining_slot',
        'recommended_missions',
        'total_points',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function serviceType(): BelongsTo
    {
        return $this->belongsTo(ServiceType::class);
    }
    
    public function serviceStyle(): BelongsTo
    {
        return $this->belongsTo(ServiceStyle::class);
    }

    public function minLevel(): BelongsTo
    {
        return $this->belongsTo(UserLevel::class, 'min_level_id');
    }
    
    public function maxLevel(): BelongsTo
    {
        return $this->belongsTo(UserLevel::class, 'max_level_id');
    }

    public function applicants(): HasMany
    {
        return $this->hasMany(MissionApplicant::class);
    }
    /**
     * Get the images for this mission.
     */
    public function images(): HasMany
    {
        return $this->hasMany(MissionImage::class);
    }



    public function isPending(): bool
    {
        return $this->status === 'open';
    }

    public function isClosed(): bool
    {
        return $this->status === 'closed';
    }

    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }
    
    public function isCancelled(): bool
    {
        return $this->status === 'cancelled';
    }
    
    /**
     * Get the users who bookmarked this mission.
     */
    public function bookmarkedBy(): \Illuminate\Database\Eloquent\Relations\BelongsToMany
    {
        return $this->belongsToMany(User::class, 'mission_bookmarks')
            ->withTimestamps();
    }
    
    /**
     * Get the mission bookmarks for this mission.
     */
    public function bookmarks(): HasMany
    {
        return $this->hasMany(MissionBookmark::class);
    }
    
    /**
     * Check if the mission is bookmarked by the authenticated user.
     */
    public function getIsBookmarkedAttribute(): bool
    {
        if (!auth()->check()) {
            return false;
        }
        
        return MissionBookmark::where('user_id', auth()->id())
            ->where('mission_id', $this->id)
            ->exists();
    }
    
    /**
     * Calculate the remaining slots for this mission.
     *
     * @return int
     */
    public function getRemainingSlotAttribute(): int
    {
        try {
            $applicantsCount = $this->applicants()->count();
            return max(0, $this->pax_required - $applicantsCount);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error calculating remaining slot', [
                'mission_id' => $this->id,
                'error' => $e->getMessage()
            ]);
            return 0;
        }
    }
    
    /**
     * Get the total points for this mission based on service type experience.
     *
     * @return int
     */
    public function getTotalPointsAttribute(): int
    {
        try {
            return $this->serviceType->experience ?? 0;
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error retrieving total points', [
                'mission_id' => $this->id,
                'error' => $e->getMessage()
            ]);
            return 0;
        }
    }
    
    /**
     * Get recommended missions based on similar service type.
     *
     * @param int $limit
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getRecommendedMissionsAttribute($limit = 5)
    {
        return Mission::where('id', '!=', $this->id)
            ->where('status', 'open')
            ->where('service_type_id', $this->service_type_id) // Only recommend missions with the same service type
            ->with([
                'serviceType.serviceCategory',
                'serviceStyle',
                'minLevel',
                'maxLevel',
                'images',
                'user' => function($query) {
                    $query->select('id', 'name', 'gender', 'level_id', 'profile_picture');
                    $query->with('level:id,name,level');
                }
            ])
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get()
            ->makeHidden('recommended_missions'); // Prevent infinite recursion
    }
    
    public function reviews(): \Illuminate\Database\Eloquent\Relations\MorphMany
    {
        return $this->morphMany(OrderReview::class, 'reviewable');
    }
}
