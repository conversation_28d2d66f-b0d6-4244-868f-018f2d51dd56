<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class PricingOption extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'pricing_option_type_id',
        'name',
        'description',
        'credits',
        'is_active',
        'display_order',
    ];

    protected $casts = [
        'credits' => 'integer',
        'is_active' => 'boolean',
        'display_order' => 'integer',
    ];

    public function translations(): HasMany
    {
        return $this->hasMany(PricingOptionTranslation::class);
    }

    public function pricingOptionType(): BelongsTo
    {
        return $this->belongsTo(PricingOptionType::class);
    }
}
