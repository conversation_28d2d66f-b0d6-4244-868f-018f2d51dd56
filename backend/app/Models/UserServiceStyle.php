<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserServiceStyle extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'user_service_id',
        'service_style_id',
        'is_active',
        'price',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'price' => 'integer',
    ];

    public function userService(): BelongsTo
    {
        return $this->belongsTo(UserService::class);
    }

    public function serviceStyle(): BelongsTo
    {
        return $this->belongsTo(ServiceStyle::class);
    }
}
