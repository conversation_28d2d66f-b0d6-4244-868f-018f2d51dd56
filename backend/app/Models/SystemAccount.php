<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;

class SystemAccount extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'name',
        'balance',
    ];

    protected $casts = [
        'balance' => 'integer',
    ];

    /**
     * Get the transactions for the system account.
     */
    public function transactions(): HasMany
    {
        return $this->hasMany(SystemAccountTransaction::class);
    }

    /**
     * Get the total balance of the system account.
     */
    public function getTotalBalance(): int
    {
        return $this->balance;
    }
}
