<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class TalentView extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'talent_id',
        'viewer_id',
        'ip_address',
        'user_agent',
        'is_counted',
        'view_count',
        'last_view_at',
    ];

    protected $casts = [
        'is_counted' => 'boolean',
        'last_view_at' => 'datetime',
    ];

    /**
     * Get the talent associated with this view.
     */
    public function talent(): BelongsTo
    {
        return $this->belongsTo(User::class, 'talent_id');
    }

    /**
     * Get the viewer associated with this view (if authenticated).
     */
    public function viewer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'viewer_id');
    }

    /**
     * Check if this view is from the same IP within the cooldown period (1 hour).
     *
     * @return bool
     */
    public function isWithinCooldown(): bool
    {
        if (!$this->last_view_at) {
            return false;
        }

        return $this->last_view_at->addHour()->greaterThan(now());
    }
}
