<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SocialPostResource\Pages;
use App\Filament\Resources\SocialPostResource\RelationManagers;
use App\Models\SocialPost;
use App\Models\PostModerationLog;
use Filament\Forms;
use Filament\Resources\Form;
use Filament\Resources\Resource;
use Filament\Resources\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Auth;

class SocialPostResource extends Resource
{
    protected static ?string $model = SocialPost::class;

    protected static ?string $navigationIcon = 'heroicon-o-collection';

    protected static ?string $navigationGroup = 'Social Media';

    protected static ?string $recordTitleAttribute = 'title';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('title')
                    ->required()
                    ->maxLength(45),
                Forms\Components\Textarea::make('description')
                    ->required()
                    ->maxLength(1000),
                Forms\Components\Toggle::make('is_hidden')
                    ->label('Hidden')
                    ->default(false),
                Forms\Components\KeyValue::make('location_data')
                    ->label('Location Data'),
                Forms\Components\Placeholder::make('created_at')
                    ->label('Created at')
                    ->content(fn (?SocialPost $record): string => $record?->created_at?->diffForHumans() ?? '-'),
                Forms\Components\Placeholder::make('updated_at')
                    ->label('Last modified at')
                    ->content(fn (?SocialPost $record): string => $record?->updated_at?->diffForHumans() ?? '-'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->sortable(),
                Tables\Columns\TextColumn::make('user.nickname')
                    ->label('User')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('title')
                    ->searchable()
                    ->sortable()
                    ->limit(30),
                Tables\Columns\TextColumn::make('description')
                    ->limit(50)
                    ->searchable(),
                Tables\Columns\BooleanColumn::make('is_hidden')
                    ->label('Hidden')
                    ->sortable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->label('Deleted')
                    ->sortable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
                Tables\Filters\Filter::make('hidden')
                    ->query(fn (Builder $query): Builder => $query->where('is_hidden', true)),
                Tables\Filters\Filter::make('visible')
                    ->query(fn (Builder $query): Builder => $query->where('is_hidden', false)),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('toggle_visibility')
                    ->label(fn (SocialPost $record): string => $record->is_hidden ? 'Show' : 'Hide')
                    ->icon(fn (SocialPost $record): string => $record->is_hidden ? 'heroicon-o-eye' : 'heroicon-o-eye-off')
                    ->action(function (SocialPost $record): void {
                        $newState = !$record->is_hidden;
                        $record->update(['is_hidden' => $newState]);
                        
                        // Log the moderation action
                        PostModerationLog::create([
                            'post_id' => $record->id,
                            'action' => $newState ? 'hidden' : 'approved',
                            'moderator_id' => Auth::id(),
                            'notes' => $newState ? 'Post hidden by moderator' : 'Post approved by moderator',
                        ]);
                    }),
                Tables\Actions\DeleteAction::make()
                    ->after(function (SocialPost $record): void {
                        // Log the moderation action
                        PostModerationLog::create([
                            'post_id' => $record->id,
                            'action' => 'deleted',
                            'moderator_id' => Auth::id(),
                            'notes' => 'Post deleted by moderator',
                        ]);
                    }),
                Tables\Actions\RestoreAction::make()
                    ->after(function (SocialPost $record): void {
                        // Log the moderation action
                        PostModerationLog::create([
                            'post_id' => $record->id,
                            'action' => 'restored',
                            'moderator_id' => Auth::id(),
                            'notes' => 'Post restored by moderator',
                        ]);
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkAction::make('approve')
                    ->action(function (\Illuminate\Database\Eloquent\Collection $records): void {
                        foreach ($records as $record) {
                            $record->update(['is_hidden' => false]);
                            
                            // Log the moderation action
                            PostModerationLog::create([
                                'post_id' => $record->id,
                                'action' => 'approved',
                                'moderator_id' => Auth::id(),
                                'notes' => 'Post approved in bulk action',
                            ]);
                        }
                    }),
                Tables\Actions\BulkAction::make('hide')
                    ->action(function (\Illuminate\Database\Eloquent\Collection $records): void {
                        foreach ($records as $record) {
                            $record->update(['is_hidden' => true]);
                            
                            // Log the moderation action
                            PostModerationLog::create([
                                'post_id' => $record->id,
                                'action' => 'hidden',
                                'moderator_id' => Auth::id(),
                                'notes' => 'Post hidden in bulk action',
                            ]);
                        }
                    }),
                Tables\Actions\DeleteBulkAction::make()
                    ->after(function (\Illuminate\Database\Eloquent\Collection $records): void {
                        foreach ($records as $record) {
                            // Log the moderation action
                            PostModerationLog::create([
                                'post_id' => $record->id,
                                'action' => 'deleted',
                                'moderator_id' => Auth::id(),
                                'notes' => 'Post deleted in bulk action',
                            ]);
                        }
                    }),
                Tables\Actions\RestoreBulkAction::make()
                    ->after(function (\Illuminate\Database\Eloquent\Collection $records): void {
                        foreach ($records as $record) {
                            // Log the moderation action
                            PostModerationLog::create([
                                'post_id' => $record->id,
                                'action' => 'restored',
                                'moderator_id' => Auth::id(),
                                'notes' => 'Post restored in bulk action',
                            ]);
                        }
                    }),
            ]);
    }
    
    public static function getRelations(): array
    {
        return [
            RelationManagers\CommentsRelationManager::class,
            RelationManagers\LikesRelationManager::class,
            RelationManagers\ModerationLogsRelationManager::class,
        ];
    }
    
    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSocialPosts::route('/'),
            'create' => Pages\CreateSocialPost::route('/create'),
            'edit' => Pages\EditSocialPost::route('/{record}/edit'),
            'view' => Pages\ViewSocialPost::route('/{record}'),
        ];
    }    
}
