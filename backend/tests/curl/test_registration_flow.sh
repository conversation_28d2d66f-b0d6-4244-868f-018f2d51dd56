#!/bin/bash

API_URL="http://103.233.0.105:8000/api"
GREEN='\033[0;32m'
NC='\033[0m'

echo "Testing Registration Flow..."
echo "=========================="

# Test 1: Request OTP
echo -e "\n${G<PERSON><PERSON>}Testing OTP Request${NC}"
curl -X POST "${API_URL}/auth/request-otp" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "mobile_number": "+601122220000"
  }'

# Test 2: Register with OTP
echo -e "\n${GREEN}Testing Registration with OTP${NC}"
curl -X POST "${API_URL}/auth/register" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "mobile_number": "+601122220000",
    "country_code": "MY",
    "nickname": "Test User 👨‍💻",
    "gender": "Male",
    "date_of_birth": "1990-01-01",
    "password": "TestPass123!",
    "otp": "123456",
    "referral_type": "code",
    "referral_value": "ABC123XY"
  }'

# Test 3: Invalid Phone Format
echo -e "\n${GREEN}Testing Invalid Phone Format${NC}"
curl -X POST "${API_URL}/auth/request-otp" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "mobile_number": "+65123456789"
  }'

# Test 4: Invalid OTP
echo -e "\n${GREEN}Testing Invalid OTP${NC}"
curl -X POST "${API_URL}/auth/register" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "mobile_number": "+601122220000",
    "country_code": "MY",
    "nickname": "Test User",
    "gender": "Male",
    "date_of_birth": "1990-01-01",
    "password": "TestPass123!",
    "otp": "000000",
    "referral_type": "code",
    "referral_value": "ABC123XY"
  }'

# Test 5: Weak Password
echo -e "\n${GREEN}Testing Weak Password${NC}"
curl -X POST "${API_URL}/auth/register" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "mobile_number": "+601122220000",
    "country_code": "MY",
    "nickname": "Test User",
    "gender": "Male",
    "date_of_birth": "1990-01-01",
    "password": "123",
    "otp": "123456",
    "referral_type": "code",
    "referral_value": "ABC123XY"
  }'

# Test 6: Long Nickname
echo -e "\n${GREEN}Testing Long Nickname${NC}"
curl -X POST "${API_URL}/auth/register" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "mobile_number": "+601122220000",
    "country_code": "MY",
    "nickname": "ThisNicknameIsTooLongAndShouldFail",
    "gender": "Male",
    "date_of_birth": "1990-01-01",
    "password": "TestPass123!",
    "otp": "123456",
    "referral_type": "code",
    "referral_value": "ABC123XY"
  }'

# Test 7: Missing Required Fields
echo -e "\n${GREEN}Testing Missing Required Fields${NC}"
curl -X POST "${API_URL}/auth/register" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "mobile_number": "+601122220000",
    "nickname": "Test User"
  }'

# Test 8: Invalid Referral Code
echo -e "\n${GREEN}Testing Invalid Referral Code${NC}"
curl -X POST "${API_URL}/auth/register" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "mobile_number": "+601122220000",
    "country_code": "MY",
    "nickname": "Test User",
    "gender": "Male",
    "date_of_birth": "1990-01-01",
    "password": "TestPass123!",
    "otp": "123456",
    "referral_type": "code",
    "referral_value": "INVALID"
  }'

# Test 9: OTP Request Cooldown
echo -e "\n${GREEN}Testing OTP Request Cooldown${NC}"
curl -X POST "${API_URL}/auth/request-otp" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "mobile_number": "+601122220000"
  }'
sleep 1
curl -X POST "${API_URL}/auth/request-otp" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "mobile_number": "+601122220000"
  }'
