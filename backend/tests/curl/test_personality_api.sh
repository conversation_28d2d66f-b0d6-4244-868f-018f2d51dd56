#!/bin/bash

# Start Laravel development server
php artisan serve --port=8000 &
SERVER_PID=$!

# Wait for server to start
sleep 5

# Login and get token
TOKEN=$(curl -s -X POST http://localhost:8000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "mobile_number": "+60123456789",
    "password": "Password123!"
  }' | grep -o '"token":"[^"]*' | cut -d'"' -f4)

# Test get all personalities
echo "Testing get all personalities endpoint..."
curl -X GET http://localhost:8000/api/personalities \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json"
echo -e "\n"

# Test get user personalities
echo "Testing get user personalities endpoint..."
curl -X GET http://localhost:8000/api/users/personalities \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json"
echo -e "\n"

# Test update user personalities
echo "Testing update user personalities endpoint..."
curl -X POST http://localhost:8000/api/users/personalities \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "personality_ids": [1, 2, 3]
  }'
echo -e "\n"

# Kill the Laravel server
kill $SERVER_PID
