#!/bin/bash

# Configuration
TOKEN="your_jwt_token"
API_URL="http://localhost:8000/api"

# List emergency contacts
echo "Testing: List emergency contacts"
curl -X GET \
  "$API_URL/emergency-contacts" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Accept: application/json"

echo -e "\n\n"

# Add emergency contact
echo "Testing: Add emergency contact"
curl -X POST \
  "$API_URL/emergency-contacts" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "relationship_id": 1,
    "contact_person_name": "<PERSON>",
    "phone_number": "+60123456789",
    "is_default": true
  }'

echo -e "\n\n"

# Update emergency contact
echo "Testing: Update emergency contact"
curl -X PUT \
  "$API_URL/emergency-contacts/1" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "relationship_id": 1,
    "contact_person_name": "<PERSON>",
    "phone_number": "+60123456788",
    "is_default": false
  }'

echo -e "\n\n"

# Delete emergency contact
echo "Testing: Delete emergency contact"
curl -X DELETE \
  "$API_URL/emergency-contacts/1" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Accept: application/json"
