#!/bin/bash

API_URL="http://103.233.0.105:8000/api"
GREEN='\033[0;32m'
NC='\033[0m'

echo "Testing Referral Validation..."
echo "============================="

# Mock OTP for testing (123456 is the default test OTP)
OTP="123456"

# Test 1: Registration without referral
echo -e "\n${GREEN}Testing Registration without Referral${NC}"
curl -X POST "${API_URL}/auth/register" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "mobile_number": "+60123456789",
    "country_code": "MY",
    "nickname": "Test User",
    "gender": "Male",
    "date_of_birth": "1990-01-01",
    "password": "TestPass123!",
    "otp": "123456"
  }'

# Test 2: Registration with valid phone referral
echo -e "\n${GREEN}Testing Registration with Valid Phone Referral${NC}"
curl -X POST "${API_URL}/auth/register" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "mobile_number": "+60123456789",
    "country_code": "MY",
    "nickname": "Test User",
    "gender": "Male",
    "date_of_birth": "1990-01-01",
    "password": "TestPass123!",
    "otp": "123456",
    "referral_type": "phone",
    "referral_value": "+60125351196"
  }'

# Test 3: Registration with valid code referral
echo -e "\n${GREEN}Testing Registration with Valid Code Referral${NC}"
curl -X POST "${API_URL}/auth/register" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "mobile_number": "+60123456789",
    "country_code": "MY",
    "nickname": "Test User",
    "gender": "Male",
    "date_of_birth": "1990-01-01",
    "password": "TestPass123!",
    "otp": "123456"
  }'

# Test 4: Registration with invalid phone format
echo -e "\n${GREEN}Testing Registration with Invalid Phone Format${NC}"
curl -X POST "${API_URL}/auth/register" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "mobile_number": "+60123456789",
    "country_code": "MY",
    "nickname": "Test User",
    "gender": "Male",
    "date_of_birth": "1990-01-01",
    "password": "TestPass123!",
    "otp": "123456",
    "referral_type": "phone",
    "referral_value": "invalid-phone"
  }'

# Test 5: Registration with non-existent phone
echo -e "\n${GREEN}Testing Registration with Non-existent Phone${NC}"
curl -X POST "${API_URL}/auth/register" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "mobile_number": "+60123456789",
    "country_code": "MY",
    "nickname": "Test User",
    "gender": "Male",
    "date_of_birth": "1990-01-01",
    "password": "TestPass123!",
    "otp": "123456",
    "referral_type": "phone",
    "referral_value": "+60111111111"
  }'

# Test 6: Registration with non-existent code
echo -e "\n${GREEN}Testing Registration with Non-existent Code${NC}"
curl -X POST "${API_URL}/auth/register" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "mobile_number": "+60123456789",
    "country_code": "MY",
    "nickname": "Test User",
    "gender": "Male",
    "date_of_birth": "1990-01-01",
    "password": "TestPass123!",
    "otp": "123456",
    "referral_type": "code",
    "referral_value": "INVALID"
  }'

# Test 7: Registration with referral type but no value
echo -e "\n${GREEN}Testing Registration with Missing Referral Value${NC}"
curl -X POST "${API_URL}/auth/register" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "mobile_number": "+60123456789",
    "country_code": "MY",
    "nickname": "Test User",
    "gender": "Male",
    "date_of_birth": "1990-01-01",
    "password": "TestPass123!",
    "otp": "123456",
    "referral_type": "code"
  }'
