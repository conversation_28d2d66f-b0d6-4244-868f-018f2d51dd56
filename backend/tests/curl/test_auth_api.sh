#!/bin/bash

# Start Laravel development server
php artisan serve --port=8000 &
SERVER_PID=$!

# Wait for server to start
sleep 5

# Test registration endpoint
echo "Testing registration endpoint..."
curl -X POST http://localhost:8000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "mobile_number": "+60123456789",
    "country_code": "MY",
    "nickname": "Test User 👨‍💻",
    "gender": "Male",
    "date_of_birth": "1990-01-01",
    "password": "Password123!"
  }'
echo -e "\n"

# Test login endpoint
echo "Testing login endpoint..."
curl -X POST http://localhost:8000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "mobile_number": "+60123456789",
    "password": "Password123!"
  }'
echo -e "\n"

# Test personalities endpoint (requires authentication)
echo "Testing personalities endpoint..."
TOKEN=$(curl -s -X POST http://localhost:8000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "mobile_number": "+60123456789",
    "password": "Password123!"
  }' | grep -o '"token":"[^"]*' | cut -d'"' -f4)

curl -X GET http://localhost:8000/api/personalities \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json"
echo -e "\n"

# Kill the Laravel server
kill $SERVER_PID
