# Implement Dual Service Structure Support with Global Service Styles

This PR implements support for the dual service structure with Game (ID 1) and Talent (ID 2) categories, with future categories following the Game category pattern. It also modifies service styles to be global at the category level instead of being tied to specific service types.

## Changes Made

1. Updated UserService model:
   - Added service_style accessor that returns array format for Game category and null for Talent
   - Made serviceStyles relationship protected to hide it from responses
   - Added toArray override to ensure correct format
   - Added pricing_option_id accessor for compatibility with new structure

2. Updated UserServiceController:
   - Changed condition from `if ($serviceData['service_category_id'] == 1)` to `if ($serviceData['service_category_id'] != 2)`
   - Updated validation rules to handle both service categories
   - Modified service creation and update logic to support both structures

3. Updated Order and ScheduledOrder models:
   - Added service_category_id and service_style_id to fillable arrays
   - Added relationships for serviceCategory and serviceStyle
   - Updated estimatedDuration method to use pricingOptionType

4. Updated OrderController and ScheduledOrderController:
   - Added validation for service_category_id and service_style_id
   - Updated order creation logic to store new fields
   - Modified pricing calculation based on service category
   - Updated relationship references in response data

5. Added database migrations:
   - Renamed pricing_option_id to pricing_option_type_id in orders and scheduled_orders tables
   - Added service_category_id and service_style_id to orders and scheduled_orders tables
   - Made service_type_id nullable in user_services table

6. Updated Backoffice UserServiceResource:
   - Added support for viewing both service categories
   - Created ServiceStylesRelationManager for viewing service styles
   - Added conditional display of fields based on service category

## Migration Sequence

The migrations are sequenced to ensure they run in the correct order:
1. First rename pricing_option_id to pricing_option_type_id in orders and scheduled_orders tables
2. Then add service_category_id and service_style_id columns to these tables

## Response Format

### Game Category (ID 1)
```json
{
  "id": 1,
  "service_category_id": 1,
  "service_type_id": 1,
  "pricing_option_id": 1,
  "service_style": [
    {
      "service_style_id": 1,
      "is_active": false,
      "price": 10
    }
  ],
  "service_type_title": null,
  "service_type_description": null,
  "price": null
}
```

### Talent Category (ID 2)
```json
{
  "id": 3,
  "service_category_id": 2,
  "service_type_id": null,
  "pricing_option_id": 1,
  "service_style": null,
  "service_type_title": "string",
  "service_type_description": "string",
  "price": 10
}
```

## Testing Instructions

### 1. Run Migrations
```bash
php artisan migrate
```

### 2. Test Creating Game Category Service
```json
{
  "services": [
    {
      "service_category_id": 1,
      "service_type_id": 1,
      "pricing_option_id": 1,
      "service_style": [
        {
          "service_style_id": 1,
          "is_active": true,
          "price": 50
        },
        {
          "service_style_id": 2,
          "is_active": true,
          "price": 75
        }
      ],
      "title": "Mobile Legends Coaching",
      "description": "Professional coaching for all skill levels",
      "service_elements": {
        "game_title": "Mobile Legends",
        "experience": "5+ years"
      }
    }
  ]
}
```

### 3. Test Creating Talent Category Service
```json
{
  "services": [
    {
      "service_category_id": 2,
      "service_type_id": null,
      "pricing_option_id": 1,
      "service_style": null,
      "service_type_title": "Photography",
      "service_type_description": "Professional photography services",
      "price": 80,
      "title": "Event Photography",
      "description": "High-quality photography for events",
      "service_elements": {
        "equipment": "Professional DSLR",
        "experience": "7 years"
      }
    }
  ]
}
```

### 4. Test Creating Order for Game Service
```json
{
  "talent_id": 1,
  "user_service_id": 2,
  "service_category_id": 1,
  "pricing_option_type_id": 1,
  "service_style_id": 1,
  "quantity": 1,
  "remarks": "Looking forward to improving my skills"
}
```

### 5. Test Creating Order for Talent Service
```json
{
  "talent_id": 1,
  "user_service_id": 3,
  "service_category_id": 2,
  "pricing_option_type_id": 1,
  "quantity": 1,
  "remarks": "Need photography for my wedding"
}
```

## Link to Devin run
https://app.devin.ai/sessions/2729fadc75774b06860520486b2e6471

## Requested by
<EMAIL>
