<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\MalaysianBank;

class MalaysianBankSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $banks = [
            [
                'code' => 'MBB',
                'name' => 'Maybank',
                'swift_code' => 'MBBEMYKL',
                'logo_url' => '/images/banks/maybank.png',
                'account_number_format' => '13 digits',
                'account_number_regex' => '^[0-9]{13}$',
                'description' => 'Malayan Banking Berhad',
                'is_active' => true,
                'display_order' => 1
            ],
            [
                'code' => 'CIMB',
                'name' => 'CIMB Bank',
                'swift_code' => 'CIBBMYKL',
                'logo_url' => '/images/banks/cimb.png',
                'account_number_format' => '13 digits',
                'account_number_regex' => '^[0-9]{13}$',
                'description' => 'CIMB Bank Berhad',
                'is_active' => true,
                'display_order' => 2
            ],
            [
                'code' => 'PBB',
                'name' => 'Public Bank',
                'swift_code' => 'PBBEMYKL',
                'logo_url' => '/images/banks/public-bank.png',
                'account_number_format' => '10-12 digits',
                'account_number_regex' => '^[0-9]{10,12}$',
                'description' => 'Public Bank Berhad',
                'is_active' => true,
                'display_order' => 3
            ],
            [
                'code' => 'RHB',
                'name' => 'RHB Bank',
                'swift_code' => 'RHBBMYKL',
                'logo_url' => '/images/banks/rhb.png',
                'account_number_format' => '14 digits',
                'account_number_regex' => '^[0-9]{14}$',
                'description' => 'RHB Bank Berhad',
                'is_active' => true,
                'display_order' => 4
            ],
            [
                'code' => 'HLB',
                'name' => 'Hong Leong Bank',
                'swift_code' => 'HLBBMYKL',
                'logo_url' => '/images/banks/hong-leong.png',
                'account_number_format' => '11-15 digits',
                'account_number_regex' => '^[0-9]{11,15}$',
                'description' => 'Hong Leong Bank Berhad',
                'is_active' => true,
                'display_order' => 5
            ],
            [
                'code' => 'AMB',
                'name' => 'AmBank',
                'swift_code' => 'ARBKMYKL',
                'logo_url' => '/images/banks/ambank.png',
                'account_number_format' => '12 digits',
                'account_number_regex' => '^[0-9]{12}$',
                'description' => 'AmBank (M) Berhad',
                'is_active' => true,
                'display_order' => 6
            ],
            [
                'code' => 'BIMB',
                'name' => 'Bank Islam',
                'swift_code' => 'BIMBMYKL',
                'logo_url' => '/images/banks/bank-islam.png',
                'account_number_format' => '14 digits',
                'account_number_regex' => '^[0-9]{14}$',
                'description' => 'Bank Islam Malaysia Berhad',
                'is_active' => true,
                'display_order' => 7
            ],
            [
                'code' => 'BKRM',
                'name' => 'Bank Rakyat',
                'swift_code' => 'BKRMMYKL',
                'logo_url' => '/images/banks/bank-rakyat.png',
                'account_number_format' => '12 digits',
                'account_number_regex' => '^[0-9]{12}$',
                'description' => 'Bank Kerjasama Rakyat Malaysia Berhad',
                'is_active' => true,
                'display_order' => 8
            ],
            [
                'code' => 'OCBC',
                'name' => 'OCBC Bank',
                'swift_code' => 'OCBCMYKL',
                'logo_url' => '/images/banks/ocbc.png',
                'account_number_format' => '10-12 digits',
                'account_number_regex' => '^[0-9]{10,12}$',
                'description' => 'OCBC Bank (Malaysia) Berhad',
                'is_active' => true,
                'display_order' => 9
            ],
            [
                'code' => 'SCB',
                'name' => 'Standard Chartered',
                'swift_code' => 'SCBLMYKX',
                'logo_url' => '/images/banks/standard-chartered.png',
                'account_number_format' => '10-14 digits',
                'account_number_regex' => '^[0-9]{10,14}$',
                'description' => 'Standard Chartered Bank Malaysia Berhad',
                'is_active' => true,
                'display_order' => 10
            ]
        ];

        foreach ($banks as $bank) {
            MalaysianBank::updateOrCreate(
                ['code' => $bank['code']],
                $bank
            );
        }
    }
}
