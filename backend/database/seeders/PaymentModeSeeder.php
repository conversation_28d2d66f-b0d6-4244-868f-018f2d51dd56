<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class PaymentModeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $paymentModes = [
            [
                'payment_mode_code' => 'duitnow',
                'payment_mode_name' => 'DuitNow'
            ],
            [
                'payment_mode_code' => 'ibft',
                'payment_mode_name' => 'Interbank Fund Transfer'
            ],
            [
                'payment_mode_code' => 'swift',
                'payment_mode_name' => 'SWIFT Transfer'
            ]
        ];
        
        foreach ($paymentModes as $paymentMode) {
            \App\Models\PaymentMode::updateOrCreate(
                ['payment_mode_code' => $paymentMode['payment_mode_code']],
                ['payment_mode_name' => $paymentMode['payment_mode_name']]
            );
        }
    }
}
