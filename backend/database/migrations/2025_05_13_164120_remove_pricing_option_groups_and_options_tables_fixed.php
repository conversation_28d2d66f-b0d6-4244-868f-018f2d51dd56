<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        try {
            if (Schema::hasTable('pricing_option_types') && Schema::hasColumn('pricing_option_types', 'pricing_option_group_id')) {
                Schema::table('pricing_option_types', function (Blueprint $table) {
                    $table->dropForeign(['pricing_option_group_id']);
                });
            }
        } catch (\Exception $e) {
        }
        
        try {
            if (Schema::hasTable('pricing_option_translations') && Schema::hasColumn('pricing_option_translations', 'pricing_option_id')) {
                Schema::table('pricing_option_translations', function (Blueprint $table) {
                    $table->dropForeign(['pricing_option_id']);
                });
            }
        } catch (\Exception $e) {
        }
        
        try {
            if (Schema::hasTable('pricing_option_group_translations') && Schema::hasColumn('pricing_option_group_translations', 'pricing_option_group_id')) {
                Schema::table('pricing_option_group_translations', function (Blueprint $table) {
                    $table->dropForeign(['pricing_option_group_id']);
                });
            }
        } catch (\Exception $e) {
        }
        
        try {
            if (Schema::hasTable('service_style_pricing_groups')) {
                if (Schema::hasColumn('service_style_pricing_groups', 'pricing_option_group_id')) {
                    Schema::table('service_style_pricing_groups', function (Blueprint $table) {
                        $table->dropForeign(['pricing_option_group_id']);
                    });
                }
            }
        } catch (\Exception $e) {
        }
        
        try {
            if (Schema::hasTable('service_style_pricing_groups')) {
                if (Schema::hasColumn('service_style_pricing_groups', 'service_style_id')) {
                    Schema::table('service_style_pricing_groups', function (Blueprint $table) {
                        $table->dropForeign(['service_style_id']);
                    });
                }
            }
        } catch (\Exception $e) {
        }
        
        try {
            if (Schema::hasTable('scheduled_orders') && Schema::hasColumn('scheduled_orders', 'pricing_option_id')) {
                Schema::table('scheduled_orders', function (Blueprint $table) {
                    $table->dropForeign(['pricing_option_id']);
                });
            }
        } catch (\Exception $e) {
        }
        
        try {
            if (Schema::hasTable('orders') && Schema::hasColumn('orders', 'pricing_option_id')) {
                Schema::table('orders', function (Blueprint $table) {
                    $table->dropForeign(['pricing_option_id']);
                });
            }
        } catch (\Exception $e) {
        }
        
        try {
            if (Schema::hasTable('user_service_pricing_options')) {
                DB::statement('ALTER TABLE user_service_pricing_options DROP FOREIGN KEY user_service_pricing_options_pricing_option_id_foreign');
            }
        } catch (\Exception $e) {
            \Log::info('Failed to drop foreign key from user_service_pricing_options: ' . $e->getMessage());
        }
        
        try {
            if (Schema::hasTable('service_links') && Schema::hasColumn('service_links', 'pricing_option_id')) {
                Schema::table('service_links', function (Blueprint $table) {
                    $table->dropForeign(['pricing_option_id']);
                });
            }
        } catch (\Exception $e) {
        }
        
        try {
            if (Schema::hasTable('service_links') && Schema::hasColumn('service_links', 'pricing_option_group_id')) {
                Schema::table('service_links', function (Blueprint $table) {
                    $table->dropForeign(['pricing_option_group_id']);
                });
            }
        } catch (\Exception $e) {
        }
        
        Schema::dropIfExists('pricing_option_translations');
        Schema::dropIfExists('pricing_options');
        Schema::dropIfExists('pricing_option_group_translations');
        Schema::dropIfExists('service_style_pricing_groups');
        Schema::dropIfExists('pricing_option_groups');
        
        if (Schema::hasTable('pricing_option_types')) {
            Schema::table('pricing_option_types', function (Blueprint $table) {
                if (Schema::hasColumn('pricing_option_types', 'pricing_option_group_id')) {
                    $table->dropColumn('pricing_option_group_id');
                }
                
                if (!Schema::hasColumn('pricing_option_types', 'has_duration')) {
                    $table->boolean('has_duration')->default(false);
                }
                
                if (!Schema::hasColumn('pricing_option_types', 'unit')) {
                    $table->string('unit')->nullable();
                }
                
                if (!Schema::hasColumn('pricing_option_types', 'quantity')) {
                    $table->integer('quantity')->nullable();
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::create('pricing_option_groups', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('unit')->nullable();
            $table->boolean('has_duration')->default(false);
            $table->integer('quantity')->nullable();
            $table->boolean('is_active')->default(true);
            $table->integer('display_order')->default(0);
            $table->timestamps();
            $table->softDeletes();
        });
        
        Schema::create('pricing_option_group_translations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('pricing_option_group_id')->constrained()->onDelete('cascade');
            $table->string('locale');
            $table->string('name');
            $table->text('description')->nullable();
            $table->timestamps();
            
            $table->unique(['pricing_option_group_id', 'locale']);
        });
        
        Schema::create('service_style_pricing_groups', function (Blueprint $table) {
            $table->id();
            $table->foreignId('service_style_id')->constrained()->onDelete('cascade');
            $table->foreignId('pricing_option_group_id')->constrained()->onDelete('cascade');
            $table->timestamps();
            
            $table->unique(['service_style_id', 'pricing_option_group_id']);
        });
        
        Schema::create('pricing_options', function (Blueprint $table) {
            $table->id();
            $table->foreignId('pricing_option_type_id')->constrained()->onDelete('cascade');
            $table->string('name');
            $table->text('description')->nullable();
            $table->integer('credits');
            $table->boolean('is_active')->default(true);
            $table->integer('display_order')->default(0);
            $table->timestamps();
            $table->softDeletes();
        });
        
        Schema::create('pricing_option_translations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('pricing_option_id')->constrained()->onDelete('cascade');
            $table->string('locale');
            $table->string('name');
            $table->text('description')->nullable();
            $table->timestamps();
            
            $table->unique(['pricing_option_id', 'locale']);
        });
        
        Schema::table('pricing_option_types', function (Blueprint $table) {
            if (Schema::hasColumn('pricing_option_types', 'unit')) {
                $table->dropColumn('unit');
            }
            
            if (Schema::hasColumn('pricing_option_types', 'quantity')) {
                $table->dropColumn('quantity');
            }
            
            
            $table->foreignId('pricing_option_group_id')->after('id')->nullable();
            $table->foreign('pricing_option_group_id')->references('id')->on('pricing_option_groups')->onDelete('cascade');
        });
    }
};
