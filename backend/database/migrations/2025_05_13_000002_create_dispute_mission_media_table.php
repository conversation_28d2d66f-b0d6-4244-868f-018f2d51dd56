<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('dispute_mission_media', function (Blueprint $table) {
            $table->id();
            $table->foreignId('mission_dispute_id')->constrained('mission_disputes')->onDelete('cascade');
            $table->string('media_type')->comment('image or video');
            $table->string('original_path');
            $table->string('optimized_path')->nullable();
            $table->string('mime_type');
            $table->integer('file_size')->comment('Size in bytes');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('dispute_mission_media');
    }
};
