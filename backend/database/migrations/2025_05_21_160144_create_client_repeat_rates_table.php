<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('client_repeat_rates', function (Blueprint $table) {
            $table->id();
            $table->foreignId('customer_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('talent_id')->constrained('users')->onDelete('cascade');
            $table->unsignedInteger('order_count')->default(0);
            $table->foreignId('first_order_id')->nullable()->constrained('orders')->onDelete('set null');
            $table->foreignId('last_order_id')->nullable()->constrained('orders')->onDelete('set null');
            $table->foreignId('first_scheduled_order_id')->nullable()->constrained('scheduled_orders')->onDelete('set null');
            $table->foreignId('last_scheduled_order_id')->nullable()->constrained('scheduled_orders')->onDelete('set null');
            $table->timestamp('first_order_at')->nullable();
            $table->timestamp('last_order_at')->nullable();
            $table->timestamps();
            $table->softDeletes();
            
            $table->unique(['customer_id', 'talent_id']);
            
            $table->index(['talent_id', 'order_count']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('client_repeat_rates');
    }
};
