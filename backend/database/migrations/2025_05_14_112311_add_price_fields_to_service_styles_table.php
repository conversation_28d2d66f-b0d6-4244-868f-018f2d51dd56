<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('service_styles', function (Blueprint $table) {
            $table->integer('recommended_price')->nullable()->after('can_bypass');
            $table->integer('preset_price')->nullable()->after('recommended_price');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('service_styles', function (Blueprint $table) {
            $table->dropColumn(['recommended_price', 'preset_price']);
        });
    }
};
