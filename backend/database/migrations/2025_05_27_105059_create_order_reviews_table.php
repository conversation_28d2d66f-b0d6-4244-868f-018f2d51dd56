<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_reviews', function (Blueprint $table) {
            $table->id();
            $table->morphs('reviewable'); // Creates reviewable_id and reviewable_type columns
            $table->foreignId('reviewer_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('reviewee_id')->constrained('users')->onDelete('cascade');
            $table->tinyInteger('rating');
            $table->text('review_text')->nullable();
            $table->boolean('is_anonymous')->default(false);
            $table->boolean('is_hidden')->default(false);
            $table->timestamps();
            $table->softDeletes();
            
            $table->index('reviewee_id');
        });

        $this->migrateReviewsData();
        
        $this->migrateScheduledOrderReviewsData();
        
        Schema::dropIfExists('reviews');
        Schema::dropIfExists('scheduled_order_reviews');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_reviews');
    }
    
    /**
     * Migrate data from reviews table to order_reviews table
     */
    private function migrateReviewsData(): void
    {
        $reviews = DB::table('reviews')->get();
        
        foreach ($reviews as $review) {
            DB::table('order_reviews')->insert([
                'reviewable_id' => $review->order_id,
                'reviewable_type' => 'App\\Models\\Order',
                'reviewer_id' => $review->reviewer_id,
                'reviewee_id' => $review->reviewee_id,
                'rating' => $review->rating,
                'review_text' => $review->review_text,
                'is_anonymous' => $review->is_anonymous,
                'is_hidden' => false,
                'created_at' => $review->created_at,
                'updated_at' => $review->updated_at,
                'deleted_at' => $review->deleted_at,
            ]);
        }
    }
    
    /**
     * Migrate data from scheduled_order_reviews table to order_reviews table
     */
    private function migrateScheduledOrderReviewsData(): void
    {
        $scheduledReviews = DB::table('scheduled_order_reviews')->get();
        
        foreach ($scheduledReviews as $review) {
            DB::table('order_reviews')->insert([
                'reviewable_id' => $review->scheduled_order_id,
                'reviewable_type' => 'App\\Models\\ScheduledOrder',
                'reviewer_id' => $review->reviewer_id,
                'reviewee_id' => $review->reviewee_id,
                'rating' => $review->rating,
                'review_text' => $review->review_text,
                'is_anonymous' => $review->is_anonymous,
                'is_hidden' => $review->is_hidden ?? false,
                'created_at' => $review->created_at,
                'updated_at' => $review->updated_at,
                'deleted_at' => $review->deleted_at,
            ]);
        }
    }
};
