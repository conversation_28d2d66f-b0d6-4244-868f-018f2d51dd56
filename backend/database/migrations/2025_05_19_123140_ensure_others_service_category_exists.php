<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use App\Models\ServiceCategory;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $exists = DB::table('service_categories')->where('id', ServiceCategory::OTHER_CATEGORY_ID)->exists();
        
        if (!$exists) {
            $categoryWithId2 = DB::table('service_categories')->where('id', ServiceCategory::OTHER_CATEGORY_ID)->first();
            if ($categoryWithId2) {
                $maxId = DB::table('service_categories')->max('id');
                DB::table('service_categories')
                    ->where('id', ServiceCategory::OTHER_CATEGORY_ID)
                    ->update(['id' => $maxId + 1]);
            }
            
            DB::table('service_categories')->insert([
                'id' => ServiceCategory::OTHER_CATEGORY_ID,
                'name' => 'Others',
                'description' => 'Other services that do not fit into specific categories',
                'is_active' => true,
                'display_order' => 99,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        } else {
            $category = DB::table('service_categories')->where('id', ServiceCategory::OTHER_CATEGORY_ID)->first();
            if ($category && $category->name != 'Others') {
                DB::table('service_categories')
                    ->where('id', ServiceCategory::OTHER_CATEGORY_ID)
                    ->update([
                        'name' => 'Others',
                        'description' => 'Other services that do not fit into specific categories',
                        'is_active' => true,
                    ]);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
    }
};
