<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\WithdrawalTransaction;
use App\Models\ValidationType;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $seeder = new \Database\Seeders\ValidationTypeSeeder();
        $seeder->run();
        
        $transactions = WithdrawalTransaction::whereNotNull('id_validation_type')->get();
        
        foreach ($transactions as $transaction) {
            $validationType = ValidationType::where('validation_type', $transaction->id_validation_type)->first();
            
            if ($validationType) {
                $transaction->validation_type_id = $validationType->id;
                $transaction->save();
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
    }
};
