<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->decimal('average_rating', 3, 2)->nullable()->after('acceptance_rate');
        });
        
        $this->calculateInitialAverageRatings();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('average_rating');
        });
    }
    
    /**
     * Calculate and populate initial average ratings for existing users
     */
    private function calculateInitialAverageRatings(): void
    {
        $usersWithReviews = DB::table('users')
            ->whereIn('id', function($query) {
                $query->select('reviewee_id')
                    ->from('order_reviews')
                    ->distinct();
            })
            ->get();
        
        foreach ($usersWithReviews as $user) {
            $averageRating = DB::table('order_reviews')
                ->where('reviewee_id', $user->id)
                ->avg('rating');
            
            DB::table('users')
                ->where('id', $user->id)
                ->update(['average_rating' => $averageRating]);
        }
    }
};
