<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_services', function (Blueprint $table) {
            $table->string('service_type_title')->nullable()->after('service_style_id');
            $table->text('service_type_description')->nullable()->after('service_type_title');
            $table->unsignedInteger('price')->nullable()->after('service_type_description');
            $table->nullableMorphs('priceable');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_services', function (Blueprint $table) {
            $table->dropColumn([
                'service_type_title',
                'service_type_description',
                'price',
                'priceable_id',
                'priceable_type'
            ]);
        });
    }
};
