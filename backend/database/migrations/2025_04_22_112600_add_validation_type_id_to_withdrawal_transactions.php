<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('withdrawal_transactions', function (Blueprint $table) {
            $table->foreignId('validation_type_id')->nullable()->after('id_validation_value')->constrained('validation_types');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('withdrawal_transactions', function (Blueprint $table) {
            $table->dropForeign(['validation_type_id']);
            $table->dropColumn('validation_type_id');
        });
    }
};
