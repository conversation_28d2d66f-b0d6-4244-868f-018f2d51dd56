<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('service_style_user_bypasses', function (Blueprint $table) {
            // Debug: Log the current schema
            \Log::info('Current schema for service_style_user_bypasses: ' . json_encode(DB::select('SHOW CREATE TABLE service_style_user_bypasses')));

            // Drop foreign keys that depend on the unique index
            try {
                \Log::info("Dropping foreign key for service_style_id");
                $table->dropForeign(['service_style_id']);
            } catch (\Exception $e) {
                \Log::warning("Failed to drop foreign key for service_style_id: " . $e->getMessage());
            }
            try {
                \Log::info("Dropping foreign key for user_id");
                $table->dropForeign(['user_id']);
            } catch (\Exception $e) {
                \Log::warning("Failed to drop foreign key for user_id: " . $e->getMessage());
            }

            // Drop the existing unique index
            try {
                \Log::info("Dropping unique index: service_style_user_bypasses_service_style_id_user_id_unique");
                $table->dropUnique(['service_style_id', 'user_id']);
            } catch (\Exception $e) {
                \Log::warning("Failed to drop unique index: " . $e->getMessage());
            }

            // Add the service_type_id column and foreign key if it doesn't exist
            if (!Schema::hasColumn('service_style_user_bypasses', 'service_type_id')) {
                \Log::info("Adding service_type_id column and foreign key");
                $table->foreignId('service_type_id')->nullable()->after('service_style_id')->constrained('service_types')->onDelete('cascade');
            } else {
                // Ensure the foreign key exists
                try {
                    \Log::info("Ensuring foreign key for service_type_id");
                    $table->foreign('service_type_id')->references('id')->on('service_types')->onDelete('cascade');
                } catch (\Exception $e) {
                    \Log::warning("Failed to add foreign key for service_type_id: " . $e->getMessage());
                }
            }

            // Re-add foreign keys
            try {
                \Log::info("Re-adding foreign key for service_style_id");
                $table->foreign('service_style_id')->references('id')->on('service_styles')->onDelete('cascade');
            } catch (\Exception $e) {
                \Log::warning("Failed to re-add foreign key for service_style_id: " . $e->getMessage());
            }
            try {
                \Log::info("Re-adding foreign key for user_id");
                $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            } catch (\Exception $e) {
                \Log::warning("Failed to re-add foreign key for user_id: " . $e->getMessage());
            }

            // Add the new unique index with a shorter name
            try {
                \Log::info("Adding new unique index: ssub_style_user_type_unique");
                $table->unique(['service_style_id', 'user_id', 'service_type_id'], 'ssub_style_user_type_unique');
            } catch (\Exception $e) {
                \Log::warning("Failed to add new unique index: " . $e->getMessage());
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('service_style_user_bypasses', function (Blueprint $table) {
            // Drop the new unique index
            try {
                \Log::info("Dropping unique index: ssub_style_user_type_unique");
                $table->dropUnique(['service_style_id', 'user_id', 'service_type_id']);
            } catch (\Exception $e) {
                \Log::warning("Failed to drop unique index: " . $e->getMessage());
            }

            // Drop foreign keys
            try {
                \Log::info("Dropping foreign key for service_style_id");
                $table->dropForeign(['service_style_id']);
            } catch (\Exception $e) {
                \Log::warning("Failed to drop foreign key for service_style_id: " . $e->getMessage());
            }
            try {
                \Log::info("Dropping foreign key for user_id");
                $table->dropForeign(['user_id']);
            } catch (\Exception $e) {
                \Log::warning("Failed to drop foreign key for user_id: " . $e->getMessage());
            }
            try {
                \Log::info("Dropping foreign key for service_type_id");
                $table->dropForeign(['service_type_id']);
            } catch (\Exception $e) {
                \Log::warning("Failed to drop foreign key for service_type_id: " . $e->getMessage());
            }

            // Drop the service_type_id column
            try {
                if (Schema::hasColumn('service_style_user_bypasses', 'service_type_id')) {
                    \Log::info("Dropping column service_type_id");
                    $table->dropColumn('service_type_id');
                }
            } catch (\Exception $e) {
                \Log::warning("Failed to drop service_type_id column: " . $e->getMessage());
            }

            // Re-add the original unique index
            try {
                \Log::info("Re-adding unique index: service_style_user_bypasses_service_style_id_user_id_unique");
                $table->unique(['service_style_id', 'user_id'], 'service_style_user_bypasses_service_style_id_user_id_unique');
            } catch (\Exception $e) {
                \Log::warning("Failed to re-add unique index: " . $e->getMessage());
            }

            // Re-add foreign keys
            try {
                \Log::info("Re-adding foreign key for service_style_id");
                $table->foreign('service_style_id')->references('id')->on('service_styles')->onDelete('cascade');
            } catch (\Exception $e) {
                \Log::warning("Failed to re-add foreign key for service_style_id: " . $e->getMessage());
            }
            try {
                \Log::info("Re-adding foreign key for user_id");
                $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            } catch (\Exception $e) {
                \Log::warning("Failed to re-add foreign key for user_id: " . $e->getMessage());
            }
        });
    }
};