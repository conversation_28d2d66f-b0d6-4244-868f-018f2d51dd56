<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chat_response_times', function (Blueprint $table) {
            $table->id();
            $table->foreignId('conversation_id')->constrained('chat_conversations')->onDelete('cascade');
            $table->foreignId('initial_message_id')->constrained('chat_messages')->onDelete('cascade');
            $table->foreignId('response_message_id')->constrained('chat_messages')->onDelete('cascade');
            $table->foreignId('sender_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('responder_id')->constrained('users')->onDelete('cascade');
            $table->integer('response_time')->nullable(); // in seconds
            $table->boolean('is_talent_response')->default(false);
            $table->timestamps();
            $table->softDeletes();
            
            $table->index(['responder_id', 'is_talent_response']);
            $table->index(['conversation_id', 'response_time']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chat_response_times');
    }
};
