<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->boolean('special_commission')->default(false)->after('credits_balance');
            $table->unsignedInteger('special_commission_percentage')->nullable()->comment('Percentage value (0-100)')->after('special_commission');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            if (Schema::hasColumn('users', 'special_commission')) {
                $table->dropColumn('special_commission');
            }
            
            if (Schema::hasColumn('users', 'special_commission_percentage')) {
                $table->dropColumn('special_commission_percentage');
            } elseif (Schema::hasColumn('users', 'special_commission_value')) {
                $table->dropColumn('special_commission_value');
            }
        });
    }
};
