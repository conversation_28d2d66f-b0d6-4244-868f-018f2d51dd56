<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_service_styles', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_service_id');
            $table->unsignedBigInteger('service_style_id');
            $table->boolean('is_active')->default(false);
            $table->unsignedInteger('price')->nullable();
            $table->timestamps();
            $table->softDeletes();
            
            $table->foreign('user_service_id')->references('id')->on('user_services')->onDelete('cascade');
            $table->foreign('service_style_id')->references('id')->on('service_styles')->onDelete('cascade');
            
            $table->unique(['user_service_id', 'service_style_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_service_styles');
    }
};
