<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chat_response_metrics', function (Blueprint $table) {
            $table->id();
            $table->foreignId('talent_id')->constrained('users')->onDelete('cascade');
            $table->unsignedInteger('total_received_messages')->default(0);
            $table->unsignedInteger('total_responded_messages')->default(0);
            $table->float('average_response_time')->nullable(); // in seconds
            $table->float('response_rate')->default(0); // percentage (0-100)
            $table->timestamp('last_calculation_at')->nullable();
            $table->timestamps();
            $table->softDeletes();
            
            $table->unique('talent_id');
            
            $table->index(['response_rate']);
            $table->index(['average_response_time']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chat_response_metrics');
    }
};
