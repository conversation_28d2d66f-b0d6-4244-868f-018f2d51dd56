<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('disputes', function (Blueprint $table) {
            $columnsToDrop = [
                'media1_original_path',
                'media1_optimized_path',
                'media1_mime_type',
                'media1_file_size',
                'media2_original_path',
                'media2_optimized_path',
                'media2_mime_type',
                'media2_file_size',
                'media3_original_path',
                'media3_optimized_path',
                'media3_mime_type',
                'media3_file_size',
                'video_original_path',
                'video_mime_type',
                'video_file_size',
            ];

            // Only drop columns that exist
            $table->dropColumn(array_filter($columnsToDrop, function ($column) {
                return Schema::hasColumn('disputes', $column);
            }));
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('disputes', function (Blueprint $table) {
            // Only add columns if they don't already exist
            if (!Schema::hasColumn('disputes', 'media1_original_path')) {
                $table->string('media1_original_path')->nullable();
            }
            if (!Schema::hasColumn('disputes', 'media1_optimized_path')) {
                $table->string('media1_optimized_path')->nullable();
            }
            if (!Schema::hasColumn('disputes', 'media1_mime_type')) {
                $table->string('media1_mime_type')->nullable();
            }
            if (!Schema::hasColumn('disputes', 'media1_file_size')) {
                $table->integer('media1_file_size')->nullable();
            }
            if (!Schema::hasColumn('disputes', 'media2_original_path')) {
                $table->string('media2_original_path')->nullable();
            }
            if (!Schema::hasColumn('disputes', 'media2_optimized_path')) {
                $table->string('media2_optimized_path')->nullable();
            }
            if (!Schema::hasColumn('disputes', 'media2_mime_type')) {
                $table->string('media2_mime_type')->nullable();
            }
            if (!Schema::hasColumn('disputes', 'media2_file_size')) {
                $table->integer('media2_file_size')->nullable();
            }
            if (!Schema::hasColumn('disputes', 'media3_original_path')) {
                $table->string('media3_original_path')->nullable();
            }
            if (!Schema::hasColumn('disputes', 'media3_optimized_path')) {
                $table->string('media3_optimized_path')->nullable();
            }
            if (!Schema::hasColumn('disputes', 'media3_mime_type')) {
                $table->string('media3_mime_type')->nullable();
            }
            if (!Schema::hasColumn('disputes', 'media3_file_size')) {
                $table->integer('media3_file_size')->nullable();
            }
            if (!Schema::hasColumn('disputes', 'video_original_path')) {
                $table->string('video_original_path')->nullable();
            }
            if (!Schema::hasColumn('disputes', 'video_mime_type')) {
                $table->string('video_mime_type')->nullable();
            }
            if (!Schema::hasColumn('disputes', 'video_file_size')) {
                $table->integer('video_file_size')->nullable();
            }
        });
    }
};
