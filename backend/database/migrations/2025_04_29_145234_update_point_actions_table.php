<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::table('point_actions')->whereIn('code', [
            'registration',
            'profile_completion',
            'topup',
            'job_completion',
            'referral_job_completion'
        ])->delete();

        DB::table('point_actions')->insert([
            [
                'name' => 'Referral Profile Completion',
                'code' => 'referral_profile_completion',
                'points' => 25,
                'description' => 'Points earned when a referred user completes their profile',
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Referral Order Completion',
                'code' => 'referral_order_completion',
                'points' => 10,
                'description' => 'Points earned when a referred user completes an order',
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Referral Mission Completion',
                'code' => 'referral_mission_completion',
                'points' => 15,
                'description' => 'Points earned when a referred user completes a mission',
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Referral Talent Certified',
                'code' => 'referral_talent_certified',
                'points' => 30,
                'description' => 'Points earned when a referred user becomes a certified talent',
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now()
            ]
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::table('point_actions')->whereIn('code', [
            'referral_profile_completion',
            'referral_order_completion',
            'referral_mission_completion',
            'referral_talent_certified'
        ])->delete();

        DB::table('point_actions')->insert([
            [
                'name' => 'Registration',
                'code' => 'registration',
                'points' => 100,
                'description' => 'Points earned for registering an account',
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Profile Completion',
                'code' => 'profile_completion',
                'points' => 25,
                'description' => 'Points earned for completing profile',
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Top-up',
                'code' => 'topup',
                'points' => 10,
                'description' => 'Points earned for each top-up',
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Job Completion',
                'code' => 'job_completion',
                'points' => 20,
                'description' => 'Points earned for completing a job',
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Referral Job Completion',
                'code' => 'referral_job_completion',
                'points' => 10,
                'description' => 'Points earned when a referred user completes a job',
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now()
            ]
        ]);
    }
};
