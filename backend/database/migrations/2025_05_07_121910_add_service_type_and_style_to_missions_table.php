<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasColumn('missions', 'service_type_id')) {
            Schema::table('missions', function (Blueprint $table) {
                $table->foreignId('service_type_id')->after('user_id')->nullable();
            });
        }
        
        if (!Schema::hasColumn('missions', 'service_style_id')) {
            Schema::table('missions', function (Blueprint $table) {
                $table->foreignId('service_style_id')->after('service_type_id')->nullable();
            });
        }
        
        if (Schema::hasColumn('missions', 'service_category_id') && 
            Schema::hasColumn('missions', 'service_type_id') && 
            Schema::hasColumn('missions', 'service_style_id')) {
            
            $serviceTypes = DB::table('service_types')->get();
            $serviceTypeMap = [];
            
            foreach ($serviceTypes as $serviceType) {
                if (property_exists($serviceType, 'service_category_id')) {
                    $serviceTypeMap[$serviceType->service_category_id] = $serviceType->id;
                }
            }
            
            $missions = DB::table('missions')->get();
            
            foreach ($missions as $mission) {
                if (property_exists($mission, 'service_category_id') && 
                    isset($serviceTypeMap[$mission->service_category_id])) {
                    
                    $serviceTypeId = $serviceTypeMap[$mission->service_category_id];
                    
                    $serviceStyle = DB::table('service_styles')
                        ->where('service_type_id', $serviceTypeId)
                        ->orderBy('id')
                        ->first();
                    
                    $serviceStyleId = $serviceStyle ? $serviceStyle->id : null;
                    
                    if ($serviceTypeId && $serviceStyleId) {
                        try {
                            DB::table('missions')
                                ->where('id', $mission->id)
                                ->update([
                                    'service_type_id' => $serviceTypeId,
                                    'service_style_id' => $serviceStyleId,
                                ]);
                        } catch (\Exception $e) {
                            error_log('Error updating mission ' . $mission->id . ': ' . $e->getMessage());
                        }
                    }
                }
            }
            
            Schema::table('missions', function (Blueprint $table) {
                try {
                    if (DB::getSchemaBuilder()->getColumnType('missions', 'service_type_id') === 'bigint') {
                        $table->foreignId('service_type_id')->nullable(false)->change();
                    }
                    
                    if (DB::getSchemaBuilder()->getColumnType('missions', 'service_style_id') === 'bigint') {
                        $table->foreignId('service_style_id')->nullable(false)->change();
                    }
                    
                    $table->foreign('service_type_id')->references('id')->on('service_types');
                    $table->foreign('service_style_id')->references('id')->on('service_styles');
                    
                    if (Schema::hasColumn('missions', 'service_category_id')) {
                        $table->dropForeign(['service_category_id']);
                        $table->dropColumn('service_category_id');
                    }
                } catch (\Exception $e) {
                    error_log('Error in final schema updates: ' . $e->getMessage());
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (!Schema::hasColumn('missions', 'service_category_id')) {
            Schema::table('missions', function (Blueprint $table) {
                $table->foreignId('service_category_id')->after('user_id')->nullable();
            });
            
            if (Schema::hasColumn('missions', 'service_type_id') && 
                Schema::hasColumn('missions', 'service_category_id')) {
                
                $serviceTypes = DB::table('service_types')->get();
                $reverseCategoryMap = [];
                
                foreach ($serviceTypes as $serviceType) {
                    if (property_exists($serviceType, 'service_category_id')) {
                        $reverseCategoryMap[$serviceType->id] = $serviceType->service_category_id;
                    }
                }
                
                $missions = DB::table('missions')->get();
                
                foreach ($missions as $mission) {
                    if (property_exists($mission, 'service_type_id') && 
                        isset($reverseCategoryMap[$mission->service_type_id])) {
                        
                        try {
                            DB::table('missions')
                                ->where('id', $mission->id)
                                ->update([
                                    'service_category_id' => $reverseCategoryMap[$mission->service_type_id],
                                ]);
                        } catch (\Exception $e) {
                            error_log('Error updating mission ' . $mission->id . ': ' . $e->getMessage());
                        }
                    }
                }
                
                Schema::table('missions', function (Blueprint $table) {
                    try {
                        $table->foreignId('service_category_id')->nullable(false)->change();
                        $table->foreign('service_category_id')->references('id')->on('service_categories');
                    } catch (\Exception $e) {
                        error_log('Error adding service_category_id constraints: ' . $e->getMessage());
                    }
                });
            }
        }
        
        if (Schema::hasColumn('missions', 'service_type_id') || 
            Schema::hasColumn('missions', 'service_style_id')) {
            
            Schema::table('missions', function (Blueprint $table) {
                try {
                    if (Schema::hasColumn('missions', 'service_type_id')) {
                        $table->dropForeign(['service_type_id']);
                        $table->dropColumn('service_type_id');
                    }
                    
                    if (Schema::hasColumn('missions', 'service_style_id')) {
                        $table->dropForeign(['service_style_id']);
                        $table->dropColumn('service_style_id');
                    }
                } catch (\Exception $e) {
                    error_log('Error dropping columns: ' . $e->getMessage());
                }
            });
        }
    }
};
