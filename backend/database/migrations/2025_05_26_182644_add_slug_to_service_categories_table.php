<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;
use App\Models\ServiceCategory; // Adjust the namespace based on your model location

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('service_categories', function (Blueprint $table) {
            // Add the slug column as nullable initially
            $table->string('slug')->nullable()->after('name');
        });

        // Populate slugs for existing records
        $categories = ServiceCategory::all();
        foreach ($categories as $category) {
            // Generate a unique slug based on the name
            $slug = Str::slug($category->name);
            $baseSlug = $slug;
            $counter = 1;

            // Ensure the slug is unique
            while (ServiceCategory::where('slug', $slug)->where('id', '!=', $category->id)->exists()) {
                $slug = $baseSlug . '-' . $counter++;
            }

            $category->slug = $slug;
            $category->save();
        }

        Schema::table('service_categories', function (Blueprint $table) {
            // Make slug non-nullable and add unique constraint
            $table->string('slug')->change();
            $table->unique('slug');
            $table->index('slug');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('service_categories', function (Blueprint $table) {
            $table->dropIndex(['slug']);
            $table->dropColumn('slug');
        });
    }
};
