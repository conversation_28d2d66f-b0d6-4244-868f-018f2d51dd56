<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('mission_applicants', function (Blueprint $table) {
            $table->integer('credit_amount')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('mission_applicants', function (Blueprint $table) {
            $table->decimal('credit_amount', 10, 2)->nullable()->change();
        });
    }
};
