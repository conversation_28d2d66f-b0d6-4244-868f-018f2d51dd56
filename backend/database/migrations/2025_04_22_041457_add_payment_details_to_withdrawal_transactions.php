<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('withdrawal_transactions', function (Blueprint $table) {
            $table->string('payment_mode')->default('duitnow')->after('fiat_amount');
            $table->string('recipient_reference')->nullable()->after('payment_mode');
            $table->text('other_payment_details')->nullable()->after('recipient_reference');
            $table->string('id_validation_type')->nullable()->after('other_payment_details');
            $table->string('id_validation_value')->nullable()->after('id_validation_type');
            $table->string('transaction_type')->nullable()->after('id_validation_value');
            $table->string('transaction_code')->default('duitnw')->nullable()->after('transaction_type');
            $table->string('purpose_of_transfer')->nullable()->after('transaction_code');
            $table->string('email')->nullable()->after('purpose_of_transfer');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('withdrawal_transactions', function (Blueprint $table) {
            $table->dropColumn([
                'payment_mode',
                'recipient_reference',
                'other_payment_details',
                'id_validation_type',
                'id_validation_value',
                'transaction_type',
                'transaction_code',
                'purpose_of_transfer',
                'email'
            ]);
        });
    }
};
