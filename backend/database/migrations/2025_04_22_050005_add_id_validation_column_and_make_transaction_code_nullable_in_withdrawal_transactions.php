<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('withdrawal_transactions', function (Blueprint $table) {
            $table->string('id_validation')->nullable()->after('id_validation_value');
            $table->string('transaction_code')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('withdrawal_transactions', function (Blueprint $table) {
            $table->dropColumn('id_validation');
            $table->string('transaction_code')->default('duitnw')->change();
        });
    }
};
