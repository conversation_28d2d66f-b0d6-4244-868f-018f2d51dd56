<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('scheduled_orders', function (Blueprint $table) {
            $table->foreignId('service_style_id')->nullable()->after('pricing_option_type_id')->constrained('service_styles');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('scheduled_orders', function (Blueprint $table) {
            $table->dropForeign(['service_style_id']);
            $table->dropColumn('service_style_id');
        });
    }
};
