<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $userModelPath = app_path('Models/User.php');
        
        if (file_exists($userModelPath)) {
            $content = file_get_contents($userModelPath);
            
            $newCode = '$specialToday = $this->specialAvailabilities()
            ->whereRaw("JSON_CONTAINS(special_dates, JSON_OBJECT(\'date\', CONCAT(\'"\', ?, \'"\') ), \'$\')", [$today])
            ->orWhereRaw("JSON_CONTAINS(special_dates, JSON_OBJECT(\'date\', CONCAT(\'"\', ?, \'"\') ), \'$[*]\')", [$today])
            ->get();';
            
            $oldCode = '$specialToday = $this->specialAvailabilities()
            ->whereRaw("JSON_CONTAINS(special_dates, JSON_OBJECT(\'date\', ?), \'$\')", [$today])
            ->orWhereRaw("JSON_CONTAINS(special_dates, JSON_OBJECT(\'date\', ?), \'$[*]\')", [$today])
            ->get();';
            
            $updatedContent = str_replace($newCode, $oldCode, $content);
            
            if ($content !== $updatedContent) {
                file_put_contents($userModelPath, $updatedContent);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        $userModelPath = app_path('Models/User.php');
        
        if (file_exists($userModelPath)) {
            $content = file_get_contents($userModelPath);
            
            $oldCode = '$specialToday = $this->specialAvailabilities()
            ->whereRaw("JSON_CONTAINS(special_dates, JSON_OBJECT(\'date\', ?), \'$\')", [$today])
            ->orWhereRaw("JSON_CONTAINS(special_dates, JSON_OBJECT(\'date\', ?), \'$[*]\')", [$today])
            ->get();';
            
            $newCode = '$specialToday = $this->specialAvailabilities()
            ->whereRaw("JSON_CONTAINS(special_dates, JSON_OBJECT(\'date\', CONCAT(\'"\', ?, \'"\') ), \'$\')", [$today])
            ->orWhereRaw("JSON_CONTAINS(special_dates, JSON_OBJECT(\'date\', CONCAT(\'"\', ?, \'"\') ), \'$[*]\')", [$today])
            ->get();';
            
            $updatedContent = str_replace($oldCode, $newCode, $content);
            
            if ($content !== $updatedContent) {
                file_put_contents($userModelPath, $updatedContent);
            }
        }
    }
};
