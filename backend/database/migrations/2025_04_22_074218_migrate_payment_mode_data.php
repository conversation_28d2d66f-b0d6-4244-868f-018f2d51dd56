<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $seeder = new \Database\Seeders\PaymentModeSeeder();
        $seeder->run();
        
        $withdrawalTransactions = \App\Models\WithdrawalTransaction::whereNotNull('payment_mode')->get();
        
        foreach ($withdrawalTransactions as $transaction) {
            $paymentMode = \App\Models\PaymentMode::where('payment_mode_code', $transaction->payment_mode)->first();
            
            if ($paymentMode) {
                $transaction->payment_mode_id = $paymentMode->id;
                $transaction->save();
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
    }
};
