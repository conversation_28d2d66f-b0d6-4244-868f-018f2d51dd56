<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_service_pricing_options', function (Blueprint $table) {
            $table->renameColumn('pricing_option_id', 'pricing_option_type_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_service_pricing_options', function (Blueprint $table) {
            $table->renameColumn('pricing_option_type_id', 'pricing_option_id');
        });
    }
};
