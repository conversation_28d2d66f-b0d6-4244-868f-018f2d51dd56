<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up(): void
    {
        // Drop the foreign key constraint and column for service_type_id
        if (Schema::hasColumn('service_styles', 'service_type_id')) {
            Schema::table('service_styles', function (Blueprint $table) {
                // Drop the foreign key constraint
                $table->dropForeign(['service_type_id']);
                // Drop the column
                $table->dropColumn('service_type_id');
            });
        }

        // Add the service_category_id column if it doesn't exist
        if (!Schema::hasColumn('service_styles', 'service_category_id')) {
            Schema::table('service_styles', function (Blueprint $table) {
                $table->foreignId('service_category_id')->after('id')->constrained()->onDelete('cascade');
            });
        }
    }

    /**
     * Get all foreign keys for a table
     */
    private function getForeignKeys(string $tableName): array
    {
        $conn = Schema::getConnection()->getDoctrineSchemaManager();
        $foreignKeys = [];

        try {
            $tableIndexes = $conn->listTableForeignKeys($tableName);
            foreach ($tableIndexes as $tableIndex) {
                $foreignKeys[] = $tableIndex->getName();
            }
        } catch (\Exception $e) {
        }

        return $foreignKeys;
    }

    public function down(): void
    {
        // Drop the service_category_id column
        if (Schema::hasColumn('service_styles', 'service_category_id')) {
            Schema::table('service_styles', function (Blueprint $table) {
                // Drop the foreign key constraint and column
                $table->dropForeign(['service_category_id']);
                $table->dropColumn('service_category_id');
            });
        }

        // Re-add the service_type_id column if it doesn't exist
        if (!Schema::hasColumn('service_styles', 'service_type_id')) {
            Schema::table('service_styles', function (Blueprint $table) {
                $table->foreignId('service_type_id')->after('id');
                $table->foreign('service_type_id')->references('id')->on('service_types')->onDelete('cascade');
            });
        }
    }
};