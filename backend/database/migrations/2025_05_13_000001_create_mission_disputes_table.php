<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('mission_disputes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->comment('ID of the user who created the dispute');
            $table->enum('requestor_type', ['creator', 'applicant'])->comment('Indicates whether the dispute was created by the mission creator or an applicant');
            $table->foreignId('mission_id')->constrained()->comment('ID of the mission being disputed');
            $table->foreignId('child_id')->nullable()->constrained('users')->comment('Null if the requestor_type is creator');
            $table->foreignId('dispute_type_id')->constrained();
            $table->text('description');
            $table->enum('status', ['submitted', 'in_review', 'resolved', 'rejected'])->default('submitted');
            $table->enum('resolution_type', ['refund', 'partial_settlement', 'full_settlement'])->nullable();
            $table->text('resolution_notes')->nullable()->comment('Notes added by staff when resolving the dispute');
            $table->decimal('refund_amount', 10, 2)->default(0)->comment('Amount refunded if any');
            $table->timestamp('resolved_at')->nullable();
            $table->foreignId('resolved_by')->nullable()->references('id')->on('users')->comment('Staff who resolved the dispute');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('mission_disputes');
    }
};
