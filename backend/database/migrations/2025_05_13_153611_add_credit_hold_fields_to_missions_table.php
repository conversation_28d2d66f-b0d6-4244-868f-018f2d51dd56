<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('missions', function (Blueprint $table) {
            $table->boolean('is_credit_held')->default(false)->after('status');
            $table->timestamp('credit_held_at')->nullable()->after('is_credit_held');
            $table->unsignedInteger('credit_amount')->nullable()->after('credit_held_at');
            $table->unsignedInteger('commission_amount')->nullable()->after('credit_amount');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('missions', function (Blueprint $table) {
            $table->dropColumn('is_credit_held');
            $table->dropColumn('credit_held_at');
            $table->dropColumn('credit_amount');
            $table->dropColumn('commission_amount');
        });
    }
};
