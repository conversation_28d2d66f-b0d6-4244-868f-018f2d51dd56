<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("ALTER TABLE disputes MODIFY COLUMN requestor_type ENUM('customer', 'client', 'talent') COMMENT 'Role of the user who created the dispute'");
        
        DB::statement("UPDATE disputes SET requestor_type = 'client' WHERE requestor_type = 'customer'");
        
        DB::statement("ALTER TABLE disputes MODIFY COLUMN requestor_type ENUM('client', 'talent') COMMENT 'Role of the user who created the dispute'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement("ALTER TABLE disputes MODIFY COLUMN requestor_type ENUM('customer', 'talent') COMMENT 'Role of the user who created the dispute'");
        
        DB::statement("UPDATE disputes SET requestor_type = 'customer' WHERE requestor_type = 'client'");
    }
};
