<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('talent_views', function (Blueprint $table) {
            $table->id();
            $table->foreignId('talent_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('viewer_id')->nullable()->constrained('users')->onDelete('set null');
            $table->string('ip_address', 45);
            $table->text('user_agent')->nullable();
            $table->boolean('is_counted')->default(true);
            $table->unsignedInteger('view_count')->default(1);
            $table->timestamp('last_view_at')->nullable();
            $table->timestamps();
            $table->softDeletes();
            
            $table->unique(['talent_id', 'ip_address']);
            
            $table->index(['talent_id', 'view_count']);
            $table->index(['ip_address', 'last_view_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('talent_views');
    }
};
