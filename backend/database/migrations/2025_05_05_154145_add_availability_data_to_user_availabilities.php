<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_availabilities', function (Blueprint $table) {
            $table->json('availability_data')->nullable()->after('is_recurring');
        });

        Schema::table('user_availabilities', function (Blueprint $table) {
            $table->dropColumn(['day_of_week', 'start_time', 'end_time']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_availabilities', function (Blueprint $table) {
            $table->enum('day_of_week', ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'])->nullable()->after('user_id');
            $table->time('start_time')->after('special_date');
            $table->time('end_time')->after('start_time');
        });

        Schema::table('user_availabilities', function (Blueprint $table) {
            $table->dropColumn('availability_data');
        });
    }
};
