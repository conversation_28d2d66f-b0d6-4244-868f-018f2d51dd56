<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            if (Schema::hasColumn('users', 'special_commission_value') && !Schema::hasColumn('users', 'special_commission_percentage')) {
                $table->renameColumn('special_commission_value', 'special_commission_percentage');
            } elseif (!Schema::hasColumn('users', 'special_commission_percentage')) {
                $table->unsignedInteger('special_commission_percentage')->nullable()->comment('Percentage value (0-100)')->after('special_commission');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            if (Schema::hasColumn('users', 'special_commission_percentage')) {
                $table->renameColumn('special_commission_percentage', 'special_commission_value');
            }
        });
    }
};
