<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('game_id_service_type', function (Blueprint $table) {
            $table->foreignId('game_id')->constrained('game_ids')->cascadeOnDelete();
            $table->foreignId('service_type_id')->constrained('service_types')->cascadeOnDelete();
            $table->timestamps();

            $table->primary(['game_id', 'service_type_id']);
        });

        $serviceTypes = DB::table('service_types')->whereNotNull('id_fields')->get();
        foreach ($serviceTypes as $serviceType) {
            $idFields = json_decode($serviceType->id_fields, true);
            if (is_array($idFields) && !empty($idFields)) {
                foreach ($idFields as $gameId) {
                    DB::table('game_id_service_type')->insert([
                        'game_id' => $gameId,
                        'service_type_id' => $serviceType->id,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('game_id_service_type');
    }
};
