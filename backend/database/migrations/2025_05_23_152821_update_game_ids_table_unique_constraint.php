<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('game_ids', function (Blueprint $table) {
            $table->dropUnique('game_ids_slug_unique');
            
            $table->unique(['slug', 'deleted_at'], 'game_ids_slug_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('game_ids', function (Blueprint $table) {
            $table->dropUnique('game_ids_slug_unique');
            
            $table->unique('slug', 'game_ids_slug_unique');
        });
    }
};
