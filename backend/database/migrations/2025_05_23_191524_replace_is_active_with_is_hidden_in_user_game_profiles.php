<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_game_profiles', function (Blueprint $table) {
            $table->boolean('is_hidden')->default(true);
            $table->dropColumn('is_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_game_profiles', function (Blueprint $table) {
            $table->boolean('is_active')->default(true);
            $table->dropColumn('is_hidden');
        });
    }
};
