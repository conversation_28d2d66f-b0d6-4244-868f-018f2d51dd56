<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('mission_applicants', function (Blueprint $table) {
            $table->decimal('credit_amount', 10, 2)->nullable()->after('notes');
            $table->boolean('is_credit_held')->default(false)->after('credit_amount');
            $table->timestamp('credit_held_at')->nullable()->after('is_credit_held');
            $table->timestamp('credit_release_at')->nullable()->after('credit_held_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('mission_applicants', function (Blueprint $table) {
            $table->dropColumn('credit_amount');
            $table->dropColumn('is_credit_held');
            $table->dropColumn('credit_held_at');
            $table->dropColumn('credit_release_at');
        });
    }
};
