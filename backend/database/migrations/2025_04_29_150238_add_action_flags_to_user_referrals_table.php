<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_referrals', function (Blueprint $table) {
            $table->boolean('isRegistered')->default(false)->after('converted_at');
            $table->boolean('isTopUp')->default(false)->after('isRegistered');
            $table->boolean('isProfileCompleted')->default(false)->after('isTopUp');
            $table->boolean('isOrdersCompleted')->default(false)->after('isProfileCompleted');
            $table->boolean('isMissionsCompleted')->default(false)->after('isOrdersCompleted');
            $table->boolean('isTalentCertified')->default(false)->after('isMissionsCompleted');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_referrals', function (Blueprint $table) {
            $table->dropColumn([
                'isRegistered',
                'isTopUp',
                'isProfileCompleted',
                'isOrdersCompleted',
                'isMissionsCompleted',
                'isTalentCertified'
            ]);
        });
    }
};
