<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('mission_applicants', function (Blueprint $table) {
            $table->unsignedInteger('child_id')->nullable()->after('mission_id');
            $table->unique(['mission_id', 'child_id'], 'mission_applicant_unique');
        });

        $missions = DB::table('missions')->pluck('id');

        foreach ($missions as $missionId) {
            $applicants = DB::table('mission_applicants')
                ->where('mission_id', $missionId)
                ->orderBy('created_at')
                ->get();
            
            $childId = 1;
            foreach ($applicants as $applicant) {
                DB::table('mission_applicants')
                    ->where('id', $applicant->id)
                    ->update(['child_id' => $childId]);
                
                $childId++;
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('mission_applicants', function (Blueprint $table) {
            $table->dropUnique('mission_applicant_unique');
            $table->dropColumn('child_id');
        });
    }
};
