<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_game_profiles', function (Blueprint $table) {
            $table->enum('status', ['pendingReview', 'Reviewed'])->default('pendingReview');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_game_profiles', function (Blueprint $table) {
            $table->dropColumn('status');
        });
    }
};
